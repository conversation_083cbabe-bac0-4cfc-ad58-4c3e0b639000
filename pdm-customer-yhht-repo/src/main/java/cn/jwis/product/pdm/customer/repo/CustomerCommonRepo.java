package cn.jwis.product.pdm.customer.repo;

import cn.jwis.framework.base.exception.JWIException;
import cn.jwis.framework.base.response.PageResult;
import cn.jwis.framework.database.core.query.dynamic.Condition;
import cn.jwis.platform.iam.structure.TreeAbleEx;
import cn.jwis.platform.plm.account.entity.team.response.TeamTemplateRoleWithUser;
import cn.jwis.platform.plm.container.entity.Folder;
import cn.jwis.platform.plm.container.entity.TeamRole;
import cn.jwis.platform.plm.foundation.classification.entity.Classification;
import cn.jwis.platform.plm.foundation.classification.related.ClsLayout;
import cn.jwis.platform.plm.foundation.common.response.InstanceEntity;
import cn.jwis.platform.plm.permission.administrativedomain.entity.AdministrativeDomain;
import cn.jwis.platform.plm.workflow.engine.dto.ProOrdOfBizObjFuzzyPageDTO;
import cn.jwis.platform.plm.workflow.engine.dto.ProcessOrderDetail;
import cn.jwis.platform.plm.workflow.engine.order.entity.ProcessOrder;
import cn.jwis.product.pdm.change.entity.Issue;
import cn.jwis.product.pdm.customer.dos.PathDo;
import cn.jwis.product.pdm.customer.dos.UserRoleInfo;
import cn.jwis.product.pdm.customer.entity.*;
import cn.jwis.product.pdm.delivery.entity.Delivery;
import cn.jwis.product.pdm.document.entity.DocumentIteration;
import cn.jwis.product.pdm.partbom.part.dto.ConfigDTO;
import cn.jwis.product.pdm.partbom.part.entity.PartIteration;
import com.alibaba.fastjson.JSONObject;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2023/8/16 10:45
 * @Email <EMAIL>
 */
public interface CustomerCommonRepo {

    public List<PartIteration> querySearch(List<ConfigDTO> systemProp, List<ConfigDTO> extendProp, List<ConfigDTO> classificationProp, JSONObject partIteration);

    List<Folder> getFolderPath(String type, String oid) throws Exception;

    List<Issue> findIssueByEntity(String key, List<String> value,boolean onlyUnClosed) throws Exception;

    List<TeamTemplateRoleWithUser> autoUser(List<String> contentOidList);

    Map<String, List<TeamRole>> queryContainerRole(String tenantOid, String userOid);

    List<AdministrativeDomain> queryFolderDomain(String tenantOid);

    int lastIsParent(String prevOid, String nextOid);

    List<InstanceEntity> queryReviewsDetail(String processOrderOid);

    List<DeliveryReport> getDeliveryData(String containerOid);

    Boolean batchSendERPPermission(String account);

    String queryInCls(Collection<String> ancestorClsList, String instanceOid);

    String queryClsType(Collection<String> ancestorClsDisplaNameList, String instanceOid);

    String cvtLifcycleStatus(String lifecycleStatus);

    List<Map> findByOidList(List<String> oidList);

    List<DocumentIteration> queryDoc(List<String> oidList);

//    List<FileMetadata> queryFile();

    List<DocumentIteration> queryBorrowDocs(String docOid);

    void setSecondFile(String docOid, List<String> secondFileStrList);
    List<DocumentIteration> queryFileDocumentIteration(String pdmProcessOrderId);

    void setSecondFile(String docOid, String secondFileStr);

    String  queryDingProcessOrderId(String processInstanceId);

    void updateLifeStatus(String type,List<String> oidList,String lifecycleStatus);

    void updateContainerModelDefinition(Collection<String> oidList, String containerModelDefinition);

    Map<String,String> queryRepeatOidLink();

    DocumentIteration dynamicQueryByFrom(String processOrderOid) throws JWIException;

    List<Map<String, Object>> getUserList(List<String> accountList);

    List<ProcessOrder> queryReviewDocProcessOrder(String docOid);

    List<String> stopStatusSetPartName(List<String> oidList);

    List<String> unStopStatusSetPartName(List<String> oidList);

    String queryName(String userPhone);

    String getUserDepart(List<String> accountList);


    List<PathDo> queryAllPath(List<String> nodeOidList, String type);

    Delivery findByClsOidAndName(String catalogOid, String oid, String clsOid, String name, String modelDefinition, String parentOid);

    DingTaskRecord queryDingTaskRecored(String documentOid);

    Map queryEcrDingTaskRecored(String documentOid) throws JWIException;

    List<CustomerDeliveryTreeNode> querySelectDeliveryTree(String containerOid, String userOid);


    PageResult<ProcessOrderDetail> queryProcessOrderByCreator(String createBy, int index, int size);

    PageResult<ProcessOrderDetail> fuzzyPageByBiz(ProOrdOfBizObjFuzzyPageDTO dto);


    void updateDeliveryExtension(String oid, Object FZR, Object jhsj, Object wcsj);

    List<CustomerFolderTreeNode> searchFoldersWithPermisson(String containerType, String containerModel, String containerOid, String searchKey) throws JWIException;

    List<ClsLayout> queryLayoutByClassCode(Collection<String> clsCodeList);

    PageResult<PermApplyEntity> queryPerm(PermApplyEntity permApplyEntity);

    List<UserRoleInfo> queryUserRoleInfo(String account);

    List<UserRoleInfo> queryVisitorRoleInFolder(Collection<String> folderOidList);

    long updateDingTalkRecordDocList(String oid,List<String> docOidList);

    List<TreeAbleEx> searchUserTree(String tenantOid, Condition condition);

    String queryClsRelCodeByOid(String oid);

    <T> List<T> findRelationByTo(String fromType, String relationType, String toType, Collection<String> toOid);
    List<DeliveryReportCustom> getDeliveryDataForExport(String containerOid, boolean isNormal);

    Map<String,List<String>> findRelationBothSideByTo(String fromType, String relationType, String toType,
                                           Collection<String> toOid);

    List<Classification> queryTreeByChildOid(String oid);

    List<CustomerDeliveryTreeNode> querySelectDeliveryTreeNew(String containerOid, String userOid);

    List<CustomerDeliveryTreeNode> querySelectDeliveryTreeNewTwo(String containerOid, String userOid);
}
