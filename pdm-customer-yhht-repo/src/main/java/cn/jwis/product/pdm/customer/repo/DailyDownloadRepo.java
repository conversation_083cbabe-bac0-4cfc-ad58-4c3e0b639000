package cn.jwis.product.pdm.customer.repo;

import cn.jwis.framework.database.neo4j.annotation.JwiNeo4jRepository;
import cn.jwis.framework.database.neo4j.annotation.Param;
import cn.jwis.framework.database.neo4j.annotation.Query;

import java.util.Map;
import java.util.List;


@JwiNeo4jRepository
public interface DailyDownloadRepo {


    @Query("MATCH (u:User)-[:BELONG_TO]->(p:Position) " +
            "WHERE u.oid IN $oids " +
            "WITH u, p " +
            "ORDER BY p.createdTime DESC " +
            "WITH u, COLLECT(p)[0] AS position " +
            "OPTIONAL MATCH path=(position)-[:BELONG_TO*]->(d:Department) " +
            "WITH u, position, COLLECT(COALESCE(d.name, '')) AS department_names " +
            "RETURN u.email AS email, " +
            "       u.account AS account, " +
            "       u.phone AS phone, " +
            "       u.number AS number, " +
            "       u.name AS name, " +
            "       u.oid AS oid, " +
            "       REDUCE(path_string = '', name IN department_names | " +
            "              CASE WHEN path_string = '' THEN name ELSE path_string + '-' + name END) AS department_path")
    List<Map<String, Object>> fetchUserAndDepartmentData(@Param("oids") List<String> oids);

    @Query("MATCH (u:User)-[:BELONG_TO]->(p:Position) " +
            "WHERE u.oid = $oid " +
            "WITH p " +
            "OPTIONAL MATCH path=(p)-[:BELONG_TO*]->(d:Department) " +
            "WITH COLLECT(COALESCE(d.name, '')) AS department_names " +
            "RETURN REDUCE(path_string = '', name IN department_names | " +
            "              CASE WHEN path_string = '' THEN name ELSE path_string + '-' + name END) AS department_path")
    String getUserDepart(@Param("oid") String oid);
}
