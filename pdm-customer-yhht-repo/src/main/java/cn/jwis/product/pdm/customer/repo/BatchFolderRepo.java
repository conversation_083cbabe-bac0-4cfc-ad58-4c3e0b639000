package cn.jwis.product.pdm.customer.repo;

import cn.jwis.platform.iam.user.User;
import cn.jwis.platform.plm.container.entity.TeamRole;
import com.alibaba.fastjson.JSONObject;

import java.util.List;

/**
 * <AUTHOR> zhaikun
 * @Date ： 2024/2/2
 * @Description :
 */
public interface BatchFolderRepo {

     List<JSONObject> queryFolderPaths(String containerOid);

     List<JSONObject> queryAdministrativeDomainPaths(String rootFolderOid);

    TeamRole queryContainerMemberRole(String containerOid);


    List<User> queryContainerMembers(String containerOid);

}
