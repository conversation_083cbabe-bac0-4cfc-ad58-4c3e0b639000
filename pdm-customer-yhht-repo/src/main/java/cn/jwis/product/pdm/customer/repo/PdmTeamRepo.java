package cn.jwis.product.pdm.customer.repo;

import cn.jwis.framework.base.response.PageResult;
import cn.jwis.platform.iam.user.User;
import cn.jwis.platform.plm.account.entity.team.TeamTemplate;
import cn.jwis.platform.plm.container.entity.TeamRole;
import cn.jwis.product.pdm.customer.entity.CustomerUnBindUserDTO;

import java.util.List;

/**
 * <AUTHOR> lhm
 * @Date ： Created in 2023/9/13 10:51
 * @Description :
 */
public interface PdmTeamRepo {

    List<TeamTemplate> findProcessTeam(String searchKey);

    PageResult<TeamTemplate> findProcessTeamPage(String searchKey, int index, int size);

    TeamRole findContainerMemberRole(String containerOid, String roleName);

    List<User> findFolderTeamAllUser(String containerOid);

    Object searchAllTeamRole(String containerOid, String searchKey, int index, int size);

    void  deleteAllTeamRoleUser(CustomerUnBindUserDTO dto);

    void batchHandleTeamRoleUser(CustomerUnBindUserDTO dto);
}
