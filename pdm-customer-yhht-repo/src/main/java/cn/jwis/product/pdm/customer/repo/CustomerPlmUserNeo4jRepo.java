package cn.jwis.product.pdm.customer.repo;

import cn.jwis.framework.database.neo4j.annotation.JwiNeo4jRepository;
import cn.jwis.framework.database.neo4j.annotation.Param;
import cn.jwis.framework.database.neo4j.annotation.Query;

import java.util.List;

/**
 * <AUTHOR> yefei
 * @Date ： 2024/4/25
 * @Description :
 */
@JwiNeo4jRepository
public interface CustomerPlmUserNeo4jRepo {


    @Query("MATCH  (s:SystemRole)-[r:ASSIGN]->(u:User) WHERE u.oid=$userOid and (s.name='DataAdministrators') and r.tenantOid=$containerOid RETURN count(u) > 0 ")
    boolean assertUserDataAdmin(@Param("userOid") String userOid, @Param("containerOid") String containerOid);

    /**
     * 用户每日下载 角色白名单
     * @param userOid 用户oid
     * @param tenantOid 租户oid
     * @param roleList 角色清单
     * @return boolean
     */
    @Query("MATCH (s:SystemRole)-[r:ASSIGN]->(u:User) " +
            "WHERE u.oid=$userOid AND s.name IN $roleList AND r.tenantOid=$tenantOid " +
            "RETURN count(u) > 0")
    boolean assertUserIsAdmin(@Param("userOid") String userOid,
                              @Param("tenantOid") String tenantOid,
                              @Param("roleList") List<String> roleList);
}
