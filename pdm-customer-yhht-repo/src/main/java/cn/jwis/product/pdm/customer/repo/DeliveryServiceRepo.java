package cn.jwis.product.pdm.customer.repo;

import cn.jwis.product.pdm.delivery.entity.Delivery;
import cn.jwis.product.pdm.delivery.entity.DeliveryTreeNode;

import java.util.List;

public interface DeliveryServiceRepo extends cn.jwis.product.pdm.delivery.repo.DeliveryServiceRepo {

    Long delete(String oid, String level);

    Long deleteByOid(List<String> oid, String level);

    List<DeliveryTreeNode> findByProductCatalogOid(String productCatalogOid);

    List<DeliveryTreeNode> findStructureTree(String catalogType, String catalogOid, Integer level, String nodeSearch, String objectSearch, boolean hasObject);

    List<DeliveryTreeNode> findNodesByCatalogOid(String catalogType, String catalogOid, Integer level);

    List<DeliveryTreeNode> findChildrenByDeliveryOid(String oid);

    void createContainChild(String parentOid, String oid);

    Delivery findByOid(String oid);

    Delivery findByClsOidAndName(String catalogOid, String oid, String clsOid, String name, String modelDefinition);

    Delivery findByClsOid(String catalogOid, String clsOid);

    List<Delivery> findLeafNodes(String containerOid, String searchKey);

    List<DeliveryTreeNode> findAllChildrenObjByDeliveryOid(String oid, String level);

    Delivery findByParentOidAndName(String name, String parentOid,Boolean isRoot);
}
