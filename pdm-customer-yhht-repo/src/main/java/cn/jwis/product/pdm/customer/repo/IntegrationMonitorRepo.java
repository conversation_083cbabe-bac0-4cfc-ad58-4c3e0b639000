package cn.jwis.product.pdm.customer.repo;

import cn.jwis.platform.plm.foundation.common.response.InstanceEntity;
import cn.jwis.product.pdm.cad.mcad.param.Page;
import cn.jwis.product.pdm.customer.entity.IntegrationRecord;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2023/8/9 22:46
 * @Email <EMAIL>
 */
public interface IntegrationMonitorRepo {
    IntegrationRecord findByOid(String oid);

    List<IntegrationRecord> findRecord(Page page);

    long updateStatusByNumber(String number, String type, String lifeCycleStatus);

    long updateStatusByOid(String number, String type, String lifeCycleStatus);

    IntegrationRecord findRecordByNumber(InstanceEntity number);
}
