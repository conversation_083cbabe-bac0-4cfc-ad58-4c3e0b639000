<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <artifactId>pdm-customer-yhht-server</artifactId>
    <packaging>pom</packaging>
    <version>1.0.0</version>
    <description>银河pdm定制服务</description>
    <modules>
        <module>pdm-customer-yhht-web</module>
        <module>pdm-customer-yhht-launcher</module>
        <module>pdm-customer-yhht-service</module>
        <module>pdm-customer-yhht-entity</module>
        <module>pdm-customer-yhht-repo</module>
        <module>pdm-customer-yhht-repo-neo4j</module>
        <module>pdm-customer-yhht-remote</module>
        <module>pdm-customer-yhht-remote-service</module>
    </modules>

    <parent>
        <groupId>cn.jwis.product.pdm</groupId>
        <artifactId>jwi-product-pdm-parent</artifactId>
        <version>1.5.0</version>
    </parent>

    <properties>
        <!-- properties 必须要有一个namespace,否则开发云运行会失败 -->
        <namespace></namespace>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <maven.compiler.encoding>UTF-8</maven.compiler.encoding>
        <java.version>1.8</java.version>
        <jwi.product.pdm.common.version>3.5.0</jwi.product.pdm.common.version>
        <jwi.product.pdm.doc.version>3.5.0</jwi.product.pdm.doc.version>
<!--        <jwi.product.pdm.partbom.version>3.5.0</jwi.product.pdm.partbom.version>-->
        <jwi.product.pdm.foundation.version>3.5.0_yhht</jwi.product.pdm.foundation.version>
        <jwi.product.pdm.change.version>3.5.0</jwi.product.pdm.change.version>
        <jwi.product.pdm.baseline.version>3.5.0</jwi.product.pdm.baseline.version>
        <jwi.product.pdm.security.version>3.5.0</jwi.product.pdm.security.version>
        <jwi.product.pdm.cad.version>3.5.0_yhht</jwi.product.pdm.cad.version>
        <jwi.product.pdm.cad.converter.version>1.2.0</jwi.product.pdm.cad.converter.version>
        <jwi.product.pdm.customer.version>1.0.0</jwi.product.pdm.customer.version>
        <jwi.platform.iam.server.version>1.0.0</jwi.platform.iam.server.version>
        <jwi.platform.iam.gateway.version>1.0.0</jwi.platform.iam.gateway.version>
        <jwi.platform.plm.permission.version>3.5.0_yhht</jwi.platform.plm.permission.version>
        <jwi.product.pdm.sdk.cad.version>3.5.0_yhht</jwi.product.pdm.sdk.cad.version>
        <jwi.platform.plm.container.version>3.5.0_yhht</jwi.platform.plm.container.version>
        <jwi.platform.pdm.container.version>3.5.0</jwi.platform.pdm.container.version>
        <jwi.product.pdm.sdk.partbom.version>3.5.0_yhht</jwi.product.pdm.sdk.partbom.version>
        <jwi.product.pdm.partbom.version>3.5.0_yhht</jwi.product.pdm.partbom.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>cn.jwis.platform.plm</groupId>
                <artifactId>container-helper</artifactId>
                <version>${jwi.platform.plm.container.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.jwis.product.pdm</groupId>
                <artifactId>container-entity</artifactId>
                <version>${jwi.platform.pdm.container.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.jwis.product.pdm</groupId>
                <artifactId>pdm-customer-yhht-web</artifactId>
                <version>${jwi.product.pdm.customer.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.jwis.product.pdm</groupId>
                <artifactId>pdm-customer-yhht-service</artifactId>
                <version>${jwi.product.pdm.customer.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.jwis.product.pdm</groupId>
                <artifactId>pdm-customer-yhht-repo</artifactId>
                <version>${jwi.product.pdm.customer.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.jwis.product.pdm</groupId>
                <artifactId>pdm-customer-yhht-entity</artifactId>
                <version>${jwi.product.pdm.customer.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.jwis.product.pdm</groupId>
                <artifactId>pdm-customer-yhht-remote</artifactId>
                <version>${jwi.product.pdm.customer.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.jwis.product.pdm</groupId>
                <artifactId>partbom-remote-service</artifactId>
                <version>${jwi.product.pdm.partbom.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.jwis.product.pdm</groupId>
                <artifactId>pdm-customer-yhht-remote-service</artifactId>
                <version>${jwi.product.pdm.customer.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.jwis.product.pdm</groupId>
                <artifactId>pdm-customer-yhht-repo-neo4j</artifactId>
                <version>${jwi.product.pdm.customer.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.jwis.product.pdm</groupId>
                <artifactId>pdm-customer-yhht-launcher</artifactId>
                <version>${jwi.product.pdm.customer.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.jwis.product.pdm</groupId>
                <artifactId>partbom-helper</artifactId>
                <version>${jwi.product.pdm.partbom.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.jwis.product.pdm</groupId>
                <artifactId>change-helper</artifactId>
                <version>${jwi.product.pdm.change.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.jwis.product.pdm</groupId>
                <artifactId>change-service</artifactId>
                <version>${jwi.product.pdm.change.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.jwis.product.pdm</groupId>
                <artifactId>doc-mgmt-helper</artifactId>
                <version>${jwi.product.pdm.doc.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.jwis.product.pdm</groupId>
                <artifactId>baseline-helper</artifactId>
                <version>${jwi.product.pdm.baseline.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.jwis.platform.plm</groupId>
                <artifactId>file-helper</artifactId>
                <version>3.5.0_yhht</version>
            </dependency>

            <dependency>
                <groupId>cn.jwis.platform.plm</groupId>
                <artifactId>foundation-entity</artifactId>
                <version>${jwi.product.pdm.foundation.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.jwis.platform.plm</groupId>
                <artifactId>foundation-model-excel</artifactId>
                <version>${jwi.product.pdm.foundation.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.jwis.platform.plm</groupId>
                <artifactId>foundation-helper</artifactId>
                <version>${jwi.product.pdm.foundation.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.jwis.platform.plm</groupId>
                <artifactId>foundation-service</artifactId>
                <version>${jwi.product.pdm.foundation.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.jwis.platform.plm</groupId>
                <artifactId>foundation-repo</artifactId>
                <version>${jwi.product.pdm.foundation.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.jwis.platform.plm</groupId>
                <artifactId>foundation-repo-neo4j</artifactId>
                <version>${jwi.product.pdm.foundation.version}</version>
            </dependency>
            <dependency>
                <groupId>com.aspose</groupId>
                <artifactId>aspose-words-jdk17</artifactId>
                <version>22.5.0</version>
            </dependency>
            <dependency>
                <groupId>cn.jwis.platform.plm</groupId>
                <artifactId>search-engine-repo-neo4j</artifactId>
                <version>3.6.0</version>
            </dependency>

            <dependency>
                <groupId>cn.jwis.product.pdm</groupId>
                <artifactId>cad-helper</artifactId>
                <version>${jwi.product.pdm.cad.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.jwis.product.pdm</groupId>
                <artifactId>cad-remote-service</artifactId>
                <version>${jwi.product.pdm.cad.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.jwis.product.pdm</groupId>
                <artifactId>cad-entity</artifactId>
                <version>${jwi.product.pdm.cad.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <!--        IAM -->
        <dependency>
            <groupId>cn.jwis.platform.iam</groupId>
            <artifactId>iam-repo-neo4j</artifactId>
            <version>${jwi.platform.iam.server.version}</version>
        </dependency>
        <dependency>
            <groupId>cn.jwis.platform.iam</groupId>
            <artifactId>iam-remote-service</artifactId>
            <version>${jwi.platform.iam.server.version}</version>
        </dependency>

        <dependency>
            <groupId>jdk.tools</groupId>
            <artifactId>jdk.tools</artifactId>
            <version>1.8</version>
            <scope>system</scope>
            <!--            <systemPath>${project.basedir}/lib/tools.jar</systemPath>-->
            <systemPath>${env.JAVA_HOME}/lib/tools.jar</systemPath>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-websocket</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.jwis.framework</groupId>
            <artifactId>jwis-base-service</artifactId>
        </dependency>



    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.1</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <encoding>UTF-8</encoding>
                    <!-- 此处配置打包时必须排除本地的配置文件，防止配置中心的配置信息失效-->
                    <excludes>
                        <exclude>**/application*.properties</exclude>
                    </excludes>
                    <!--编译时接口中的函数的参数会以明文的方式存在class文件中，Repo中就可以不使用Param注解-->
                    <compilerArgs>
                        <arg>-parameters</arg>
                    </compilerArgs>
                </configuration>
            </plugin>
            <!-- Source plugin -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>3.2.1</version>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>

    </build>

</project>
