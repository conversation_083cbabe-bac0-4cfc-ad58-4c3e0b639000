package cn.jwis.product.pdm.customer.web;

import cn.jwis.framework.base.exception.JWIException;
import cn.jwis.framework.base.response.Result;
import cn.jwis.framework.base.util.StringUtil;
import cn.jwis.framework.base.web.session.SessionHelper;
import cn.jwis.platform.plm.container.dto.folder.FolderFromDTO;
import cn.jwis.product.pdm.customer.entity.FuzzySubPageWithModelDTO;
import cn.jwis.product.pdm.customer.service.dto.FolderUpdateDTO;
import cn.jwis.product.pdm.customer.service.dto.PdmFolderCreateDTO;
import cn.jwis.product.pdm.customer.service.impl.CustomFolderHelperImpl;
import cn.jwis.product.pdm.customer.service.interf.PDMFolderServiceI;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

@RequestMapping(value = "pdm-folder")
@RestController
public class PDMFolderTwoController {

    @Resource
    private PDMFolderServiceI pdmFolderServiceI;

    @Resource
    private CustomFolderHelperImpl customFolderHelperImpl;

    @RequestMapping(
            value = {"/pdmfuzzySubPageWithModel"},
            method = {RequestMethod.POST}
    )
    @ApiOperation(
            response = Result.class,
            value = "模糊分页查询文件夹下的数据-银河版"
    )
    public Result fuzzySubPageWithModel(@RequestBody FuzzySubPageWithModelDTO dto) {
        return Result.success(this.pdmFolderServiceI.fuzzySubPageWithModel(dto));
    }

    @PostMapping(value = "update")
    public Result<?> updateFolder(@RequestBody FolderUpdateDTO dto){
        if(StringUtil.isBlank(dto.getOid())){
            throw new JWIException("oid不能为空");
        }
        return Result.success(pdmFolderServiceI.update(dto));
    }

    @PostMapping(value = "create")
    public Result<?> create(@RequestBody PdmFolderCreateDTO dto){
        return Result.success(pdmFolderServiceI.create(dto));
    }

    @GetMapping(value = "/searchTree")
    public Result<?> searchTree(@RequestParam(required = false) String containerModel,
                                @RequestParam(required = false) String containerOid,@RequestParam(required = false) String searchKey){
        return Result.success(pdmFolderServiceI.searchFoldersWithPermisson(containerModel, containerOid, searchKey));
    }

    @GetMapping(value = "/searchTreeWithOutPermission")
    public Result<?> searchTreeWithOutPermission(@RequestParam(required = false) String containerModel,
                                @RequestParam(required = false) String containerOid,@RequestParam(required = false) String searchKey){
        SessionHelper.getCurrentUser().setSystemAdmin(true);
        return Result.success(pdmFolderServiceI.searchFoldersWithPermisson(containerModel, containerOid, searchKey));
    }

    /**
     * 获取文件夹团队
     * @return
     */
    @GetMapping(value = "/getTeamRole")
    public Result<?> getTeamRole(@RequestParam("folderOid") String folderOid, @RequestParam(value = "searchKey", required = false) String searchKey) {
        return Result.success(pdmFolderServiceI.getTeamRole(folderOid, searchKey));
    }

    @PostMapping({"/createTree"})
    @ApiOperation(response = Result.class, value = "创建文件夹树并附带原团队成员")
    public Result<?> createTree(@RequestBody @Valid FolderFromDTO dto) {
        this.customFolderHelperImpl.createFoldersWithContainerTeam(dto);
        return Result.success();
    }


}
