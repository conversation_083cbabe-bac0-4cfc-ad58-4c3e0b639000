package cn.jwis.product.pdm.customer.web.conf;

import cn.jwis.framework.base.dto.UserDTO;
import cn.jwis.framework.base.web.session.SessionHelper;
import cn.jwis.platform.plm.foundation.common.response.InstanceEntity;
import cn.jwis.platform.plm.permission.util.PermissionCacheUtil;
import cn.jwis.product.pdm.customer.repo.CustomerCommonRepo;
import cn.jwis.product.pdm.customer.service.delegate.BatchSendERP;
import cn.jwis.product.pdm.partbom.part.dto.LifecycleStatusUpdateDTO;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Aspect
@Component
@Slf4j
public class CustomerControllerAspect implements InitializingBean {


    @Autowired
    private PermissionCacheUtil permissionCacheUtil;

    @Autowired
    private CustomerCommonRepo customerCommonRepo;

    @Autowired
    private BatchSendERP batchSendERP;

    @Around("execution(* cn.jwis.product.pdm.customer.web.CustomerCommonController.batchSetStatus(..))"
    )
    public Object docPartBomSetStatus(ProceedingJoinPoint jp) {
        permissionCacheUtil.clearCatch();
        try {
            Object obj = jp.proceed();
            return obj;
        } catch (Throwable e){
            log.error(e.getMessage(), e);
        }finally {
            List<LifecycleStatusUpdateDTO> dtoList = (List<LifecycleStatusUpdateDTO>) jp.getArgs()[0];
            dtoList.stream().forEach(dto -> {
                List<String> updateList;
                if("PartIteration".equals(dto.getModelInfo().getType())){
                    if("Deactivated".equals(dto.getStatus()))
                        updateList = customerCommonRepo.stopStatusSetPartName(Arrays.asList(dto.getModelInfo().getOid()));
                    else
                        updateList = customerCommonRepo.unStopStatusSetPartName(Arrays.asList(dto.getModelInfo().getOid()));
                    if(updateList != null && updateList.size() > 0){
                        List<InstanceEntity> instanceList = updateList.stream().map(it -> {
                            InstanceEntity instance = new InstanceEntity();
                            instance.setOid(it);
                            instance.setType("PartIteration");
                            return instance;
                        }).collect(Collectors.toList());
                        batchSendERP.batchSendERP(instanceList, Boolean.TRUE);
                    }
                }
            });
        }
        return null;
    }

    @Value("${tenantOid:6deb5dde-aa39-46fb-962d-a5951f8fab5e}")
    private String tenantOid;

    @Before("execution(* cn.jwis.product.pdm.customer.web.PDMFolderTwoController.searchTree(..)) ")
    public void searchFolder(JoinPoint jp) {
        UserDTO currUser = SessionHelper.getCurrentUser();
        if(currUser == null || (currUser != null && currUser.getTenantOid() == null)){
            UserDTO userDto = new UserDTO();
            userDto.setTenantOid(tenantOid);
            userDto.setOid("sys_admin");
            userDto.setAccount("sys_admin");
            userDto.setSystemAdmin(Boolean.TRUE);
            userDto.setIpAddress("127.0.0.1");
            SessionHelper.addCurrentUser(userDto);
        }
    }

//    @Before("execution(* cn.jwis.platform.plm.permission.filter.web.PermissionFilterController.executeFilters(..)) ")
//    public void executeFilters(JoinPoint jp) {
//        permissionCacheUtil.clearCatch();
//    }

    @Override
    public void afterPropertiesSet() throws Exception {

    }
}
