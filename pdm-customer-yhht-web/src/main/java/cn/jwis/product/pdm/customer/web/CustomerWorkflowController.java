package cn.jwis.product.pdm.customer.web;

import cn.jwis.framework.base.response.Result;
import cn.jwis.product.pdm.customer.service.dto.WorkflowQueryParam;
import cn.jwis.product.pdm.customer.service.interf.CustomerWorkflowService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @author: 汪江
 * @data: 2023-12-21 13:30
 * @description:
 **/
@RestController
@RequestMapping({"/workflow-micro/workflow/model"})
public class CustomerWorkflowController {

    @Autowired
    private CustomerWorkflowService customerWorkflowService;

    @ApiOperation(value = "workflow查询")
    @PostMapping(value = "/deployed/latest")
    public Result<?> latest(@RequestBody WorkflowQueryParam param) {
        return Result.success(customerWorkflowService.latest(param));
    }
}
