package cn.jwis.product.pdm.customer.web;

import cn.hutool.json.JSONUtil;
import cn.jwis.framework.base.exception.JWIException;
import cn.jwis.framework.base.response.Result;
import cn.jwis.platform.plm.file.entity.FileMetadata;
import cn.jwis.product.pdm.cad.ecad.service.EdaIntegrationHelper;
import cn.jwis.product.pdm.customer.entity.SyncLibraryRequest;
import cn.jwis.product.pdm.customer.service.impl.CustomerPartHelperImpl;
import cn.jwis.product.pdm.customer.service.interf.CustomerEdaIntegrationHelper;
import cn.jwis.product.pdm.partbom.part.dto.PartCreateDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> ych
 * @Date ： 2024/9/23
 * @Description :元器件同步Controller
 */

@RestController
@RequestMapping({"/eda"})
@Api(
        tags = {"eda"},
        value = "eda",
        description = "eda"
)
public class CustomerCadController {

    private static final Logger log = LoggerFactory.getLogger(CustomerCadController.class);
    @Resource(name = "customerAltiumDesignerHelperImpl")
    private CustomerEdaIntegrationHelper edaIntegrationHelper;

    @PostMapping({"/ad/syncLibrary"})
    @ApiOperation(
            response = Result.class,
            value = "初始化mentor资源库-重写",
            notes = "初始化mentor资源库-重写"
    )
    public Result syncLibraryForMentor(@RequestBody SyncLibraryRequest request) throws JWIException {
        // 获取 containerOid 和 selectList 参数
        String containerOid = request.getContainerOid();
        List<String> selectList = request.getSelectList();
        log.info("containerOid:{}", JSONUtil.toJsonStr(containerOid));
        log.info("selectList:{}", JSONUtil.toJsonStr(selectList));
        // 调用业务逻辑处理
        FileMetadata file = this.edaIntegrationHelper.syncLibraryForMentor(containerOid, selectList);
        return Result.success();
    }
}
