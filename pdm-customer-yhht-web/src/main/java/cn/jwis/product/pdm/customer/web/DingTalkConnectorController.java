package cn.jwis.product.pdm.customer.web;

import cn.jwis.framework.base.response.Result;
import cn.jwis.platform.iam.gateway.access.annotation.IgnoreRestUrlAccess;
import cn.jwis.product.pdm.customer.service.impl.DingTalkConnectorService;
import cn.jwis.product.pdm.customer.service.impl.DingTalkConnectorService.SearchRequest;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * 钉钉连接器接口
 */
@RestController
@RequestMapping(value = "/dingtalk/connector/")
@Api(tags = "钉钉连接器接口", value = "钉钉连接器接口", description = "PDM DingTalk Connector")
public class DingTalkConnectorController {

    @Autowired
    private DingTalkConnectorService dingTalkConnectorService;

    /**
     * 根据容器名称获取调度角色和产品经理角色的钉钉用户ID及真实姓名
     * @param request 包含容器名称的请求对象
     * @return 角色对应的钉钉用户ID和真实姓名
     */
    @RequestMapping(value = "getTeamRoleDingUsers", method = RequestMethod.POST)
    @ApiOperation(response = Result.class, value = "获取产品容器中调度角色和产品经理角色的钉钉用户ID及真实姓名")
    @IgnoreRestUrlAccess
    public Result getTeamRoleDingUsers(@RequestBody SearchRequest request) {
        Map<String, Object> response = dingTalkConnectorService.getTeamRoleUsers(request);
        
        if (response.containsKey("message")) {
            return Result.success((String)response.get("message"), response.get("result"));
        } else {
            return Result.success(response.get("result"));
        }
    }
} 