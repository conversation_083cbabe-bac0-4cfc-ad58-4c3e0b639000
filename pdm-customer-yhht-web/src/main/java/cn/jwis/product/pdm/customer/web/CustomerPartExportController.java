package cn.jwis.product.pdm.customer.web;

import cn.jwis.framework.base.response.Result;

import cn.jwis.platform.plm.modelexcel.dto.ModelExcelAllImportDTO;
import cn.jwis.product.pdm.customer.service.interf.CustomerPartService3;
import cn.jwis.product.pdm.partbom.part.dto.ImportParamDTO;
import cn.jwis.product.pdm.partbom.part.dto.PartClassificationDTO;
import cn.jwis.product.pdm.partbom.part.service.IPartService;
import cn.jwis.product.pdm.partbom.part.style.handler.CustomCellWriteHandler;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.builder.ExcelWriterSheetBuilder;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @author: 汪江
 * @data: 2023-12-21 13:30
 * @description:
 **/
@RestController
@RequestMapping({"/part-bom-micro/part-export"})
@Api(
        tags = {"部件的导入导出的操作"},
        value = "部件的导入导出的操作"
)
public class CustomerPartExportController {
    @Autowired
    private CustomerPartService3 customerPartService3;

    @Autowired
    private IPartService partService;


    @ApiOperation(value = "按模板导入部件")
    @PostMapping(value = "/importExcelTemp")
    public Result<?> importExcelTemp(@Valid ModelExcelAllImportDTO importDTO, HttpServletRequest request) {
        customerPartService3.importExcelTemp(importDTO, request);
        return Result.success();
    }


    @ApiOperation(response = Result.class, value = "导出部件详情", notes = "导出部件详情")
    @PostMapping(value = "/export-part-info")
    public void exportPartInfo(HttpServletResponse response, @RequestBody ImportParamDTO importParamDTO) throws IOException {
        responseExcel(response, "导出部件详情");
        ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream()).build();
        try {
            ExcelWriterSheetBuilder sheetBuilder;
            Map<String, Map<String, String>> headNameCodeMap = new HashMap<>(16);
            Map<String, Map<String, Boolean>> headNameIsSystemDefaultMap = new HashMap<>(16);
            List<PartClassificationDTO> partDtoList = new ArrayList<>();
            Map<String, List<PartClassificationDTO>> partClsDtoMap = new HashMap<>(16);
            Map<String, List<List<String>>> partHeads = partService.queryPartHeads(partClsDtoMap, importParamDTO, headNameCodeMap, partDtoList, headNameIsSystemDefaultMap);
            List<List<String>> headList;
            List<Map<Integer, Object>> dataList;
            Map<String, Map<String, Map<String, String>>> drownData4ModelDefinition = new HashMap<>();
            for (String sheetName : partHeads.keySet()) {
                headList = partHeads.get(sheetName);
                partDtoList = partClsDtoMap.get(sheetName);
                sheetBuilder = EasyExcel.writerSheet(sheetName).head(headList).registerWriteHandler(new CustomCellWriteHandler());
                //获取每个sheet页的数据
                dataList = customerPartService3.queryPartDataByHeadMap(partDtoList, headNameCodeMap.get(sheetName), headList, headNameIsSystemDefaultMap.get(sheetName), drownData4ModelDefinition);
                excelWriter.write(dataList, sheetBuilder.build());
            }
        } finally {
            excelWriter.finish();
        }
    }

    private void responseExcel(HttpServletResponse response, String name) throws UnsupportedEncodingException {
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode(name, "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
    }
}
