package cn.jwis.product.pdm.customer.web;

import cn.jwis.framework.base.response.Result;
import cn.jwis.platform.iam.gateway.access.annotation.IgnoreRestUrlAccess;
import cn.jwis.product.pdm.customer.entity.DeliveryPMSDto;
import cn.jwis.product.pdm.customer.service.impl.CustomerDeliveryHelperImpl;
import cn.jwis.product.pdm.customer.service.impl.DeliveryBatchService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;


/**
 * <AUTHOR> yefei
 * @Date ： 2024/3/5
 * @Description : 创建文档交付清单选择接口
 */

@RestController
@RequestMapping(value = "/delivery/")
@Api(tags = "交付清单客制化", value = "交付清单客制化", description = "交付清单客制化")
public class CustomerDeliveryController {

    @Resource
    CustomerDeliveryHelperImpl customerDeliveryHelper;

    @Resource
    private DeliveryBatchService deliveryBatchService;

    @ApiOperation(value = "查询可关联的交付清单")
    @GetMapping(value = "/querySelectDeliveryTree")
    public Result querySelectDeliveryTree(@RequestParam String containerOid) {
        return Result.success(customerDeliveryHelper.querySelectDeliveryTreeNew(containerOid));
    }

    @ApiOperation("批量处理交付清单（新增、修改、删除）")
    @PostMapping("/batchCreate")
    @IgnoreRestUrlAccess
    public Result batchCreateDelivery(@RequestBody @Valid DeliveryPMSDto deliveryPMSDto) {
        return deliveryBatchService.batchCreateDelivery(deliveryPMSDto);
    }

}
