package cn.jwis.product.pdm.customer.web;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import cn.jwis.framework.base.domain.able.info.ModelInfo;
import cn.jwis.framework.base.dto.PageSimpleDTO;
import cn.jwis.framework.base.dto.UserDTO;
import cn.jwis.framework.base.exception.JWIException;
import cn.jwis.framework.base.response.PageResult;
import cn.jwis.framework.base.response.Result;
import cn.jwis.framework.base.util.Assert;
import cn.jwis.framework.base.util.SpringContextUtil;
import cn.jwis.framework.base.web.session.SessionHelper;
import cn.jwis.framework.database.core.query.dynamic.Condition;
import cn.jwis.platform.iam.gateway.access.annotation.IgnoreRestUrlAccess;
import cn.jwis.platform.plm.account.user.service.UserHelper;
import cn.jwis.platform.plm.container.dto.container.FuzzySubPageDTO;
import cn.jwis.platform.plm.container.entity.Folder;
import cn.jwis.platform.plm.container.entity.FolderTreeNode;
import cn.jwis.platform.plm.container.entity.response.ContainerDetail;
import cn.jwis.platform.plm.container.entity.response.InstanceEntityWithCatalog;
import cn.jwis.platform.plm.container.service.ContainerHelper;
import cn.jwis.platform.plm.container.service.FolderHelper;
import cn.jwis.platform.plm.datadistribution.dto.DataDistributionDTO;
import cn.jwis.platform.plm.dataengine.common.JWICommonService;
import cn.jwis.platform.plm.file.entity.FileMetadata;
import cn.jwis.platform.plm.foundation.attachment.entity.File;
import cn.jwis.platform.plm.foundation.classification.entity.Classification;
import cn.jwis.platform.plm.foundation.classification.service.ClassificationHelper;
import cn.jwis.platform.plm.foundation.common.dto.InstanceBasicDTO;
import cn.jwis.platform.plm.foundation.common.param.VersionInfo;
import cn.jwis.platform.plm.foundation.common.service.CommonAbilityHelper;
import cn.jwis.platform.plm.foundation.common.service.InstanceHelper;
import cn.jwis.platform.plm.foundation.common.service.InstanceService;
import cn.jwis.platform.plm.foundation.lifecycle.able.LifecycleAble;
import cn.jwis.platform.plm.workflow.engine.dto.ProOrdOfBizObjFuzzyPageDTO;
import cn.jwis.platform.plm.workflow.engine.dto.ProcessOrderDetail;
import cn.jwis.platform.plm.workflow.engine.service.interf.ProcessOrderHelper;
import cn.jwis.product.pdm.cad.ecad.entity.ECADIteration;
import cn.jwis.product.pdm.cad.ecad.service.ECADService;
import cn.jwis.product.pdm.cad.mcad.entity.MCADIteration;
import cn.jwis.product.pdm.cad.mcad.service.MCADService;
import cn.jwis.product.pdm.customer.entity.CheckNameRequest;
import cn.jwis.product.pdm.customer.entity.DingTaskRecord;
import cn.jwis.product.pdm.customer.entity.FolderParam;
import cn.jwis.product.pdm.customer.entity.assistant.CheckedAssist;
import cn.jwis.product.pdm.customer.entity.assistant.SafeWrapAssist;
import cn.jwis.product.pdm.customer.repo.CustomerCommonRepo;
import cn.jwis.product.pdm.customer.service.dto.ContainerOidNameDTO;
import cn.jwis.product.pdm.customer.service.dto.DocumentOidNameDTO;
import cn.jwis.product.pdm.customer.service.dto.FolderOidNameDTO;
import cn.jwis.product.pdm.customer.service.dto.UpdateRequest;
import cn.jwis.product.pdm.customer.service.impl.*;
import cn.jwis.product.pdm.customer.service.interf.CustomerDocumentHelper;
import cn.jwis.product.pdm.customer.service.interf.PDMFolderServiceI;
import cn.jwis.product.pdm.customer.service.util.BomExcelParserUtil;
import cn.jwis.product.pdm.customer.service.util.U9BomExcelParserUtil;
import cn.jwis.product.pdm.delivery.dto.AddInstanceToDeliveryDTO;
import cn.jwis.product.pdm.delivery.dto.MasterDTO;
import cn.jwis.product.pdm.delivery.entity.Delivery;
import cn.jwis.product.pdm.delivery.helper.DeliveryHelper;
import cn.jwis.product.pdm.document.dto.DocSignWorkflowDTO;
import cn.jwis.product.pdm.document.dto.DwgSignWorkflowDTO;
import cn.jwis.product.pdm.document.entity.DocumentIteration;
import cn.jwis.product.pdm.document.service.DocumentHelper;
import cn.jwis.product.pdm.partbom.part.dto.LifecycleStatusUpdateDTO;
import cn.jwis.product.pdm.partbom.part.entity.PartIteration;
import cn.jwis.product.pdm.partbom.part.response.GlobalSubstituteWithMutualFlag;
import cn.jwis.product.pdm.partbom.part.service.IPartService;
import cn.jwis.product.pdm.partbom.part.service.PartService;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Sheet;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2023/10/23 17:03
 * @Email <EMAIL>
 */
@RestController
@RequestMapping(value = "/common/")
@Api(tags = "pdm通用接口", value = "pdm通用接口", description = "PDM others")
public class CustomerCommonController implements SafeWrapAssist, CheckedAssist {

    @Autowired
    CustomerCommonServiceImpl customerCommonService;
    @Autowired
    private DeliveryHelper deliveryService;
    @Autowired
    private ECADService ecadService;
    @Autowired
    private MCADService mcadService;
    @Autowired
    ContainerHelper containerHelper;
    @Resource
    private FolderHelper folderHelper;
    @Autowired
    private ClassificationHelper classificationHelper;
    @Autowired
    CustomerDocumentHelper customerDocumentHelper;
    @Autowired
    private DailyDownloadService dailyDownloadService;
    @Autowired
    private InstanceHelper instanceHelper;



    @RequestMapping(value = "queryPdf", method = RequestMethod.GET)
    @ApiOperation(response = Result.class, value = "查询剩余无附件文档")
    public Result queryPdf(long limit) {
        // 增加pdf生成接口防重复请求
        RLock nameLock = redissonClient.getLock("queryPdf");
        nameLock.tryLock();
        if(!nameLock.isHeldByCurrentThread()) {
            return Result.success("");
        }
        int success = 0;
        try {
            List<DocumentIteration> dingTaskRecordList = jwiCommonService.dynamicQuery(DocumentIteration.TYPE,
                            Condition.where("lifecycleStatus").eq("Released")
                                    .and(Condition.where("primaryFile").nnull()
                                            .and(Condition.where("secondaryFile").isnull()).or(Condition.where("secondaryFile").eq(""))), DocumentIteration.class).stream()
                    .limit(limit)
                    .collect(Collectors.toList());
            logger.info("总数量---->>>> {}", dingTaskRecordList.size());
            Set<String> oidSet = dingTaskRecordList.stream()
                    .map(DocumentIteration::getOid)
                    .collect(Collectors.toSet());
            // 输出为 JSON 格式
            String jsonOutput = JSONUtil.toJsonStr(oidSet);
            logger.info("剩余未转换文档oid为---->>>> {}", jsonOutput);
            success = dingTaskRecordList.size();

        }catch (Exception e) {
            logger.error("queryPdf 方法出错: {}", e.getMessage(), e);
        } finally {
            if (nameLock.isHeldByCurrentThread()) {
                nameLock.unlock();
                logger.info("queryPdf finally");
            }
        }
        logger.info("queryPdf end");
        return Result.success("剩余" + success + "条数据");
    }

    @RequestMapping(value = "BatchPdfCreateDD", method = RequestMethod.GET)
    @ApiOperation(response = Result.class, value = "批量修复没有附件的文档流程")
    public Result BatchPdfCreateDD(long limit) {
        // 增加pdf生成接口防重复请求
        RLock nameLock = redissonClient.getLock("BatchPdfCreateDD");
        nameLock.tryLock();
        if(!nameLock.isHeldByCurrentThread()) {
            return Result.success("");
        }
        int success = 0;
        try {
            List<DocumentIteration> docList = Collections.emptyList();
            List<DocumentIteration> dingTaskRecordList = jwiCommonService.dynamicQuery(DocumentIteration.TYPE,
                            Condition.where("lifecycleStatus").eq("Released")
                                    .and(Condition.where("primaryFile").nnull()
                                            .and(Condition.where("secondaryFile").isnull()).or(Condition.where("secondaryFile").eq(""))), DocumentIteration.class).stream()
                    .limit(limit)
                    .collect(Collectors.toList());
            logger.info("总数量---->>>> {}", dingTaskRecordList.size());
            docList = dingTaskRecordList;

            for (DocumentIteration doc : docList) {
                logger.info("当前执行数据{}", JSONUtil.toJsonStr(doc));

                List<File> priFile = doc.getPrimaryFile();
                if (priFile != null && priFile.size() > 0) {
                    DingTaskRecord dingTaskRecord;

                    if (doc.getExtensionContent() != null && doc.getExtensionContent().getString("processInstanceId") != null) {
                        dingTaskRecord = jwiCommonService.dynamicQueryOne(DingTaskRecord.TYPE,
                                Condition.where("dingProcessInstanceId").eq(doc.getExtensionContent().getString("processInstanceId")),
                                DingTaskRecord.class);

                        if (dingTaskRecord == null) { // 历史的 有钉钉记录 但没有dingTaskRecord记录 先创建dingTaskRecord记录
                            dingTaskRecord = new DingTaskRecord();
                            dingTaskRecord.setIsChange(Boolean.FALSE);
                            dingTaskRecord.setDingProcessInstanceId(doc.getExtensionContent().getString("processInstanceId"));
                            dingTaskRecord.setBusinessId(doc.getExtensionContent().getString("processBusinessId"));
                            dingTaskRecord.setCreateDate(Long.valueOf(doc.getExtensionContent().getString("subTime")));
                            dingTaskRecord.setTenantOid(doc.getTenantOid());
                            dingTaskRecord.setOwner(doc.getOwner());
                            dingTaskRecord.setDocumentIterationOidList(Arrays.asList(doc.getOid()));
                            commonService.create(dingTaskRecord);
                        } else {
                            if (!dingTaskRecord.getDocumentIterationOidList().contains(doc.getOid())) {
                                List<String> newDocOidList = Lists.newArrayList(doc.getOid());
                                customerCommonRepo.updateDingTalkRecordDocList(dingTaskRecord.getOid(), newDocOidList);
                            }
                        }
                    } else {
                        dingTaskRecord = customerCommonRepo.queryDingTaskRecored(doc.getOid());
                    }

                    if (dingTaskRecord != null) {
                        DocSignWorkflowDTO docSignWorkflowDTO = new DocSignWorkflowDTO();
                        try {
                            docSignWorkflowDTO = dingTalkCallListen.getDocSignWorkflowDTO(dingTaskRecord);
                            docSignWorkflowDTO.getData().fluentPut("checkInOut", "0")
                                    .fluentPut("isChange", Boolean.FALSE)
                                    .fluentPut("documentOidList", Arrays.asList(doc.getOid())); // 从dingTaskRecod生成返回的docSignWorkflowDTO里的data里的documentOidList只能是
                            // dingTaskRecod里记录的文档oid 这里要把文档oid修改成当前查询到的文档oid

                            UserDTO byAccount = userHelper.findByAccount(dingTaskRecord.getOwner());
                            SessionHelper.addCurrentUser(byAccount);
                            boolean b = documentHelper.docSignWorkflow(docSignWorkflowDTO);
                            logger.info("执行结果{}", b);
                            success++;
                        } catch (Exception e) {
                            logger.info(e.getMessage(), e);
                        }
                    } else {
                        logger.info("未找到编号" + doc.getNumber() + "最新版本的钉钉审批流程记录");
                    }
                } else {
                    logger.info("当前编号" + doc.getNumber() + "主文件为空");
                }
            }


        }catch (Exception e) {
            logger.error("BatchPdfCreateDD 方法出错: {}", e.getMessage(), e);
        } finally {
            if (nameLock.isHeldByCurrentThread()) {
                nameLock.unlock();
                logger.info("BatchPdfCreateDD finally");
            }
        }
        logger.info("BatchPdfCreateDD end");
        return Result.success("本次执行成功" + success + "条数据");
    }


    /**
     * 查询当前审批流程实例，并给当前审批人发送DING消息
     *
     * @param processInstanceId 钉钉流程ID
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "workflow/dingUserById", method = RequestMethod.GET)
    @ApiOperation(response = Result.class, value = "Ding user by process instance ID")
    public Result dingUserByProcessInstanceId(@RequestParam String processInstanceId,@RequestParam String dingCorPid) {
        return Result.success(customerCommonService.dingUserByProcessInstanceId(processInstanceId, dingCorPid));
    }


    @RequestMapping(value = "issue/findIssueByEntity", method = RequestMethod.POST)
    @ApiOperation(response = Result.class, value = "")
    public Result findIssueByEntity(@RequestBody List<InstanceBasicDTO> instances,
                                    @RequestParam(value = "onlyUnClosed", required = false) Boolean onlyUnClosed) throws Exception {
        return Result.success(customerCommonService.findIssueByEntity(instances,onlyUnClosed));
    }

    @RequestMapping(value = "doc2PdfUpload", method = RequestMethod.POST)
    @ApiOperation(response = Result.class, value = "")
    public Result<FileMetadata> doc2PdfUpload(@RequestBody FileMetadata fileMetadata){
        return Result.success(customerCommonService.doc2PdfUpload(fileMetadata, null));
    }

    @RequestMapping(value = "doc/checkDownloadAccess", method = RequestMethod.GET)
    @ApiOperation(response = Result.class, value = "")
    public Result checkDownloadAccess(@RequestParam String oid,@RequestParam String type){
        return Result.success(customerCommonService.checkDownloadAccess(oid,type));
    }

    @Resource
    private PDMFolderServiceI pdmFolderServiceI;

    @Value("${tenantOid:6deb5dde-aa39-46fb-962d-a5951f8fab5e}")
    private String tenantOid;

    @RequestMapping(value = "searchTree", method = RequestMethod.POST)
    @ApiOperation(response = Result.class, value = "")
    public Result searchTree(@RequestBody FolderParam param){
        UserDTO currUser = SessionHelper.getCurrentUser();
        if(currUser == null || (currUser != null && currUser.getTenantOid() == null)){
            UserDTO userDto = new UserDTO();
            userDto.setTenantOid(tenantOid);
            userDto.setOid("sys_admin");
            userDto.setAccount("sys_admin");
            userDto.setSystemAdmin(Boolean.TRUE);
            userDto.setIpAddress("127.0.0.1");
            SessionHelper.addCurrentUser(userDto);
        }
        List<FolderTreeNode> list = pdmFolderServiceI.searchFoldersWithPermisson("ProductContainer", param.getContainerOid(), "");
        if(list != null && list.size() > 0 && param.getCatalogOid() != null){
            List<FolderTreeNode> children = list.get(0).getChildren();
            if(children != null && children.size() > 0){
                List<FolderTreeNode> child = children.stream().filter(it -> param.getCatalogOid().contains(it.getOid())).collect(Collectors.toList());
                if(child != null)
                    return Result.success(flatMapCollect(child, it -> collectThen(it.getChildren(), i -> {
                        i.setName(it.getName() + "-" + i.getName());
                        i.setChildren(Collections.emptyList());
                        return i;
                    })));
            }
        }else
            return Result.success(getBrevityInfo(list));
        return Result.success(Arrays.asList());
    }

    private List<JSONObject> getBrevityInfo(List<FolderTreeNode> list){
        if(list != null && list.size() > 0){
            return list.stream().map(it -> new JSONObject(true).set("name", it.getName()).set("oid", it.getOid()).set("children", getBrevityInfo(it.getChildren()))).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

//    @RequestMapping(value = "searchTree", method = RequestMethod.GET)
//    @ApiOperation(response = Result.class, value = "")
//    public Result searchTree(@RequestParam String containerOid, @RequestParam Set<String> catalogOid){
//        UserDTO currUser = SessionHelper.getCurrentUser();
//        if(currUser == null || (currUser != null && currUser.getTenantOid() == null)){
//            UserDTO userDto = new UserDTO();
//            userDto.setTenantOid(tenantOid);
//            userDto.setOid("sys_admin");
//            userDto.setIpAddress("127.0.0.1");
//            SessionHelper.addCurrentUser(userDto);
//        }
//        List<FolderTreeNode> list = pdmFolderServiceI.searchFoldersWithPermisson("ProductContainer", containerOid, "");
//        if(list != null && list.size() > 0){
//            List<FolderTreeNode> children = list.get(0).getChildren();
//            if(children != null && children.size() > 0){
//                FolderTreeNode child = children.stream().filter(it -> catalogOid.contains(it.getOid())).findFirst().orElse(null);
//                if(child != null)
//                    return Result.success(child.getChildren());
//            }
//        }
//        return Result.success(Arrays.asList());
//    }

    @Autowired
    private DocumentHelper docAppService;
    @Autowired
    private CustomerPartHelperImpl partHelper;

    @Resource
    CommonAbilityHelper commonAbilityHelper;
    @Autowired
    private IPartService iPartService;

    @Autowired
    PartService partService;
    @Resource
    DingTalkServiceImpl dingTalkService;

    @RequestMapping(value = "/verifyContent", method = RequestMethod.POST)
    @ApiOperation(response = Result.class, value = "校验BOM变更流程中审批内容")
    public Result verifyContent(@RequestBody com.alibaba.fastjson.JSONObject delegateTask) {
        return Result.success(dingTalkService.verifyContent(delegateTask));
    }

    @RequestMapping(value = "batchImportGLOBAL", method = RequestMethod.POST)
    @ApiOperation(response = Result.class, value = "批量导入全局替换")
    @Transactional
    public Result batchImportGLOBAL(@RequestParam(value = "file") MultipartFile file) {
        int success = 0;
        int count = 0;

        try (InputStream inputStream = file.getInputStream()) {
            ExcelReader reader = ExcelUtil.getReader(inputStream);
            List<List<Object>> rows = reader.read(); // 读取全部行

            if (rows == null || rows.size() < 2) {
                return Result.Fail("导入失败：文件数据为空或缺少数据行！");
            }

            List<Object> headers = rows.get(0);
            int mainIndex = headers.indexOf("主对象");
            int subIndex = headers.indexOf("从对象");

            if (mainIndex == -1 || subIndex == -1) {
                return Result.Fail("导入失败：请确保表头包含“主对象”和“从对象”两列！");
            }

            Set<String> allCodes = new HashSet<>();
            Set<String> mainCodes = new HashSet<>();
            Set<String> subCodes = new HashSet<>();
            List<String[]> relationPairs = new ArrayList<>();

            // 读取数据
            for (int i = 1; i < rows.size(); i++) {
                List<Object> row = rows.get(i);
                if (row.size() <= Math.max(mainIndex, subIndex)) {
                    continue;
                }

                String mainCode = String.valueOf(row.get(mainIndex)).trim();
                String subCode = String.valueOf(row.get(subIndex)).trim();

                if (StrUtil.isEmpty(mainCode) || StrUtil.isEmpty(subCode)) {
                    continue;
                }

                allCodes.add(mainCode);
                allCodes.add(subCode);
                mainCodes.add(mainCode);
                subCodes.add(subCode);
                relationPairs.add(new String[]{mainCode, subCode});
            }

            // 查询 PartIteration
            List<PartIteration> allParts = partService.findByCodesAndCatalogOid(new ArrayList<>(allCodes));
            Map<String, PartIteration> partMap = allParts.stream()
                    .collect(Collectors.toMap(PartIteration::getNumber, Function.identity()));

            // 检查主对象和从对象是否全部存在
            List<String> notFoundMainCodes = mainCodes.stream()
                    .filter(code -> !partMap.containsKey(code))
                    .collect(Collectors.toList());
            List<String> notFoundSubCodes = subCodes.stream()
                    .filter(code -> !partMap.containsKey(code))
                    .collect(Collectors.toList());

            if (!notFoundMainCodes.isEmpty() || !notFoundSubCodes.isEmpty()) {
                StringBuilder msg = new StringBuilder("导入失败，以下对象未在系统中找到：");
                if (!notFoundMainCodes.isEmpty()) {
                    msg.append("【主对象】").append(String.join(", ", notFoundMainCodes)).append("；");
                }
                if (!notFoundSubCodes.isEmpty()) {
                    msg.append("【从对象】").append(String.join(", ", notFoundSubCodes)).append("；");
                }
                return Result.Fail(msg.toString());
            }

            // 批量添加 GLOBAL_SUBSTITUTE 关系
            for (String[] pair : relationPairs) {
                PartIteration main = partMap.get(pair[0]);
                PartIteration sub = partMap.get(pair[1]);
                GlobalSubstituteWithMutualFlag substitute = new GlobalSubstituteWithMutualFlag();
                substitute.setMutual(Boolean.TRUE);
                substitute.setFromOid(main.getMasterOid());
                substitute.setFromType(main.getMasterType());
                substitute.setToOid(sub.getMasterOid());
                substitute.setToType(sub.getMasterType());
                substitute.setType("GLOBAL_SUBSTITUTE");

                partHelper.addGlobalSubstitute(substitute);
                /*addRelationship(
                        sub, main, // slave -> main
                        "GLOBAL_SUBSTITUTE",
                        RelationConstraint.MM,
                        PartIteration.TYPE
                );*/
                success++;
            }

            count = relationPairs.size();
            logger.info("本次导入总共 {} 条，成功 {} 条", count, success);
            return Result.success("导入成功，共导入：" + success + " 条");

        } catch (Exception e) {
            logger.error("【更新失败】文件解析异常", e);
            return Result.Fail("更新失败: " + e.getMessage());
        }
    }


    @RequestMapping(value = "updateParentPartStatus", method = RequestMethod.POST)
    @ApiOperation(response = Result.class, value = "批量更新BOM（U9模板）-所有者和状态")
    public Result updateParentPartStatus(@RequestParam(value = "file") MultipartFile file) {
        int success = 0;
        List<String> errorMessages = new ArrayList<>();

        try (InputStream inputStream = file.getInputStream()) {
            List<U9BomExcelParserUtil.U9BOMItem> bomItems = U9BomExcelParserUtil.parse(inputStream);
            logger.info("【批量更新BOM父级料号】解析到 {} 条数据", bomItems.size());

            // 收集所有父子料号
            Set<String> allCodes = bomItems.stream()
                    .flatMap(item -> Stream.of(item.getParentPartNo(), item.getPartNo()))
                    .collect(Collectors.toSet());

            // 查询物料
            List<PartIteration> allParts = partService.findByCodesAndCatalogOid(new ArrayList<>(allCodes));
            checkDuplicateNumber(new ArrayList<>(allCodes), allParts);

            // 按父级料号分组
            Map<String, List<U9BomExcelParserUtil.U9BOMItem>> grouped = bomItems.stream()
                    .collect(Collectors.groupingBy(U9BomExcelParserUtil.U9BOMItem::getParentPartNo));

            for (Map.Entry<String, List<U9BomExcelParserUtil.U9BOMItem>> entry : grouped.entrySet()) {
                String parentPartNo = entry.getKey();
                List<U9BomExcelParserUtil.U9BOMItem> children = entry.getValue();

                PartIteration parentPart = allParts.stream()
                        .filter(p -> p.getNumber().equals(parentPartNo))
                        .findFirst().orElse(null);

                if (parentPart == null) {
                    logger.warn("【跳过】父级料号 {} 未找到对应 PartIteration", parentPartNo);
                    errorMessages.add("父级料号 [" + parentPartNo + "] 不存在，已跳过");
                    continue;
                }

                logger.info("【处理父项】parentPartNo = {}", parentPartNo);

                // 获取所有者
                String owner = children.stream()
                        .map(U9BomExcelParserUtil.U9BOMItem::getOwner)
                        .filter(StringUtils::isNotBlank)
                        .findFirst()
                        .orElse(null);
                String safeOwner = StringUtils.defaultIfBlank(owner, "yuchenghui");
                try {
                    parentPart.setOwner(safeOwner);
                    parentPart.setLifecycleStatus("Released");
                    jwiCommonService.update(parentPart);

                    logger.info("【更新成功】parentPartNo = {}, 设置 owner={}，状态=Released", parentPartNo, safeOwner);
                    success++;

                } catch (Exception e) {
                    logger.error("【更新失败】父级料号 {} 更新异常: {}", parentPartNo, e.getMessage(), e);
                    errorMessages.add("父级料号 [" + parentPartNo + "] 更新失败: " + e.getMessage());
                }
            }

        } catch (Exception e) {
            logger.error("【更新失败】文件解析异常", e);
            return Result.Fail("更新失败: " + e.getMessage());
        }

        if (!errorMessages.isEmpty()) {
            return Result.Fail("部分更新失败（成功 " + success + " 条）：\n" + String.join("\n", errorMessages));
        }

        return Result.success("更新成功，共更新 " + success + " 条父项");
    }

    @RequestMapping(value = "batchImportBOMU9", method = RequestMethod.POST)
    @ApiOperation(response = Result.class, value = "批量导入BOM（U9模板）")
    public Result batchImportBOMU9(@RequestParam(value = "file") MultipartFile file) {
        int success = 0;
        int count = 0;
        List<String> errorMessages = new ArrayList<>();

        try (InputStream inputStream = file.getInputStream()) {
            List<U9BomExcelParserUtil.U9BOMItem> bomItems = U9BomExcelParserUtil.parse(inputStream);
            logger.info("解析到 {} 条数据", bomItems.size());

            // 收集所有父子料号
            Set<String> allCodes = new HashSet<>();
            for (U9BomExcelParserUtil.U9BOMItem item : bomItems) {
                allCodes.add(item.getParentPartNo());
                allCodes.add(item.getPartNo());
            }

            // 查询物料是否存在
            List<PartIteration> allParts = partService.findByCodesAndCatalogOid(new ArrayList<>(allCodes));
            checkDuplicateNumber(new ArrayList<>(allCodes), allParts);

            // 分组处理
            Map<String, List<U9BomExcelParserUtil.U9BOMItem>> grouped = bomItems.stream()
                    .collect(Collectors.groupingBy(U9BomExcelParserUtil.U9BOMItem::getParentPartNo));
            iPartService.queryPartBomAttributes("Part", (String) null);
            for (Map.Entry<String, List<U9BomExcelParserUtil.U9BOMItem>> entry : grouped.entrySet()) {
                String parentPartNo = entry.getKey();
                List<U9BomExcelParserUtil.U9BOMItem> children = entry.getValue();

                // 获取父级料号对象及当前版本
                PartIteration parentPart = allParts.stream()
                        .filter(p -> p.getNumber().equals(parentPartNo))
                        .findFirst().orElse(null);
                if (parentPart == null) {
                    errorMessages.add("父级料号 " + parentPartNo + " 不存在");
                    continue;
                }
                String currentVersion = parentPart.getVersion();
                String targetVersion = currentVersion;
                String lifecycleStatus = parentPart.getLifecycleStatus();

                // 获取子项版本中以 A/B 开头的最大版本
                Optional<String> maxVersionA = children.stream()
                        .map(U9BomExcelParserUtil.U9BOMItem::getVersion)
                        .filter(v -> v != null && v.startsWith("A"))
                        .max(String::compareTo);

                Optional<String> versionB = children.stream()
                        .map(U9BomExcelParserUtil.U9BOMItem::getVersion)
                        .filter(v -> v != null && v.startsWith("B"))
                        .findFirst();

                if (versionB.isPresent()) {
                    compareAndRevise(currentVersion, versionB.get(), parentPart);
                } else if (maxVersionA.isPresent()) {
                    compareAndRevise(currentVersion, maxVersionA.get(), parentPart);
                }

                // 导入逻辑
                List<Map<Integer, String>> dataList = new ArrayList<>();
                for (U9BomExcelParserUtil.U9BOMItem item : children) {
                    Map<Integer, String> row = new HashMap<>();
                    row.put(0, parentPartNo);
                    row.put(1, item.getPartNo());
                    row.put(2, item.getQty() != null ? item.getQty().toString() : "");
                    row.put(3, item.getPosition());
                    row.put(4, item.getLineNumber() != null ? item.getLineNumber().toString() : "");
                    row.put(5, targetVersion);
                    dataList.add(row);
                }

                Map<Integer, String> headIndexMap = new HashMap<>();
                headIndexMap.put(1, "编码");
                headIndexMap.put(2, "数量");
                headIndexMap.put(3, "位号");
                headIndexMap.put(4, "行号");

                try {
                    logger.info("当前处理父级料号:{}", parentPartNo);
                    iPartService.batchAddBomParts(dataList, headIndexMap, parentPartNo);
                    success += children.size();
                    count += children.size();
                } catch (Exception e) {
                    logger.error("导入父项 {} 时发生异常", parentPartNo, e);
                    errorMessages.add("导入父项 " + parentPartNo + " 失败: " + e.getMessage());
                }
            }
        } catch (Exception e) {
            logger.error("导入失败", e);
            return Result.Fail("导入失败: " + e.getMessage());
        }

        if (!errorMessages.isEmpty()) {
            return Result.Fail("部分导入失败:\n" + String.join("\n", errorMessages));
        }
        return Result.success("导入成功，共导入：" + success + " 条");
    }




    @RequestMapping(value = "batchImportBOM", method = RequestMethod.POST)
    @ApiOperation(response = Result.class, value = "电缆-批量导入BOM（产品容器-批量导入BOM）")
    @Transactional
    public Result batchImportBOM(@RequestParam(value = "file") MultipartFile file) {
        int success = 0;
        int count = 0;

        try (InputStream inputStream = file.getInputStream()) {
            List<BomExcelParserUtil.BOMItem> bomItems = BomExcelParserUtil.parseBomFromExcel(inputStream);

            if (bomItems.isEmpty()) {
                throw new JWIException("导入数据为空");
            }
            Set<String> allCodes = new HashSet<>();
            for (BomExcelParserUtil.BOMItem item : bomItems) {
                if (StringUtils.isNotBlank(item.getParentPartNo())) {
                    allCodes.add(item.getParentPartNo().trim());
                }
                if (StringUtils.isNotBlank(item.getPartNo())) {
                    allCodes.add(item.getPartNo().trim());
                }
            }
            // 查询物料是否存在
            List<PartIteration> allParts = partService.findByCodesAndCatalogOid(new ArrayList<>(allCodes));
            checkDuplicateNumber(new ArrayList<>(allCodes), allParts);


            // 按父项分组
            Map<String, List<BomExcelParserUtil.BOMItem>> groupedByParent = bomItems.stream()
                    .filter(item -> item.getParentPartNo() != null) // 忽略 ROOT 层
                    .collect(Collectors.groupingBy(BomExcelParserUtil.BOMItem::getParentPartNo));

            for (Map.Entry<String, List<BomExcelParserUtil.BOMItem>> entry : groupedByParent.entrySet()) {
                String parentPartNo = entry.getKey();
                List<BomExcelParserUtil.BOMItem> children = entry.getValue();

                logger.info("开始处理父项料号: {}, 子项数量: {}", parentPartNo, children.size());
                logger.info("当前子项数据:{}", JSONUtil.toJsonStr(children));
                // 获取父级料号对象及当前版本
                PartIteration parentPart = allParts.stream()
                        .filter(p -> p.getNumber().equals(parentPartNo))
                        .findFirst().orElse(null);
                if ("Released".equalsIgnoreCase(parentPart.getLifecycleStatus())) {
                    compareAndRevise(parentPart.getVersion(), parentPart.getVersion(), parentPart);
                }


                // 构造 dataList
                List<Map<Integer, String>> dataList = new ArrayList<>();
                for (BomExcelParserUtil.BOMItem item : children) {
                    Map<Integer, String> row = new HashMap<>();
                    row.put(0, parentPartNo); //  0 位置是父项料号
                    row.put(1, item.getPartNo()); //  1 是子项料号
                    row.put(2, item.getQty());    //  2 是用量
                    row.put(3, item.getPosition());    //  3 是位号
                    row.put(4, item.getLineNumber());    //  4 是行号
                    dataList.add(row);
                    logger.info("  子项 -> partNo: {}, qty: {}", item.getPartNo(), item.getQty());
                }

                // 构造 headIndexMap（列索引 -> 字段名）
                Map<Integer, String> headIndexMap = new HashMap<>();
                headIndexMap.put(1, "编码");
                headIndexMap.put(2, "数量");
                headIndexMap.put(3, "位号");
                headIndexMap.put(4, "行号");

                // 批量处理方法
                iPartService.queryPartBomAttributes("Part", (String) null);
                iPartService.batchAddBomParts(dataList, headIndexMap, parentPartNo);

                logger.info("父项: {} 下的 {} 个子项导入成功", parentPartNo, children.size());

                success += children.size();
                count += children.size();
            }

        } catch (IOException e) {
            e.printStackTrace();
        }

        logger.info("本次更新共 {} 条数据，完成了 {} 条数据更新", count, success);
        return Result.success("导入成功，共导入：" + success + " 条");
    }


    @RequestMapping(value = "batchSetStatus", method = RequestMethod.POST)
    @ApiOperation(response = Result.class, value = "批量设置状态")
    public Result batchSetStatus(@RequestBody List<LifecycleStatusUpdateDTO> dtoList){
        logger.info("batchSetStatus入参---->>>>{}", JSONUtil.toJsonStr(dtoList));
        logger.info("batchSetStatus.user---->>>>{}", JSONUtil.toJsonStr(SessionHelper.getCurrentUser()));
        StringBuffer stringBuffer = new StringBuffer();
        //数据发放至下游。
        List<LifecycleAble> lifecycleAbleList = dtoList.parallelStream().map(dto -> {
            try {
                String type = dto.getModelInfo().getType();
                if ("DocumentIteration".equals(type)) {
                    return docAppService.setStatus(dto.getModelInfo(), dto.getStatus());
                } else if ("PartIteration".equals(type)) {
                    LifecycleAble lifecycleAble = partHelper.setStatus(dto.getModelInfo(), dto.getStatus());
                    //数据发放至下游。
                    DataDistributionDTO dataDistributionDTO = new DataDistributionDTO(PartIteration.TYPE, dto.getModelInfo().getOid(), "setStatus", SessionHelper.getAccessToken(), SessionHelper.getCurrentUser(), SessionHelper.getAppId());
                    dataDistributionDTO.setStatus(dto.getStatus());
                    partHelper.dataDistribution(dataDistributionDTO);
                    return lifecycleAble;
                } else {
                    return commonAbilityHelper.setStatus(dto.getModelInfo(),dto.getStatus());
                }
            } catch (Exception e) {
                stringBuffer.append(e.getMessage());
            }
            return null;
        }).filter(Objects::nonNull).collect(Collectors.toList());

        if(stringBuffer.length() > 0)
            throw new JWIException(stringBuffer.toString());

        return Result.success(lifecycleAbleList);
    }

    private static final Logger logger = LoggerFactory.getLogger(CustomerCommonController.class);

    @Autowired
    private CustomerCommonRepo customerCommonRepo;

    @Autowired
    JWICommonService jwiCommonService;

    @Autowired
    DingTalkCall dingTalkCall;

    @Autowired
    DingTalkCallListen dingTalkCallListen;

    @Resource
    private UserHelper userHelper;

    @Autowired
    private DocumentHelper documentHelper;

    @Resource
    ProcessOrderHelper processOrderHelper;

    @Autowired
    InstanceService instanceService;

    @Resource
    JWICommonService commonService;

    @Resource
    private RedissonClient redissonClient;

    @RequestMapping(value = "pdfCreateDD", method = RequestMethod.GET)
    @ApiOperation(response = Result.class, value = "")
    public Result pdfCreateDD(String numberStr, String oidStr) {
        // 增加pdf生成接口防重复请求
        RLock nameLock = redissonClient.getLock("pdfCreateDD");
        nameLock.tryLock();
        if(!nameLock.isHeldByCurrentThread()) {
            return Result.success("");
        }
        try {
            List<DocumentIteration> docList = Collections.emptyList();
            if (oidStr != null && oidStr.length() > 0) {
                List<String> oidList = Arrays.stream(oidStr.split(",")).collect(Collectors.toList());
                docList = oidList.stream().map(oid -> JSON.toJavaObject(instanceService.findDetailByOid(oid, "DocumentIteration"), DocumentIteration.class)).collect(Collectors.toList());
            } else if (numberStr != null && numberStr.length() > 0) {
                List<String> numberList = Arrays.stream(numberStr.split(",")).collect(Collectors.toList());
                docList = customerCommonRepo.queryDoc(numberList);
            }

            docList.stream().forEach(doc -> {
                List<File> priFile = doc.getPrimaryFile();
                if (priFile != null && priFile.size() > 0 && (priFile.get(0).getName().endsWith(".doc") || priFile.get(0).getName().endsWith(".docx"))) {

                    DingTaskRecord dingTaskRecord;
                    if (doc.getExtensionContent() != null && doc.getExtensionContent().getString("processInstanceId") != null) {
                        dingTaskRecord = jwiCommonService.dynamicQueryOne(DingTaskRecord.TYPE, Condition.where("dingProcessInstanceId").eq(doc.getExtensionContent().getString("processInstanceId")), DingTaskRecord.class);
                        if (dingTaskRecord == null) { // 历史的 有钉钉记录 但没有dingTaskRecord记录 先创建dingTaskRecord记录
                            dingTaskRecord = new DingTaskRecord();
                            dingTaskRecord.setIsChange(Boolean.FALSE);
                            dingTaskRecord.setDingProcessInstanceId(doc.getExtensionContent().getString("processInstanceId"));
                            dingTaskRecord.setBusinessId(doc.getExtensionContent().getString("processBusinessId"));
                            dingTaskRecord.setCreateDate(Long.valueOf(doc.getExtensionContent().getString("subTime")));
                            dingTaskRecord.setTenantOid(doc.getTenantOid());
                            dingTaskRecord.setOwner(doc.getOwner());
                            dingTaskRecord.setDocumentIterationOidList(Arrays.asList(doc.getOid()));
                            commonService.create(dingTaskRecord);
                        } else {
                            if(!dingTaskRecord.getDocumentIterationOidList().contains(doc.getOid())) {
                                List<String> newDocOidList = Lists.newArrayList(doc.getOid());
                                customerCommonRepo.updateDingTalkRecordDocList(dingTaskRecord.getOid(), newDocOidList);
                            }
                        }
                    } else
                        dingTaskRecord = customerCommonRepo.queryDingTaskRecored(doc.getOid());

                    if (dingTaskRecord != null) {
                        DocSignWorkflowDTO docSignWorkflowDTO;
                        try {
                            docSignWorkflowDTO = dingTalkCallListen.getDocSignWorkflowDTO(dingTaskRecord);
                            docSignWorkflowDTO.getData().fluentPut("checkInOut", "0").fluentPut("isChange", Boolean.FALSE)
                                    .fluentPut("documentOidList", Arrays.asList(doc.getOid())); // 从dingTaskRecod生成返回的docSignWorkflowDTO里的data里的documentOidList只能是
                            // dingTaskRecod里记录的文档oid 这里要把文档oid修改成当前查询到的文档oid
                        } catch (Exception e) {
                            throw new JWIException(e.getMessage(), e);
                        }

                        UserDTO byAccount = userHelper.findByAccount(dingTaskRecord.getOwner());
                        SessionHelper.addCurrentUser(byAccount);
                        boolean b = documentHelper.docSignWorkflow(docSignWorkflowDTO);
                    } else
                        throw new JWIException("未找到编号" + doc.getNumber() + "最新版本的钉钉审批流程记录");
                }

            });
        }catch (Exception e) {
            throw e;
        } finally {
            if (nameLock.isHeldByCurrentThread()) {
                nameLock.unlock();
            }
        }
        return Result.success("");
    }

    @IgnoreRestUrlAccess
    @RequestMapping(value = "bomReSign", method = RequestMethod.GET)
    @ApiOperation(response = Result.class, value = "")
    public Result bomReSign(String numberStr) {
        RLock nameLock = redissonClient.getLock("bomReSign");
        nameLock.tryLock();
        if(!nameLock.isHeldByCurrentThread()) {
            return Result.success("");
        }
        try{
            MCADIteration mcadIteration = jwiCommonService.dynamicQueryOne(MCADIteration.TYPE, Condition.where(
                    "number").eq(numberStr).and(Condition.where("latest").eq(true)),MCADIteration.class);
            Assert.notNull(mcadIteration,"未找到编码对应文档:" + numberStr);
            ProOrdOfBizObjFuzzyPageDTO dto = new ProOrdOfBizObjFuzzyPageDTO();
            dto.setBizOid(mcadIteration.getOid());
            dto.setBizType(mcadIteration.getType());
            dto.setIndex(1);
            dto.setSize(9999);
            PageResult<ProcessOrderDetail> pageResult =  processOrderHelper.fuzzyPageByBiz(dto);

            List<ProcessOrderDetail> rowList = pageResult.getRows();
            rowList = rowList.stream().filter(item->"done".equalsIgnoreCase(item.getProcessState())).collect(Collectors.toList());
            ProcessOrderDetail processOrderDetail = rowList.stream().sorted(Comparator.comparing(ProcessOrderDetail::getCreateDate).reversed()).findFirst().orElse(null);
            Assert.notNull(processOrderDetail,"未找到编码对应流程信息:" + numberStr);

            List<DingTaskRecord> dingTaskRecordList = jwiCommonService.dynamicQuery(DingTaskRecord.TYPE, Condition.where(
                    "pdmProcessInstanceId").eq(processOrderDetail.getProcessInstanceId()),DingTaskRecord.class);
            DingTaskRecord dingTaskRecord =
                    dingTaskRecordList.stream().sorted(Comparator.comparing(DingTaskRecord::getCreateDate).reversed()).findFirst().orElse(null);
            Assert.notNull(dingTaskRecord,"未找到流程审批信息:" + numberStr);

            DocumentHelper documentHelper = SpringContextUtil.getBean(DocumentHelper.class);
            if(StringUtils.isBlank(dingTaskRecord.getSignRequest())) {
                logger.info("未找到对应流程记录 流程节点:{},流程id:{}", dingTaskRecord.getPdmTaskName(), dingTaskRecord.getOid());
                return Result.success("");
            }
            documentHelper.degSignWorkflow(com.alibaba.fastjson.JSONObject.toJavaObject(com.alibaba.fastjson.JSONObject.parseObject(dingTaskRecord.getSignRequest()), DwgSignWorkflowDTO.class));
        }catch (Exception e) {
            throw e;
        } finally {
            if (nameLock.isHeldByCurrentThread()) {
                nameLock.unlock();
            }
        }
        return Result.success("");

    }

    @RequestMapping(value = "fileTest", method = RequestMethod.POST)
    @ApiOperation(response = Result.class, value = "")
    public Result fileTest(@RequestParam(value = "file") MultipartFile file) throws Exception {
        String originalFileName = file.getOriginalFilename();
        logger.info("originalFileName==>" + originalFileName);
        return Result.success("");
    }

    @RequestMapping(value = "importPartLink", method = RequestMethod.POST)
    @ApiOperation(response = Result.class, value = "")
    public Result importPartLink(@RequestParam(value = "file") MultipartFile file) throws Exception {
        partHelper.importPartLink(file);
        return Result.success("");
    }

    @RequestMapping(value = "wordAddCodeTest", method = RequestMethod.POST)
    @ApiOperation(response = Result.class, value = "")
    public Result wordAddCodeTest() throws Exception {
        setTime();
        return Result.success("清除页眉内容完成");
    }

    @RequestMapping(value = "addDeliveryDocument", method = RequestMethod.POST)
    @ApiOperation(response = Result.class, value = "批量更新交付清单中文档")
    public Result addDeliveryDocument(@RequestParam(value = "file") MultipartFile file, @RequestParam(value = "catalogId") String catalogId) {
        HashMap<String, String> map = new LinkedHashMap<>();
        String CatalogOid = "8848dadd-b430-4f1d-b5b1-b4567a952c29";
        if (null != catalogId && StrUtil.isNotEmpty(catalogId)) {
            CatalogOid = catalogId;
        }

        System.out.println(catalogId);

        try (InputStream inputStream = file.getInputStream()) {
            ExcelReader reader = ExcelUtil.getReader(inputStream);
            List<Map<String, Object>> readAll = reader.readAll();
            // 处理读取的数据,校验
            if(readAll.size() == 0) {
                throw new JWIException("导入数据为空");
            }
            //循环readAll ，找到每个map中 key为编码，且不为空的节点
            for (Map<String, Object> map1 : readAll) {
                String code = map1.get("编码").toString();
                // 如果code不为空且不为null，那么找到map中key 为名称的属性，
                if (null != code && StrUtil.isNotEmpty(code) && null != map1.get("masterOid")) {
                    String name = map1.get("名称").toString();
                    String masterOid = map1.get("masterOid").toString();
                    String masterType = map1.get("masterType").toString();
                    Delivery delivery = jwiCommonService.dynamicQueryOne(Delivery.TYPE,
                            Condition.where("name").eq(name).and(Condition.where("catalogOid").eq(CatalogOid)),
                            Delivery.class);
                    //设置 交付清单和文档的关系
                    if (null != delivery) {
                        AddInstanceToDeliveryDTO addInstanceToDeliveryDTO = new AddInstanceToDeliveryDTO();
                        addInstanceToDeliveryDTO.setDeliveryOid(delivery.getOid());
                        ArrayList<MasterDTO> secObjList = new ArrayList<>();
                        MasterDTO masterDTO = new MasterDTO();
                        masterDTO.setMasterOid(masterOid);
                        masterDTO.setMasterType(masterType);
                        secObjList.add(masterDTO);
                        addInstanceToDeliveryDTO.setSecObjList(secObjList);
                        deliveryService.batchAddInstance(addInstanceToDeliveryDTO);
                    } else {
                        logger.info(name + "没有查到对应交付清单数据");
                    }
                }

            }


        } catch (IOException e) {
            e.printStackTrace();
        }

        return Result.success("更新完成");
    }

    @RequestMapping(value = "checkNameExist", method = RequestMethod.POST)
    @ApiOperation(response = Result.class, value = "检查物料中的封装名称是否存在")
    public Result checkNameExist(@RequestBody CheckNameRequest checkNameRequest) {
        List<String> VALID_MODEL_DEFINITIONS = Arrays.asList("MCADIteration", "Encapsulation","Symbol");

        String name = checkNameRequest.getName();
        String modelDefinition = checkNameRequest.getModelDefinition();
        logger.info("接收入参 名称--->>>{}", name);
        logger.info("接收入参 modelDefinition--->>>{}", modelDefinition);
        initUser();
        // 校验 modelDefinition 是否有效
        if (!VALID_MODEL_DEFINITIONS.contains(modelDefinition)) {
            return Result.success("无效的 modelDefinition");
        }

        if (StrUtil.isEmpty(name)) {
            return Result.success(" ");
        }
        String fileNameWithoutSuffix = "";
        if (null != name && StrUtil.isNotEmpty(name)) {
            JSONArray jsonArray = JSONUtil.parseArray(name);
            // 获取第一个对象
            JSONObject jsonObject = null;
            try {
                jsonObject = jsonArray.getJSONObject(0);
            } catch (Exception e) {
                logger.error(modelDefinition + "Json 结构异常");
                return Result.success("json 结构异常 ");
            }
            // 提取 fileName 属性
            String fileName = jsonObject.getStr("fileName");
            // 获取文件名，不包括后缀
            fileNameWithoutSuffix = FileUtil.getPrefix(fileName);
        }

        if (StrUtil.isEmpty(fileNameWithoutSuffix)) {
            return Result.success(" ");
        }

        boolean nameExists = checkNameExistence(fileNameWithoutSuffix, modelDefinition);

        if (nameExists) {
            return Result.success("名称重复");
        }
        return Result.success("名称不重复");
    }

    @RequestMapping(value = "findAllProductContainer", method = RequestMethod.POST)
    @ApiOperation(response = Result.class, value = "查询所有产品容器列表")
    public Result findAllProductContainer() {
        initUser();
        PageSimpleDTO pageSimpleDTO = new PageSimpleDTO();
        pageSimpleDTO.setSize(999);
        pageSimpleDTO.setIndex(1);
        PageResult<ContainerDetail> containerDetailPageResult = containerHelper.searchContainerDetail(pageSimpleDTO, "ProductContainer");
        List<ContainerDetail> rows = containerDetailPageResult.getRows();
        List<ContainerOidNameDTO> oidNameList = rows.stream()
                .map(row -> new ContainerOidNameDTO(row.getOid(), row.getName()))
                .collect(Collectors.toList());
        logger.info("oidNameList: {}", JSONUtil.toJsonStr(oidNameList));
        return Result.success(oidNameList);
    }

    @RequestMapping(value = "findContainerDetail", method = RequestMethod.POST)
    @ApiOperation(response = Result.class, value = "查询产品容器型号代号")
    public Result findContainerDetail(@RequestBody ContainerDetail containerDetail) {
        initUser();
        containerDetail = containerHelper.findDetail(containerDetail.getOid());
        com.alibaba.fastjson.JSONObject extensionContent = containerDetail.getExtensionContent();
        String code = extensionContent.getString("cn_jwis_cpdh");
        logger.info("containerDetail: {}", JSONUtil.toJsonStr(containerDetail));
        return Result.success(code);
    }

    @RequestMapping(value = "findFolderByContainerOid", method = RequestMethod.POST)
    @ApiOperation(response = Result.class, value = "查询产品容器下文件夹")
    public Result findFolderByContainerOid(@RequestBody(required = false)  ContainerDetail containerDetail) {
        initUser();
        logger.info("containerDetail入参: {}", JSONUtil.toJsonStr(containerDetail));
        List<FolderTreeNode> folderTreeNodes = new ArrayList<>();
        if (Objects.isNull(containerDetail) || StrUtil.isBlank(containerDetail.getOid())) {
            return Result.success(folderTreeNodes);
        }
        folderTreeNodes = pdmFolderServiceI.searchFoldersWithPermisson("ProductContainer", containerDetail.getOid(), "");
        Assert.notNull(folderTreeNodes, "当前容器oid:" + containerDetail.getOid() + "下没有创建文件夹");
        List<FolderOidNameDTO> folderOidNameDTOS = processFolders(folderTreeNodes);
        logger.info("folderOidNameDTOS: {}", JSONUtil.toJsonStr(folderOidNameDTOS));
        return Result.success(folderOidNameDTOS);
    }

    @IgnoreRestUrlAccess
    @RequestMapping(value = "findDocsByFolderOid", method = RequestMethod.POST)
    @ApiOperation(response = Result.class, value = "查询产品容器文件夹下软件配置文件")
    public Result findDocsByFolderOid(@RequestBody(required = false)  FolderOidNameDTO folderOidNameDTO) {
        List<DocumentIteration> documentIterationList = new ArrayList<>();
        initUser();
        if (Objects.isNull(folderOidNameDTO) || StrUtil.isBlank(folderOidNameDTO.getOid())) {
            return Result.success(documentIterationList);
        }
        FuzzySubPageDTO fuzzySubPageDTO = new FuzzySubPageDTO();
        fuzzySubPageDTO.setFromOid(folderOidNameDTO.getOid());
        fuzzySubPageDTO.setFromType(Folder.TYPE);
        fuzzySubPageDTO.setIndex(1);
        fuzzySubPageDTO.setSize(999);
        fuzzySubPageDTO.setSearchKey("");
        PageResult<InstanceEntityWithCatalog> pageResult = this.folderHelper.fuzzySubPage(fuzzySubPageDTO);
        List<InstanceEntityWithCatalog> rows = pageResult.getRows();
        Assert.notNull(rows, " 未查到 " + folderOidNameDTO.getOid() + " 文件夹下的文件");
        Classification classification = classificationHelper.findByCode("cn_yh_rjpz");
        String clsCode = classification.getClsCode();
        // 过滤和转换数据
        List<DocumentOidNameDTO> documentOidNameList = rows.stream()
                .map(row -> documentHelper.findByOid(row.getOid())) // 查询 DocumentIteration 数据
                .filter(documentIteration -> {
                    // 确保 documentIteration 和 clsProperty 不为空，并检查 clsCode
                    return documentIteration != null &&
                            documentIteration.getClsProperty() != null &&
                            clsCode.equals(documentIteration.getClsProperty().getString("clsCode"));
                })
                .map(documentIteration ->
                        new DocumentOidNameDTO(documentIteration.getOid(), documentIteration.getName()) // 转换为 DocumentOidNameDTO
                )
                .collect(Collectors.toList()); // 收集为 List

        // 日志记录
        logger.info("rows: {}", JSONUtil.toJsonStr(rows));
        logger.info("classification: {}", JSONUtil.toJsonStr(classification));
        logger.info("documentOidNameList: {}", JSONUtil.toJsonStr(documentOidNameList));
        // 查询当前软件配置项cn_yh_rjpz
        return Result.success(documentOidNameList);
    }

    @IgnoreRestUrlAccess
    @RequestMapping(value = "ddBomReSign", method = RequestMethod.GET)
    @ApiOperation(response = Result.class, value = "钉钉流程的bom重新签名")
    public Result ddBomReSign(String number) {
        RLock nameLock = redissonClient.getLock("ddBomReSign");
        nameLock.tryLock();
        if(!nameLock.isHeldByCurrentThread()) {
            return Result.success("");
        }
        try{
            ProOrdOfBizObjFuzzyPageDTO dto = new ProOrdOfBizObjFuzzyPageDTO();
            boolean isMCAD = Boolean.FALSE;
            String oid = "";

            MCADIteration mcadIteration = jwiCommonService.dynamicQueryOne(MCADIteration.TYPE, Condition.where(
                    "number").eq(number).and(Condition.where("latest").eq(true)),MCADIteration.class);
            if (null == mcadIteration) {
                ECADIteration ecadIteration = jwiCommonService.dynamicQueryOne(ECADIteration.TYPE, Condition.where(
                        "number").eq(number).and(Condition.where("latest").eq(true)),ECADIteration.class);
                Assert.notNull(ecadIteration,"未找到编码对应文档:" + number);
                dto.setBizOid(ecadIteration.getOid());
                dto.setBizType(ecadIteration.getType());
                dto.setIndex(1);
                dto.setSize(9999);
                oid = ecadIteration.getOid();
            }else {
                dto.setBizOid(mcadIteration.getOid());
                dto.setBizType(mcadIteration.getType());
                dto.setIndex(1);
                dto.setSize(9999);
                isMCAD = Boolean.TRUE;
                oid = mcadIteration.getOid();
            }

            PageResult<ProcessOrderDetail> pageResult =  processOrderHelper.fuzzyPageByBiz(dto);

            List<ProcessOrderDetail> rowList = pageResult.getRows();
            logger.info("rowList---->>>{}", JSONUtil.toJsonStr(rowList));
            rowList = rowList.stream()
                    .filter(item -> "agree".equalsIgnoreCase(item.getProcessState()) && "物料BOM及图纸发布流程".equals(item.getName()))
                    .collect(Collectors.toList());
            ProcessOrderDetail processOrderDetail = rowList.stream().sorted(Comparator.comparing(ProcessOrderDetail::getCreateDate).reversed()).findFirst().orElse(null);
            Assert.notNull(processOrderDetail,"未找到编码对应流程信息:" + number);

            List<DingTaskRecord> dingTaskRecordList = jwiCommonService.dynamicQuery(DingTaskRecord.TYPE, Condition.where(
                    "dingProcessInstanceId").eq(processOrderDetail.getProcessInstanceId()),DingTaskRecord.class);
            DingTaskRecord dingTaskRecord =
                    dingTaskRecordList.stream().sorted(Comparator.comparing(DingTaskRecord::getCreateDate).reversed()).findFirst().orElse(null);
            Assert.notNull(dingTaskRecord,"未找到流程审批信息:" + number);

            if(StringUtils.isBlank(dingTaskRecord.getSignRequest())) {
                logger.info("当前流程未保存SignRequest信息:{}",  dingTaskRecord.getOid());
                return Result.Fail("当前流程id" + dingTaskRecord.getOid() + "未保存SignRequest信息");
            }
            logger.info("ddBomReSign.dingTaskRecord:{}", JSONUtil.toJsonStr(dingTaskRecord));
            if (isMCAD) {
                //MCAD PDF签名
                DwgSignWorkflowDTO dwgSignWorkflowDTO = com.alibaba.fastjson.JSONObject.toJavaObject(com.alibaba.fastjson.JSONObject.parseObject(dingTaskRecord.getSignRequest()), DwgSignWorkflowDTO.class);
                com.alibaba.fastjson.JSONObject data = dwgSignWorkflowDTO.getData();
                com.alibaba.fastjson.JSONArray newDocumentOidList = new com.alibaba.fastjson.JSONArray();
                newDocumentOidList.add(oid);
                // 设置新的数组到 documentOidList
                data.put("documentOidList", newDocumentOidList);
                logger.info("mcad.dwgSignWorkflowDTO--->>{}", JSONUtil.toJsonStr(dwgSignWorkflowDTO));
                customerDocumentHelper.degSignByDingTalk(dwgSignWorkflowDTO);
            }else {
                //处理ECAD类的签名 1、构造ecad签名参数
                com.alibaba.fastjson.JSONObject dwgSignWorkflowDTO = com.alibaba.fastjson.JSONObject.parseObject(dingTaskRecord.getSignRequest());
                com.alibaba.fastjson.JSONArray newDocumentOidList = new com.alibaba.fastjson.JSONArray();
                newDocumentOidList.add(oid);
                dwgSignWorkflowDTO.put("ecadOidList", newDocumentOidList);
                logger.info("ecad.dwgSignWorkflowDTO--->>{}", JSONUtil.toJsonStr(dwgSignWorkflowDTO));
                customerDocumentHelper.ecadDegSignWorkflow(dwgSignWorkflowDTO);
            }
        }catch (Exception e) {
            throw e;
        } finally {
            if (nameLock.isHeldByCurrentThread()) {
                nameLock.unlock();
            }
        }
        return Result.success(number + "重新签名完成");

    }


    @RequestMapping(value = "update", method = RequestMethod.POST)
    @ApiOperation(response = Result.class, value = "更新文档状态")
    public Result update(@RequestBody UpdateRequest updateRequest) {
        String type = updateRequest.getType();
        List<String> docOrEcadOidList = updateRequest.getDocOrEcadOidList();
        String lifecycleStatus = updateRequest.getLifecycleStatus();
        customerCommonRepo.updateLifeStatus(type, docOrEcadOidList, lifecycleStatus);
        // 更新逻辑
        return Result.success("更新成功");
    }


    @IgnoreRestUrlAccess
    @GetMapping("/dailyDownload/runTask")
    @ApiOperation(response = String.class, value = "手动触发下载限制任务")
    public String runDailyDownloadTask() {
        try {
            dailyDownloadService.runTask();
            return "任务已手动触发执行";
        } catch (Exception e) {
            e.printStackTrace();
            return "手动触发任务失败：" + e.getMessage();
        }
    }

    public List<FolderOidNameDTO> processFolders(List<FolderTreeNode> folderTreeNodes) {
        List<FolderOidNameDTO> folderOidNameList = new ArrayList<>();

        for (FolderTreeNode rootNode : folderTreeNodes) {
            // 从根节点的子节点开始遍历
            List<FolderTreeNode> firstNode = rootNode.getChildren();
            if (firstNode != null && !firstNode.isEmpty()) {
                for (FolderTreeNode node : firstNode) {
                    processNodeRecursively(node, node.getName(), folderOidNameList);
                }
            }
        }

        return folderOidNameList;
    }

    private void processNodeRecursively(FolderTreeNode node, String currentNamePath, List<FolderOidNameDTO> folderOidNameList) {
        // 获取当前节点的子节点
        List<FolderTreeNode> children = node.getChildren();

        if (children == null || children.isEmpty()) {
            // 当前节点没有子节点，拼接的name是完整路径，将其添加到结果列表中
            FolderOidNameDTO dto = new FolderOidNameDTO(node.getOid(), currentNamePath);
            folderOidNameList.add(dto);
        } else {
            // 当前节点有子节点，继续递归处理每个子节点
            for (FolderTreeNode childNode : children) {
                // 拼接每一级的name，以"-"作为分隔符
                String newNamePath = currentNamePath + "-" + childNode.getName();
                processNodeRecursively(childNode, newNamePath, folderOidNameList);
            }
        }
    }




    private boolean checkNameExistence(String fileNameWithoutSuffix, String modelDefinition) {
        if ("MCADIteration".equals(modelDefinition)) {
            MCADIteration byName = mcadService.findByName(fileNameWithoutSuffix);
            return byName != null;
        } else {
            ECADIteration byName = ecadService.findByName(fileNameWithoutSuffix, modelDefinition);
            return byName != null;
        }
    }


    private void initUser() {
        UserDTO userDto = new UserDTO();
        userDto.setTenantOid(tenantOid);
        userDto.setOid("sys_admin");
        userDto.setAccount("sys_admin");
        userDto.setSystemAdmin(Boolean.TRUE);
        userDto.setIpAddress("127.0.0.1");
        SessionHelper.addCurrentUser(userDto);
    }

    private  void setTime() throws Exception {
        //   /Users/<USER>/Documents/编码查询/测试文件清单.xlsx
        String fileNameSrc = "/Users/<USER>/Documents/编码查询/副本灵犀05项目文件清单.xlsx";
        String fileNameDest = "/Users/<USER>/Documents/编码查询/副本灵犀05项目文件清单-out.xlsx";

        ExcelReader reader = ExcelUtil.getReader(fileNameSrc);

        List<Map<String, Object>> readAll = reader.readAll();
        // 遍历每一行，处理数据
        for (Map<String, Object> row : readAll) {
            Object codeValue = row.get("编码");
            if (codeValue != null && !codeValue.toString().trim().isEmpty()) {
                // 编码不为空，设置归档时间
                DocumentIteration documentIteration = jwiCommonService.dynamicQueryOne(DocumentIteration.TYPE,
                        Condition.where("number").eq(codeValue), DocumentIteration.class);
                if (documentIteration != null) {
                    long createDate = documentIteration.getCreateDate();
                    String masterOid = documentIteration.getMasterOid();
                    // 将 createDate 转换为 yyyy-MM-dd HH:mm:ss 格式
                    String formattedDate = DateUtil.date(createDate).toString(DatePattern.NORM_DATETIME_PATTERN);
                    // 将格式化后的日期写入归档时间列
                    row.put("归档时间", formattedDate);
                    row.put("masterOid", masterOid);
                    row.put("masterType", documentIteration.getMasterType());
                    row.put("modelDefinition", documentIteration.getModelDefinition());
                    logger.info("当前文档信息:--->>>>" + documentIteration);
                }
            }
        }
//        Map<String, DocumentIteration> numberMap = jwiCommonService.dynamicQuery(DocumentIteration.TYPE,
//                Condition.where("number").eq(objects), DocumentIteration.class).stream().collect(Collectors.toMap(DocumentIteration::getNumber, v -> v));;

        // 将处理后的数据写入新的Excel文件
        ExcelWriter writer = ExcelUtil.getWriter(fileNameDest);
        writer.write(readAll, true);
        Sheet sheet = writer.getSheet();
        org.apache.poi.ss.usermodel.Row firstRow = sheet.getRow(0);
        // 检查 sheet 和第一行是否为空
        if (firstRow != null) {
            // 获取第一行中的列数
            int columnCount = firstRow.getPhysicalNumberOfCells(); // 获取列数
            for (int i = 0; i < columnCount; i++) {
                sheet.autoSizeColumn(i);
            }
        }

        writer.close();
        reader.close();
        logger.info(fileNameDest + " 数据写入完成");

    }

    private void compareAndRevise(String currentVersion, String targetVersion, PartIteration parentPart) {
        String finalVersion;
        int cmp = currentVersion.compareTo(targetVersion);
        if (cmp < 0) {
            finalVersion = targetVersion;
        } else {
            finalVersion = incrementVersion(currentVersion);
        }
        if (finalVersion != null) {
            revisePart(finalVersion, parentPart);
        }
    }

    private String incrementVersion(String version) {
        if (version.length() < 3) return version; // 防御性处理
        String prefix = version.substring(0, 1); // A or B
        String numPart = version.substring(1);
        try {
            int number = Integer.parseInt(numPart);
            number++;
            return String.format("%s%02d", prefix, number);
        } catch (NumberFormatException e) {
            // fallback
            return version;
        }
    }

    private void revisePart(String targetVersion, PartIteration parentPart) {
        VersionInfo node = new VersionInfo();
        node.setModelDefinition("JWIRawMaterial");
        node.setVersion(targetVersion);
        node.setOid(parentPart.getOid());
        node.setType(parentPart.getType());
        LifecycleAble revise = this.partHelper.revise(node);
        DataDistributionDTO dataDistributionDTO = new DataDistributionDTO("PartIteration", revise.getOid(), "revise", SessionHelper.getAccessToken(), SessionHelper.getCurrentUser(), SessionHelper.getAppId());
        partHelper.dataDistribution(dataDistributionDTO);

        ModelInfo modelInfo = new ModelInfo();
        modelInfo.setModelDefinition("JWIComponentsParts");
        modelInfo.setOid(revise.getOid());
        modelInfo.setType(revise.getType());
        LifecycleAble lifecycleAble = partHelper.setStatus(modelInfo, "Design");

        DataDistributionDTO dataDistributionDTO1 = new DataDistributionDTO(PartIteration.TYPE, lifecycleAble.getOid(), "setStatus", SessionHelper.getAccessToken(), SessionHelper.getCurrentUser(), SessionHelper.getAppId());
        dataDistributionDTO1.setStatus("Design");
        partHelper.dataDistribution(dataDistributionDTO1);

    }

    private static void checkDuplicateNumber(List<String> codes, List<PartIteration> allParts) {
        if (allParts.size() != codes.size()) {
            List<String> nonExistentList = new ArrayList<>(codes);
            List<String> dbPartCodes = allParts.stream().map(PartIteration::getNumber).collect(Collectors.toList());
            nonExistentList.removeAll(dbPartCodes);
            if (!nonExistentList.isEmpty()) {
                throw new JWIException(String.join(",", nonExistentList) + " 物料不存在，请先申请物料");
            }

            Map<String, Integer> duplicateMap = new HashMap<>();
            for (String partCode : dbPartCodes) {
                duplicateMap.put(partCode, duplicateMap.getOrDefault(partCode, 0) + 1);
            }

            List<String> duplicateNumberList = new ArrayList<>();
            for (Map.Entry<String, Integer> entry : duplicateMap.entrySet()) {
                if (entry.getValue() > 1) {
                    duplicateNumberList.add(entry.getKey());
                }
            }

            if (!duplicateNumberList.isEmpty()) {
                throw new JWIException("存在重复的部件编码: " + JSON.toJSONString(duplicateNumberList));
            }
        }
    }


}
