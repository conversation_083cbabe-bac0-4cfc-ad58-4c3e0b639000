package cn.jwis.product.pdm.customer.web;

import cn.jwis.framework.base.response.Result;
import cn.jwis.product.pdm.customer.service.interf.PartScreenAutoHelper;
import cn.jwis.product.pdm.partbom.part.dto.BatchCopyPartDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/part/screenSave")
@Api(tags = "物料筛选另存", value = "物料筛选另存", description = "Part Screen")
public class PartScreenController {

//    @Autowired
//    private PartScreenHelper partHelper;

    @Autowired
    private PartScreenAutoHelper partScreenAutoHelper;

    @PostMapping({"/batchScreen"})
    @ApiOperation(
            response = Result.class,
            value = "批量复制"
    )
    public Result<?> batchCopy(@RequestBody BatchCopyPartDTO batchDTO) {
        this.partScreenAutoHelper.partBatchCopy(batchDTO, Boolean.TRUE);
        return Result.success();
    }

}
