package cn.jwis.product.pdm.customer.web;

import cn.jwis.framework.base.response.Result;
import cn.jwis.platform.iam.gateway.access.annotation.IgnoreRestUrlAccess;
import cn.jwis.product.pdm.customer.service.impl.CustomerDefaultAuthHelperImpl;
import cn.jwis.product.pdm.customer.service.impl.CustomerTeamHelperImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR> yefei
 * @Date ： 2024/4/24
 * @Description :
 */
@RestController
@RequestMapping({"/authentication"})
@Api(
        tags = {"认证服务"},
        value = "AuthController"
)
public class CustomerAuthController {

    @Resource
    CustomerDefaultAuthHelperImpl customerDefaultAuthHelper;

    @Resource
    CustomerTeamHelperImpl teamHelper;

    @RequestMapping(
            value = {"/user/tenantInfo"},
            method = {RequestMethod.GET}
    )
    @ApiOperation(
            value = "User Tenant Info",
            notes = "获取用户所属的组织、默认的组织、在默认组织的身边信息"
    )
    public Result findUserTenantInfo() {
        return Result.success(this.customerDefaultAuthHelper.queryUserTenantInfo());
    }

    @IgnoreRestUrlAccess
    @RequestMapping(
            value = {"/user/changeRole"},
            method = {RequestMethod.GET}
    )
    @ApiOperation(
            value = "User Tenant Info",
            notes = "获取用户所属的组织、默认的组织、在默认组织的身边信息"
    )
    public Result changeRole(@RequestParam String account) {
        return Result.success(this.teamHelper.changeRole(account));
    }
}
