package cn.jwis.product.pdm.customer.web;

import cn.hutool.core.date.DateUtil;
import cn.jwis.framework.base.exception.JWIException;
import cn.jwis.framework.base.response.Result;
import cn.jwis.framework.base.util.Assert;
import cn.jwis.framework.base.util.OidGenerator;
import cn.jwis.platform.plm.file.entity.FileMetadata;
import cn.jwis.platform.plm.file.service.FileService;
import cn.jwis.platform.plm.foundation.attachment.entity.File;
import cn.jwis.platform.plm.foundation.common.service.CommonAbilityHelper;
import cn.jwis.platform.plm.foundation.common.service.InstanceHelper;
import cn.jwis.product.pdm.cad.ecad.service.EdaIntegrationHelper;
import cn.jwis.product.pdm.cad.mcad.dto.MCADCreateDTO;
import cn.jwis.product.pdm.cad.mcad.entity.CADFile;
import cn.jwis.product.pdm.cad.mcad.entity.MCADIteration;
import cn.jwis.product.pdm.cad.mcad.relation.Primary;
import cn.jwis.product.pdm.cad.mcad.service.MCADService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> ych
 * @Date ： 2024/10/23
 * @Description :mcad创建Controller
 */

@RestController
@RequestMapping({"/mcad"})
@Api(tags = {"mcad"},value = "mcad", description = "mcad")
public class CustomerMCADController {

    @Autowired
    CommonAbilityHelper commonAbilityHelper;

    @Autowired
    MCADService mcadService;
    @Autowired
    private InstanceHelper instanceHelper;

    @Autowired
    FileService fileService;


    @PostMapping({"/create"})
    @ApiOperation(
            response = Result.class,
            value = "customer创建MCAD-重写",
            notes = "customer创建MCAD-重写"
    )
    public Result create(@RequestBody MCADCreateDTO dto) throws JWIException {
        MCADIteration mcadIteration = new MCADIteration();
        BeanUtils.copyProperties(dto, mcadIteration);
        MCADIteration byName = this.mcadService.findByName(dto.getName());
        if (null != byName) {
            return Result.Fail("已存在相同名称MCAD对象 编码:" + byName.getNumber() + " 名称:" + byName.getName());
        }
        this.mcadService.setLocation(mcadIteration, dto.getLocationInfo());
        MCADIteration result = commonAbilityHelper.doCreate(mcadIteration);
        result.setLifecycleStatus("Released");
        result = commonAbilityHelper.doUpdate(result);

        List<File> primaryFile = dto.getPrimaryFile();
        if (null != primaryFile && primaryFile.size() > 0) {
            File file = primaryFile.get(0);
            FileMetadata fileMetadata = fileService.findByOid(file.getOid());
            Assert.notNull(fileMetadata, "文件不存在");
            CADFile cadFile = new CADFile();
            cadFile.setOid(OidGenerator.newOid());
            cadFile.setType(CADFile.TYPE);
            cadFile.setFileType("Creo 7.0");
            cadFile.setPrimary(Boolean.TRUE);
            cadFile.setLastModified(DateUtil.now());
            cadFile.setFileName(fileMetadata.getFileOriginalName());
            cadFile.setUrl(fileMetadata.getOid());
            CADFile cadFile1 = mcadService.createCADFile(cadFile);
            mcadService.linkMCADFile(mcadIteration.getOid(), cadFile1.getOid(), Primary.TYPE);
        }
        return Result.success(result);
    }
}
