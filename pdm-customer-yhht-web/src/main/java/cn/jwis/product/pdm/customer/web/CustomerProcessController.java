package cn.jwis.product.pdm.customer.web;

import cn.jwis.framework.base.domain.entity.BaseEntity;
import cn.jwis.framework.base.response.Result;
import cn.jwis.product.pdm.customer.entity.CustomerInstanceEntity;
import cn.jwis.product.pdm.customer.service.dto.ProcessTeamUpdateDTO;
import cn.jwis.product.pdm.customer.service.impl.CustomerProcessOrderHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2023/10/19 17:15
 * @Email <EMAIL>
 */
@RestController
@RequestMapping(value = "/process/")
@Api(tags = "流程相关", value = "流程相关接口", description = "流程相关接口")
public class CustomerProcessController {

    @Autowired
    CustomerProcessOrderHelper processOrderHelper;

    @RequestMapping(value = "team/update", method = RequestMethod.POST)
    @ApiOperation(response = Result.class, value = "供流程中更新流程团队")
    public Result updateProcessTeam(@RequestBody ProcessTeamUpdateDTO dto) {
        return Result.success(processOrderHelper.updateProcessTeam(dto));
    }

    @RequestMapping(value = "team/autoUser", method = RequestMethod.POST)
    @ApiOperation(response = Result.class, value = "根据评审对象自动获取流程团队所需角色（来自文件夹团队及其以上团队）")
    public Result autoUser(@RequestBody List<CustomerInstanceEntity> instanceEntityList, @RequestParam(required = false) String workflowId) throws Exception {
        return Result.success(processOrderHelper.autoUser(instanceEntityList));
    }

    @RequestMapping(value = "preNextStepCheck", method = RequestMethod.POST)
    @ApiOperation(response = Result.class, value = "对象选择完毕后下一步时检查对象清单）")
    public Result preNextStepCheck(@RequestBody List<BaseEntity> baseEntityList) {
        processOrderHelper.preNextStepCheck(baseEntityList);
        return Result.success();
    }
}


