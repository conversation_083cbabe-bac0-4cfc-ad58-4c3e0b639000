package cn.jwis.product.pdm.customer.web;

import cn.jwis.framework.base.response.Result;
import cn.jwis.platform.plm.permission.util.PermissionCacheUtil;
import cn.jwis.product.pdm.customer.service.impl.CustomerPartService2Impl;
import cn.jwis.product.pdm.customer.service.interf.CustomerPartCompHelper;
import cn.jwis.product.pdm.partbom.part.data.handler.PartDynamicHeaderDataListener;
import cn.jwis.product.pdm.partbom.part.dto.CompareBomParamDTO;
import cn.jwis.product.pdm.partbom.part.entity.PartIteration;
import cn.jwis.product.pdm.partbom.part.service.IPartService;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.read.metadata.ReadSheet;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;


@RestController
@RequestMapping(value = "/part")
@Slf4j
public class CustomerPartCompController {

    @Resource
    CustomerPartCompHelper customerPartCompHelper;
    @Resource
    private PermissionCacheUtil permissionCacheUtil;
    @Resource
    private IPartService iPartService;
    @Resource
    CustomerPartService2Impl customerPartService2;



    @PostMapping(value = "/bom/attribute")
    public Result<?> cum(@RequestBody CompareBomParamDTO dto){
        return Result.success(customerPartCompHelper.compareAttribute(dto));
    }

    @GetMapping(value = "/partCallBack")
    public Result<?> partCallBack(@RequestParam("processId") String processId){
        return Result.success(customerPartCompHelper.partCallBack(processId));
    }

    @ApiOperation(
            response = Result.class,
            value = "导入部件(带母料变更oid)",
            notes = "导入部件并传入母料变更oid参数"
    )
    @PostMapping({"/bom/upload/withOid"})
    public Result uploadPartBomWithOid(@RequestParam("file") MultipartFile file,
                                       @RequestParam("oid") String oid) throws IOException {
        // 清除权限缓存（如果该步骤必须保留）
        this.permissionCacheUtil.clearCatch();

        // 构建 EasyExcel 读取器
        ExcelReader excelReader = EasyExcel.read(file.getInputStream()).build();
        Result result;

        try {
            List<ReadSheet> sheetList = excelReader.excelExecutor().sheetList();
            List<ReadSheet> readSheetList = new ArrayList<>();
            List<PartDynamicHeaderDataListener> dataListenerList = new ArrayList<>();

            for (int i = 0; i < sheetList.size(); ++i) {
                ReadSheet sheet = sheetList.get(i);
                if (!sheet.getSheetName().startsWith("下拉列表隐藏sheet页签")) {
                    List<List<String>> headList = new ArrayList<>(this.iPartService.queryPartBomAttributes("Part", null));
                    PartDynamicHeaderDataListener dataListener = new PartDynamicHeaderDataListener(headList, "部件BOM导入");

                    ReadSheet readSheet = EasyExcel.readSheet(i).registerReadListener(dataListener).build();
                    readSheetList.add(readSheet);
                    dataListenerList.add(dataListener);
                }
            }

            // 开始读取数据
            excelReader.read(readSheetList);

            // 取数据
            List<Map<Integer, String>> bomDataList = dataListenerList.get(0).getCachedDataList();
            Map<Integer, String> headIndexMap = dataListenerList.get(0).getHeadIndexMap();

            // 打印入参数据
            log.info("传入的 OID 为: {}", oid);
            log.info("Excel 数据共 {} 行", bomDataList.size());
            log.info("HeadIndexMap: {}", headIndexMap);
            for (int i = 0; i < Math.min(bomDataList.size(), 5); i++) {
                log.info("第 {} 行数据: {}", i + 1, bomDataList.get(i));
            }

            // 使用customerPartService2的batchUpdateBomForPartOid方法处理BOM数据
            PartIteration updatedPart = customerPartService2.batchUpdateBomForPartOid(bomDataList, headIndexMap, oid);
            result = Result.success(updatedPart.getOid());
        } finally {
            // 释放资源
            excelReader.finish();
        }

        return result;
    }


}
