package cn.jwis.product.pdm.customer.web;

import cn.hutool.core.map.MapUtil;
import cn.hutool.json.JSONUtil;
import cn.jwis.framework.base.dto.UserDTO;
import cn.jwis.framework.base.exception.JWIException;
import cn.jwis.framework.base.response.Result;
import cn.jwis.framework.base.util.Assert;
import cn.jwis.framework.base.web.session.SessionHelper;
import cn.jwis.platform.plm.foundation.location.able.info.LocationInfo;
import cn.jwis.platform.plm.workflow.engine.dto.LifecycleDTO;
import cn.jwis.platform.plm.workflow.engine.dto.ProOrdCreateDTO;
import cn.jwis.platform.plm.workflow.engine.dto.TeamRoleUserDTO;
import cn.jwis.product.pdm.customer.dos.UserInfo;
import cn.jwis.product.pdm.customer.service.impl.CustomerProcessOrderHelper;
import cn.jwis.product.pdm.customer.service.interf.DingTalkService;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.gson.Gson;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.text.MessageFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2023/8/10 13:34
 * @Email <EMAIL>
 */
@RestController
@RequestMapping(value = "/ding/task")
@Api(tags = "钉钉任务集成", value = "钉钉任务集成", description = "dingTalk task integration")
public class DingTalkController  {

    private static final Logger logger = LoggerFactory.getLogger(DingTalkController.class);

    @Autowired
    private DingTalkService dingTalkService;

    @Autowired
    CustomerProcessOrderHelper processOrderHelper;

    @Value("${dingTalk.processCodeflow:false}")
    private boolean dingTalkProcessCodeflow;

    @Value("${dingTalk.materialSwitch:false}")
    private boolean materialSwitch;

    private static final String CREATE_TASK_KEY = "createSendTask:{0}:{1}";
    @Autowired
    private RedissonClient redissonClient;
    @RequestMapping(value = "/autoCompleteTask", method = RequestMethod.POST)
    @ApiOperation(response = Result.class, value = "任务结束")
    public Result finishTask(@RequestBody JSONObject record) {
        dingTalkService.autoCompleteTask(record);
        return Result.success();
    }

    @RequestMapping(value = "/createSendTask", method = RequestMethod.POST)
    @ApiOperation(response = Result.class, value = "创建钉钉流程")
    public Result createTask(@RequestBody JSONObject delegateTask) {
        logger.info("delegateTask==>" + JSONUtil.toJsonStr(delegateTask));
        //防呆
        RLock nameLock = redissonClient.getLock(MessageFormat.format(CREATE_TASK_KEY, delegateTask.getString("name"),SessionHelper.getCurrentUser().getAccount()));
        try {
            nameLock.tryLock();
            Assert.isTrue(nameLock.isHeldByCurrentThread(), "流程正在启动中,请勿重复点击");
            ProOrdCreateDTO dto = new ProOrdCreateDTO();
            String nameString = delegateTask.getString("name");
            String processModelIdString = delegateTask.getString("processModelId");
            String modelDefinitionString = delegateTask.getString("modelDefinition");

            JSONArray teamContent = delegateTask.getJSONArray("teamContent");
            List<TeamRoleUserDTO> userInfoTos = JSONArray.parseArray(teamContent.toString(), TeamRoleUserDTO.class);
            List<UserInfo> userInfoss = JSONArray.parseArray(teamContent.toString(), UserInfo.class);
            UserDTO currentUser = SessionHelper.getCurrentUser();
            for (UserInfo userInfosss : userInfoss)
                for (TeamRoleUserDTO userInfoTo : userInfoTos)
                    if (userInfosss.getRoleName().equals(userInfoTo.getRoleName()))
                        userInfoTo.setUserAccounts(userInfosss.getUsers());
            if (nameString.equals("物料BOM及图纸发布流程")) {
                userInfoTos = new ArrayList<>();
                List<Map<String, Object>> roleInfoList = Arrays.asList(
                        MapUtil.builder(new HashMap<String, Object>()).put("name", "owner").put("displayName", "所有者").build(),
                        MapUtil.builder(new HashMap<String, Object>()).put("name", "cn_jwis_bzh").put("displayName", "标准化").build(),
                        MapUtil.builder(new HashMap<String, Object>()).put("name", "cn_jwis_proofreader").put("displayName", "校对").build(),
                        MapUtil.builder(new HashMap<String, Object>()).put("name", "cn_jwis_shz").put("displayName", "审核").build(),
                        MapUtil.builder(new HashMap<String, Object>()).put("name", "cn_jwis_countersigner").put("displayName", "会签").build(),
                        MapUtil.builder(new HashMap<String, Object>()).put("name", "cn_jwis_ratifier").put("displayName", "批准").build()
                );
                for (Map<String, Object> roleInfo : roleInfoList) {
                    TeamRoleUserDTO userInfo = new TeamRoleUserDTO();
                    userInfo.setRoleName((String) roleInfo.get("name"));
                    userInfo.setUserAccounts(Collections.singletonList(currentUser.getAccount()));
                    userInfoTos.add(userInfo);
                }

            }

            JSONArray bizObjects = delegateTask.getJSONArray("bizObjects");
            if (bizObjects == null || bizObjects.size() == 0)
                throw new JWIException("未找到发起流程的相关对象，请添加审批对象");
            List<LifecycleDTO> instanceList = JSONArray.parseArray(bizObjects.toString(), LifecycleDTO.class);
            JSONObject locationInfo = delegateTask.getJSONObject("locationInfo");

            Gson gson = new Gson();
            LocationInfo locationInfo1 = gson.fromJson(locationInfo.toString(), LocationInfo.class);
            JSONObject extensionContent = delegateTask.getJSONObject("extensionContent");

            dto.setBizObjects(instanceList);
            dto.setTeamContent(userInfoTos);
            dto.setName(nameString);
            dto.setProcessModelId(processModelIdString);
            dto.setModelDefinition(modelDefinitionString);
            dto.setLocationInfo(locationInfo1);
            dto.setExtensionContent(extensionContent);

            try {
                if (dingTalkProcessCodeflow && ("文档变更流程".equals(delegateTask.getString("name")) || "技术文档发布流程".equals(delegateTask.getString("name")))) {
                    processOrderHelper.checkWorkflowParam(dto);
                    return Result.success(dingTalkService.createSendTask(delegateTask));
                } else if (materialSwitch && "物料编码申请流程".equals(delegateTask.getString("name"))) {
                    processOrderHelper.checkWorkflowParam(dto);
                    dingTalkService.startMaterialNew(dto);
//                    dingTalkService.startMaterial(dto);
                } else if (dingTalkProcessCodeflow && "文件外发流程".equals(delegateTask.getString("name"))) {
                    processOrderHelper.checkWorkflowParam(dto);
                    return Result.success(dingTalkService.createDingTaskForFileOutGoing(delegateTask));
                } else {
                    processOrderHelper.createThenStart(dto);
                }

            } catch (Exception e) {
                logger.error(e.getMessage(), e);
                throw new JWIException("流程发起失败\n" + e.getMessage(), e);
            }
        } catch (Exception e) {
            throw e;
        } finally {
            //释放锁
            if (nameLock.isHeldByCurrentThread()) {
                nameLock.unlock();
            }
        }
        return Result.success();

    }


}
