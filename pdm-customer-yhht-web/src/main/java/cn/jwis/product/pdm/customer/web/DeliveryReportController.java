package cn.jwis.product.pdm.customer.web;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import cn.jwis.framework.base.exception.JWIException;
import cn.jwis.framework.base.response.Result;
import cn.jwis.framework.base.util.StringUtil;
import cn.jwis.framework.base.web.session.SessionHelper;
import cn.jwis.platform.iam.user.User;
import cn.jwis.platform.plm.account.user.service.UserService;
import cn.jwis.platform.plm.container.entity.ContainerTemplate;
import cn.jwis.platform.plm.file.service.FileHelper;
import cn.jwis.platform.plm.foundation.attachment.entity.File;
import cn.jwis.platform.plm.foundation.classification.entity.Classification;
import cn.jwis.platform.plm.foundation.common.service.InstanceHelper;
import cn.jwis.platform.plm.foundation.location.able.info.LocationInfo;
import cn.jwis.platform.plm.foundation.repo.classification.ClassificationRepo;
import cn.jwis.platform.plm.sysconfig.entity.ConfigItem;
import cn.jwis.platform.plm.sysconfig.preferences.PreferencesService;
import cn.jwis.product.pdm.customer.entity.DeliveryReport;
import cn.jwis.product.pdm.customer.entity.DeliveryReportCustom;
import cn.jwis.product.pdm.customer.entity.DeliveryReportCustomExport;
import cn.jwis.product.pdm.customer.repo.CustomerCommonRepo;
import cn.jwis.product.pdm.customer.service.impl.CustomerCommonServiceImpl;
import cn.jwis.product.pdm.customer.service.interf.ExcelReadService;
import cn.jwis.product.pdm.delivery.dto.DeliveryCreateDTO;
import cn.jwis.product.pdm.delivery.dto.FindNodesDTO;
import cn.jwis.product.pdm.delivery.entity.Delivery;
import cn.jwis.product.pdm.delivery.entity.DeliveryTreeNode;
import cn.jwis.product.pdm.delivery.helper.DeliveryHelper;
import cn.jwis.product.pdm.document.remote.container.DocMgmtContainerRemote;
import cn.jwis.product.pdm.document.remote.container.dto.TemplateFindDTO;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static cn.jwis.product.pdm.customer.service.impl.CustomerCommonServiceImpl.fileSeparator;
import static cn.jwis.product.pdm.customer.service.impl.CustomerCommonServiceImpl.tmpPath;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2023/10/23 17:03
 * @Email <EMAIL>
 */
@RestController
@RequestMapping(value = "/deliveryReport/")
@Api(tags = "交付文档", value = "交付文档", description = "交付文档")
public class DeliveryReportController {

    private static final Logger logger = LoggerFactory.getLogger(DeliveryReportController.class);

    @Autowired
    CustomerCommonServiceImpl customerCommonService;

    @Autowired
    private DeliveryHelper deliveryHelper;

    @Autowired
    private CustomerCommonRepo customerCommonRepo;

    @Autowired
    private ClassificationRepo classificationRepo;

    @Resource
    private DocMgmtContainerRemote containerRemote;

    @Autowired
    private FileHelper fileHelper;

    @Resource
    private ExcelReadService excelReadService;

    @Autowired
    InstanceHelper instanceHelper;

    @Autowired
    UserService userService;
    @Autowired
    private PreferencesService preferencesService;



    private static final String JG = "_jiegoujiedian", LX = "_leixingjiedian", parentOidStr = "parentOid";
    private static String FZR_STR = "fzr", FL_STR = "fl", JHSJ_STR = "_jhsj", WCSJ_STR= "_wcsj", MS_STR = "_ms",DELIVERY_OID="_oid", FZR, FL, JHSJ, WCSJ, MS, DOID;
    private static final String EXCEL_COL_FZR = "负责人";
    private static final String EXCEL_COL_JHWCSJ = "计划完成时间";
    private static final String EXCEL_COL_OID = "唯一标识";
    private static final String EXCEL_COL_XM = "项目";
    private static final SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");


//    @RequestMapping(value = "issue/findIssueByEntity", method = RequestMethod.POST)
//    @ApiOperation(response = Result.class, value = "")
//    public Result findIssueByEntity(@RequestBody List<InstanceBasicDTO> instances,
//                                    @RequestParam(value = "onlyUnClosed", required = false) Boolean onlyUnClosed) throws Exception {
//        return Result.success(customerCommonService.findIssueByEntity(instances,onlyUnClosed));
//    }



    /**
     * 交付清单更新（产品容器-交付清单-导入）
     * @param file 文件流
     * @return
     */
    @RequestMapping(value = "updateDelivery", method = RequestMethod.POST)
    @ApiOperation(response = Result.class, value = "批量更新交付清单中计划时间和负责人")
    public Result updateDelivery(@RequestParam(value = "file") MultipartFile file) {
        int success = 0;
        int count = 0;
        try (InputStream inputStream = file.getInputStream()) {
            ExcelReader reader = ExcelUtil.getReader(inputStream);
            List<Map<String, Object>> readAll = reader.readAll();
            // 处理读取的数据,校验
            if(readAll.size() == 0) {
                throw new JWIException("导入数据为空");
            }
            validateData(readAll);
            count = readAll.size();
            for (int i = 0; i < readAll.size(); i++) {
                int excelRowNum = i + 2;
                Map<String, Object> row = readAll.get(i);
                //如果说row不为空，代表是有效数据
                //根据 id查询 当前delivery数据，比对，负责人和时间和当前时间，不一致走更新
                String rowOid = String.valueOf(row.get(EXCEL_COL_OID));
                Delivery oldEntity = JSON.toJavaObject(instanceHelper.findDetailByOid(rowOid, Delivery.TYPE), Delivery.class);
                Assert.notNull(oldEntity, "第{}行 根据唯一标识:{} 查询结果为空", excelRowNum, rowOid);
                Object fzrObject = null;
                String jhsjStr = String.valueOf(row.get(EXCEL_COL_JHWCSJ));
                String jhsjFormat = null;
                // 格式化为目标字符串
                String account = String.valueOf(row.get(EXCEL_COL_FZR));
                User currentUser = userService.searchByAccount(account);
                if (currentUser != null) {
                    String currentUserOid = currentUser.getOid();
                    String currentUserName = currentUser.getName();
                    fzrObject = new com.alibaba.fastjson.JSONObject().fluentPut("oid", currentUserOid).fluentPut("name", currentUserName).toJSONString();
                }
                if (!StrUtil.isEmpty(jhsjStr)) {
                    jhsjFormat = parseDate(jhsjStr);
                }
                customerCommonRepo.updateDeliveryExtension(oldEntity.getOid(), fzrObject, jhsjFormat, null);
                success++;
            }
        } catch (IOException e) {
            e.printStackTrace();
        }

        logger.info("本次更新共" + count + "条数据，完成了" + success + "条数据更新");
        return Result.success("");
    }



    /**
     *   导出当前交付清单（产品容器-交付清单-导出）
     * @param response
     * @param dto
     * @throws IOException
     */
    @RequestMapping(value = "exportExcel", method = RequestMethod.POST)
    @ApiOperation(response = Result.class, value = "导出当前容器中交付清单")
    public void exportExcelNew(HttpServletResponse response,@RequestBody FindNodesDTO dto) throws IOException {
        int count = 0;
        String destPath = tmpPath + "exportExcel" + fileSeparator + UUID.fastUUID() + "-" + DateUtil.now() + "-" + "xxx.xlsx";
        List<DeliveryTreeNode> structureTree = deliveryHelper.findStructureTree(dto);
        List<Map<String, Object>> rows = new ArrayList<>();

        for (DeliveryTreeNode rootNode : structureTree) {
            String rootName = rootNode.getName();
            for (DeliveryTreeNode parent : rootNode.getChildren()) {
                traverseNode(parent, rootName, parent.getName(), rows);
            }
        }

        ExcelWriter writer = ExcelUtil.getWriter(destPath);

        // 设置别名
        writer.addHeaderAlias("交付包", "交付包");
        writer.addHeaderAlias("区分", "区分");
        writer.addHeaderAlias("项目", "项目");
        writer.addHeaderAlias("负责人", "负责人");
        writer.addHeaderAlias("计划完成时间", "计划完成时间");
        writer.addHeaderAlias("完成时间", "完成时间");
        writer.addHeaderAlias("唯一标识", "唯一标识");

        // 写数据
        writer.write(rows, true);
        if (rows.size() > 0) {
            count = rows.size();
        }
        Sheet sheet = writer.getSheet();
        int columnCount = sheet.getRow(0).getPhysicalNumberOfCells(); // 获取列数
        for (int i = 0; i < columnCount; i++) {
            sheet.autoSizeColumn(i);
        }
        logger.info("本次共导出" + count + "条交付清单数据");

        // 获取输出流并写入Excel
        writer.flush();
        writer.close();
        String format = cn.hutool.core.date.DateUtil.format(new Date(), "yyyy-MM-dd");
        outputExcelFile(response, destPath, "交付清单报表" + format);
    }

    /**
     *  批量导出在研卫星交付清单报表
     * @param response
     * @param isNormal true=在研交付清单导出，false=产保交付清单导出
     */
    @RequestMapping(value = "batchExport", method = RequestMethod.GET)
    public void batchExportDeliveryReports(HttpServletResponse response, boolean isNormal) {
        String outFileName = isNormal ? "在研卫星交付清单报表" : "产保交付清单报表";
        ConfigItem valueItem = preferencesService.queryConfigValue("batch_export_delivery");
        logger.info("当前valueItem值为:{}", JSONUtil.toJsonStr(valueItem));
        Map<String,String> map = initPartModelDefinitionMapping(valueItem);

//        HashMap<String, String> map = new LinkedHashMap<>();
        if (map.isEmpty()) {
            map.put("诺亚", "302bef7d-04d0-4d62-be24-37c7d52c657c");
            map.put("AS-vast01遥感卫星", "fd4ec63e-f266-4978-86ff-5e74ca289205");
            map.put("AS-infra01红外遥感卫星", "1da6c864-4b35-4c28-b9ae-119669295bf6");
            map.put("灵犀04倾斜轨道实验星", "d7593a7a-0293-4241-8b81-07b2eb61cb11");
            map.put("灵犀04倾斜轨道组网-第一轨", "75b6abf5-53e2-4f8b-a896-168780ef07eb");
            map.put("灵犀05", "8848dadd-b430-4f1d-b5b1-b4567a952c29");
            map.put("灵犀06电性星", "155e5702-cf4b-4467-8a90-7c65e6a081a8");
            map.put("灵犀06正样星", "b2239ab7-22ea-4271-84a0-3c0c1d3e1c07");
            map.put("灵犀07星", "4b63fc19-e544-44d8-bfc8-f9c9112ae7b5");
            map.put("灵犀07A", "b3dc4cfe-9675-42c9-a6b9-46d29bd0e88b");
            map.put("灵犀09", "0d232a43-af76-45c7-971b-10db4ee14a2c");
            map.put("灵知04卫星", "abe5825f-ae18-4356-8594-fe464fff9797");
            map.put("灵知05星", "dd02ceae-fc68-447f-b498-168e972962c7");
            map.put("灵知07-A星", "87046133-249b-4eb9-8962-d35a4bfba1a7");
            map.put("灵知07-BCD星", "ae5a1b70-e1ca-4fc5-9be6-16722a278f1a");
            map.put("产品化文件库", "145d5c51-62cb-4dde-aedf-1703903fe9ba");
        }

        String destPath = tmpPath + "batchExport" + fileSeparator + UUID.fastUUID() + "-" + DateUtil.now() + "-" + "xxx.xlsx";
        ExcelUtil.getWriter(destPath);
        for (Map.Entry<String, String> entry : map.entrySet()) {
            String sheetName = entry.getKey();
            String containerOid = entry.getValue();

            List<DeliveryReportCustom> deliveryReportList = customerCommonService.getDeliveryDataForExport(containerOid,isNormal);
            List<DeliveryReportCustomExport> sortedResultNew = sortDeliveryReportCustomExports(deliveryReportList);
            if (sortedResultNew == null || sortedResultNew.isEmpty()) {
                continue; // 如果没有数据，则跳过当前 sheet 的创建和写入
            }
            ExcelWriter writer = ExcelUtil.getWriter(destPath, sheetName);
            writer.addHeaderAlias("deliveryBigType", "阶段");
            writer.addHeaderAlias("deliverySecondType", "区分");
            writer.addHeaderAlias("deliverySmallType", "类别");
            writer.addHeaderAlias("deliveryFourthType", "交付清单");
            writer.addHeaderAlias("deliverydocument", "交付文档");
            writer.addHeaderAlias("position", "部门");
            writer.addHeaderAlias("responPerson", "负责人");
            writer.addHeaderAlias("status", "文档状态");
            writer.addHeaderAlias("jhsj", "计划时间");
            writer.addHeaderAlias("wcsj", "完成时间");


            writer.write(sortedResultNew, true);

            Sheet sheet = writer.getSheet();
            // 创建绿色背景样式
            Workbook workbook = writer.getWorkbook();
            CellStyle greenStyle = workbook.createCellStyle();
            greenStyle.setFillForegroundColor(IndexedColors.LIGHT_GREEN.getIndex());
            greenStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            greenStyle.setAlignment(HorizontalAlignment.CENTER); // 设置水平居中
            // 设置边框样式
            greenStyle.setBorderBottom(BorderStyle.THIN);
            greenStyle.setBorderTop(BorderStyle.THIN);
            greenStyle.setBorderLeft(BorderStyle.THIN);
            greenStyle.setBorderRight(BorderStyle.THIN);
            greenStyle.setBottomBorderColor(IndexedColors.BLACK.getIndex());
            greenStyle.setTopBorderColor(IndexedColors.BLACK.getIndex());
            greenStyle.setLeftBorderColor(IndexedColors.BLACK.getIndex());
            greenStyle.setRightBorderColor(IndexedColors.BLACK.getIndex());

            // 创建左对齐绿色样式
            CellStyle leftGreenAlignStyle = workbook.createCellStyle();
            leftGreenAlignStyle.setFillForegroundColor(IndexedColors.LIGHT_GREEN.getIndex());
            leftGreenAlignStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            leftGreenAlignStyle.setAlignment(HorizontalAlignment.LEFT);
            leftGreenAlignStyle.setBorderBottom(BorderStyle.THIN);
            leftGreenAlignStyle.setBorderTop(BorderStyle.THIN);
            leftGreenAlignStyle.setBorderLeft(BorderStyle.THIN);
            leftGreenAlignStyle.setBorderRight(BorderStyle.THIN);
            leftGreenAlignStyle.setBottomBorderColor(IndexedColors.BLACK.getIndex());
            leftGreenAlignStyle.setTopBorderColor(IndexedColors.BLACK.getIndex());
            leftGreenAlignStyle.setLeftBorderColor(IndexedColors.BLACK.getIndex());
            leftGreenAlignStyle.setRightBorderColor(IndexedColors.BLACK.getIndex());

            CellStyle leftAlignStyle = workbook.createCellStyle();
            leftAlignStyle.setAlignment(HorizontalAlignment.LEFT);
            leftAlignStyle.setBorderBottom(BorderStyle.THIN);
            leftAlignStyle.setBorderTop(BorderStyle.THIN);
            leftAlignStyle.setBorderLeft(BorderStyle.THIN);
            leftAlignStyle.setBorderRight(BorderStyle.THIN);
            leftAlignStyle.setBottomBorderColor(IndexedColors.BLACK.getIndex());
            leftAlignStyle.setTopBorderColor(IndexedColors.BLACK.getIndex());
            leftAlignStyle.setLeftBorderColor(IndexedColors.BLACK.getIndex());
            leftAlignStyle.setRightBorderColor(IndexedColors.BLACK.getIndex());
            // 遍历每行数据，设置行颜色
            int rowIndex = 1; // 从第一行开始，因为第0行是表头
            for (DeliveryReportCustomExport report : sortedResultNew) {
                if ("已发布".equals(report.getStatus())) {
                    int columnCount = sheet.getRow(rowIndex).getPhysicalNumberOfCells();
                    for (int colIndex = 0; colIndex < columnCount; colIndex++) {
                        Cell cell = sheet.getRow(rowIndex).getCell(colIndex);
                        cell.setCellStyle(greenStyle);
                        // 单独对deliverySmallType、deliveryFourthType和deliverydocument 这三列的对齐方式改为左对齐
                        if (colIndex == 2 ||colIndex == 3 || colIndex == 4) {
                            cell.setCellStyle(leftGreenAlignStyle);
                        }
                    }
                } else {
                    int columnCount = sheet.getRow(rowIndex).getPhysicalNumberOfCells();
                    for (int colIndex = 0; colIndex < columnCount; colIndex++) {
                        Cell cell = sheet.getRow(rowIndex).getCell(colIndex);
                        // 单独对deliverySmallType、deliveryFourthType、和deliverydocument 这三列的对齐方式改为左对齐
                        if (colIndex == 2 ||colIndex == 3 || colIndex == 4) {
                            cell.setCellStyle(leftAlignStyle);
                        }
                    }
                }
                rowIndex++;
            }

            // 自适应列宽
            Row firstRow = sheet.getRow(0);
            // 检查 sheet 和第一行是否为空
            if (firstRow != null) {
                // 获取第一行中的列数
                int columnCount = firstRow.getPhysicalNumberOfCells(); // 获取列数
                for (int i = 0; i < columnCount; i++) {
                    sheet.autoSizeColumn(i);
                }
            }
//            writer.autoSizeColumnAll();

            // 刷新到文件
            writer.flush();
            writer.close();
        }

        logger.info("Excel数据写入中................................................................");
        String format = cn.hutool.core.date.DateUtil.format(new Date(), "yyyy-MM-dd");
        // 将文件输出到浏览器
        outputExcelFile(response, destPath, outFileName + format);
        logger.info("Excel数据写入完成................................................................");
    }



    @RequestMapping(value = "export", method = RequestMethod.GET)
    public void exportDeliveryReports(HttpServletResponse response, String containerOid, boolean isNormal) {
        List<DeliveryReportCustom> deliveryReportList = customerCommonService.getDeliveryDataForExport(containerOid, isNormal);
        String destPath = tmpPath + "batchExport" + fileSeparator + UUID.fastUUID() + "-" + DateUtil.now() + "-" + "xxx.xlsx";
        HashMap<String, Integer> map = new HashMap<>();
        int ss = 3;
        int sss = 20;
        int sort = 5;
        for (DeliveryReportCustom deliveryReportCustom : deliveryReportList) {
            String position = deliveryReportCustom.getPosition();
            if (StrUtil.isNotEmpty(position)) {
                if (position.contains("平台研发部")) {
                    if (ObjectUtils.equals(position, "平台研发部-总体技术组-总体信息组")) {
                        sort = 1;
                    } else if (ObjectUtils.equals(position, "平台研发部-总体技术组-系统总体组")) {
                        sort = 2;
                    } else if (ObjectUtils.equals(position, "平台研发部-总体技术组-载荷总体组")) {
                        sort = 3;
                    } else {
                        Integer s = map.get(position);
                        if (s == null) {
                            ss += 1;
                            sort = ss;
                            map.put(position, ss);
                        } else {
                            sort = s;
                        }
                    }
                } else {
                    Integer s = map.get(position);
                    if (s == null) {
                        sss += 1;
                        sort = sss;
                        map.put(position, sss);
                    } else {
                        sort = s;
                    }
                }
            } else {
                sort = 9999;
            }
            deliveryReportCustom.setSort(sort);
        }

        deliveryReportList = deliveryReportList.stream()
                .sorted(Comparator.comparing(DeliveryReportCustom::getSort))
                .collect(Collectors.toList());

        // 排序规则
        Map<String, Integer> deliveryBigTypePriority = new HashMap<>();
        deliveryBigTypePriority.put("方案设计阶段", 1);
        deliveryBigTypePriority.put("正样实现阶段", 2);
        deliveryBigTypePriority.put("验收交付阶段", 3);
        deliveryBigTypePriority.put("发射场阶段", 4);
        deliveryBigTypePriority.put("在轨交付阶段", 5);
        deliveryBigTypePriority.put("产品保证", 6);

        // 根据sort分组
        Map<Integer, List<DeliveryReportCustom>> groupedBySort = deliveryReportList.stream()
                .collect(Collectors.groupingBy(DeliveryReportCustom::getSort, LinkedHashMap::new, Collectors.toList()));

        // 对每个分组内的数据进行排序
        List<DeliveryReportCustom> sortedResult = new ArrayList<>();
        List<DeliveryReportCustomExport> sortedResultNew = new ArrayList<>();
        for (Map.Entry<Integer, List<DeliveryReportCustom>> listEntry : groupedBySort.entrySet()) {
            List<DeliveryReportCustom> group = listEntry.getValue();

            // 根据deliveryBigType进行分组并排序
            Map<Integer, List<DeliveryReportCustom>> groupedByBigType = group.stream()
                    .collect(Collectors.groupingBy(report -> deliveryBigTypePriority.getOrDefault(report.getDeliveryBigType(), Integer.MAX_VALUE)));

            List<DeliveryReportCustom> sortedByBigTypeAndDate = new ArrayList<>();
            for (Map.Entry<Integer, List<DeliveryReportCustom>> bigTypeEntry : groupedByBigType.entrySet()) {
                List<DeliveryReportCustom> bigTypeGroup = bigTypeEntry.getValue();

                // 按照jhsj进行排序
                bigTypeGroup.sort(Comparator.comparing(report -> {
                    String jhsj = report.getJhsj();
                    if (StrUtil.isEmpty(jhsj)) {
                        return new Date(Long.MAX_VALUE);
                    } else {
                        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                        try {
                            return sdf.parse(jhsj);
                        } catch (Exception e) {
                            e.printStackTrace();
                            return new Date(Long.MAX_VALUE);
                        }
                    }
                }));

                sortedByBigTypeAndDate.addAll(bigTypeGroup);
            }

            sortedResult.addAll(sortedByBigTypeAndDate);
        }

        for (DeliveryReportCustom custom : sortedResult) {
            DeliveryReportCustomExport export = new DeliveryReportCustomExport(custom);
            sortedResultNew.add(export);
        }
        ExcelWriter writer = ExcelUtil.getWriter(destPath);
        writer.addHeaderAlias("deliveryBigType", "阶段");
        writer.addHeaderAlias("deliverySecondType", "区分");
        writer.addHeaderAlias("deliverySmallType", "类别");
        writer.addHeaderAlias("deliveryFourthType", "交付清单");
        writer.addHeaderAlias("deliverydocument", "交付文档");
        writer.addHeaderAlias("position", "部门");
        writer.addHeaderAlias("responPerson", "负责人");
        writer.addHeaderAlias("status", "文档状态");
        writer.addHeaderAlias("jhsj", "计划时间");
        writer.addHeaderAlias("wcsj", "完成时间");
//        writer.addHeaderAlias("sort", "排序");

        writer.write(sortedResultNew, true);
        // 获取 Apache POI 的 Sheet 对象
        Sheet sheet = writer.getSheet();

        // 创建绿色背景样式
        Workbook workbook = writer.getWorkbook();
        CellStyle greenStyle = workbook.createCellStyle();
        greenStyle.setFillForegroundColor(IndexedColors.LIGHT_GREEN.getIndex());
        greenStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        greenStyle.setAlignment(HorizontalAlignment.CENTER); // 设置水平居中
        // 设置边框样式
        greenStyle.setBorderBottom(BorderStyle.THIN);
        greenStyle.setBorderTop(BorderStyle.THIN);
        greenStyle.setBorderLeft(BorderStyle.THIN);
        greenStyle.setBorderRight(BorderStyle.THIN);
        greenStyle.setBottomBorderColor(IndexedColors.BLACK.getIndex());
        greenStyle.setTopBorderColor(IndexedColors.BLACK.getIndex());
        greenStyle.setLeftBorderColor(IndexedColors.BLACK.getIndex());
        greenStyle.setRightBorderColor(IndexedColors.BLACK.getIndex());

        // 创建左对齐绿色样式
        CellStyle leftGreenAlignStyle = workbook.createCellStyle();
        leftGreenAlignStyle.setFillForegroundColor(IndexedColors.LIGHT_GREEN.getIndex());
        leftGreenAlignStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        leftGreenAlignStyle.setAlignment(HorizontalAlignment.LEFT);
        leftGreenAlignStyle.setBorderBottom(BorderStyle.THIN);
        leftGreenAlignStyle.setBorderTop(BorderStyle.THIN);
        leftGreenAlignStyle.setBorderLeft(BorderStyle.THIN);
        leftGreenAlignStyle.setBorderRight(BorderStyle.THIN);
        leftGreenAlignStyle.setBottomBorderColor(IndexedColors.BLACK.getIndex());
        leftGreenAlignStyle.setTopBorderColor(IndexedColors.BLACK.getIndex());
        leftGreenAlignStyle.setLeftBorderColor(IndexedColors.BLACK.getIndex());
        leftGreenAlignStyle.setRightBorderColor(IndexedColors.BLACK.getIndex());

        CellStyle leftAlignStyle = workbook.createCellStyle();
        leftAlignStyle.setAlignment(HorizontalAlignment.LEFT);
        leftAlignStyle.setBorderBottom(BorderStyle.THIN);
        leftAlignStyle.setBorderTop(BorderStyle.THIN);
        leftAlignStyle.setBorderLeft(BorderStyle.THIN);
        leftAlignStyle.setBorderRight(BorderStyle.THIN);
        leftAlignStyle.setBottomBorderColor(IndexedColors.BLACK.getIndex());
        leftAlignStyle.setTopBorderColor(IndexedColors.BLACK.getIndex());
        leftAlignStyle.setLeftBorderColor(IndexedColors.BLACK.getIndex());
        leftAlignStyle.setRightBorderColor(IndexedColors.BLACK.getIndex());

        // 遍历每行数据，设置行颜色
        int rowIndex = 1; // 从第一行开始，因为第0行是表头
        for (DeliveryReportCustom report : deliveryReportList) {
            if ("已发布".equals(report.getStatus())) {
                int columnCount = sheet.getRow(rowIndex).getPhysicalNumberOfCells();
                for (int colIndex = 0; colIndex < columnCount; colIndex++) {
//                    sheet.getRow(rowIndex).getCell(colIndex).setCellStyle(greenStyle);
                    Cell cell = sheet.getRow(rowIndex).getCell(colIndex);
                    cell.setCellStyle(greenStyle);
                    // 单独对deliveryFourthType和deliverydocument 这两列的对齐方式改为左对齐
                    if (colIndex == 2 ||colIndex == 3 || colIndex == 4) {
                        cell.setCellStyle(leftGreenAlignStyle);
                    }
                }
            } else {
                int columnCount = sheet.getRow(rowIndex).getPhysicalNumberOfCells();
                for (int colIndex = 0; colIndex < columnCount; colIndex++) {
                    Cell cell = sheet.getRow(rowIndex).getCell(colIndex);
                    // 单独对deliveryFourthType和deliverydocument 这两列的对齐方式改为左对齐
                    if (colIndex == 2 ||colIndex == 3 || colIndex == 4) {
                        cell.setCellStyle(leftAlignStyle);
                    }
                }
            }
            rowIndex++;
        }
        writer.autoSizeColumnAll();

        // 设置响应头
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8");
        String fileName = null;
        try {
            fileName = URLEncoder.encode("交付报告", "UTF-8");
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        }
        response.setHeader("Content-Disposition", "attachment;filename=" + fileName + ".xlsx");
        // 输出到浏览器
        try {
            writer.flush(response.getOutputStream(), true);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        writer.close();
    }


    @RequestMapping(value = "getData", method = RequestMethod.GET)
    @ApiOperation(response = Result.class, value = "交付文档报表")
    public Result<List<DeliveryReport>> getDeliveryData(String containerOid){
        return Result.success(customerCommonService.getDeliveryData(containerOid));
    }

    @RequestMapping(value = "exportDeliveryData", method = RequestMethod.GET)
    @ApiOperation(response = Result.class, value = "导出")
    public Result exportDeliveryData(@RequestParam(name = "containerOid") String containerOid, HttpServletResponse response){
        try {
            customerCommonService.exportDeliveryData(containerOid, response);
        } catch (Exception e){
            logger.error(e.getMessage(), e);
            return Result.Fail(e.getMessage());
        }
        return null;
    }


    /**
     * 交付文档导入
     * @param file
     * @param oid
     * @param modelDefinition
     * @return
     */
    @RequestMapping(value = "importDelivery", method = RequestMethod.POST)
    @ApiOperation(response = Result.class, value = "")
    public Result importDelivery(@RequestParam(value = "file") MultipartFile file, @RequestParam(required = false) String oid, @RequestParam(required = false) String modelDefinition) {
        HashMap<Integer, Integer> titleAndDataAndIndexMap = new HashMap<>();
        titleAndDataAndIndexMap.put(0, 1 << 16 | 2);
        Map<Integer, List<JSONObject>> excelDataList = excelReadService.horizonReadGetSheetDataJsonMap(() -> file.getInputStream(), Stream.of(0).collect(Collectors.toSet()), titleAndDataAndIndexMap, null);
        if(excelDataList != null && excelDataList.size() > 0){
            List<JSONObject> dataList = excelDataList.get(0);

            JSONObject maxKVJson = dataList.get(1);
            List<String> keySortedList = maxKVJson.keySet().stream().filter(k -> {
                if(k.contains(FZR_STR)) FZR = k;
//                else if(k.contains(FL_STR)) FL = k;
                else if(k.contains(JHSJ_STR)) JHSJ = k;
                else if(k.contains(WCSJ_STR)) WCSJ = k;
                else if(k.contains(MS_STR)) MS = k;
                return k.contains(JG) || k.contains(LX);
            }).sorted(Comparator.comparing(k -> new Integer(k.substring(0, k.indexOf("_"))))).collect(Collectors.toList());

            if(!maxKVJson.containsKey(FZR))
                throw new JWIException("请检查是否有负责人列 即第行二中是否有fzr单元格 并且第二行中不能有空白单元格");
//            if(!maxKVJson.containsKey(FL))
//                throw new JWIException("请检查是否有分类列 即第行二中是否有fl单元格 并且第二行中不能有空白单元格");
            if(!maxKVJson.containsKey(JHSJ))
                throw new JWIException("请检查是否有计划时间列 即第行二中是否有jhsj单元格 并且第二行中不能有空白单元格");

            List<String> userList = dataList.stream().map(it -> (String) it.get(FZR)).filter(Objects::nonNull).distinct().collect(Collectors.toList());
            Map<Object, Map<String, Object>> accountUserInfoMap = customerCommonRepo.getUserList(userList).stream().collect(Collectors.toMap(it -> it.get("account"), v -> v, (v1, v2) -> v1));

//            List<String> clsCodeList = dataList.stream().map(it -> (String) it.get(FL)).filter(Objects::nonNull).distinct().collect(Collectors.toList());
//            Map<String, Classification> clsCodeInfoMap = classificationRepo.findByCodes(clsCodeList).stream().collect(Collectors.toMap(k -> k.getCode(), v -> v, (v1, v2) -> v1));

            keySortedList.stream().collect(() -> dataList, (list, k) -> {
                Map<Object, List<JSONObject>> levelNameDataMap = list.stream().collect(Collectors.groupingBy(data -> data.getOrDefault(k, "未知值"), LinkedHashMap::new, Collectors.toList()));
                List<Object> levelNameList = levelNameDataMap.keySet().stream().collect(Collectors.toList());

                levelNameList.forEach((levelName) -> {
                    Map<String, List<JSONObject>> parentOidLevelDataMap = levelNameDataMap.get(levelName).stream().collect(Collectors.groupingBy(data -> String.valueOf(data.getOrDefault(parentOidStr, ""))));
                    parentOidLevelDataMap.forEach((parentOid, currList) -> {
                        JSONObject curr = currList.get(0);
                        String newParentOid = createLevelName(oid, modelDefinition, k, String.valueOf(levelName), parentOid
                                , accountUserInfoMap.getOrDefault(curr.get(FZR), Collections.emptyMap())
//                                , clsCodeInfoMap.getOrDefault(curr.getStr(FL), null), curr.getDate(JHSJ, null), curr.getStr(FL));
                                , null, curr.getDate(JHSJ, null), curr.getDate(WCSJ, null), curr.getStr(MS, null), curr.getStr(FL));
                        currList.stream().forEach(json -> json.set(parentOidStr, newParentOid));
                    });
                });

            }, (d1, d2) -> {});
        }else
            throw new JWIException("导入数据为空");

        return Result.success("");
    }


    String createLevelName(String oid, String modelDefinition, String k, String levelName, String parentOid, Map<String, Object> userInfoMap
            , Classification classification, Date jhsjDate, Date wcsjDate, String ms, String classificationStr){
        DeliveryCreateDTO dto = new DeliveryCreateDTO();
        dto.setName(levelName);

        LocationInfo locationInfo = new LocationInfo();
        locationInfo.setCatalogOid(oid);
        locationInfo.setContainerOid(oid);
        String catlogType = "ProductContainer".equals(modelDefinition) ? "Container" : modelDefinition;
        locationInfo.setCatalogType(catlogType);
        locationInfo.setContainerType(catlogType);
        locationInfo.setContainerModelDefinition(modelDefinition);

        dto.setLocationInfo(locationInfo);

        Delivery delivery = null;
        try {
            if(StringUtils.isEmpty(parentOid)){
                dto.setRoot(Boolean.TRUE);
                dto.setModelDefinition("Structure");
                delivery = deliveryHelper.create(dto);
                return delivery.getOid();
            } else if(k.contains(JG)){
                dto.setParentOid(parentOid);
                dto.setModelDefinition("Structure");
                dto.setRoot(Boolean.FALSE);
                delivery = deliveryHelper.create(dto);
            } else if(k.contains(LX)){

//                if(classification == null)
//                    throw new JWIException(levelName + " 分类" + classificationStr + "不存在");
//                ClassificationInfo classificationInfo = new ClassificationInfo();
//                classificationInfo.setOid(classification.getOid());
//                classificationInfo.setDisplayName(classification.getDisplayName());
//                dto.setClassificationInfo(classificationInfo);

                dto.setParentOid(parentOid);
                dto.setModelDefinition("Category");
                dto.setRoot(Boolean.FALSE);

                com.alibaba.fastjson.JSONObject jsonObject = new com.alibaba.fastjson.JSONObject();
                com.alibaba.fastjson.JSONObject fzrJson = new com.alibaba.fastjson.JSONObject();
                jsonObject.fluentPut("FZR", fzrJson);

                if(jhsjDate != null && !"".equals(jhsjDate)){
                    String dataStr = sdf.format(jhsjDate);
                    jsonObject.fluentPut("jhsj", dataStr);
                }

                if(wcsjDate != null && !"".equals(wcsjDate)){
                    String dataStr = sdf.format(wcsjDate);
                    jsonObject.fluentPut("wcsj", dataStr);
                }

                if(ms != null && !"".equals(ms))
                    dto.setDescription(ms);

                fzrJson.fluentPut("oid", userInfoMap.get("oid")).fluentPut("name", userInfoMap.get("name"));
                dto.setExtensionContent(jsonObject);

                delivery = deliveryHelper.create(dto);
            }

        }catch (Exception e){
            String msg = dto.getName() + " " + e.getMessage();
            throw new JWIException(msg, e);
        }

        if(delivery == null)
            throw new RuntimeException("创建失败");
        return delivery.getOid();
    }


    @RequestMapping(value = {"/importDeliveryTemplate"}, method = {RequestMethod.GET})
    @ApiOperation(response = Result.class, value = "交付清单导入模板")
    public void exportDocument(HttpServletResponse response) throws Exception {

        TemplateFindDTO templateFindDTO = new TemplateFindDTO();
        templateFindDTO.setCategory("product");
        templateFindDTO.setName("交付清单导入模板");
        templateFindDTO.setContainerOid(SessionHelper.getCurrentUser().getTenantOid());
        ContainerTemplate signTemplate = containerRemote.findTemplateByName(templateFindDTO);
        File file = signTemplate.getFile();

        fileHelper.downloadByOid(file.getOid(), response);
    }


    /**
     * 交付清单导入数据校验
     * @param dataList 数据源
     */
    private static void validateData(List<Map<String, Object>> dataList) {

        for (int i = 0; i < dataList.size(); i++) {
            Map<String, Object> row = dataList.get(i);
            String xm = (String) row.get(EXCEL_COL_XM);
            int excelRowNum = i + 2;
            /*if (!row.containsKey(EXCEL_COL_FZR) || StrUtil.isBlankIfStr(row.get(EXCEL_COL_FZR))) {
                throw new JWIException("第" + excelRowNum + "行" + StrUtil.SPACE + xm + "  负责人为空");
            }
            if (row.containsKey(EXCEL_COL_JHWCSJ)) {
                String dateValue = String.valueOf(row.get(EXCEL_COL_JHWCSJ));
                if (dateValue == null || StrUtil.isBlank(dateValue)) {
                    throw new JWIException("第" + excelRowNum + "行" + StrUtil.SPACE + xm + "  计划完成时间为空");
                }
            }*/
            if (!row.containsKey(EXCEL_COL_OID) || StrUtil.isBlankIfStr(row.get(EXCEL_COL_OID))) {
                throw new JWIException("第" + excelRowNum + "行" + StrUtil.SPACE + xm + "  唯一标识为空");
            }

        }
    }

    /**
     * 解析日期字符串，支持多种格式
     * @param dateStr 日期字符串
     * @return 解析后的日期字符串，格式为 yyyy-MM-dd
     */
    private static String parseDate(String dateStr) {
        // 尝试多种日期格式解析
        List<String> datePatterns = Arrays.asList(
                DatePattern.NORM_DATE_PATTERN,         // yyyy-MM-dd
                DatePattern.NORM_DATETIME_PATTERN,     // yyyy-MM-dd HH:mm:ss
                DatePattern.NORM_DATETIME_MINUTE_PATTERN, // yyyy-MM-dd HH:mm
                DatePattern.NORM_DATETIME_MS_PATTERN   // yyyy-MM-dd HH:mm:ss.SSS
        );

        for (String pattern : datePatterns) {
            try {
                return DateUtil.parse(dateStr, pattern).toString(DatePattern.NORM_DATE_PATTERN);
            } catch (Exception e) {
                // 忽略异常，尝试下一个格式
            }
        }

        throw new IllegalArgumentException("Unsupported date format: " + dateStr);
    }

    /**
     * 输出到浏览器通用方法
     * @param response 响应
     * @param destPath 目标路径
     * @param downloadFileName 下载文件名称
     */
    private static void outputExcelFile(HttpServletResponse response, String destPath, String downloadFileName) {
        java.io.File file = FileUtil.file(destPath);
        if (!file.exists()) {
            throw new RuntimeException("File not found: " + destPath);
        }

        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8");
        try {
            String encodedFileName = URLEncoder.encode(downloadFileName, "UTF-8");
            // 处理空格变成加号的问题
            encodedFileName = encodedFileName.replaceAll("\\+", "%20");
//            response.setHeader("Content-Disposition", "attachment;filename=" + encodedFileName + ".xlsx");
            response.setHeader("Content-Disposition", "attachment;filename*=UTF-8''" + encodedFileName + ".xlsx");
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException("Error encoding file name", e);
        }

        try (FileInputStream fis = new FileInputStream(file);
             OutputStream os = response.getOutputStream()) {
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = fis.read(buffer)) != -1) {
                os.write(buffer, 0, bytesRead);
            }
        } catch (IOException e) {
            throw new RuntimeException("Error writing file to output stream", e);
        }
    }

    /**
     * 对交付清单数据进行排序，分组等处理
     * @param deliveryReportList 数据源
     * @return
     */
    private static List<DeliveryReportCustomExport> sortDeliveryReportCustomExports(List<DeliveryReportCustom> deliveryReportList) {
        HashMap<String, Integer> sortMap = new HashMap<>();
        int ss = 3;
        int sss = 20;
        int sort = 5;
        // 处理 deliveryReportList 的逻辑
        for (DeliveryReportCustom deliveryReportCustom : deliveryReportList) {
            String position = deliveryReportCustom.getPosition();
            if (StrUtil.isNotEmpty(position)) {
                if (position.contains("平台研发部")) {
                    if (ObjectUtils.equals(position, "平台研发部-总体技术组-总体信息组")) {
                        sort = 1;
                    } else if (ObjectUtils.equals(position, "平台研发部-总体技术组-系统总体组")) {
                        sort = 2;
                    } else if (ObjectUtils.equals(position, "平台研发部-总体技术组-载荷总体组")) {
                        sort = 3;
                    } else {
                        Integer s = sortMap.get(position);
                        if (s == null) {
                            ss += 1;
                            sort = ss;
                            sortMap.put(position, ss);
                        } else {
                            sort = s;
                        }
                    }
                } else {
                    Integer s = sortMap.get(position);
                    if (s == null) {
                        sss += 1;
                        sort = sss;
                        sortMap.put(position, sss);
                    } else {
                        sort = s;
                    }
                }
            } else {
                sort = 9999;
            }
            deliveryReportCustom.setSort(sort);
        }

        deliveryReportList = deliveryReportList.stream()
                .sorted(Comparator.comparing(DeliveryReportCustom::getSort))
                .collect(Collectors.toList());
        // 排序规则
        Map<String, Integer> deliveryBigTypePriority = new HashMap<>();
        deliveryBigTypePriority.put("方案设计阶段", 1);
        deliveryBigTypePriority.put("正样实现阶段", 2);
        deliveryBigTypePriority.put("验收交付阶段", 3);
        deliveryBigTypePriority.put("发射场阶段", 4);
        deliveryBigTypePriority.put("在轨测试阶段", 5);
        deliveryBigTypePriority.put("产品保证", 6);
        deliveryBigTypePriority.put("结构布局输入", 7);
        deliveryBigTypePriority.put("综合测试输入", 8);
        deliveryBigTypePriority.put("星务软件输入", 9);

        // 根据sort分组
        Map<Integer, List<DeliveryReportCustom>> groupedBySort = deliveryReportList.stream()
                .collect(Collectors.groupingBy(DeliveryReportCustom::getSort, LinkedHashMap::new, Collectors.toList()));

        // 对每个分组内的数据进行排序
        List<DeliveryReportCustom> sortedResult = new ArrayList<>();
        List<DeliveryReportCustomExport> sortedResultNew = new ArrayList<>();
        for (Map.Entry<Integer, List<DeliveryReportCustom>> listEntry : groupedBySort.entrySet()) {
            List<DeliveryReportCustom> group = listEntry.getValue();

            // 根据deliveryBigType进行分组并排序
            Map<Integer, List<DeliveryReportCustom>> groupedByBigType = group.stream()
                    .collect(Collectors.groupingBy(report -> deliveryBigTypePriority.getOrDefault(report.getDeliveryBigType(), Integer.MAX_VALUE)));

            List<DeliveryReportCustom> sortedByBigTypeAndDate = new ArrayList<>();
            for (Map.Entry<Integer, List<DeliveryReportCustom>> bigTypeEntry : groupedByBigType.entrySet()) {
                List<DeliveryReportCustom> bigTypeGroup = bigTypeEntry.getValue();

                // 按照jhsj进行排序
                bigTypeGroup.sort(Comparator.comparing(report -> {
                    String jhsj = report.getJhsj();
                    if (StrUtil.isEmpty(jhsj)) {
                        return new Date(Long.MAX_VALUE);
                    } else {
                        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                        try {
                            return sdf.parse(jhsj);
                        } catch (Exception e) {
                            e.printStackTrace();
                            return new Date(Long.MAX_VALUE);
                        }
                    }
                }));

                sortedByBigTypeAndDate.addAll(bigTypeGroup);
            }

            sortedResult.addAll(sortedByBigTypeAndDate);
        }

        for (DeliveryReportCustom custom : sortedResult) {
            DeliveryReportCustomExport export = new DeliveryReportCustomExport(custom);
            sortedResultNew.add(export);
        }
        return sortedResultNew;
    }

    /**
     * 导出递归方法
     * @param node 当前节点
     * @param rootName  顶级节点名称
     * @param parentName 父级节点名称
     * @param rows 目标数组
     */
    private void traverseNode(DeliveryTreeNode node, String rootName, String parentName,List<Map<String, Object>> rows) {

        String modelDefinition = node.getModelDefinition();

        if (Objects.equals("Category", modelDefinition)) {
            Map<String, Object> row2 = new HashMap<>();
            row2.put("交付包", rootName);
            row2.put("区分", parentName);
            row2.put(EXCEL_COL_XM, node.getName());

            com.alibaba.fastjson.JSONObject documentInfo = instanceHelper.findDetailWithContainer(node.getOid(), node.getType());
            com.alibaba.fastjson.JSONObject extensionContent = documentInfo.getJSONObject("extensionContent");

            String account = "";
            String jhwcsj = "";
            if (null != extensionContent) {
                jhwcsj = extensionContent.containsKey("jhsj") ? extensionContent.getString("jhsj") : "";
                com.alibaba.fastjson.JSONObject fzr = extensionContent.getJSONObject("FZR");
                if (null != fzr) {
                    String userOid = fzr.containsKey("oid") ? fzr.getString("oid") : "";
                    User user = userService.searchByOid(userOid);
                    account = user != null ? user.getAccount() : "";
                }
            }
            row2.put(EXCEL_COL_FZR, account);
            row2.put(EXCEL_COL_JHWCSJ, jhwcsj);
            row2.put("完成时间", "");
            row2.put(EXCEL_COL_OID, node.getOid());
            rows.add(row2);
            logger.info("当前交付清单数据---->>>>>>：" + row2);
        } else {
            List<DeliveryTreeNode> children = node.getChildren();
            if (null != children && !children.isEmpty()) {
                for (DeliveryTreeNode child : children) {
                    String pname = parentName;
                    if (Objects.equals("Structure", child.getModelDefinition())) {
                        pname = parentName + "-" + child.getName();
                    }
                    traverseNode(child, rootName, pname, rows);
                }
            }
        }
    }

    private Map<String, String> initPartModelDefinitionMapping(ConfigItem valueItem) {
        Map<String, String> partModelDefinitionMapping = new LinkedHashMap<>();
        if(valueItem==null){
            return partModelDefinitionMapping;
        }
        if(StringUtil.isBlank(valueItem.getValue())){
            return partModelDefinitionMapping;
        }
        String[] arr = valueItem.getValue().split("\\|");
        for(int i = 0; i<arr.length; i++){
            String key = arr[i].split(";;;")[0];
            String value = arr[i].split(";;;")[1];
            partModelDefinitionMapping.put(key,value);
        }
        logger.info("当前partModelDefinitionMapping值为:{}", JSONUtil.toJsonStr(partModelDefinitionMapping));
        return partModelDefinitionMapping;
    }

}
