package cn.jwis.product.pdm.customer.web;

import cn.jwis.framework.base.exception.JWIException;
import cn.jwis.framework.base.response.Result;
import cn.jwis.platform.plm.search.engine.entity.SearchHistory;
import cn.jwis.platform.plm.search.engine.service.SearchHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> yefei
 * @Date ： 2024/7/4
 * @Description :
 */
@RestController
@RequestMapping({"/search"})
@Api(
        tags = {"搜索引擎"},
        value = "搜索引擎"
)
public class CustomerSearchController {

    @Autowired
    SearchHelper searchHelper;

    @ApiOperation(
            value = "全局搜索",
            notes = "全局搜索"
    )
    @PostMapping({"/globalSearch"})
    public Result globalSearch(@RequestBody SearchHistory dto) throws JWIException {
        dto.setModelDefinition("CADAssembly;JWI;CADPart");
        return Result.success(this.searchHelper.globalSearch(dto));
    }
}
