package cn.jwis.product.pdm.customer.web;

import cn.jwis.framework.base.response.Result;
import cn.jwis.product.pdm.customer.entity.DataDictionary;
import cn.jwis.product.pdm.customer.service.interf.DataDictionaryService;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

/**
 * 数据字典
 */
@RestController
@RequestMapping(value = "/dataDictionary")
public class DataDictionaryController {

    @Resource
    private DataDictionaryService dataDictionaryService;


    @GetMapping(value = "/treeList")
    public Result<?> treeList(@RequestParam(required = false) String search){
        return Result.success(dataDictionaryService.treeList(search));
    }

    @PostMapping(value = "/save")
    public Result<?> save(@RequestBody DataDictionary data){
        return Result.success(dataDictionaryService.save(data, Boolean.FALSE));
    }

    @GetMapping(value = "/delete")
    public Result<?> delete(@RequestParam("oid") String oid){
        return Result.success(dataDictionaryService.delete(oid));
    }

    @GetMapping(value = "/getListByCode")
    public Result<?> getListByCode(@RequestParam("code") String code){
        return Result.success(dataDictionaryService.getEnableList(code));
    }


    @GetMapping(value = "/toggleEnable")
    public Result<?> toggleEnable(@RequestParam("oid") String oid, @RequestParam("enable") Boolean enable){
        return Result.success(dataDictionaryService.toggleEnable(oid, enable));
    }


    @GetMapping(value = "/exportExcel")
    public void exportExcel(HttpServletResponse response, @RequestParam(required = false) String search){
        dataDictionaryService.exportExcel(response, search);
    }

    @GetMapping(value = "/exportExcelTemp")
    public void exportExcelTemp(HttpServletResponse response){
        dataDictionaryService.exportExcelTemp(response);
    }

    @PostMapping(value = "/importExcel")
    public Result<?> importExcel(@RequestParam(name = "file") MultipartFile file){
        dataDictionaryService.importExcel(file);
        return Result.success();
    }

}
