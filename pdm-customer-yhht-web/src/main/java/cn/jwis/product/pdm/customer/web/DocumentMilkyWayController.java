package cn.jwis.product.pdm.customer.web;

import cn.jwis.framework.base.response.Result;
import cn.jwis.product.pdm.customer.service.dto.IDSDocCreateDTO;
import cn.jwis.product.pdm.customer.service.interf.DocumentMilkyWayHelper;
import cn.jwis.product.pdm.customer.service.release.DocReleaseIDS;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@RestController
@RequestMapping(value = "/documentMilkyWay")
@Api(tags = "documentMilkyWay", value = "documentMilkyWay", description = "documentMilkyWay")
public class DocumentMilkyWayController {

    @Autowired
    private DocumentMilkyWayHelper documentHelper;

    // IDS来获取容器文件夹目录
    @RequestMapping(value = {"/searchTree"}, method = {RequestMethod.GET})
    @ApiOperation(response = Result.class, value = "免登录获取目标位置")
    public Result searchTree(@RequestParam String account) {
        return Result.success(this.documentHelper.searchFolders(account));
    }

    // IDS根据选择的位置和其它信息，来创建文档，返回一个文档编码。
    @RequestMapping(value = {"/getIDSNumber"}, method = {RequestMethod.POST})
    @ApiOperation(response = Result.class, value = "免登录获取文档编码")
    public Result getIDSNumber(@RequestBody IDSDocCreateDTO dto,@RequestParam String account) {
        return Result.success(documentHelper.getIDSNumber(dto, account, null));
    }

    // IDS 根据文档编码来补充实体文件
    @RequestMapping(value = "/createDoc", method = RequestMethod.POST)
    @ApiOperation(response = Result.class, value = "免登录创建文档")
    public Result createData(@RequestParam(name = "file") MultipartFile[] file,
                             @RequestParam String account,
                             @RequestParam String number) throws Exception {
        return Result.success(documentHelper.createData(file,account,number));
    }

    @Autowired
    private DocReleaseIDS docReleaseIDS;

    @RequestMapping(value = "/sendIDS", method = RequestMethod.POST)
    @ApiOperation(response = Result.class, value = "更新ids状态")
    public Result sendIDS(@RequestBody List<String> oidList) throws Exception {
        return Result.success(docReleaseIDS.sendIDSEdit(oidList));
    }


    /**
     * IDS传递 实体文件、文档名称、文件名称来创建文档
     *
     * @param file    实体文件
     * @param account 账号
     * @param name 初次创建的文档名称
     * @param location pdm产品容器名称
     * @param source 文件来源
     * @param auditlist 会签人
     * @param number pdm文档编号
     * @param deliveryOid 交付清单oid
     * @return Result
     * @throws Exception
     */
    @RequestMapping(value = "/createDocumentForIDS", method = RequestMethod.POST)
    @ApiOperation(response = Result.class, value = "免登录自动创建文档关联附件并关联交付清单")
    public Result createDocumentForIDS(@RequestParam(name = "file",required = false) MultipartFile[] file,
                                       @RequestParam String account,
                                       @RequestParam String name,
                                       @RequestParam String location,
                                       @RequestParam String source,
                                       @RequestParam(required = false) String auditlist,
                                       @RequestParam(required = false) String number, String deliveryOid) {
        return Result.success("success",documentHelper.createDocumentForIDS(file, account, name,location,source,auditlist, number,deliveryOid));
    }



}
