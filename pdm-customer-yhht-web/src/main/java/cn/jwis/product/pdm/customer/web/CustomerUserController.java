package cn.jwis.product.pdm.customer.web;


import cn.jwis.framework.base.response.Result;
import cn.jwis.product.pdm.customer.service.interf.CustomerUserHelper;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 人员信息
 */
@RestController
@RequestMapping(value = "/user")
@Api(tags = "客制化人员查询", value = "客制化人员查询", description = "客制化人员查询")
public class CustomerUserController {

    @Autowired
    private CustomerUserHelper customerUserHelper;

    @GetMapping(value = "/list")
     public Result<?> list(){
         return Result.success(customerUserHelper.getAllUserInfo());
     }

}
