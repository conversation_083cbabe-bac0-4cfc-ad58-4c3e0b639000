package cn.jwis.product.pdm.customer.web;

import cn.jwis.framework.base.dto.UserDTO;
import cn.jwis.framework.base.exception.JWIException;
import cn.jwis.framework.base.response.Result;
import cn.jwis.framework.base.util.StringUtil;
import cn.jwis.framework.base.web.session.SessionHelper;
import cn.jwis.platform.plm.workflow.engine.dto.ProOrdCreateDTO;
import cn.jwis.platform.plm.workflow.engine.web.ProcessOrderController;
import cn.jwis.product.pdm.customer.service.dto.WorkflowActivityRules;
import cn.jwis.product.pdm.customer.service.impl.DingTalkCall;
import cn.jwis.product.pdm.customer.service.interf.DingTalkService;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.dingtalkoauth2_1_0.models.GetAccessTokenResponse;
import com.aliyun.dingtalkworkflow_1_0.models.ProcessForecastHeaders;
import com.aliyun.dingtalkworkflow_1_0.models.ProcessForecastRequest;
import com.aliyun.dingtalkworkflow_1_0.models.ProcessForecastResponse;
import com.aliyun.dingtalkworkflow_1_0.models.ProcessForecastResponseBody;
import com.aliyun.tea.TeaException;
import com.aliyun.teautil.models.RuntimeOptions;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2023/8/10 13:34
 * @Email <EMAIL>
 */
@RestController
@RequestMapping(value = "/ding/task")
@Api(tags = "钉钉任务集成", value = "钉钉任务集成", description = "dingTalk task integration")
@Primary
public class SendDingController  {

    @Autowired
    private DingTalkService dingTalkService;


    @Value("${dingTalk.processCode}")
    private String dingTalkProcessCode;

    @Autowired
    RedisTemplate redisTemplate;

    private static String dingtalk_token_redis_key = "pdm.integration.dingtalk.token";

    private static String dingtalk_token_redis_key_ding = "dingtalk.token";

    // 应用名：PDM  用于与PDM的审批任务集成
    @Value("${dingTalk.appKey}")
    private String dingTalkAppKey;

    @Value("${dingTalk.appSecret}")
    private String dingTalkAppSecret;

    @Autowired
    DingTalkCall dingTalkCall;


    @RequestMapping(value = {"/targetSelectActioners"}, method = {RequestMethod.POST})
    @ApiOperation( value = "获取钉钉的所有审批流程",notes = "获取钉钉的所有审批流程")
    private List<WorkflowActivityRules> targetSelectActioners() throws Exception {

        final UserDTO currentUser = SessionHelper.getCurrentUser();
        final String accessToken = SessionHelper.getAccessToken();
        final String appId = SessionHelper.getAppId();

        SessionHelper.addCurrentUser(currentUser);
        SessionHelper.setAccessToken(accessToken);
        SessionHelper.setAppId(appId);
        String accessTokenDing = getToken();
        String sendUserName = currentUser.getName();
        com.aliyun.dingtalkworkflow_1_0.Client client = SendDingController.createClient();
        ProcessForecastHeaders processForecastHeaders = new ProcessForecastHeaders();
        processForecastHeaders.xAcsDingtalkAccessToken = accessTokenDing;


        ProcessForecastRequest.ProcessForecastRequestFormComponentValues formComponentValues0 = new ProcessForecastRequest.ProcessForecastRequestFormComponentValues()

                .setName("单选框")
                .setValue("通过");
        ProcessForecastRequest processForecastRequest = new ProcessForecastRequest()
                .setProcessCode(dingTalkProcessCode)
                .setDeptId(1)
                .setUserId(sendUserName)
                .setFormComponentValues(java.util.Arrays.asList(
                        formComponentValues0
                ));

        try {
            ArrayList<WorkflowActivityRules> list = new ArrayList<>();
            String actorKey ="";
            String activityName ="";
            ProcessForecastResponse processForecastResponse = client.processForecastWithOptions(processForecastRequest, processForecastHeaders, new RuntimeOptions());

            List<ProcessForecastResponseBody.ProcessForecastResponseBodyResultWorkflowActivityRules> workflowActivityRules = processForecastResponse.getBody().result.workflowActivityRules;

            for(ProcessForecastResponseBody.ProcessForecastResponseBodyResultWorkflowActivityRules workflowActivityRule:workflowActivityRules){
                WorkflowActivityRules workflowActivityRules1 = new WorkflowActivityRules();
                actorKey = workflowActivityRule.getWorkflowActor().getActorKey();
                activityName = workflowActivityRule.activityName;
                workflowActivityRules1.setActivityId(actorKey);
                workflowActivityRules1.setActivityName(activityName);
                list.add(workflowActivityRules1);
            }

            return list;
        } catch (TeaException err) {
            ArrayList<WorkflowActivityRules> list = new ArrayList<>();
            if (!com.aliyun.teautil.Common.empty(err.code) && !com.aliyun.teautil.Common.empty(err.message)) {

            }
            return list;
        }


    }
    public static com.aliyun.dingtalkworkflow_1_0.Client createClient() throws Exception {
        com.aliyun.teaopenapi.models.Config config = new com.aliyun.teaopenapi.models.Config();
        config.protocol = "https";
        config.regionId = "central";
        return new com.aliyun.dingtalkworkflow_1_0.Client(config);
    }

    public String getToken() throws Exception {
        String token = (String) redisTemplate.opsForValue().get(dingtalk_token_redis_key);
        if(StringUtil.isNotBlank(token)){
            return token;
        }
        com.aliyun.teaopenapi.models.Config config = new com.aliyun.teaopenapi.models.Config();
        config.protocol = "https";
        config.regionId = "central";
        com.aliyun.dingtalkoauth2_1_0.Client client = new com.aliyun.dingtalkoauth2_1_0.Client(config);
        com.aliyun.dingtalkoauth2_1_0.models.GetAccessTokenRequest getAccessTokenRequest = new com.aliyun.dingtalkoauth2_1_0.models.GetAccessTokenRequest()
                .setAppKey(dingTalkAppKey)
                .setAppSecret(dingTalkAppSecret);
        try {
            GetAccessTokenResponse response = client.getAccessToken(getAccessTokenRequest);
            token = response.getBody().getAccessToken();

            // token 2小时后失效，此处提前200s重新获取
            redisTemplate.opsForValue().set(dingtalk_token_redis_key, token,7000, TimeUnit.SECONDS);
        } catch (Exception err) {

            throw new JWIException(err);
        }
        return token;
    }
}
