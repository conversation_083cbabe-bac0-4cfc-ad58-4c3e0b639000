package cn.jwis.product.pdm.customer.web;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import cn.jwis.framework.base.dto.PageSimpleDTO;
import cn.jwis.framework.base.response.Result;
import cn.jwis.product.pdm.baseline.entity.BaselinePage;
import cn.jwis.product.pdm.customer.entity.CustomerUnBindUserDTO;
import cn.jwis.product.pdm.customer.service.interf.PdmTeamService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;

/**
 * <AUTHOR> lhm
 * @Date ： Created in 2023/9/13 10:59
 * @Description :
 */
@RestController
@RequestMapping(value = "/team")
@Api(
        tags = {"团队服务"},
        value = "TeamController"
)

public class PdmTeamController {

    private static final Logger log = LoggerFactory.getLogger(PdmTeamController.class);
    @Autowired
    PdmTeamService pdmTeamService;

    @RequestMapping(value = {"/pdmFindAllTeam"}, method = {RequestMethod.GET})
    @ApiOperation(response = Result.class, value = "查询所有团队")
    public Result searchResourceContainer(@RequestParam(required = false) String searchKey) {
        return Result.success(pdmTeamService.findProcessTeam(searchKey));
    }

    @RequestMapping(value = {"/pdmFindProcessTeamPage"}, method = {RequestMethod.POST})
    @ApiOperation(response = Result.class, value = "查询私有团队")
    public Result pdmFindProcessTeamPage(@RequestBody PageSimpleDTO dto) {
        return Result.success(pdmTeamService.findProcessTeamPage(dto.getSearchKey(),dto.getIndex(),dto.getSize()));
    }

    @RequestMapping(value = {"/searchAllTeamRole"}, method = {RequestMethod.POST})
    @ApiOperation(response = Result.class, value = "查询用户所有 产品-文件夹-团队-团队角色 信息")
    public Result searchAllTeamRole(@RequestBody BaselinePage dto) {
        return Result.success(pdmTeamService.searchAllTeamRole(dto.getContainerOid(),dto.getSearchKey(),dto.getIndex(),dto.getSize()));
    }

    @RequestMapping(value = {"/deleteAllTeamRoleUser"}, method = {RequestMethod.POST})
    @ApiOperation(response = Result.class, value = "取消用户产品容器-文件夹下teamRole的角色绑定")
    public Result deleteAllTeamRoleUser(@RequestBody CustomerUnBindUserDTO dto) {
        pdmTeamService.deleteAllTeamRoleUser(dto);
        return Result.success();
    }

    @RequestMapping(value = {"/batchHandleTeamRoleUser"}, method = {RequestMethod.POST})
    @ApiOperation(response = Result.class, value = "批量处理用户的 TeamRole 权限（删除/转移）")
    public Result batchHandleTeamRoleUser(@RequestBody CustomerUnBindUserDTO dto) {
        pdmTeamService.batchHandleTeamRoleUser(dto);
        return Result.success();
    }

//    @IgnoreRestUrlAccess
    @GetMapping("/exportAllTeamRoles")
    public void exportAllTeamRoles(
            @RequestParam("searchKey") String searchKey,
            @RequestParam("index") int index,
            @RequestParam("size") int size,
            HttpServletResponse response) {

        // 从服务层获取数据
        Object data = pdmTeamService.searchAllTeamRole(null, searchKey, index, size);
        log.info("当前数据: {}", JSONUtil.toJsonStr(data));
        // 创建 ExcelWriter
        ExcelWriter writer = ExcelUtil.getWriter(true);

        // 设置列标题
        writer.addHeaderAlias("product", "产品容器名称");
        writer.addHeaderAlias("folderNames", "文件夹名称");
        writer.addHeaderAlias("team", "角色");
        writer.addHeaderAlias("role", "用户名称");

        // 写入数据
        writer.write(CollUtil.newArrayList(data), true);


        // 输出到浏览器
        try {
            // 设置浏览器响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("TeamRoles.xlsx", "UTF-8"));
            writer.flush(response.getOutputStream(), true);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            writer.close();
        }
    }


}
