package cn.jwis.product.pdm.customer.web;

import cn.jwis.framework.base.response.Result;
import cn.jwis.platform.plm.container.dto.container.ContainerCreateDTO;
import cn.jwis.platform.plm.container.dto.container.FuzzySubPageDTO;
import cn.jwis.platform.plm.container.entity.enums.ContainerTemplateType;
import cn.jwis.product.pdm.customer.entity.PermApplyEntity;
import cn.jwis.product.pdm.customer.service.dto.FuzzyContainerInstanceExportDTO;
import cn.jwis.product.pdm.customer.service.dto.SaveAsTemplateDTO;
import cn.jwis.product.pdm.customer.service.impl.CustomPartExportHelperImpl;
import cn.jwis.product.pdm.customer.service.impl.CustomerCommonServiceImpl;
import cn.jwis.product.pdm.customer.service.interf.CustomerContainerHelper;
import cn.jwis.product.pdm.customer.service.interf.DingTalkService;
import cn.jwis.product.pdm.customer.service.interf.ExportDocumentHelper;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * @author: 汪江
 * @data: 2023-12-29 11:04
 * @description:
 **/
@RestController
@RequestMapping({"/customerContainer"})
public class CustomerContainerController {
    @Resource
    private CustomerContainerHelper containerHelper;
    @Resource
    private ExportDocumentHelper exportDocumentHelper;
    @Resource
    private CustomPartExportHelperImpl customPartExportHelper;

    @RequestMapping(
            value = {"/saveAsTemplate"},
            method = {RequestMethod.POST}
    )
    @ApiOperation(
            response = Result.class,
            value = "产品容器另存为"
    )
    public Result saveAsTemplate(@RequestBody SaveAsTemplateDTO dto) {
        return Result.success(this.containerHelper.saveAsTemplate(dto, ContainerTemplateType.PRODUCT.getCategory()));
    }

    @RequestMapping(
            value = {"/product/create"},
            method = {RequestMethod.POST}
    )
    @ApiOperation(
            response = Result.class,
            value = "创建产品容器"
    )
    public Result createProductContainer(@RequestBody ContainerCreateDTO dto) {
        dto.setModelDefinition("ProductContainer");
        return Result.success(this.containerHelper.createContainer(dto));
    }

    @RequestMapping(
            value = {"/exportDocument"},
            method = {RequestMethod.POST}
    )
    @ApiOperation(
            response = Result.class,
            value = "导出文档"
    )
    public void exportDocument(HttpServletResponse response, @RequestBody FuzzyContainerInstanceExportDTO fuzzySubPageDTO) {
        fuzzySubPageDTO.setIndex(1);
        fuzzySubPageDTO.setSize(99999);
        fuzzySubPageDTO.setSubTypes(Arrays.asList("DocumentIteration"));//只查文档
        boolean isExportAll = true;
        exportDocumentHelper.exportDocument(response, fuzzySubPageDTO,isExportAll);
    }

    @RequestMapping(value = {"/exportContentExcel"}, method = {RequestMethod.POST})
    @ApiOperation(response = Result.class, value = "导出文档列表")
    public void exportContentExcel(HttpServletResponse response, @RequestBody FuzzyContainerInstanceExportDTO fuzzySubPageDTO) {
        fuzzySubPageDTO.setIndex(1);
        fuzzySubPageDTO.setSize(99999);
        fuzzySubPageDTO.setSubTypes(Arrays.asList("DocumentIteration"));//只查文档
        boolean isExportAll = false;
        exportDocumentHelper.exportDocument(response, fuzzySubPageDTO, isExportAll);
    }

    @RequestMapping(value = {"/exportExcel"}, method = {RequestMethod.GET})
    @ApiOperation(response = Result.class, value = "导出Excel")
    public void exportExcel(HttpServletResponse response, String fromOid) {
        FuzzySubPageDTO fuzzySubPageDTO1 = new FuzzySubPageDTO();
        fuzzySubPageDTO1.setFromOid(fromOid);
        fuzzySubPageDTO1.setIndex(1);
        fuzzySubPageDTO1.setSize(9999);
        fuzzySubPageDTO1.setFromType("Folder");
        List<String> strings = new ArrayList<>();
        strings.add("PartIteration");
        strings.add("DocumentIteration");
        strings.add("MCADIteration");
        strings.add("ECADIteration");
        fuzzySubPageDTO1.setSubTypes(strings);
        exportDocumentHelper.exportExcel(response, fuzzySubPageDTO1);
    }

//    @Autowired
//    private DingTalkCall dingTalkCall;

    @Autowired
    private DingTalkService dingTalkService;

    @RequestMapping(value = {"/submit"}, method = {RequestMethod.POST})
    @ApiOperation(response = Result.class, value = "权限申请")
    public Result submit(@RequestBody PermApplyEntity permApplyEntity) {
        dingTalkService.startPermApply(permApplyEntity);
        return Result.success();
    }

    @Autowired
    private CustomerCommonServiceImpl customerCommonService;

    @RequestMapping(value = {"/queryPerm"}, method = {RequestMethod.POST})
    @ApiOperation(response = Result.class, value = "权限申请记录查询")
    public Result queryPerm(@RequestBody PermApplyEntity permApplyEntity) {
        return Result.success(customerCommonService.queryPerm(permApplyEntity));
    }

    @RequestMapping(value = {"/exportBOMExcel"}, method = {RequestMethod.POST})
    @ApiOperation(response = Result.class, value = "批量导出BOM")
    public void exportBOMExcel(HttpServletResponse response, @RequestBody Map<String, List<String>> body) throws IOException {

        List<String> oidList = body.get("oidList");

        // 非空校验
        if (CollectionUtils.isEmpty(oidList)) {
            throw new IllegalArgumentException("oidList 不能为空");
        }
        customPartExportHelper.exportMultiBomLine(response, oidList);
    }
}
