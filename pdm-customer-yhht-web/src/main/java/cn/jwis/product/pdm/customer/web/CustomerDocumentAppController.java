package cn.jwis.product.pdm.customer.web;

import cn.jwis.framework.base.response.Result;

import cn.jwis.platform.plm.modelexcel.dto.ModelExcelAllImportDTO;
import cn.jwis.product.pdm.customer.service.dto.ExtDocumentCreateDTO;
import cn.jwis.product.pdm.customer.service.interf.CustomerDocumentHelper;
import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

/**
 * @author: 汪江
 * @data: 2023-12-23 11:04
 * @description:
 **/
@RestController
@RequestMapping(value = "/doc-micro/document")
@Api(tags = "document", value = "document", description = "document")
public class CustomerDocumentAppController {

    @Autowired
    private CustomerDocumentHelper docAppService;

    @ApiOperation(value = "按模板导入部件")
    @PostMapping(value = "/importExcelTemp")
    public Result<?> importExcelTemp(@Valid ModelExcelAllImportDTO importDTO, HttpServletRequest request) {
        docAppService.importExcelTemp(importDTO, request);
        return Result.success();
    }

    @ApiOperation(value = "导入服务器已解压目录下文件")
    @PostMapping(value = "/importUnzipFile")
    public Result<?> importUnzipFile(@RequestBody JSONObject jsonObject) {
        docAppService.importUnzipFile(jsonObject);
        return Result.success();
    }

    @ApiOperation(value = "校对导入历史数据完整性")
    @PostMapping(value = "/checkImportHisData")
    public Result<?> checkImportHisData(@ApiParam("导入的组织结构以及人员excel") @RequestParam(value = "file") MultipartFile file) {
        return Result.success(docAppService.checkImportHisData(file));
    }

    @RequestMapping(value = "/create", method = RequestMethod.POST)
    @ApiOperation(response = Result.class, value = "")
    public Result create(@RequestBody ExtDocumentCreateDTO dto) {
        return Result.success(docAppService.create(dto));
    }
}
