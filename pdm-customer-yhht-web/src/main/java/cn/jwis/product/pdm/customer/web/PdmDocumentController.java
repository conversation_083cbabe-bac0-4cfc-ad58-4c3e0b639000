package cn.jwis.product.pdm.customer.web;

import cn.jwis.framework.base.exception.JWIException;
import cn.jwis.framework.base.response.Result;
import cn.jwis.platform.iam.gateway.access.annotation.IgnoreRestUrlAccess;
import cn.jwis.product.pdm.customer.service.dto.PdmDocBorrowWorkflowDTO;
import cn.jwis.product.pdm.customer.service.dto.PermApplyDTO;
import cn.jwis.product.pdm.customer.service.impl.CustomFolderHelperImpl;
import cn.jwis.product.pdm.customer.service.interf.PdmDocumentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR> lhm
 * @Date ： Created in 2023/9/19 10:33
 * @Description :
 */
@RestController
@RequestMapping(value = "/document")
@Api(tags = "document", value = "document", description = "document")
public class PdmDocumentController {

    @Autowired
    private PdmDocumentService pdmDocumentService;

    @Resource
    private CustomFolderHelperImpl customFolderHelperImpl;


    @PostMapping("/workflow/docBorrow")
    @ApiOperation(value = "项目文档产品化流程结束", notes = "项目文档产品化流程结束")
    public Result docBorrowWorkflow(@RequestBody PdmDocBorrowWorkflowDTO dto) throws JWIException {
        pdmDocumentService.docBorrowWorkflow(dto);
        return Result.success();
    }

    @PostMapping("/create/team")
    @ApiOperation(value = "文档创建带Team", notes = "文档创建带Team")
    @IgnoreRestUrlAccess
    public Result teamCreate(@RequestBody PermApplyDTO dto) throws JWIException {
        customFolderHelperImpl.teamCreate(dto);
        return Result.success();
    }


}
