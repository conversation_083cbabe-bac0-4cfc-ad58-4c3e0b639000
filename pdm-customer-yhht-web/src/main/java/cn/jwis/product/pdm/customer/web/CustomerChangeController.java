package cn.jwis.product.pdm.customer.web;

import cn.jwis.framework.base.response.Result;
import cn.jwis.product.pdm.change.dto.WorkflowDTO;
import cn.jwis.product.pdm.customer.service.interf.CustomerChangeHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> lhm
 * @Date ： Created in 2023/12/28 22:07
 * @Description :
 */
@RestController
@RequestMapping("/change")
@Api(tags = "change",value = "change",description = "变更管理")
public class CustomerChangeController {

    @Autowired
    CustomerChangeHelper changeHelper;

    @RequestMapping(value = "/workflow/updateObject",method = RequestMethod.POST)
    @ApiOperation(response = Result.class,value = "变更对象更新")
    public Result updateObject(@RequestBody WorkflowDTO dto){
        return  Result.success(changeHelper.updateObject(dto));
    }
}
