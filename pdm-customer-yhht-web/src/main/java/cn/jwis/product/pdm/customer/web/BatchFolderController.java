package cn.jwis.product.pdm.customer.web;

import cn.jwis.framework.base.response.Result;
import cn.jwis.product.pdm.customer.service.interf.BatchFolderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR> zhaikun
 * @Date ： 2024/2/1
 * @Description :
 */

@RestController
@RequestMapping("/batchFolder")
@Api(tags = "batchFolder", value = "batchFolder", description = "批量创建文件夹")
public class BatchFolderController {


    @Autowired
    private BatchFolderService batchFolderService;

    @RequestMapping(value = "/batchCreateFolder", method = RequestMethod.POST)
    @ApiOperation(response = Result.class, value = "批量创建文件夹")
    public Result<List<String>> batchCreateFolder(@RequestParam(name = "file") MultipartFile file,
                                                  @RequestParam String containerOid, @RequestParam boolean isNew) {
        return Result.success(batchFolderService.batchCreateFolder(file, containerOid,isNew));
    }


}
