package cn.jwis.product.pdm.customer.web;

import cn.jwis.framework.base.response.Result;
import cn.jwis.product.pdm.customer.service.impl.CustomerPartHelperImpl;
import cn.jwis.product.pdm.partbom.part.dto.PartCreateDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR> yefei
 * @Date ： 2024/8/2
 * @Description :
 */

@RestController
@RequestMapping({"/part"})
@Api(
        tags = {"Part以及bom的操作"},
        value = "Part以及bom的操作",
        description = "Part BOM"
)
public class CustomerPartAppController {

    @Resource
    CustomerPartHelperImpl partHelper;

    @RequestMapping(
            value = {"/create"},
            method = {RequestMethod.POST}
    )
    @ApiOperation(
            response = Result.class,
            value = "创建"
    )
    public Result create(@RequestBody PartCreateDTO dto) {
        return Result.success(this.partHelper.create(dto,true));
    }
}
