package cn.jwis.product.pdm.customer.web;

import cn.jwis.framework.base.response.Result;
import cn.jwis.platform.iam.gateway.access.annotation.IgnoreRestUrlAccess;
import cn.jwis.product.pdm.customer.service.impl.CustomerClassificationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR> yefei
 * @Date ： 2024/3/26
 * @Description :
 */

@RestController
@RequestMapping("/classification")
@Api(tags = "classification",value = "classification",description = "分类信息")
public class CustomerClassificationController {

    @Resource
    CustomerClassificationService customerClassificationService;

    @IgnoreRestUrlAccess
    @RequestMapping(value = "/queryClassification",method = RequestMethod.GET)
    @ApiOperation(response = Result.class,value = "查询分类信息")
    public Result queryClassification(){
        return  Result.success(customerClassificationService.queryClassification());
    }

    @IgnoreRestUrlAccess
    @RequestMapping(value = "/updateClassLayoutMode",method = RequestMethod.GET)
    @ApiOperation(response = Result.class,value = "更新Layout信息")
    public Result updateClassLayoutMode(@RequestParam(defaultValue = "ALL") String code) {
        return  Result.success(customerClassificationService.updateClassLayoutMode(code));
    }
}
