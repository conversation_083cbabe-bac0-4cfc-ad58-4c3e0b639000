package cn.jwis.product.pdm.customer.remote.change;

import cn.jwis.platform.plm.workflow.engine.dto.SearchChangeDataDTO;
import cn.jwis.product.pdm.change.dto.ECACreateDTO;
import cn.jwis.product.pdm.change.entity.ECA;
import cn.jwis.product.pdm.change.response.ECOChangeInfo;

import java.util.List;

/**
 * <AUTHOR> lhm
 * @Date ： Created in 2023/12/25 13:36
 * @Description :
 */
public interface YHChangeRemote {
    ECA createECA(ECACreateDTO ecaCreateDTO);

    List<ECOChangeInfo> findChangeInfoByEco(SearchChangeDataDTO searchChangeDataDTO);
}
