package cn.jwis.product.pdm.customer.service.impl;

import cn.jwis.framework.base.response.PageResult;
import cn.jwis.framework.base.util.Assert;
import cn.jwis.framework.base.util.StringUtil;
import cn.jwis.platform.plm.foundation.classification.dto.FuzzySubDTO;
import cn.jwis.platform.plm.foundation.classification.entity.Classification;
import cn.jwis.platform.plm.foundation.classification.related.ClsLayout;
import cn.jwis.platform.plm.foundation.classification.responce.ClsPropertyWithRel;
import cn.jwis.platform.plm.foundation.classification.service.ClassificationLayoutHelper;
import cn.jwis.platform.plm.foundation.classification.service.ClassificationPropertyHelper;
import cn.jwis.platform.plm.foundation.model.dto.ModeWithPropertyAndClsDTO;
import cn.jwis.platform.plm.foundation.model.dto.PropertyWithValidateDTO;
import cn.jwis.platform.plm.foundation.model.entity.VertexDef;
import cn.jwis.platform.plm.foundation.model.related.ModelLayout;
import cn.jwis.platform.plm.foundation.model.response.ModelPropertyWithRel;
import cn.jwis.platform.plm.foundation.model.service.ModelHelper;
import cn.jwis.platform.plm.foundation.model.service.ModelLayoutHelper;
import cn.jwis.platform.plm.foundation.model.service.ModelPropertyHelper;
import cn.jwis.platform.plm.foundation.table.service.TableViewHelper;
import cn.jwis.platform.plm.modelexcel.dto.ModelExcelAllImportDTO;
import cn.jwis.platform.plm.modelexcel.service.ModelDataSaveService;
import cn.jwis.platform.plm.modelexcel.service.ModelExcelActService;
import cn.jwis.product.pdm.customer.repo.PdmUserRepo;
import cn.jwis.product.pdm.customer.service.dto.LayoutSelectDTO;
import cn.jwis.product.pdm.customer.service.interf.CustomerPartService3;
import cn.jwis.product.pdm.customer.service.interf.DataDictionaryService;
import cn.jwis.product.pdm.partbom.part.data.constant.Constants;
import cn.jwis.product.pdm.partbom.part.data.util.ColumnUtil;
import cn.jwis.product.pdm.partbom.part.dto.PartClassificationDTO;
import cn.jwis.product.pdm.partbom.security.PartPartSecurityRemote;
import cn.jwis.product.pdm.partbom.sysconfig.PartSysConfigRemote;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: 汪江
 * @data: 2023-12-21 14:01
 * @description:
 **/
@Slf4j
@Service
public class CustomerPartService3Impl implements CustomerPartService3 {

    private final static String CREATE = "create";

    @Resource
    private ModelExcelActService modelExcelActService;

    @Resource
    private DataDictionaryService dataDictionaryService;

    @Autowired
    private ModelLayoutHelper modelLayoutHelper;

    @Resource(name = "customerPartDataSaveServiceImpl")
    private ModelDataSaveService modelDataSaveService;

    @Autowired
    private ClassificationPropertyHelper clsPropertyHelper;

    //电子件桶
    @Value("${elePartBucket:ele-part-bucket}")
    private String elePartBucket;

    @Autowired
    private PartSysConfigRemote sysConfig;

    @Autowired
    private ModelHelper modelHelper;

    @Autowired
    private PartPartSecurityRemote securityFeign;

    @Autowired
    private ClassificationLayoutHelper classificationLayoutHelper;

    @Autowired
    private ModelPropertyHelper modelPropertyHelper;

    @Resource
    private PdmUserRepo pdmUserRepo;

    @Resource
    private TableViewHelper tableViewHelper;

    @Override
    public void importExcelTemp(ModelExcelAllImportDTO importDTO, HttpServletRequest request) {
        String modelDefinition = request.getParameter("modelDefinition");
        if(Objects.equals(modelDefinition, "ElectricalPart")){
            //电子件使用特定的桶
            modelExcelActService.importExcelAllModel(importDTO, modelDataSaveService, request, elePartBucket);
        }else{
            modelExcelActService.importExcelAllModel(importDTO, modelDataSaveService, request);
        }

    }

    @Override
    public List<Map<Integer, Object>> queryPartDataByHeadMap(List<PartClassificationDTO> partDtoList, Map<String, String> headNameCodeMap, List<List<String>> headList, Map<String, Boolean> headNameIsSystemDefaultMap, Map<String, Map<String, Map<String, String>>> drownData4ModelDefinition) {
        Map<String, String> attrSystemDefaultMap = new HashMap<>(16);
        Map<String, String> attrExtendPropertyMap = new HashMap<>(16);
        String attrCode;
        for (String name : headNameCodeMap.keySet()) {
            attrCode = headNameCodeMap.get(name);
            if (headNameIsSystemDefaultMap.get(attrCode)) {
                attrSystemDefaultMap.put(name, attrCode);
            } else {
                attrExtendPropertyMap.put(name, attrCode);
            }
        }

        //单位
        List<Map<String,Object>> units = sysConfig.specialSearchUnits();
        Map<String, String> unitsMap = new HashMap<>(8);
        if (!CollectionUtils.isEmpty(units)) {
            for (Map<String, Object> unitMap : units) {
                unitsMap.put((String) unitMap.get("val"), (String) unitMap.get("txt"));
            }
        }

        //密级
        List<Map<String, String>> specialList = securityFeign.specialSearch();
        Map<String, String> specialMap = new HashMap<>(8);
        if(!CollectionUtils.isEmpty(specialList)) {
            for (Map<String, String> special : specialList) {
                specialMap.put((String) special.get("val"), (String) special.get("txt"));
            }
        }

        //模型名称状态类型转换
        Map<String, String> keyTxt = tableViewHelper.findAllDataI18n();

        List<Map<Integer, Object>> dataList = new ArrayList<>();
        Map<Integer, Object> dataMap;
        String code;
        JSONObject extensionContent;
        JSONObject clsProperty;
        String extensionContentValue;
        String clsPropertyValue;
        Object value;
        for (PartClassificationDTO partClassificationDTO : partDtoList) {
            Map<String, Map<String, String>> dropDownValue;
            if (drownData4ModelDefinition.containsKey(partClassificationDTO.getModelDefinition())) {
//                dropDownValue = drownData4ModelDefinition.get(partClassificationDTO.getModelDefinition());
                dropDownValue = drownData4ModelDefinition.get(partClassificationDTO.getClsOid());
            } else {
                Map<String, String> drownData = new HashMap<>();
                List<List<String>> headList2 = new ArrayList<>(this.queryPartAttributes(partClassificationDTO.getModelDefinition(), drownData, partClassificationDTO.getClsCode(), partClassificationDTO.getClsOid()));
                log.info("exportTemplate ==>{}", JSON.toJSONString(drownData));
                Map<String, String> dropDownValueUntreated = this.getDropDownValueIncludeCls(drownData, partClassificationDTO.getModelDefinition(), partClassificationDTO.getClsOid());
//                log.info("queryPartDataByHeadMap.dropDownValueUntreated=" + JSON.toJSONString(dropDownValueUntreated));
                dropDownValue = new HashMap<>();
                if (null != dropDownValueUntreated) {
                    for (Map.Entry<String, String> entry : dropDownValueUntreated.entrySet()) {
                        Map<String, String> enumMap = this.splitJsonToMap4Enum(entry.getValue(), entry.getKey(), true);
                        if (!CollectionUtils.isEmpty(enumMap)) {
                            dropDownValue.put(this.getPartAttrName(Integer.parseInt(entry.getKey()), headList2), enumMap);
                        }
                    }
                }
//                drownData4ModelDefinition.put(partClassificationDTO.getModelDefinition(), dropDownValue);
                drownData4ModelDefinition.put(partClassificationDTO.getClsOid(), dropDownValue);
            }
            dataMap = new HashMap<>(16);
            dataMap.put(getPartAttrIndex(Constants.HEAD_PART_TYPE, headList), partClassificationDTO.getModelDefinition());
            dataMap.put(getPartAttrIndex(Constants.HEAD_PART_NUMBER, headList), partClassificationDTO.getNumber());
            dataMap.put(getPartAttrIndex(Constants.HEAD_NAME, headList), partClassificationDTO.getName());
            extensionContent = partClassificationDTO.getExtensionContent();
            clsProperty = partClassificationDTO.getClsProperty();
            Map<String, String> clsInfoMap = new HashMap<>(8);
            for (String name : attrSystemDefaultMap.keySet()) {
                code = attrSystemDefaultMap.get(name);
                if ("classificationInfo".equals(code)) {
                    clsInfoMap.put(name, code);
                    break;
                }
                if ("defaultUnit".equals(code)) {
                    Object unitKey = ColumnUtil.getFieldValueByName(partClassificationDTO, code);
                    String unitDisplayName = unitsMap.containsKey(unitKey) ? unitsMap.get(unitKey) : unitKey == null ? "" : unitKey.toString();
                    dataMap.put(getPartAttrIndex(name, headList), unitDisplayName);
                } else if("levelForSecrecy".equals(code)) {
                    value = ColumnUtil.getFieldValueByName(partClassificationDTO, code);
                    if(value == null) {
                        dataMap.put(getPartAttrIndex(name, headList),"");
                    } else {
                        dataMap.put(getPartAttrIndex(name, headList), specialMap.get(String.valueOf(value)));
                    }
                } else if("modelDefinition".equals(code) || "lifecycleStatus".equals(code)){
                    value = ColumnUtil.getFieldValueByName(partClassificationDTO, code);
                    dataMap.put(getPartAttrIndex(name, headList),value == null ? "" : keyTxt.get(value.toString()));
                } else {
                    if (dropDownValue.containsKey(name)) {
                        dataMap.put(getPartAttrIndex(name, headList), dropDownValue.get(name).get((String) ColumnUtil.getFieldValueByName(partClassificationDTO, code)));
                    } else {
                        dataMap.put(getPartAttrIndex(name, headList), ColumnUtil.getFieldValueByName(partClassificationDTO, code));
                    }
                }
            }
            for (String name : clsInfoMap.keySet()) {
                dataMap.put(getPartAttrIndex(name, headList), partClassificationDTO.getClassification().getDisplayName());
            }
            for (String name : attrExtendPropertyMap.keySet()) {
                code = attrExtendPropertyMap.get(name);
                if(extensionContent == null){
                    extensionContentValue = "";
                } else {
                    if(extensionContent.get(code) instanceof JSONObject){
                        JSONObject val = extensionContent.getJSONObject(code);
                        extensionContentValue = StringUtil.isNotBlank(val.getString("name")) ? val.getString("name") : val.toJSONString();
                    }else{
                        extensionContentValue = extensionContent.getString(code);
                    }
                }
                if (dropDownValue.containsKey(name)) {
                    dataMap.put(getPartAttrIndex(name, headList), dropDownValue.get(name).get(extensionContentValue));
                } else {
                    dataMap.put(getPartAttrIndex(name, headList), extensionContentValue);
                }
                if (StringUtils.isEmpty(extensionContentValue) && clsProperty != null) {
                    clsPropertyValue = (String) clsProperty.get(code);
                    if (dropDownValue.containsKey(name)) {
                        dataMap.put(getPartAttrIndex(name, headList), dropDownValue.get(name).get(clsPropertyValue));
                    } else {
                        dataMap.put(getPartAttrIndex(name, headList), clsPropertyValue);
                    }
                }
            }
            dataList.add(dataMap);
        }
        return dataList;
    }

    public List<List<String>> queryPartAttributes(String code, Map<String, String> drownData, String clsCode, String clsOid) {
        List<List<String>> attributeList = new ArrayList<>();
        List<String> topHeaders = Arrays.asList("部件类型", "编码", Constants.HEAD_NAME);
        for (String name : topHeaders) {
            attributeList.add(Collections.singletonList(name));
        }
        List<ModelPropertyWithRel> modelPropertyWithRelList = modelPropertyHelper.findAllByModel(code);
        Assert.notEmpty(modelPropertyWithRelList, code + " attribute is null!");
        //解析属性列表
        Map<String, String> modelPropertyMap = modelPropertyWithRelList.stream().collect(Collectors.toMap(ModelPropertyWithRel::getName, ModelPropertyWithRel::getDisplayName, (key1, key2) -> key2));
        ModelLayout modelLayout = modelLayoutHelper.findByCodeAndModel(null, code, CREATE);
        Assert.notNull(modelLayout, code + " layout is null!");
        JSONObject jsonObject = modelLayout.getContent();
        JSONArray array = jsonObject.getJSONArray("layout");

        int index = 2;
        JSONObject dataJson;
        String fieldName;
        String compType;
        boolean classification = false;
        for (Object object : array) {
            JSONArray elementObj = (JSONArray) object;
            for (Object ele : elementObj) {
                if (ele instanceof JSONObject) {
                    dataJson = (JSONObject) ele;
                    fieldName = (String) dataJson.get("fieldName");
                    if (topHeaders.contains(modelPropertyMap.get(fieldName))) {
                        continue;
                    }
                    if ("classificationInfo".equals(fieldName)) {
                        classification = true;
                        continue;
                    }
                    if (StringUtils.isNotEmpty(fieldName)) {
                        ++index;
                    }
                    compType = (String) dataJson.get("compType");
                    if ("dropDown".equals(compType)) {
                        drownData.put(String.valueOf(index), fieldName);
                    }
                    List<String> attributes = new ArrayList<>();
                    if(StringUtils.isNotBlank(modelPropertyMap.get(fieldName))) {
                        attributes.add(modelPropertyMap.get(fieldName));
                        attributeList.add(attributes);
                    }
                } else {
                    log.info("elementObj ==>{}", JSON.toJSONString(ele));
                }
            }
        }
        if (classification && StringUtils.isNotEmpty(clsOid)) {
            List<String> attributes = new ArrayList<>();
            //分类名称放在sheet页签
            //获取分类属性表单列表
            List<ClsPropertyWithRel> clsPropertyWithRelList = getClsPropertyMap(clsOid);
            Map<String, String> clsPropertyMap = clsPropertyWithRelList.stream().collect(Collectors.toMap(ClsPropertyWithRel::getCode, ClsPropertyWithRel::getDisplayName, (key1, key2) -> key2));
            //获取分类属性
            ClsLayout clsLayout = classificationLayoutHelper.findByCodeAndCls(clsOid, "", CREATE);
            if(clsLayout.getContent() != null && clsLayout.getContent().get("layout") != null) {
                JSONArray layoutArray = clsLayout.getContent().getJSONArray("layout");
                for (Object object : layoutArray) {
                    JSONArray elementObj = (JSONArray)object;
                    for (Object ele : elementObj) {
                        if (ele instanceof JSONObject) {
                            dataJson = (JSONObject) ele;
                            fieldName = (String) dataJson.get("fieldName");
                            attributes = new ArrayList<>();
                            //分类的属性增加枚举类校验
                            if (StringUtils.isNotEmpty(fieldName)) {
                                ++index;
                            }
                            if (StringUtils.isNotEmpty(clsPropertyMap.get(fieldName))) {
                                attributes.add(clsPropertyMap.get(fieldName));
                                attributeList.add(attributes);
                            }
                            compType = (String) dataJson.get("compType");
                            if ("dropDown".equals(compType)) {
                                drownData.put(String.valueOf(index), fieldName);
                            }
                        }
                    }
                }
            }
        }
        return attributeList;
    }

    public Map<String, String>  getDropDownValueIncludeCls(Map<String, String> drownDataMap, String modelName, String clsOid) {
        Map<String, String> result = this.getDropDownValue(drownDataMap, modelName);
        List<ClsPropertyWithRel> clsPropertyWithRelList = this.getClsPropertyMap(clsOid);
        if (!CollectionUtils.isEmpty(clsPropertyWithRelList)) {
            for (Map.Entry<String, String> entry : result.entrySet()) {
                if (StringUtils.isBlank(entry.getValue())) {
                    Optional<ClsPropertyWithRel> clsProWithRelOPT = clsPropertyWithRelList.stream().filter(d -> d.getCode().equals(drownDataMap.get(entry.getKey()))).findFirst();
                    if (clsProWithRelOPT.isPresent()
                            && null != clsProWithRelOPT.get().getRelationship()
                            && StringUtils.isNotBlank(clsProWithRelOPT.get().getRelationship().getConstraintType())
                            && "enumtype".equalsIgnoreCase(clsProWithRelOPT.get().getRelationship().getConstraintType())
                            && null != clsProWithRelOPT.get().getRelationship().getConstraintContext()) {
                        result.put(entry.getKey(), clsProWithRelOPT.get().getRelationship().getConstraintContext().getString("valueEnum"));
                    }else if(clsProWithRelOPT.isPresent()
                            && null != clsProWithRelOPT.get().getRelationship()
                            && StringUtils.isNotBlank(clsProWithRelOPT.get().getRelationship().getConstraintType())
                            && "datadictionary".equalsIgnoreCase(clsProWithRelOPT.get().getRelationship().getConstraintType())
                            && null != clsProWithRelOPT.get().getRelationship().getConstraintContext()){
                        String code = clsProWithRelOPT.get().getRelationship().getConstraintContext().getString("datadictionary");
                        List<LayoutSelectDTO> list =  dataDictionaryService.getEnableList(code);
                        result.put(entry.getKey(), JSON.toJSONString(list));
                    }
                }
            }
        }
        return result;
    }

    public Map<String, String> getDropDownValue(Map<String, String> drownDataMap, String modelName) {
        Map<String, String> dropDownValue = new HashMap<>(16);
        //下拉子项
        List<VertexDef> vertexDefList = modelHelper.flapTree(modelName);
        dropDownValue.put("0", vertexDefList.stream().map(VertexDef::getCode).collect(Collectors.joining(",")));
        List<String> nameList = new ArrayList<>(drownDataMap.values());
        ModeWithPropertyAndClsDTO propertyAndClsDTO = modelHelper.findWithPropertyAndCls(modelName);
        List<PropertyWithValidateDTO> propertyWithValidateDTOList = propertyAndClsDTO.getFields();
        List<Map<String,Object>> units = sysConfig.specialSearchUnits();
        String unitArray = units.stream().map(map->(String) map.get("txt")).collect(Collectors.joining(","));
        if (!CollectionUtils.isEmpty(propertyWithValidateDTOList)) {
            Map<String, String> attributeDataMap = new HashMap<>(16);
            String code;
            List<JSONObject> validators;
            String category;
            for (PropertyWithValidateDTO propertyWithValidateDTO : propertyWithValidateDTOList) {
                code = propertyWithValidateDTO.getCode();
                if (nameList.contains(code)) {
                    //单位特殊处理
                    if("defaultUnit".equals(code)){
                        attributeDataMap.put(code, unitArray);
                    } else {
                        validators = propertyWithValidateDTO.getValidators();
                        for (JSONObject jsonObject : validators) {
                            category = (String) jsonObject.get("category");
                            if ("list".equals(category)) {
                                attributeDataMap.put(code, (String) jsonObject.get("content"));
                            }
                        }
                    }
                }
            }
            if (!CollectionUtils.isEmpty(attributeDataMap)) {
                String security = getSecurity();
                String attribute;
                for (String index : drownDataMap.keySet()) {
                    attribute = drownDataMap.get(index);
                    if("levelForSecrecy".equals(attribute)) {
                        dropDownValue.put(index, security);
                    } else {
                        dropDownValue.put(index, attributeDataMap.get(attribute));
                    }
                }
            }
        }
        return dropDownValue;
    }

    private String getSecurity() {
        List<Map<String, String>> securityList = securityFeign.specialSearch();
        String security = null;
        if(!CollectionUtils.isEmpty(securityList)) {
            StringBuilder securityAppend = new StringBuilder();
            for (Map<String, String> securityData : securityList) {
                securityAppend.append(securityData.get("txt")).append(",");
            }
            security = securityAppend.substring(0, securityAppend.lastIndexOf(","));
        }
        return security;
    }

    public Map<String, String> splitJsonToMap4Enum(String valueEnum, String colName, boolean reversal) {
        Map<String, String> result = new HashMap<>();
        if(StringUtil.isBlank(valueEnum)){
            return result;
        }
        try {
            List<LayoutSelectDTO> jsonData = JSON.parseArray(valueEnum, LayoutSelectDTO.class);
            if (reversal) {
                return jsonData.stream().collect(Collectors.toMap(LayoutSelectDTO::getVal, LayoutSelectDTO::getTxt, (o1,o2) -> o1));
            } else {
                return jsonData.stream().collect(Collectors.toMap(LayoutSelectDTO::getTxt, LayoutSelectDTO::getVal, (o1,o2) -> o1));
            }
        } catch (Exception e) {

            valueEnum = valueEnum.trim().replaceAll("},\\{", "}@{");
            List<String> valueEnumList = Arrays.asList(valueEnum.split("@"));
            if (!CollectionUtils.isEmpty(valueEnumList)) {
                valueEnumList.forEach(v -> {
                    if (this.checkIsJsonString(v)) {
                        JSONObject keyValue = JSON.parseObject(v);
                        String key = keyValue.getString("txt");
                        //考虑除txt外的其他标识，后续可追加
                        if (StringUtils.isBlank(key)) {
                            key = keyValue.getString("key");
                        }

                        String value = keyValue.getString("val");
                        //考虑除val外的其他标识，后续可追加
                        if (StringUtils.isBlank(value)) {
                            value = keyValue.getString("value");
                        }
                        if (StringUtils.isNotBlank(key) && StringUtils.isNotBlank(value)) {
                            if (reversal) {
                                result.put(value, key);
                            } else {
                                result.put(key, value);
                            }
                            log.info("增加替换内容，name：{}，key：{}，value：{}", colName, key, value);
                        }
                    }
                });
            }
            return result;
        }
    }

    private List<ClsPropertyWithRel> getClsPropertyMap(String clsOid) {
        FuzzySubDTO fuzzySubDTO = new FuzzySubDTO();
        fuzzySubDTO.setFromOid(clsOid);
        fuzzySubDTO.setFromType(Classification.TYPE);
        fuzzySubDTO.setIndex(1);
        fuzzySubDTO.setSize(500);
        fuzzySubDTO.setSearchKey("");
        PageResult<ClsPropertyWithRel> clsPropertyWithRelPageResult = clsPropertyHelper.fuzzyPageByCodeUnderCls(fuzzySubDTO);
        List<ClsPropertyWithRel> clsPropertyWithRelList = clsPropertyWithRelPageResult.getRows();
//        Assert.notEmpty(clsPropertyWithRelList,"Classification attribute does not maintain data ");
        return clsPropertyWithRelList;
    }

    private boolean checkIsJsonString(String jsonString) {
        try {
            JSON.parseObject(jsonString);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    private String getPartAttrName(int index, List<List<String>> headList){
        return headList.get(index).get(0);
    }

    private Integer getPartAttrIndex(String name, List<List<String>> headerList){
        int index = -1;
        for (int i = 0; i < headerList.size(); i++) {
            if(name.equals(headerList.get(i).get(0))) {
                index = i;
                break;
            }
        }
        return index;
    }
}
