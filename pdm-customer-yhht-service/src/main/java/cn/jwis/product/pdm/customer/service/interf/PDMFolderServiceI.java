package cn.jwis.product.pdm.customer.service.interf;

import cn.jwis.framework.base.domain.able.ModelAble;
import cn.jwis.framework.base.exception.JWIException;
import cn.jwis.platform.plm.container.entity.FolderTreeNode;
import cn.jwis.platform.plm.container.entity.Team;
import cn.jwis.platform.plm.foundation.common.response.InstanceEntity;
import cn.jwis.product.pdm.customer.entity.FuzzySubPageWithModelDTO;
import cn.jwis.product.pdm.customer.service.dto.FolderUpdateDTO;
import cn.jwis.product.pdm.customer.service.dto.PdmFolderCreateDTO;

import java.util.List;

public interface PDMFolderServiceI {

    ModelAble update(FolderUpdateDTO dto);

    ModelAble create(PdmFolderCreateDTO dto);

    Team getTeamRole(String folderOid, String searchKey);

    List<FolderTreeNode> searchFoldersWithPermisson(String containerModel, String containerOid, String searchKey) throws JWIException;

    Team getTeam(String folderOid);

    Team getInstanceTeam(InstanceEntity instance);

    Object fuzzySubPageWithModel(FuzzySubPageWithModelDTO dto);
}
