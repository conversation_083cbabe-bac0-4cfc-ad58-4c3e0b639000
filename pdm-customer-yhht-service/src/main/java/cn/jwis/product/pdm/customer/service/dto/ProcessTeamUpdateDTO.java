package cn.jwis.product.pdm.customer.service.dto;

import cn.jwis.platform.plm.workflow.engine.dto.TeamRoleUserDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2023/10/19 17:18
 * @Email <EMAIL>
 */
@Data
@EqualsAndHashCode
public class ProcessTeamUpdateDTO {
    @NotBlank
    private String processInstanceId;

    @ApiModelProperty("流程团队内容")
    private List<TeamRoleUserDTO> teamContent;
}
