package cn.jwis.product.pdm.customer.service.interf;

import cn.jwis.platform.plm.container.entity.BindUser;
import cn.jwis.platform.plm.container.entity.Folder;
import cn.jwis.platform.plm.container.entity.Team;
import cn.jwis.platform.plm.container.entity.response.TeamRoleWithUser;

import java.util.List;

public interface CustomerTeamHelper {

    boolean buildTeamByCopyFolder(Team target, List<TeamRoleWithUser> teamRoles, String folderOid, boolean isCopy);

    void bindContainerTeam(Folder folder, BindUser bindUser);
}
