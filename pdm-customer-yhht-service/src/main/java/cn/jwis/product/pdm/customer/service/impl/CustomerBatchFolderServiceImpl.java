package cn.jwis.product.pdm.customer.service.impl;

import cn.jwis.framework.base.util.Assert;
import cn.jwis.framework.base.util.CollectionUtil;
import cn.jwis.framework.base.util.ExcelUtils;
import cn.jwis.framework.base.util.OidGenerator;
import cn.jwis.framework.base.web.session.SessionHelper;
import cn.jwis.framework.database.core.entity.FromToFilter;
import cn.jwis.framework.database.core.entity.SubFilter;
import cn.jwis.framework.database.core.query.dynamic.Condition;
import cn.jwis.platform.iam.user.User;
import cn.jwis.platform.plm.account.entity.role.Role;
import cn.jwis.platform.plm.container.entity.Container;
import cn.jwis.platform.plm.container.entity.Folder;
import cn.jwis.platform.plm.container.entity.Team;
import cn.jwis.platform.plm.container.entity.TeamRole;
import cn.jwis.platform.plm.dataengine.common.JWICommonService;
import cn.jwis.platform.plm.foundation.common.service.CommonAbilityHelper;
import cn.jwis.platform.plm.foundation.relationship.Contain;
import cn.jwis.platform.plm.permission.administrativedomain.entity.AdministrativeDomain;
import cn.jwis.platform.plm.sysconfig.preferences.service.IPreferencesService;
import cn.jwis.product.pdm.customer.dos.BatchFolderDto;
import cn.jwis.product.pdm.customer.dos.BatchFolderHistoryDto;
import cn.jwis.product.pdm.customer.dos.FolderDto;
import cn.jwis.product.pdm.customer.repo.BatchFolderRepo;
import cn.jwis.product.pdm.customer.service.interf.BatchFolderService;
import cn.jwis.product.pdm.customer.service.websocket.BatchFolderWebSocket;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.websocket.Session;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;


/**
 * <AUTHOR> zhaikun
 * @Date ： 2024/2/2
 * @Description :
 */

@Service
@Slf4j
public class CustomerBatchFolderServiceImpl implements BatchFolderService {


    private static final String HORIZONTAL_SPLIT = "|j:w|";
    private static final String OBLIQUE_SPLIT = "/";

    private static final String DH_SPLIT = "、";

    private static final String COMMA_SPLIT = ",";

    private static final String POINT_SPLIT = "\\.";

    private static final String LEVEL1 = "level1";

    private static final String LEVEL2 = "level2";

    private static final String LEVEL3 = "level3";

    private static final String LEVEL4 = "level4";

    private static final String MEMBERS = "members";


    //团队成员
    private String downloadRoleKey;
    private Role downloadRole;
    //访客
    private String readRoleKey;
    private Role readRole;

    //产保
    private String cbRoleKey;
    private Role cbRole;

    //标准化
    private String bzhRoleKey;
    private Role bzhRole;

    //校对
    private String proofreaderRoleKey;
    private Role proofreaderRole;
    // 审核
    private String shzRoleKey;
    private Role shzRole;
    //会签
    private String signerRoleKey;
    private Role signerRole;
    //批准
    private String ratifierRoleKey;
    private Role ratifierRole;


    //前置抄送人
    private String preCCRoleKey;
    private Role preCCRole;

    //后置抄送人
    private String postCCRoleKey;
    private Role postCCRole;

    private Session session;

    private Map<String, Integer> level1CountMap;

    @Autowired
    JWICommonService commonService;

    @Autowired
    private CommonAbilityHelper commonAbilityHelper;

    @Autowired
    private IPreferencesService config;

    @Autowired
    private BatchFolderRepo batchFolderRepo;


    @Autowired
    private BatchFolderWebSocket webSocketService;


    @Override
    public List<String> batchCreateFolder(MultipartFile file, String containerOid, boolean isNew) {
        List<String> allMsgList = new ArrayList<>();
        try {
//            this.session = webSocketService.getSession(SessionHelper.getCurrentUser().getOid());

            sendWSMessage("读取数据并校验用户是否合法");

            Set<String> allAccountSet = new HashSet<>();
            List<? extends FolderDto> folderDtoList;
            if (isNew) {
                folderDtoList = ExcelUtils.read(file.getInputStream(), BatchFolderDto.class);
                buildData(folderDtoList, allMsgList, allAccountSet);
            } else {
                folderDtoList = ExcelUtils.read(file.getInputStream(), BatchFolderHistoryDto.class);
                buildHistoryData(folderDtoList, allAccountSet);
            }

            List<User> userList = queryUsers(allAccountSet);

            appendCheckUserMsg(folderDtoList, allMsgList, userList, isNew);

            if (allMsgList.size() > 0) {
                return allMsgList;
            }
            initRole();
            writeFolderLinkData(containerOid, folderDtoList, userList, isNew);
            bindContainerMembers(containerOid, userList, allMsgList);

        } catch (Exception e) {
            allMsgList.add("导入异常，" + e.getMessage());
            log.info(e.getMessage(),e);
        }
        return allMsgList;
    }

    private void sendWSMessage(String msg) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("msg", msg);
//        Assert.notNull(this.session, "实时进度反馈未连接，请刷新页面！");
//        webSocketService.sendMessage(jsonObject.toJSONString(), this.session);
    }

    private void initRole() {
        //团队成员
        this.downloadRoleKey = config.simpleQuery("batch_folder_download");
        this.downloadRole = queryRole(downloadRoleKey);
        //访客
        this.readRoleKey = config.simpleQuery("batch_folder_read");
        this.readRole = queryRole(readRoleKey);

        //产保
        this.cbRoleKey = config.simpleQuery("batch_folder_cb");
        this.cbRole = queryRole(cbRoleKey);

        //标注化
        this.bzhRoleKey = config.simpleQuery("batch_folder_bzh");
        this.bzhRole = queryRole(bzhRoleKey);

        //校对
        this.proofreaderRoleKey = config.simpleQuery("batch_folder_proofreader");
        this.proofreaderRole = queryRole(proofreaderRoleKey);
        // 审核
        this.shzRoleKey = config.simpleQuery("batch_folder_shz");
        this.shzRole = queryRole(shzRoleKey);
        //会签
        this.signerRoleKey = config.simpleQuery("batch_folder_counterSigner");
        this.signerRole = queryRole(signerRoleKey);
        //批准
        this.ratifierRoleKey = config.simpleQuery("batch_folder_ratifier");
        this.ratifierRole = queryRole(ratifierRoleKey);


        //前置抄送人
        this.preCCRoleKey = config.simpleQuery("batch_folder_pre_cc");
        this.preCCRole = queryRole(preCCRoleKey);

        //后置抄送人
        this.postCCRoleKey = config.simpleQuery("batch_folder_cc");
        this.postCCRole = queryRole(postCCRoleKey);
    }


    private Role queryRole(String roleKey) {
        return commonService.findByKey(Role.TYPE, "name", roleKey, Role.class);
    }

    private List<User> queryUsers(Set<String> allAccountSet) {
        Condition condition = Condition.where("account").in(allAccountSet);
        return commonService.dynamicQuery(User.TYPE, condition, User.class);
    }


    /**
     * 写文件夹相关数据
     *
     * @param containerOid
     * @param folderDtoList
     * @param userList
     * @param isNew
     */
    private void writeFolderLinkData(String containerOid, List<? extends FolderDto> folderDtoList, List<User> userList, boolean isNew) {


        Map<String, String> folderPathMap = buildPathMap(batchFolderRepo.queryFolderPaths(containerOid));

        log.info("folderPathMap={}", JSONObject.toJSONString(folderPathMap));
        log.info("level1CountMap={}", this.level1CountMap);

        String rootFolderOid = queryRootFolderOid(containerOid);

        String rootAdministrativeDomainOid = queryRootAdministrativeDomainOid(rootFolderOid);

        Map<String, String> domainPathMap = buildPathMap(batchFolderRepo.queryAdministrativeDomainPaths(containerOid));

        int index = 0;
        for (FolderDto tempData : folderDtoList) {
            index++;
            log.info("正在处理第{}行数据", index);

            sendWSMessage("正在处理第" + index + "行数据");


            // 新导入，全是空，不处理
            if (isNew && isAllNull((BatchFolderDto) tempData)) {
                continue;
            }

            List<String> pathList = new ArrayList<>();
            if (isNew) {
                buildPathList((BatchFolderDto) tempData, pathList);
            } else {
                String fullPath = ((BatchFolderHistoryDto) tempData).getFullPath();
                // 去掉第一层
                fullPath = fullPath.substring(fullPath.indexOf(".") + 1);
                pathList.addAll(Arrays.asList(fullPath.split(POINT_SPLIT)));
            }

            String finalPath = String.join(HORIZONTAL_SPLIT, pathList);

            if (!folderPathMap.containsKey(finalPath)) {
                createFolders(containerOid, folderPathMap, rootFolderOid, rootAdministrativeDomainOid, domainPathMap, pathList);
            }

            String folderOid = folderPathMap.get(finalPath);

            if (isNew) {
                // 追加第1,2层代号
                appendLevel12Code(folderPathMap, (BatchFolderDto) tempData);

                // 追加第1层或者第二次团队角色和用户
                String levelFolderOid = getLevel1Or2Path(folderPathMap, (BatchFolderDto) tempData);
                appendRoleAndUsers(userList, levelFolderOid, tempData);
            } else {
                setExtensionContent(((BatchFolderHistoryDto) tempData).getNodeCode(), folderOid);
                appendRoleAndUsers(userList, folderOid, tempData);
            }

            String teamOid = queryFolderTeamOid(folderOid);

            List<TeamRole> teamRoles = queryTeamRoleByTeamOid(teamOid);

            List<String> downloadAccounts = tempData.getDownloadAccounts();
            if (downloadAccounts != null) {
                String downloadTeamRoleOid = queryTeamRoleOid(downloadRoleKey, downloadRole, teamOid, teamRoles);
                addRoleUsers(userList, downloadTeamRoleOid, downloadAccounts);
            }
            List<String> readAccounts = tempData.getReadAccounts();
            if (readAccounts != null) {
                String readTeamRoleOid = queryTeamRoleOid(readRoleKey, readRole, teamOid, teamRoles);
                addRoleUsers(userList, readTeamRoleOid, readAccounts);
            }
        }

    }


    private static void buildPathList(BatchFolderDto tempData, List<String> pathList) {
        String level1 = tempData.getLevel1();
        String level2 = tempData.getLevel2();
        String level3 = tempData.getLevel3();
        String level4 = tempData.getLevel4();
        if (!level1.equals(OBLIQUE_SPLIT)) {
            pathList.add(level1);
        }
        if (!level2.equals(OBLIQUE_SPLIT)) {
            pathList.add(level2);
        }
        if (!level3.equals(OBLIQUE_SPLIT)) {
            pathList.add(level3);
        }
        if (!level4.equals(OBLIQUE_SPLIT)) {
            pathList.add(level4);
        }
    }

    private String queryRootFolderOid(String containerOid) {
        SubFilter filter = new SubFilter();
        filter.setFromType(Container.TYPE);
        filter.setFromOid(containerOid);
        filter.setType("CONTAIN");
        filter.setToType(Folder.TYPE);
        List<Folder> folders = commonService.dynamicQueryByFrom(filter, Folder.class);
        return folders.stream().findAny().orElse(new Folder()).getOid();
    }

    private String queryRootAdministrativeDomainOid(String rootFolderOid) {
        AdministrativeDomain admin = commonService.findByKey(AdministrativeDomain.TYPE, "containerOid", rootFolderOid, AdministrativeDomain.class);
        Assert.notNull(admin, "根文件夹无对应的管理域");
        return admin.getOid();
    }


    private List<TeamRole> queryTeamRoleByTeamOid(String teamOid) {
        SubFilter filter = new SubFilter();
        filter.setFromType(Team.TYPE);
        filter.setFromOid(teamOid);
        filter.setType("CONTAIN");
        filter.setToType(TeamRole.TYPE);
        return commonService.dynamicQueryByFrom(filter, TeamRole.class);
    }


    /**
     * 通过文件夹id查询关联的team oid
     *
     * @param folderOid
     * @return
     */
    private String queryFolderTeamOid(String folderOid) {
        SubFilter filter = new SubFilter();
        filter.setFromType(Folder.TYPE);
        filter.setFromOid(folderOid);
        filter.setType("ASSIGN");
        filter.setToType(Team.TYPE);
        List<Team> teams = commonService.dynamicQueryByFrom(filter, Team.class);
        if (!CollectionUtil.isEmpty(teams)) {
            return teams.get(0).getOid();
        }
        String teamOid = createFolderTeam();
        // 创建folder和team的关系
        createRelation(Folder.TYPE, folderOid, "ASSIGN", Team.TYPE, teamOid);
        return teamOid;
    }

    /**
     * 创建文件夹团队
     *
     * @return
     */
    private String createFolderTeam() {
        Team team = new Team();
        team.setModelDefinition(Team.TYPE);
        team.setType(Team.TYPE);
        String oid = OidGenerator.newOid();
        team.setOid(oid);
        team.setName("文件夹默认team");
        commonService.create(team);
        return oid;
    }


    private void createRelation(String fromType, String fromOid, String relationshipType, String toType, String toOid) {
        JSONObject relationData = new JSONObject();
        relationData.put("fromType", fromType);
        relationData.put("fromOid", fromOid);
        relationData.put("toType", toType);
        relationData.put("toOid", toOid);
        relationData.put("type", relationshipType);
        relationData.put("oid", OidGenerator.newOid());
        commonService.createRelation(fromType, fromOid, relationshipType, relationData, toType, toOid);
    }


    private void appendRoleAndUsers(List<User> userList, String folderOid, FolderDto tempData) {
        String teamOid = queryFolderTeamOid(folderOid);
        List<TeamRole> teamRoles = queryTeamRoleByTeamOid(teamOid);

        List<String> proofreaderAccounts = tempData.getProofreaderAccounts();
        String proofreaderRoleOid = queryTeamRoleOid(proofreaderRoleKey, proofreaderRole, teamOid, teamRoles);
        if (proofreaderAccounts != null) {
            addRoleUsers(userList, proofreaderRoleOid, proofreaderAccounts);
        }
        List<String> signerAccounts = tempData.getSignerAccounts();
        String signerRoleOid = queryTeamRoleOid(signerRoleKey, signerRole, teamOid, teamRoles);
        if (signerAccounts != null) {
            addRoleUsers(userList, signerRoleOid, signerAccounts);
        }


        List<String> cbAccounts = tempData.getCbAccounts();
        String cbTeamRoleOid = queryTeamRoleOid(cbRoleKey, cbRole, teamOid, teamRoles);
        if (cbAccounts != null) {
            addRoleUsers(userList, cbTeamRoleOid, cbAccounts);
        }

        List<String> bzhAccounts = tempData.getBzhAccounts();
        String bzhTeamRoleOid = queryTeamRoleOid(bzhRoleKey, bzhRole, teamOid, teamRoles);
        if (bzhAccounts != null) {
            addRoleUsers(userList, bzhTeamRoleOid, bzhAccounts);
        }


        List<String> shzAccounts = tempData.getShzAccounts();
        String shzTeamRoleOid = queryTeamRoleOid(shzRoleKey, shzRole, teamOid, teamRoles);
        if (shzAccounts != null) {
            addRoleUsers(userList, shzTeamRoleOid, shzAccounts);
        }

        List<String> ratifierAccounts = tempData.getRatifierAccounts();
        String ratifierTeamRoleOid = queryTeamRoleOid(ratifierRoleKey, ratifierRole, teamOid, teamRoles);
        if (ratifierAccounts != null) {
            addRoleUsers(userList, ratifierTeamRoleOid, ratifierAccounts);
        }

        List<String> preCCAccounts = tempData.getPreCCAccounts();
        String preCCTeamRoleOid = queryTeamRoleOid(preCCRoleKey, preCCRole, teamOid, teamRoles);
        if (preCCAccounts != null) {
            addRoleUsers(userList, preCCTeamRoleOid, preCCAccounts);
        }

        List<String> postCCAccounts = tempData.getPostCCAccounts();
        String postCCTeamRoleOid = queryTeamRoleOid(postCCRoleKey, postCCRole, teamOid, teamRoles);
        if (postCCAccounts != null) {
            addRoleUsers(userList, postCCTeamRoleOid, postCCAccounts);
        }
    }

    private String getLevel1Or2Path(Map<String, String> folderPathMap, BatchFolderDto tempData) {
        String level1 = tempData.getLevel1();
        String level2 = tempData.getLevel2();
        String pathKey = "";
        // level1只有一个或者level2是/，审核相关的人写入到第一层
        if (this.level1CountMap.get(level1) == 1 || level2.equals(OBLIQUE_SPLIT)) {
            pathKey = level1;
        } else {
            pathKey = level1 + HORIZONTAL_SPLIT + level2;
        }
        String levelFolderOid = folderPathMap.get(pathKey);

        Assert.notEmpty(levelFolderOid, "此路径" + pathKey + "没有对应的文件夹OID");
        return levelFolderOid;
    }


    private String queryTeamRoleOid(String roleKey, Role roleInfo, String teamOid, List<TeamRole> teamRoles) {
        TeamRole teamRole = teamRoles.stream().filter(item -> item.getName().equalsIgnoreCase(roleKey)).findAny().orElse(null);
        if (!ObjectUtils.isEmpty(teamRole)) {
            return teamRole.getOid();
        }
        String teamRoleOid = createTeamRole(roleInfo);
        // 创建team和teamRole的关系
        createRelation(Team.TYPE, teamOid, "CONTAIN", TeamRole.TYPE, teamRoleOid);
        return teamRoleOid;
    }

    private String createTeamRole(Role roleInfo) {
        TeamRole teamRole = new TeamRole();
        teamRole.setModelDefinition(TeamRole.TYPE);
        teamRole.setType(TeamRole.TYPE);
        String oid = OidGenerator.newOid();
        teamRole.setOid(oid);
        teamRole.setName(roleInfo.getName());
        teamRole.setDisplayName(roleInfo.getDisplayName());
        teamRole.setSourceOid(roleInfo.getOid());
        commonService.create(teamRole);
        return oid;
    }

    private void addRoleUsers(List<User> userList, String roleOid, List<String> userAccounts) {
        FromToFilter from = new FromToFilter();
        from.setFromType(TeamRole.TYPE);
        from.setFromFilter(Condition.where("oid").eq(roleOid));
        from.setType("CONTAIN");
        from.setToType(User.TYPE);
        // 删除TeamRole和用户的关系
        commonService.deleteRelation(from);
        // 倒序添加，保持和excel的顺序一致
        Collections.reverse(userAccounts);
        List<String> oidList = new ArrayList<>();
        List<Contain> containList = new ArrayList<>();
        userAccounts.forEach(account -> {
            User userInfo = userList.stream().filter(item -> item.getAccount().equals(account)).findAny().orElse(null);
            if (userInfo != null) {
                String userOid = userInfo.getOid();
                if (!oidList.contains(userOid)) {
                    // 建立TeamRole和人员的关系
                    Contain contain = new Contain(TeamRole.TYPE, roleOid, User.TYPE, userOid);
                    containList.add(contain);
                    oidList.add(userOid);
                }

            }
        });
        commonService.createRelation(containList);
    }


    private void appendLevel12Code(Map<String, String> folderPathMap, BatchFolderDto tempData) {
        String level1 = tempData.getLevel1();
        String level2 = tempData.getLevel2();
        String level1Code = tempData.getLevel1Code();
        if (!Objects.isNull(level1Code) && !"".equals(level1Code)) {
            String l1FolderOid = folderPathMap.get(level1);
            setExtensionContent(level1Code, l1FolderOid);
        }
        // 不是/杠
        if (!level2.equals(OBLIQUE_SPLIT)) {
            String level2Code = tempData.getLevel2Code();
            if (!Objects.isNull(level2Code) && !"".equals(level2Code)) {
                String l2FolderOid = folderPathMap.get(level1 + HORIZONTAL_SPLIT + level2);
                setExtensionContent(level2Code, l2FolderOid);
            }

        }

    }

    private void setExtensionContent(String levelCode, String folderOid) {
        if (!ObjectUtils.isEmpty(levelCode)) {
            Folder folder = (Folder) commonAbilityHelper.findDetailEntity(folderOid, Folder.TYPE);
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("code", levelCode);
            folder.setExtensionContent(jsonObject);
            commonAbilityHelper.doUpdate(folder);
        }
    }


    private Map<String, String> buildPathMap(List<JSONObject> jsonObjects) {
        Map<String, String> result = new HashMap<>();
        Map<String, JSONObject> oidJsonMap = jsonObjects.stream().collect(Collectors.toMap(k -> k.getString("oid"), v -> v));
        for (JSONObject jsonObject : jsonObjects) {
            String oid = jsonObject.getString("oid");
            List<String> pathList = new ArrayList<>();
            String fromOid = jsonObject.getString("fromOid");
            getPath(fromOid, oidJsonMap, pathList);
            Collections.reverse(pathList);
            // 添加自己的名称
            pathList.add(jsonObject.getString("name"));
            result.put(String.join(HORIZONTAL_SPLIT, pathList), oid);
        }
        return result;
    }


    private void getPath(String fromOid, Map<String, JSONObject> oidJsonMap, List<String> pathList) {
        if (oidJsonMap.containsKey(fromOid)) {
            JSONObject from = oidJsonMap.get(fromOid);
            pathList.add(from.getString("name"));
            getPath(from.getString("fromOid"), oidJsonMap, pathList);
        }
    }

    /**
     * 创建文件夹
     *
     * @param containerOid
     * @param folderPathMap
     * @param rootFolderOid
     * @param rootAdministrativeDomainOid
     * @param domainPathMap
     * @param pathList
     */
    private void createFolders(String containerOid, Map<String, String> folderPathMap, String rootFolderOid, String rootAdministrativeDomainOid, Map<String, String> domainPathMap, List<String> pathList) {
        for (int pathIndex = 0; pathIndex < pathList.size(); pathIndex++) {
            String selfName = pathList.get(pathIndex);

            List<String> selfPathList = new ArrayList<>();
            for (int temp = 0; temp <= pathIndex; temp++) {
                selfPathList.add(pathList.get(temp));

            }
            String selfPath = String.join(HORIZONTAL_SPLIT, selfPathList);

            String parentPath = "";
            if (selfPath.contains(HORIZONTAL_SPLIT)) {
                parentPath = selfPath.substring(0, selfPath.lastIndexOf(HORIZONTAL_SPLIT));
            }
            // 自己的路径不存在,创建自己，并和父项关联
            String folderOid = folderPathMap.get(selfPath);
            if (!folderPathMap.containsKey(selfPath)) {
                String parentFolderOid = rootFolderOid;

                if (folderPathMap.containsKey(parentPath)) {
                    parentFolderOid = folderPathMap.get(parentPath);
                }
                folderOid = createFolder(containerOid, parentFolderOid, selfName);
                createRelation(Folder.TYPE, parentFolderOid, "CONTAIN", Folder.TYPE, folderOid);
                folderPathMap.put(selfPath, folderOid);
            }

            // 管理域自己的路径不存在,创建自己，并和父项关联
            if (!domainPathMap.containsKey(selfPath)) {
                String parentDomainOid = rootAdministrativeDomainOid;

                if (domainPathMap.containsKey(parentPath)) {
                    parentDomainOid = domainPathMap.get(parentPath);
                }
                String domainOid = createDomain(folderOid, selfName);
                createRelation(AdministrativeDomain.TYPE, parentDomainOid, "INHERIT", AdministrativeDomain.TYPE, domainOid);
                domainPathMap.put(selfPath, domainOid);

                //创建文件夹和管理域的关系
                createRelation(Folder.TYPE, folderOid, "POINT", AdministrativeDomain.TYPE, domainOid);
            }
        }
    }

    private String createDomain(String folderOid, String selfName) {
        AdministrativeDomain administrativeDomain = new AdministrativeDomain();
        administrativeDomain.setModelDefinition(AdministrativeDomain.TYPE);
        administrativeDomain.setType(AdministrativeDomain.TYPE);
        String oid = OidGenerator.newOid();
        administrativeDomain.setOid(oid);
        administrativeDomain.setName(selfName);
        administrativeDomain.setContainerModelType(Folder.TYPE);
        administrativeDomain.setContainerOid(folderOid);
        commonService.create(administrativeDomain);
        return oid;
    }

    /**
     * 创建文件夹
     *
     * @param containerOid
     * @param parentFolderOid
     * @param selfName
     * @return
     */
    private String createFolder(String containerOid, String parentFolderOid, String selfName) {
        Folder folder = new Folder();
        folder.setModelDefinition(Folder.TYPE);
        folder.setType(Folder.TYPE);
        String oid = OidGenerator.newOid();
        folder.setOid(oid);
        folder.setName(selfName);
        folder.setCatalogOid(parentFolderOid);
        folder.setCatalogType(Folder.TYPE);
        folder.setContainerOid(containerOid);
        folder.setContainerType(Container.TYPE);
        commonService.create(folder);
        return oid;
    }


    private void bindContainerMembers(String containerOid, List<User> userList, List<String> allMsgList) {
        TeamRole containerMemberRole = batchFolderRepo.queryContainerMemberRole(containerOid);
        if (ObjectUtils.isEmpty(containerMemberRole)) {
            allMsgList.add("请维护容器的团队，添加成员角色");
            return;
        }
        List<User> containerMembers = batchFolderRepo.queryContainerMembers(containerOid);
        userList.forEach(item -> {
            String userOid = item.getOid();
            List<User> existUser = containerMembers.stream().filter(member -> member.getOid().equals(userOid)).collect(Collectors.toList());
            if (CollectionUtil.isEmpty(existUser)) {
                createRelation(TeamRole.TYPE, containerMemberRole.getOid(), "CONTAIN", User.TYPE, userOid);
            }
        });
    }


    /**
     * 检查用户
     *
     * @param folderDtoList
     * @param allMsgList
     * @param userList
     * @param isNew
     */
    private void appendCheckUserMsg(List<? extends FolderDto> folderDtoList, List<String> allMsgList, List<User> userList, boolean isNew) {
        for (int i = 0; i < folderDtoList.size(); i++) {
            FolderDto tempData = folderDtoList.get(i);

            List<String> cbAccounts = tempData.getCbAccounts();
            appendCheckUserMsg(allMsgList, userList, i, cbAccounts, "产保");

            if (isNew) {
                List<String> downloadAccounts = tempData.getDownloadAccounts();
                appendCheckUserMsg(allMsgList, userList, i, downloadAccounts, "可下载");

                List<String> readAccounts = tempData.getReadAccounts();
                appendCheckUserMsg(allMsgList, userList, i, readAccounts, "可查看");
            }

            List<String> shzAccounts = tempData.getShzAccounts();
            appendCheckUserMsg(allMsgList, userList, i, shzAccounts, "审核");

            List<String> bzhAccounts = tempData.getBzhAccounts();
            appendCheckUserMsg(allMsgList, userList, i, bzhAccounts, "标审");

            List<String> ratifierAccounts = tempData.getRatifierAccounts();
            appendCheckUserMsg(allMsgList, userList, i, ratifierAccounts, "批准");

            List<String> preCCAccounts = tempData.getPreCCAccounts();
            appendCheckUserMsg(allMsgList, userList, i, preCCAccounts, "前置抄送");

            List<String> postCCAccounts = tempData.getPostCCAccounts();
            appendCheckUserMsg(allMsgList, userList, i, postCCAccounts, (isNew ? "" : "后置") + "抄送");

            if (!isNew) {
                List<String> downloadAccounts = tempData.getDownloadAccounts();
                appendCheckUserMsg(allMsgList, userList, i, downloadAccounts, "下载权限");

                List<String> readAccounts = tempData.getReadAccounts();
                appendCheckUserMsg(allMsgList, userList, i, readAccounts, "查看");
            }

        }
    }

    private void appendCheckUserMsg(List<String> allMsgList, List<User> userList, int i, List<String> userAccounts, String roleLabel) {
        if (!CollectionUtil.isEmpty(userAccounts)) {
            List<String> msgList = new ArrayList<>();
            userAccounts.forEach(account -> {
                User userInfo = userList.stream().filter(item -> item.getAccount().equals(account)).findAny().orElse(null);
                if (userInfo == null) {
                    msgList.add(account);
                }
            });
            if (msgList.size() > 0) {
                allMsgList.add("第" + (i + 2) + "行:" + roleLabel + "清单中如下人员在系统中不存在" + msgList);
            }
        }
    }

    /**
     * 补充空行和用户数据
     *
     * @param folderDtoList
     * @param allMsgList
     * @param allAccounts
     */
    private void buildData(List<? extends FolderDto> folderDtoList, List<String> allMsgList, Set<String> allAccounts) {
        this.level1CountMap = new HashMap<>();
        Map<String, String> levelMap = new HashMap<>();
        for (int i = 0; i < folderDtoList.size(); i++) {
            BatchFolderDto tempData = (BatchFolderDto) folderDtoList.get(i);
            String level1 = tempData.getLevel1();

            if (!ObjectUtils.isEmpty(level1)) {
                levelMap.put(LEVEL1, level1);
                Integer integer = this.level1CountMap.get(level1);
                if (ObjectUtils.isEmpty(integer)) {
                    this.level1CountMap.put(level1, 1);
                } else {
                    this.level1CountMap.put(level1, integer + 1);
                }
            }
            String level2 = tempData.getLevel2();
            if (!ObjectUtils.isEmpty(level2)) {
                levelMap.put(LEVEL2, level2);
            }
            String level3 = tempData.getLevel3();
            if (!ObjectUtils.isEmpty(level3)) {
                levelMap.put(LEVEL3, level3);
            }
            String level4 = tempData.getLevel4();
            if (!ObjectUtils.isEmpty(level4)) {
                levelMap.put(LEVEL4, level4);
            }
            String members = tempData.getMembers();
            if (!ObjectUtils.isEmpty(members)) {
                levelMap.put(MEMBERS, members);
            }
            // 全是空，不处理
            if (isAllNull(tempData)) {
                continue;
            }
            if (ObjectUtils.isEmpty(level1)) {
//                    String parent = getParent(folderDtoList, String.valueOf(1), i);
//                    tempData.setLevel1(parent);
                tempData.setLevel1(levelMap.get(LEVEL1));
            }
            if (ObjectUtils.isEmpty(level2)) {
//                    String parent = getParent(folderDtoList, String.valueOf(2), i);
//                    tempData.setLevel2(parent);
                tempData.setLevel2(levelMap.get(LEVEL2));
            }
            if (ObjectUtils.isEmpty(level3)) {
//                    String parent = getParent(folderDtoList, String.valueOf(3), i);
//                    tempData.setLevel3(parent);
                tempData.setLevel3(levelMap.get(LEVEL3));
            }
            if (ObjectUtils.isEmpty(level4)) {
//                    String parent = getParent(folderDtoList, String.valueOf(4), i);
//                    tempData.setLevel4(parent);
                tempData.setLevel4(levelMap.get(LEVEL4));
            }
            if (ObjectUtils.isEmpty(members)) {
//                    String parent = getParent(folderDtoList, "members", i);
//                    tempData.setMembers(parent);
                tempData.setMembers(levelMap.get(MEMBERS));
            }

            String[] list = tempData.getMembers().split("可查看");
            if (list.length == 0) {
                allMsgList.add("第" + (i + 2) + "行:" + " 无人员清单");
                continue;
            }
            String[] download = list[0].split(DH_SPLIT);
            List<String> userAccounts = buildUsers(allMsgList, i, download);
            if (userAccounts.size() > 0) {
                tempData.setDownloadAccounts(userAccounts);
                allAccounts.addAll(userAccounts);
            }
            if (list.length == 2) {
                String[] read = list[1].split(DH_SPLIT);
                userAccounts = buildUsers(allMsgList, i, read);
                if (userAccounts.size() > 0) {
                    tempData.setReadAccounts(userAccounts);
                    allAccounts.addAll(userAccounts);
                }
            }
            String[] userStr;
            //审核
            String shz = tempData.getShz();
            if (StringUtils.hasLength(shz)) {
                userStr = shz.split(DH_SPLIT);
                userAccounts = buildUsers(allMsgList, i, userStr);
                tempData.setShzAccounts(userAccounts);
                allAccounts.addAll(userAccounts);
            }

            //批准
            String ratifier = tempData.getRatifier();
            if (StringUtils.hasLength(ratifier)) {
                userStr = ratifier.split(DH_SPLIT);
                userAccounts = buildUsers(allMsgList, i, userStr);
                tempData.setRatifierAccounts(userAccounts);
                allAccounts.addAll(userAccounts);
            }

            //抄送
            String postCC = tempData.getPostCC();
            if (StringUtils.hasLength(postCC)) {
                userStr = postCC.split(DH_SPLIT);
                userAccounts = buildUsers(allMsgList, i, userStr);
                tempData.setPostCCAccounts(userAccounts);
                allAccounts.addAll(userAccounts);

            }
        }
    }


    private void buildHistoryData(List<? extends FolderDto> folderDtoList, Set<String> allAccounts) {
        for (FolderDto folderDto : folderDtoList) {
            BatchFolderHistoryDto tempData = (BatchFolderHistoryDto) folderDto;
            List<String> userAccounts;
            //产保
            String cb = tempData.getCb();
            if (StringUtils.hasLength(cb)) {
                userAccounts = Arrays.asList(cb.split(COMMA_SPLIT));
                tempData.setCbAccounts(userAccounts);
                allAccounts.addAll(userAccounts);
            }

            //校对
            String proofreader = tempData.getProofreader();
            if (StringUtils.hasLength(proofreader)) {
                userAccounts = Arrays.asList(proofreader.split(COMMA_SPLIT));
                tempData.setProofreaderAccounts(userAccounts);
                allAccounts.addAll(userAccounts);
            }

            //审核
            String shz = tempData.getShz();
            if (StringUtils.hasLength(shz)) {
                userAccounts = Arrays.asList(shz.split(COMMA_SPLIT));
                tempData.setShzAccounts(userAccounts);
                allAccounts.addAll(userAccounts);
            }


            //会签
            String counterSigner = tempData.getCounterSigner();
            if (StringUtils.hasLength(counterSigner)) {
                userAccounts = Arrays.asList(counterSigner.split(COMMA_SPLIT));
                tempData.setSignerAccounts(userAccounts);
                allAccounts.addAll(userAccounts);
            }

            //标审
            String bzh = tempData.getBzh();
            if (StringUtils.hasLength(bzh)) {
                userAccounts = Arrays.asList(bzh.split(COMMA_SPLIT));
                tempData.setBzhAccounts(userAccounts);
                allAccounts.addAll(userAccounts);
            }


            //批准
            String ratifier = tempData.getRatifier();
            if (StringUtils.hasLength(ratifier)) {
                userAccounts = Arrays.asList(ratifier.split(COMMA_SPLIT));
                tempData.setRatifierAccounts(userAccounts);
                allAccounts.addAll(userAccounts);
            }

            //前置抄送人
            String preCC = tempData.getPreCC();
            if (StringUtils.hasLength(preCC)) {
                userAccounts = Arrays.asList(preCC.split(COMMA_SPLIT));
                tempData.setPreCCAccounts(userAccounts);
                allAccounts.addAll(userAccounts);

            }

            //后置抄送人
            String postCC = tempData.getPostCC();
            if (StringUtils.hasLength(postCC)) {
                userAccounts = Arrays.asList(postCC.split(COMMA_SPLIT));
                tempData.setPostCCAccounts(userAccounts);
                allAccounts.addAll(userAccounts);

            }

            //下载权限
            String members = tempData.getMembers();
            if (StringUtils.hasLength(members)) {
                userAccounts = Arrays.asList(members.split(COMMA_SPLIT));
                tempData.setDownloadAccounts(userAccounts);
                allAccounts.addAll(userAccounts);

            }

            //查看
            String reads = tempData.getReads();
            if (StringUtils.hasLength(reads)) {
                userAccounts = Arrays.asList(reads.split(COMMA_SPLIT));
                tempData.setReadAccounts(userAccounts);
                allAccounts.addAll(userAccounts);

            }
        }
    }

    private String getParent(List<BatchFolderDto> folderDtoList, String key, int index) {
        int count = 1;
        while (true) {
            count++;
            BatchFolderDto tempData = folderDtoList.get(index--);
            if (tempData != null) {
                switch (key) {
                    case "1":
                        if (!Objects.isNull(tempData.getLevel1())) {
                            return tempData.getLevel1();
                        }
                        break;
                    case "2":
                        if (!Objects.isNull(tempData.getLevel2())) {
                            return tempData.getLevel2();
                        }
                        break;
                    case "3":
                        if (!Objects.isNull(tempData.getLevel3())) {
                            return tempData.getLevel3();
                        }
                        break;
                    case "4":
                        if (!Objects.isNull(tempData.getLevel4())) {
                            return tempData.getLevel4();
                        }
                        break;
                    case "members":
                        if (!Objects.isNull(tempData.getMembers())) {
                            return tempData.getMembers();
                        }
                        break;
                }
            }
            if (count > 100) {
                return null;
            }
        }
    }

    private boolean isAllNull(BatchFolderDto tempData) {
        String level1 = tempData.getLevel1();
        String level2 = tempData.getLevel2();
        String level3 = tempData.getLevel3();
        String level4 = tempData.getLevel4();
        return Objects.isNull(level1) && Objects.isNull(level2) && Objects.isNull(level3) && Objects.isNull(level4) && Objects.isNull(tempData.getMembers());
    }


    private List<String> buildUsers(List<String> allMsgList, int i, String[] userStr) {
        List<String> accountList = new ArrayList<>();
        for (String user : userStr) {
            if (user.contains("（") && user.contains("）")) {
                accountList.add(user.substring(user.indexOf("（") + 1, user.indexOf("）")));
            } else if (user.contains("(") && user.contains(")")) {
                accountList.add(user.substring(user.indexOf("(") + 1, user.indexOf(")")));
            } else {
                allMsgList.add("第" + (i + 2) + "行:" + user + " 人员格式不正确");
            }
        }
        return accountList;
    }
}
