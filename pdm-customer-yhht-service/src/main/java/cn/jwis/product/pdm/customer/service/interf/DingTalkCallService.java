//package cn.jwis.product.pdm.customer.service.interf;
//
//import cn.jwis.product.pdm.customer.service.dto.DingTalkTaskCreateDTO;
//import com.alibaba.fastjson.JSONObject;
//import com.aliyun.dingtalkworkflow_1_0.models.StartProcessInstanceRequest;
//import com.aliyun.teaopenapi.models.Config;
//
//public interface DingTalkCallService {
//
//    StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValues builFileForm(String accessToken, com.aliyun.dingtalkworkflow_1_0.Client client, Config config, DingTalkTaskCreateDTO dto);
//
//
//}
