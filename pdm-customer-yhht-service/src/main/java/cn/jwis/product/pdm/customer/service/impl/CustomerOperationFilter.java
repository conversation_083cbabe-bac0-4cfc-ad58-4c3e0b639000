package cn.jwis.product.pdm.customer.service.impl;

import cn.jwis.platform.plm.container.entity.Container;
import cn.jwis.platform.plm.container.entity.Folder;
import cn.jwis.platform.plm.foundation.common.service.CommonAbilityHelper;
import cn.jwis.platform.plm.permission.filter.service.factory.noInstance.OperationFilter;
import cn.jwis.platform.plm.permission.permission.dto.FilterParameterDTO;
import cn.jwis.platform.plm.permission.permission.entity.BaseCatalogue;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> zhaikun
 * @Date ： 2024/2/24
 * @Description :
 */

@Component
public class CustomerOperationFilter extends OperationFilter {

    @Autowired
    private CommonAbilityHelper commonAbilityHelper;

    @Override
    public Boolean doFilter(BaseCatalogue baseCatalogue, FilterParameterDTO dto) {
        Boolean flag = super.doFilter(baseCatalogue, dto);
        // 产品库或者电子元器件是公开库，不考虑权限，文件夹下都可以创建对象
        if ("Folder".equals(dto.getObjectType())) {
            Folder folder = (Folder) commonAbilityHelper.findDetailEntity(dto.getContextOid(), Folder.TYPE);
            Container container = (Container) commonAbilityHelper.findDetailEntity(folder.getContainerOid(), Container.TYPE);
            if (!container.isPrivateFlag()) {
                flag = true;
            }
        }
        return flag;
    }
}
