package cn.jwis.product.pdm.customer.service.impl;

import cn.jwis.core.base.database.redis.utils.MultiTenantRedisUtil;
import cn.jwis.framework.base.dto.UserDTO;
import cn.jwis.framework.base.web.session.SessionHelper;
import cn.jwis.platform.plm.account.entity.role.Role;
import cn.jwis.platform.plm.container.entity.TeamRole;
import cn.jwis.platform.plm.permission.administrativedomain.AdministrativeDomainService;
import cn.jwis.platform.plm.permission.administrativedomain.dto.AdministrativeContainerDTO;
import cn.jwis.platform.plm.permission.administrativedomain.entity.AdministrativeDomain;
import cn.jwis.platform.plm.permission.constants.Constants;
import cn.jwis.platform.plm.permission.dto.RoleDTO;
import cn.jwis.platform.plm.permission.permission.IPermissionService;
import cn.jwis.platform.plm.permission.permission.entity.PolicyACL;
import cn.jwis.platform.plm.permission.permission.entity.PolicyItem;
import cn.jwis.platform.plm.permission.permission.repo.IPermissionPolicyRepo;
import cn.jwis.platform.plm.permission.service.IPermissionPolicyHelper;
import cn.jwis.platform.plm.permission.template.service.PermissionPolicyServiceImpl;
import cn.jwis.platform.plm.permission.util.FilterPolicyUtil;
import cn.jwis.platform.plm.permissions.util.RedisUtil;
import cn.jwis.product.pdm.customer.repo.CustomerCommonRepo;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2023/10/31 14:32
 * @Email <EMAIL>
 */
@Service
@Primary
@Transactional
@Slf4j
public class CustomerPermissionPolicyService extends PermissionPolicyServiceImpl {

    @Resource
    private IPermissionPolicyHelper permissionPolicyDomainService;

    @Autowired
    private AdministrativeDomainService administrativeDomainService;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private IPermissionService permissionService;

    @Resource
    private IPermissionPolicyRepo permissionPolicyRepo;

    @Autowired
    CustomerCommonRepo customerCommonRepo;

    @Override
    public void initPermission(UserDTO currentUser, String accessToken) {

        SessionHelper.addCurrentUser(currentUser);
        SessionHelper.setAccessToken(accessToken);
        String tenantOid = currentUser.getTenantOid();
        try {
            long t1 = System.currentTimeMillis();
            MultiTenantRedisUtil.changeOtherToDefaultDB();
            //获取当前人员，所有容器下团队角色
            List<AdministrativeContainerDTO> containerDTOS = new ArrayList<>();
            Map<String, List<TeamRole>> teamRoleMap = permissionPolicyDomainService.queryContainerRole(tenantOid, currentUser.getOid());
            String userKey = Constants.PERMISSION_CONTAINER + ":" + currentUser.getAccount();
            List<TeamRole> teamRoleList = getTeamRole("member");
            List<String> roleOidList = new ArrayList<>(teamRoleList.stream().map(TeamRole::getSourceOid).collect(Collectors.toList()));

            List<AdministrativeDomain> domainList = new ArrayList<>();
            AdministrativeDomain masterDomain = administrativeDomainService.searchByName("master");
            domainList.add(masterDomain);
            AdministrativeDomain tenantDomain = administrativeDomainService.searchByTenantOid(tenantOid);
            domainList.add(tenantDomain);
            if (CollectionUtils.isEmpty(teamRoleMap)) {
                log.info("人员：{}，没有任何权限!", currentUser.getAccount());
                AdministrativeContainerDTO administrativeContainerDTO;
                for (AdministrativeDomain domain : domainList) {
                    administrativeContainerDTO = new AdministrativeContainerDTO();
                    administrativeContainerDTO.setDomainOid(domain.getOid());
                    administrativeContainerDTO.setRoles(teamRoleList);
                    containerDTOS.add(administrativeContainerDTO);
                }
                redisUtil.set(userKey, JSONObject.toJSONString(containerDTOS, SerializerFeature.DisableCircularReferenceDetect), -1);
                return;
            }
            containerDTOS = getAdministrativeContainerDTOS(roleOidList, teamRoleMap, teamRoleList);
            List<String> roleContainerList = new ArrayList<>(teamRoleMap.keySet());

            roleOidList = roleOidList.stream().distinct().collect(Collectors.toList());

            if (!CollectionUtils.isEmpty(roleOidList)) {
                //当前组织，站点，容器下的管理域集合
                List<AdministrativeDomain> adList = administrativeDomainService.queryTenantContainer(tenantOid);
                List<AdministrativeDomain> containerDomainList = new ArrayList<>(adList);
                List<AdministrativeDomain> catalogContainerList = administrativeDomainService.queryProductCatalogContainer(tenantOid);
                List<AdministrativeDomain> folderDomainList =  customerCommonRepo.queryFolderDomain(tenantOid);
                containerDomainList.addAll(catalogContainerList);
                domainList.addAll(adList);
                domainList.addAll(catalogContainerList);
                domainList.addAll(folderDomainList);
                //其他容器，没有授予角色，统一是成员角色
                getDomainRoles(teamRoleList, containerDTOS, roleContainerList, containerDomainList);
                redisUtil.set(userKey, JSONObject.toJSONString(containerDTOS, SerializerFeature.DisableCircularReferenceDetect), -1);
                //添加所有者角色查权限策略
                List<TeamRole> ownerTeamRoleList = getTeamRole("owner");
                if (!CollectionUtils.isEmpty(ownerTeamRoleList)) {
                    TeamRole teamRole = ownerTeamRoleList.get(0);
                    roleOidList.add(teamRole.getSourceOid());
                }

                log.info("domain size==>{}", domainList.size());
                String roleOids = FilterPolicyUtil.getRoleOids(roleOidList);
                log.info("role oid ==>{}", roleOids);
                List<String> domainOidList = domainList.stream().map(AdministrativeDomain::getOid).distinct().collect(Collectors.toList());
                List<String> domainNameList = domainList.stream().map(AdministrativeDomain::getName).distinct().collect(Collectors.toList());
//                log.info("domain oid==>{}", JSONObject.toJSONString(domainOidList));
//                log.info("domain name==>{}", JSONObject.toJSONString(domainNameList));
                List<PolicyACL> policyACLList = permissionService.queryPolicyAclByRolesAndDomains(roleOids, domainOidList);
                log.info("PolicyACL size==>{}", policyACLList.size());
                if (!CollectionUtils.isEmpty(policyACLList)) {
                    Map<String, List<PolicyACL>> policyAclMap = getAclMap(policyACLList);
                    String roleOid;
                    List<PolicyItem> itemList;
                    List<String> permissionCodeList;
                    String key;
                    //log.info("initPermission runTimes  data==>{}", JSONObject.toJSONString(policyAclMap));
                    List<PolicyACL> policyACLS;

                    String modelType;
                    for (String type : policyAclMap.keySet()) {
                        modelType = type.split(";")[0];
                        roleOid = type.split(";")[1];
                        policyACLS = policyAclMap.get(type);

                        for (PolicyACL acl : policyACLS) {
                            itemList = JSONObject.parseArray(acl.getEntrySet(), PolicyItem.class);
                            permissionCodeList = itemList.stream().map(PolicyItem::getLogicKey).distinct().collect(Collectors.toList());
                            if (StringUtils.isEmpty(acl.getState())) {
                                key = Constants.PERMISSION + ":" + modelType + ":" + "true" + ":" + acl.getAdministrativeDomainOid() + ":" + roleOid;
                                setRedisData(false, permissionCodeList, key);
                            } else {
                                key = Constants.PERMISSION + ":" + modelType + ":" + acl.getState() + ":" + acl.getAdministrativeDomainOid() + ":" + roleOid;
                                setRedisData(false, permissionCodeList, key);
                            }
                        }
                    }
                }
            }
            long t2 = System.currentTimeMillis();
            log.info("initPermission runTimes ==>{} ms", (t2 - t1));


        } catch (Exception e) {
            log.error("initPermission error==>", e);
        }
    }

    private List<TeamRole> getTeamRole(String name) {
        Role memberRole = permissionPolicyRepo.queryMemberRole(name);
        List<TeamRole> teamRoleList = new ArrayList<>();
        if (memberRole != null) {
            TeamRole teamRole = new TeamRole();
            teamRole.setSourceOid(memberRole.getOid());
            teamRole.setName(memberRole.getName());
            teamRoleList.add(teamRole);
        }
        return teamRoleList;
    }

    private void setRedisData(Boolean isUpdate, List<String> permissionCodeList, String key) {
        List<String> itemKeyList;
        String redisValue = (String) redisUtil.get(key);
        if (isUpdate) {
            redisUtil.set(key, JSONObject.toJSONString(permissionCodeList), -1);
        } else {
            if (StringUtils.isNotEmpty(redisValue)) {
                itemKeyList = JSONObject.parseArray(redisValue, String.class);
                itemKeyList.addAll(permissionCodeList);
                itemKeyList = itemKeyList.stream().distinct().collect(Collectors.toList());
                redisUtil.set(key, JSONObject.toJSONString(itemKeyList), -1);
            } else {
                redisUtil.set(key, JSONObject.toJSONString(permissionCodeList), -1);
            }
        }
    }

    private List<AdministrativeContainerDTO> getAdministrativeContainerDTOS(List<String> roleOidList, Map<String, List<TeamRole>> teamRoleMap, List<TeamRole> memberTeamRoleList) {
        AdministrativeContainerDTO administrativeContainerDTO;
        List<AdministrativeContainerDTO> containerDTOS = new ArrayList<>();
        List<TeamRole> roleList;
        for (String domainOid : teamRoleMap.keySet()) {
            administrativeContainerDTO = new AdministrativeContainerDTO();
            administrativeContainerDTO.setDomainOid(domainOid);
            roleList = teamRoleMap.get(domainOid);
            if (!CollectionUtils.isEmpty(memberTeamRoleList) && !roleList.contains(memberTeamRoleList.get(0))) {
                roleList.add(memberTeamRoleList.get(0));
            }
            administrativeContainerDTO.setRoles(roleList);
            roleOidList.addAll(teamRoleMap.get(domainOid).stream().map(TeamRole::getSourceOid).collect(Collectors.toList()));
            containerDTOS.add(administrativeContainerDTO);
        }
        return containerDTOS;
    }

    private Map<String, List<PolicyACL>> getAclMap(List<PolicyACL> policyACLList) {
        List<RoleDTO> roleDTOList;
        List<PolicyItem> itemList;
        List<String> permissionCodeList;
        Map<String, List<PolicyACL>> policyAclMap = new HashMap<>(16);
        List<PolicyACL> aclList = new ArrayList<>();
        String roleOid;
        String key;
        for (PolicyACL acl : policyACLList) {
            roleDTOList = JSON.parseArray(acl.getRoles(), RoleDTO.class);
            roleOid = roleDTOList.get(0).getOid();
            key = acl.getBizModelType() + ";" + roleOid;
            aclList = policyAclMap.get(key);
            if (CollectionUtils.isEmpty(aclList)) {
                aclList = new ArrayList<>();
                policyAclMap.put(key, aclList);
            }
            aclList.add(acl);
        }
        return policyAclMap;
    }

    private void getDomainRoles(List<TeamRole> teamRoleList, List<AdministrativeContainerDTO> containerDTOS, List<String> roleContainerList, List<AdministrativeDomain> containerDomainList) {
        AdministrativeContainerDTO administrativeContainerDTO;
        for (AdministrativeDomain domain : containerDomainList) {
            if (!roleContainerList.contains(domain.getOid())) {
                administrativeContainerDTO = new AdministrativeContainerDTO();
                administrativeContainerDTO.setDomainOid(domain.getOid());
                administrativeContainerDTO.setRoles(teamRoleList);
                containerDTOS.add(administrativeContainerDTO);
            }
        }
    }

}
