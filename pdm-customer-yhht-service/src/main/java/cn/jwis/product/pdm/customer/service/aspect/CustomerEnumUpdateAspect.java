package cn.jwis.product.pdm.customer.service.aspect;

import cn.jwis.framework.base.dto.PageSimpleDTO;
import cn.jwis.framework.base.dto.UserDTO;
import cn.jwis.framework.base.util.MapUtil;
import cn.jwis.framework.base.web.session.SessionHelper;
import cn.jwis.platform.plm.foundation.classification.dto.ClsPropertyAssignCreateDTO;
import cn.jwis.platform.plm.foundation.classification.dto.ClsPropertyAssignUpdateDTO;
import cn.jwis.platform.plm.foundation.classification.entity.PropertyDef;
import cn.jwis.platform.plm.foundation.classification.service.ClassificationPropertyHelper;
import cn.jwis.platform.plm.foundation.common.response.InstanceEntity;
import cn.jwis.platform.plm.foundation.model.dto.ModelPropertyCreateDto;
import cn.jwis.platform.plm.foundation.model.dto.ModelPropertyUpdateDTO;
import cn.jwis.platform.plm.permission.util.PermissionCacheUtil;
import cn.jwis.platform.plm.sysconfig.entity.Units;
import cn.jwis.platform.plm.sysconfig.preferences.dto.ConfigGroupDTO;
import cn.jwis.product.pdm.customer.repo.CustomerCommonRepo;
import cn.jwis.product.pdm.customer.service.delegate.BatchSendERP;
import cn.jwis.product.pdm.customer.service.dto.DataDictionaryDTO;
import cn.jwis.product.pdm.customer.service.impl.DataDictionaryServiceImpl;
import cn.jwis.product.pdm.customer.service.impl.U9Call;
import cn.jwis.product.pdm.partbom.part.dto.LifecycleStatusUpdateDTO;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 单位，分类属性，模型属性的枚举值更新了传递给U9
 * @date 2023/9/4 10:33
 * @Email <EMAIL>
 */
@Aspect
@Component
@Slf4j
public class CustomerEnumUpdateAspect implements InitializingBean {

    @Autowired
    ClassificationPropertyHelper classificationPropertyHelper;

    @Autowired
    RedisTemplate redisTemplate;

    @Autowired
    U9Call u9Call;

    public String PDM_ENUM_DATA_MAP = "pdm.enum.data.";

    private Map<String,String> u9ClsTypeMap = new HashMap<>();

    @Autowired
    private PermissionCacheUtil permissionCacheUtil;

    @Autowired
    private CustomerCommonRepo customerCommonRepo;

    @Before("execution(* cn.jwis.product.pdm.document.controller.DocumentAppController.setStatus(..))"
    )
    public void docSetStatus(JoinPoint jp) {
        permissionCacheUtil.clearCatch();
    }

//    @Before("execution(* cn.jwis.platform.plm.permission.filter.web.PermissionFilterController.executeFilters(..))"
//    )
//    public void executeFilters(JoinPoint jp) {
//        permissionCacheUtil.clearCatch();
//    }

    @Autowired
    private BatchSendERP batchSendERP;

    @Around("execution(* cn.jwis.product.pdm.partbom.part.web.PartAppController.setStatus(..)) "
    )
    public Object partSetStatus(ProceedingJoinPoint jp) {
        permissionCacheUtil.clearCatch();
        try {
            Object obj = jp.proceed();
            return obj;

        }catch (Throwable e){
            log.error(e.getMessage(), e);
        } finally {
            LifecycleStatusUpdateDTO dto = (LifecycleStatusUpdateDTO) jp.getArgs()[0];
            if("PartIteration".equals(dto.getModelInfo().getType())){
                List<String> updateList;
                if("Deactivated".equals(dto.getStatus()))
                    updateList = customerCommonRepo.stopStatusSetPartName(Arrays.asList(dto.getModelInfo().getOid()));
                else
                    updateList = customerCommonRepo.unStopStatusSetPartName(Arrays.asList(dto.getModelInfo().getOid()));
                if(updateList != null && updateList.size() > 0){
                    List<InstanceEntity> instanceList = updateList.stream().map(it -> {
                        InstanceEntity instance = new InstanceEntity();
                        instance.setOid(it);
                        instance.setType("PartIteration");
                        return instance;
                    }).collect(Collectors.toList());
                    batchSendERP.batchSendERP(instanceList, Boolean.TRUE);
                }
            }
        }
        return null;
    }

    @Value("${tenantOid:6deb5dde-aa39-46fb-962d-a5951f8fab5e}")
    private String tenantOid;

    @Before("execution(* cn.jwis.platform.plm.container.web.ProductContainerController.searchProductContainer(..)) ")
    public void searchContainer(JoinPoint jp) {
        Object[] args = jp.getArgs();
        PageSimpleDTO dto = (PageSimpleDTO) args[0];
        if(dto.getSize() == 10000){
            UserDTO userDto = new UserDTO();
            userDto.setTenantOid(tenantOid);
            userDto.setOid("sys_admin");
            userDto.setAccount("sys_admin");
            userDto.setSystemAdmin(Boolean.TRUE);
            userDto.setIpAddress("127.0.0.1");
            SessionHelper.addCurrentUser(userDto);
        }
    }

    @AfterReturning(pointcut = "execution(* cn.jwis.platform.plm.sysconfig.units.service.UnitsHelperImpl.createUnits(..))",
            returning = "ret",argNames = "jp,ret")
    public void createUnitsAspect(JoinPoint jp, Units ret) {
        log.info("CustomerEnumUpdateAspect.createUnits");
        if(ret==null) return;
        Map<String,String> enumData = new HashMap<>();
        enumData.put(ret.getCode(),ret.getName());
        enumDataSendU9(enumData,"units","unit");
    }

    @AfterReturning(pointcut = "execution(* cn.jwis.platform.plm.sysconfig.units.service.UnitsHelperImpl.updateUnits(..))",
            returning = "ret",argNames = "jp,ret")
    public void updateUnitsAspect(JoinPoint jp, Units ret) {
        log.info("CustomerEnumUpdateAspect.updateUnits");
        if(ret==null) return;
        Map<String,String> enumData = new HashMap<>();
        enumData.put(ret.getCode(),ret.getName());
        enumDataSendU9(enumData,"units","unit");
    }

    @AfterReturning(pointcut = "execution(* cn.jwis.platform.plm.sysconfig.units.service.UnitsHelperImpl.disabled(..))",
            returning = "ret",argNames = "jp,ret")
    public void disabledUnitsAspect(JoinPoint jp, Units ret) {
        if(ret==null) return;
        log.info("CustomerEnumUpdateAspect.disabled");
        //enumDataSendU9(ret,"delete");
    }

    @AfterReturning(pointcut = "execution(* cn.jwis.platform.plm.foundation.model.service.ModelPropertyHelperImpl.create(..))",
            returning = "ret",argNames = "jp,ret")
    public void createModelPropertyAspect(JoinPoint jp) {
        log.info("CustomerEnumUpdateAspect.createModelPropertyAspect");
        Object[] objects = jp.getArgs();
        if(objects == null || objects.length == 0) {
            return;
        }
        if(objects[0] instanceof ModelPropertyCreateDto) {
            ModelPropertyCreateDto dto = (ModelPropertyCreateDto)objects[0];
            if("enumtype".equals(dto.getConstraints().getConstraintType())){
                JSONObject enumData = dto.getConstraints().getConstraintContext();
                if(enumData == null){
                    return;
                }
                Map<String,String> enumMap = transferMap(enumData.getString("valueEnum"));
                String clsType = dto.getProperty().getCode();
                enumDataSendU9(enumMap,clsType,"modelProperty");
            }
        }
    }

    @AfterReturning(pointcut = "execution(* cn.jwis.platform.plm.foundation.model.service.ModelPropertyHelperImpl.udpate(..))",
            returning = "ret",argNames = "jp,ret")
    public void updateModelPropertyAspect(JoinPoint jp) {
        log.info("CustomerEnumUpdateAspect.updateModelPropertyAspect");
        Object[] objects = jp.getArgs();
        if(objects == null || objects.length == 0) {
            return;
        }
        if(objects[0] instanceof ModelPropertyUpdateDTO) {
            ModelPropertyUpdateDTO dto = (ModelPropertyUpdateDTO)objects[0];
            if("enumtype".equals(dto.getConstraints().getConstraintType())){
                JSONObject enumData = dto.getConstraints().getConstraintContext();
                if(enumData == null){
                    return;
                }
                Map<String,String> enumMap = transferMap(enumData.getString("valueEnum"));
                String clsType = dto.getProperty().getCode();
                enumDataSendU9(enumMap,clsType,"modelProperty");
            }
        }
    }

    @AfterReturning(pointcut = "execution(* cn.jwis.platform.plm.foundation.model.service.ModelPropertyHelperImpl.delete(..))",
            returning = "ret",argNames = "jp,ret")
    public void disabledModelPropertyAspect(JoinPoint jp) {
        log.info("CustomerEnumUpdateAspect.deleteModelPropertyAspect");
        Object[] objects = jp.getArgs();
        if(objects == null || objects.length == 0) {
            return;
        }
        if(objects[0] instanceof String) {
            String propertyOid = (String)objects[0];
        }

    }

    @AfterReturning(pointcut = "execution(* cn.jwis.platform.plm.foundation.classification.service.DefaultClsPropertyHelperImpl.bindCls(..))",
            returning = "ret",argNames = "jp,ret")
    public void bindClsAspect(JoinPoint jp) {
        log.info("CustomerEnumUpdateAspect.bindCls");
        Object[] objects = jp.getArgs();
        if(objects == null || objects.length == 0) {
            return;
        }
        if(objects[0] instanceof List) {
            List list = (List)objects[0];
            list.stream().forEach(l->{
                ClsPropertyAssignCreateDTO dto = (ClsPropertyAssignCreateDTO)l;
                if("enumtype".equals(dto.getConstraintType())){
                    PropertyDef def = classificationPropertyHelper.findByOid(dto.getToOid());
                    String clsType = def.getCode();
                    JSONObject enumData = dto.getConstraintContext();
                    if(enumData == null){
                        return;
                    }
                    Map<String,String> enumMap = transferMap(enumData.getString("valueEnum"));
                    enumDataSendU9(enumMap,clsType,"classification");
                }
            });

        }
    }

    @AfterReturning(pointcut = "execution(* cn.jwis.platform.plm.foundation.classification.service.DefaultClsPropertyHelperImpl.updateClsPropertyAssign(..))",
            returning = "ret",argNames = "jp,ret")
    public void updateClsPropertyAssignAspect(JoinPoint jp) {
        log.info("CustomerEnumUpdateAspect.updateClsPropertyAssign");
        Object[] objects = jp.getArgs();
        if(objects == null || objects.length == 0) {
            return;
        }
        if(objects[0] instanceof ClsPropertyAssignUpdateDTO) {
            ClsPropertyAssignUpdateDTO dto = (ClsPropertyAssignUpdateDTO)objects[0];
            if("enumtype".equals(dto.getConstraintType())){
                PropertyDef def = classificationPropertyHelper.findByOid(dto.getToOid());
                String clsType = def.getCode();
                JSONObject enumData = dto.getConstraintContext();
                if(enumData == null){
                    return;
                }
                Map<String,String> enumMap = transferMap(enumData.getString("valueEnum"));
                enumDataSendU9(enumMap,clsType,"classification");
            }
        }
    }

    @AfterReturning(pointcut = "execution(* cn.jwis.platform.plm.sysconfig.preferences.service.impl.PreferencesServiceImpl.updatePreferencesConfig(..))",
            returning = "ret",argNames = "jp,ret")
    public void updatePreferencesConfig(JoinPoint jp) {
        log.info("CustomerEnumUpdateAspect.updatePreferencesConfig");
        Object[] objects = jp.getArgs();
        if(objects == null || objects.length == 0) {
            return;
        }
        if(objects[0] instanceof ConfigGroupDTO) {
            ConfigGroupDTO dto = (ConfigGroupDTO)objects[0];
            if("属性枚举值配置".equals(dto.getName())){
                Map<String, String> zldjMap = dto.getItemDTOList().stream().filter(item -> {
                    Boolean imediateSend = Boolean.FALSE;
                    String code = item.getCode();
                    if ("manufacturer".equals(code)) {
                        code = "cn_jwis_sccj";
                        imediateSend = Boolean.TRUE;
                    } else if ("PackageType".equals(code)) {
                        code = "cn_jwis_fzxs";
                        imediateSend = Boolean.TRUE;
                    } else if("standardNumber".equals(code)) {
//                        code = "cn_jwis_bzh";
                        code = "cn_jwis_fzxs";
                        imediateSend = Boolean.TRUE;
                    }
                    else if ("msl".equals(code)) {
                        code = "cn_jwis_smdj";
                        imediateSend = Boolean.TRUE;
                    }
                    if (imediateSend) {
                        Map<String, String> enumMap = Arrays.stream(item.getValue().split("\\|")).collect(Collectors.toMap(it -> it.split(";;;")[0], it -> it.split(";;;")[1], (v1, v2) -> v1));
                        enumDataSendU9(enumMap, code, "classification");
                    }
                    return !imediateSend && item.getName().contains("质量等级");
                }).flatMap(item -> Arrays.stream(item.getValue().split("\\|"))).collect(Collectors.toMap(it -> it.split(";;;")[0], it -> it.split(";;;")[1], (v1, v2) -> v1));


                String code = "cn_jwis_zldj";
                enumDataSendU9(zldjMap,code,"classification");
            }
        }
    }

    @Autowired
    private DataDictionaryServiceImpl dataDictionaryService;

    @AfterReturning(pointcut = "execution(* cn.jwis.product.pdm.customer.service.impl.DataDictionaryServiceImpl.save(..))", returning = "ret",argNames = "jp,ret")
    public void DataDictionarySave(JoinPoint jp) {
        log.info("CustomerEnumUpdateAspect.DataDictionarySend");
        Object[] objects = jp.getArgs();
        if(objects == null || objects.length == 0) {
            return;
        }

        List<DataDictionaryDTO> list = dataDictionaryService.treeList(null);
        Map<String, String> zldjMap = list.stream().flatMap(it -> it.getChildren().stream()).filter(item -> {
            Boolean imediateSend = Boolean.FALSE;
            String code = item.getCode();
            if ("manufacturer".equals(code)) {
                code = "cn_jwis_sccj";
                imediateSend = Boolean.TRUE;
            } else if ("PackageType".equals(code)) {
                code = "cn_jwis_fzxs";
                imediateSend = Boolean.TRUE;
            } else if("standardNumber".equals(code)) {
//                        code = "cn_jwis_bzh";
                code = "cn_jwis_fzxs";
                imediateSend = Boolean.TRUE;
            }
            else if ("msl".equals(code)) {
                code = "cn_jwis_smdj";
                imediateSend = Boolean.TRUE;
            }
            if (imediateSend) {
                Map<String, String> enumMap = item.getChildren().stream().collect(Collectors.toMap(it -> it.getCode(), it -> it.getDisplayName(), (v1, v2) -> v1));
                enumDataSendU9(enumMap, code, "classification");
            }
            return !imediateSend && item.getDisplayName().contains("质量等级");
        }).flatMap(item -> item.getChildren().stream()).collect(Collectors.toMap(it -> it.getCode(), it -> it.getDisplayName(), (v1, v2) -> v1));

        String code = "cn_jwis_zldj";
        enumDataSendU9(zldjMap,code,"classification");
    }

    private static final String spliterStr = "SPLITERSTR";

    @AfterReturning(pointcut = "execution(* cn.jwis.product.pdm.customer.service.impl.DataDictionaryServiceImpl.toggleEnable(..))", returning = "ret",argNames = "jp,ret")
    public void DataDictionaryToggleEnable(JoinPoint jp) {
        log.info("CustomerEnumUpdateAspect.DataDictionarySend");
        Object[] objects = jp.getArgs();
        if(objects == null || objects.length == 0) {
            return;
        }

        List<DataDictionaryDTO> list = dataDictionaryService.treeList(null);
        Map<String, String> zldjMap = list.stream().flatMap(it -> it.getChildren().stream()).filter(item -> {
            Boolean imediateSend = Boolean.FALSE;
            String code = item.getCode();
            if ("manufacturer".equals(code)) {
                code = "cn_jwis_sccj";
                imediateSend = Boolean.TRUE;
            } else if ("PackageType".equals(code)) {
                code = "cn_jwis_fzxs";
                imediateSend = Boolean.TRUE;
            } else if("standardNumber".equals(code)) {
//                        code = "cn_jwis_bzh";
                code = "cn_jwis_fzxs";
                imediateSend = Boolean.TRUE;
            }
            else if ("msl".equals(code)) {
                code = "cn_jwis_smdj";
                imediateSend = Boolean.TRUE;
            }
            if (imediateSend) {
                Map<String, String> enumMap = item.getChildren().stream().collect(Collectors.toMap(it -> it.getCode()
                        , it -> it.getOid().equals(objects[0]) ? it.getDisplayName() + spliterStr + objects[1] : it.getDisplayName(), (v1, v2) -> v1));
                enumDataSendU9(enumMap, code, "classification");
            }
            return !imediateSend && item.getDisplayName().contains("质量等级");
        }).flatMap(item -> item.getChildren().stream()).collect(Collectors.toMap(it -> it.getCode(), it -> it.getOid().equals(objects[0]) ? it.getDisplayName() + spliterStr + objects[1] : it.getDisplayName(), (v1, v2) -> v1));

        String code = "cn_jwis_zldj";
        enumDataSendU9(zldjMap,code,"classification");
    }

    @Before("execution(* cn.jwis.product.pdm.customer.service.impl.DataDictionaryServiceImpl.delete(..))")
    public void DataDictionaryDelete(JoinPoint jp) {
        log.info("CustomerEnumUpdateAspect.DataDictionarySend");
        Object[] objects = jp.getArgs();
        if(objects == null || objects.length == 0) {
            return;
        }

        List<DataDictionaryDTO> list = dataDictionaryService.treeList(null);
        Map<String, String> zldjMap = list.stream().flatMap(it -> it.getChildren().stream()).filter(item -> {
            Boolean imediateSend = Boolean.FALSE;
            String code = item.getCode();
            if ("manufacturer".equals(code)) {
                code = "cn_jwis_sccj";
                imediateSend = Boolean.TRUE;
            } else if ("PackageType".equals(code)) {
                code = "cn_jwis_fzxs";
                imediateSend = Boolean.TRUE;
            } else if("standardNumber".equals(code)) {
//                        code = "cn_jwis_bzh";
                code = "cn_jwis_fzxs";
                imediateSend = Boolean.TRUE;
            }
            else if ("msl".equals(code)) {
                code = "cn_jwis_smdj";
                imediateSend = Boolean.TRUE;
            }
            if (imediateSend) {
                Map<String, String> enumMap = item.getChildren().stream().collect(Collectors.toMap(it -> it.getCode(), it -> it.getOid().equals(objects[0]) ? it.getDisplayName() + spliterStr + "delete" : it.getDisplayName(), (v1, v2) -> v1));
                enumDataSendU9(enumMap, code, "classification");
            }
            return !imediateSend && item.getDisplayName().contains("质量等级");
        }).flatMap(item -> item.getChildren().stream()).collect(Collectors.toMap(it -> it.getCode(), it -> {
            return it.getOid().equals(objects[0]) ? it.getDisplayName() + spliterStr + "delete" : it.getDisplayName();
        }, (v1, v2) -> v1));

        String code = "cn_jwis_zldj";
        enumDataSendU9(zldjMap,code,"classification");
    }

    private void enumDataSendU9(Map<String,String> newData, String clsType, String source) {
        if(StringUtil.isBlank(clsType)){
            return;
        }
        if(MapUtil.isEmpty(newData)){
            return;
        }
        // 单位一次只会更新一个，单独处理
        if("unit".equals(source)){
            doSend(newData,clsType);
            return;
        }
        log.info("CustomerEnumUpdateAspect.enumDataSendU9.clsType:" + JSON.toJSONString(clsType));
        log.info("CustomerEnumUpdateAspect.enumDataSendU9.enumData:" + JSON.toJSONString(newData));

        String redisKey = PDM_ENUM_DATA_MAP + source + "." + clsType;
        // 取出缓存比对，多出的就是此次新增的
        Map<Object, Object> redisMap = redisTemplate.opsForHash().entries(redisKey);
        // 首次
        if(MapUtil.isEmpty(redisMap)){
            redisTemplate.opsForHash().putAll(redisKey,newData);
            doSend(newData,clsType);
        }else{
            Map<String,String> needPublish = new HashMap<>();
            for(Map.Entry<String,String> entry : newData.entrySet()){
                String code = entry.getKey();
                String name = entry.getValue();
                if(!redisMap.containsKey(code) || !name.equals(redisMap.get(code))){
                    needPublish.put(code,name);
                }
            }
            if(MapUtil.isNotEmpty(needPublish)){
                doSend(needPublish,clsType);
            }
            redisMap.putAll(needPublish);
            redisTemplate.opsForHash().putAll(redisKey  , redisMap);
        }
    }

    private void doSend(Map<String,String> newData, String clsType){
        String u9ClsType = u9ClsTypeMap.get(clsType);
        if(StringUtil.isBlank(u9ClsType)){
            log.info(clsType + " 不需要发u9！");
            return;
        }
        log.info("CustomerEnumUpdateAspect.doSend.needSendU9:" + JSONObject.toJSONString(newData));
        try {
            for (Map.Entry<String, String> entry : newData.entrySet()) {
                Map<String, String> headers = new HashMap<>();
                headers.put("Content-Type", "application/json");
                JSONObject body = new JSONObject();
                body.put("clsType", u9ClsType);
                body.put("code", entry.getKey());
                String name = entry.getValue();
                String[] nameArr;
                if(name != null && (nameArr = name.split(spliterStr)).length > 1){
                    body.put("name", nameArr[0]);
                    if("true".equals(nameArr[1]))
                        body.put("status", 1); // enable
                    else if("false".equals(nameArr[1]))
                        body.put("status", 2);
                    else if("delete".equals(nameArr[1]))
                        body.put("status", 3);
                }else
                    body.put("name", name);
                u9Call.send(u9Call.getClsUpdateUrl(),body);
            }
        }catch (Exception e){
            log.error("CustomerEnumUpdateAspect.doSend:",e);
        }
    }


        //IN_PackagingForm 封装形式
        //IN_QualityGrade 质量等级
        //MSLShiMin 湿敏
        //IN_PackagingForm，标准号也用这个， 与封装方式共用一个值集， 注意不要出现重复的编码
        //Uom 单位
        //IN_Manufacturer 生产厂家
    @Override
    public void afterPropertiesSet() throws Exception {
        u9ClsTypeMap.put("cn_jwis_sccj","IN_Manufacturer"); //生产厂家
        u9ClsTypeMap.put("cn_jwis_zldj","IN_QualityGrade"); //质量等级
        u9ClsTypeMap.put("cn_jwis_smdj","MSLShiMin"); //湿敏
        u9ClsTypeMap.put("cn_jwis_bzh","IN_PackagingForm"); //标准号
        u9ClsTypeMap.put("cn_jwis_fzxs","IN_PackagingForm"); //封装形式
        u9ClsTypeMap.put("units","Uom"); //单位
    }

    private Map<String,String> transferMap(String enumData){
        Map<String,String> enumMap = new HashMap<>();
        if(StringUtil.isBlank(enumData) || !enumData.contains("txt")){
            return enumMap;
        }
        enumData = enumData.replaceAll("[{}'\"]","");
        String[] filterValueArr = enumData.split(",|，");
        for(int i = 0; i< filterValueArr.length; i++){
            String unit1 = filterValueArr[i];
            String unit2 = filterValueArr[++i];
            if(unit1.startsWith("txt")){
                enumMap.put(unit2.split(":")[1],unit1.split(":")[1]);
            }else{
                enumMap.put(unit1.split(":")[1],unit2.split(":")[1]);
            }
        }
        return enumMap;
    }

}
