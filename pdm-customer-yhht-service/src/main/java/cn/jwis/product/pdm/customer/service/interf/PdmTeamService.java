package cn.jwis.product.pdm.customer.service.interf;

import cn.jwis.framework.base.response.PageResult;
import cn.jwis.platform.plm.account.entity.team.TeamTemplate;
import cn.jwis.product.pdm.customer.entity.CustomerUnBindUserDTO;

import java.util.List;

/**
 * <AUTHOR> lhm
 * @Date ： Created in 2023/9/13 10:55
 * @Description :
 */
public interface PdmTeamService {
    List<TeamTemplate> findProcessTeam(String searchKey);

    PageResult<TeamTemplate> findProcessTeamPage(String searchKey, int index, int size);

    Object searchAllTeamRole(String containerOid, String searchKey, int index, int size);

    void deleteAllTeamRoleUser(CustomerUnBindUserDTO dto);

    void batchHandleTeamRoleUser(CustomerUnBindUserDTO dto);
}
