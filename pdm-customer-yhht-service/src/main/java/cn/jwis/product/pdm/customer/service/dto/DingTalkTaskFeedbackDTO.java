package cn.jwis.product.pdm.customer.service.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2023/8/10 13:54
 * @Email <EMAIL>
 */
@Data
@EqualsAndHashCode
public class DingTalkTaskFeedbackDTO {

    @ApiModelProperty("任务ID")
    private String id;

    @ApiModelProperty("选择的路由")
    private String routeKey;

    @ApiModelProperty("任务完成备注")
    private String msg;

}
