package cn.jwis.product.pdm.customer.service.impl;

import cn.hutool.json.JSONObject;
import cn.jwis.core.base.database.redis.utils.MultiTenantRedisUtil;
import cn.jwis.framework.base.domain.entity.BaseEntity;
import cn.jwis.framework.base.feign.RemoteAnalysisHelper;
import cn.jwis.framework.base.response.Result;
import cn.jwis.platform.plm.container.entity.Container;
import cn.jwis.platform.plm.container.entity.Folder;
import cn.jwis.platform.plm.foundation.common.service.CommonAbilityHelper;
import cn.jwis.platform.plm.foundation.model.entity.VertexDef;
import cn.jwis.platform.plm.permission.constants.Constants;
import cn.jwis.platform.plm.permission.dto.RoleDTO;
import cn.jwis.platform.plm.permission.filter.service.PermissionFilterHelperImpl;
import cn.jwis.platform.plm.permission.foundation.FoundationRemote;
import cn.jwis.platform.plm.permission.permission.dto.FilterParameterDTO;
import cn.jwis.platform.plm.permission.permission.entity.PolicyACL;
import cn.jwis.platform.plm.permission.permission.repo.IPermissionPolicyCqlRepo;
import cn.jwis.platform.plm.permission.template.dto.PolicyItemInfo;
import cn.jwis.platform.plm.permissions.util.RedisUtil;
import com.alibaba.fastjson.JSON;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Primary
@Transactional
public class CustomerPermissionFilterHelperImpl extends PermissionFilterHelperImpl {

    @Autowired
    private IPermissionPolicyCqlRepo permissionPolicyCqlRepo;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private FoundationRemote foundationRemote;


    @Autowired
    private CommonAbilityHelper commonAbilityHelper;

    @Override
    public List<BaseEntity> querySubModel(FilterParameterDTO dto) {
        List<BaseEntity> list = super.querySubModel(dto);

        if (dto.getContextType().equals(Folder.TYPE)) {
            Folder folder = (Folder) commonAbilityHelper.findDetailEntity(dto.getContextOid(), Folder.TYPE);
            Container container = (Container) commonAbilityHelper.findDetailEntity(folder.getContainerOid(), Container.TYPE);
            //产品库或者电子元器件是公开库，不考虑权限，显示所有的子类型
            if (!container.isPrivateFlag()) {
                Result<List<VertexDef>> result = foundationRemote.querySubModelType(dto.getObjectType());
                list = new ArrayList<>(RemoteAnalysisHelper.getResult(result));
            }
        }

        List<BaseEntity> newList = list.stream().filter(it -> {
            String name = new JSONObject(it).getStr("name");
            return !"ECAD".equals(name);
        }).sorted(Comparator.comparing(t -> {
            String displayName = new JSONObject(t).getStr("displayName");
            if ("产品化文档".equals(displayName))
                return 20;
            else if ("普通文档".equals(displayName))
                return 10;
            else
                return 99;
        })).collect(Collectors.toList());

        return newList;
    }

    @Override
    public void refreshPermissions() {
        // 找到系统中所有的权限项
        List<PolicyACL> permissionPolicy = permissionPolicyCqlRepo.getAllPermissionPolicy();
        // 将redis的库修改为权限默认的库
        MultiTenantRedisUtil.changeOtherToDefaultDB();

        deletePermissionKey(Constants.PERMISSION + ":");
        // 根据权限项将redis中的数据进行修改
        for (PolicyACL n : permissionPolicy) {
            List<PolicyItemInfo> policyItems = JSON.parseArray(n.getEntrySet(), PolicyItemInfo.class);
            if (CollectionUtils.isEmpty(policyItems)) {
                permissionPolicyCqlRepo.deletePermissionPolicy(n.getOid());
                continue;
            }
            List<String> permissionCodeList = getPermissionCodeList(policyItems);
            List<RoleDTO> roleDTOList = JSON.parseArray(n.getRoles(), RoleDTO.class);
            if (CollectionUtils.isEmpty(roleDTOList)) {
                permissionPolicyCqlRepo.deletePermissionPolicy(n.getOid());
                continue;
            }
            setPolicyCacheData(permissionCodeList, roleDTOList.get(0), n);
        }
    }

    private List<String> getPermissionCodeList(List<PolicyItemInfo> list) {
        List<String> result = new ArrayList<>();
        for (PolicyItemInfo n : list) {
            String status = n.getStatus();
            String logicKey = n.getLogicKey();
            if ("refuse".equals(status)) {
                logicKey = "!;" + logicKey;
            }
            result.add(logicKey);
        }
        return result;
    }

    private void setPolicyCacheData(List<String> permissionCodeList, RoleDTO roleDTO, PolicyACL acl) {
        String modelType = acl.getBizModelType();
        String roleOid = roleDTO.getOid();
        String key;
        if (StringUtils.isEmpty(acl.getState())) {
            key = Constants.PERMISSION + ":" + modelType + ":" + "true" + ":" + acl.getAdministrativeDomainOid() + ":" + roleOid;
            redisUtil.set(key, com.alibaba.fastjson.JSONObject.toJSONString(permissionCodeList), -1);
        } else {
            key = Constants.PERMISSION + ":" + modelType + ":" + acl.getState() + ":" + acl.getAdministrativeDomainOid() + ":" + roleOid;
            redisUtil.set(key, com.alibaba.fastjson.JSONObject.toJSONString(permissionCodeList), -1);
        }
    }

}
