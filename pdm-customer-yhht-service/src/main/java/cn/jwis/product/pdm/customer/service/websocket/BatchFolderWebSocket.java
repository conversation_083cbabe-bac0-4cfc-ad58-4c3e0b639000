package cn.jwis.product.pdm.customer.service.websocket;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.websocket.OnClose;
import javax.websocket.OnError;
import javax.websocket.OnOpen;
import javax.websocket.Session;
import javax.websocket.server.PathParam;
import javax.websocket.server.ServerEndpoint;
import java.io.IOException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR> zhaikun
 * @Date ： 2024/2/5
 * @Description :
 */

@Slf4j
@Component
@ServerEndpoint("/websocket/{userOid}")
public class BatchFolderWebSocket {

    // 用户与WebSocketService集合的映射
    private static final Map<String, BatchFolderWebSocket> _WEBSOCKET_POOL = new ConcurrentHashMap<>();

    // 与某个客户端的连接会话，需要通过它来给客户端发送数据
    private Session session;


    // 用户oid
    private String userOid;


    /**
     * 连接建立成功调用的方法
     */
    @OnOpen
    public void onOpen(@PathParam("userOid") String userOid, Session session) {
        this.userOid = userOid;
        this.session = session;
        _WEBSOCKET_POOL.put(userOid, this);
        log.info("websocket open userOid:{}", userOid);
    }

    /**
     * 错误处理方法
     */
    @OnError
    public void onError(Session session, Throwable e) throws Exception {
        e.printStackTrace();
        throw new Exception(e.getMessage());
    }

    /**
     * 关闭连接
     */
    @OnClose
    public void onClose() {
        _WEBSOCKET_POOL.remove(this.userOid);
    }

    public void sendMessage(String message, Session session) {
        synchronized (session) {
            if (session.isOpen()) {
                try {
                    session.getBasicRemote().sendText(JSON.toJSONString(message));
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    /**
     * 根据用户oid获取Session
     *
     * @param userOid
     * @return
     * @throws Exception
     */
    public Session getSession(String userOid) {
        if (StringUtils.isNotBlank(userOid)) {
            BatchFolderWebSocket webSocketService = _WEBSOCKET_POOL.get(userOid);
            if (null != webSocketService) {
                return webSocketService.session;
            }
        }
        return null;
    }
}
