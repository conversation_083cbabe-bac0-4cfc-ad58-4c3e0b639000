package cn.jwis.product.pdm.customer.service.impl;

import cn.hutool.json.JSONUtil;
import cn.jwis.audit.annotation.JWIParam;
import cn.jwis.framework.base.bean.util.BeanUtil;
import cn.jwis.framework.base.domain.able.ModelAble;
import cn.jwis.framework.base.domain.able.RelationAble;
import cn.jwis.framework.base.dto.UserDTO;
import cn.jwis.framework.base.exception.JWIException;
import cn.jwis.framework.base.response.PageResult;
import cn.jwis.framework.base.util.*;
import cn.jwis.framework.base.web.session.SessionHelper;
import cn.jwis.framework.database.core.entity.FromToFilter;
import cn.jwis.framework.database.core.entity.SubFilter;
import cn.jwis.framework.database.core.query.dynamic.Condition;
import cn.jwis.platform.plm.account.auth.service.AuthHelper;
import cn.jwis.platform.plm.account.components.dto.SearchKeywordPageDTO;
import cn.jwis.platform.plm.account.components.service.SearchHelper;
import cn.jwis.platform.plm.account.role.service.RoleHelper;
import cn.jwis.platform.plm.container.dto.FolderCreateDTO;
import cn.jwis.platform.plm.container.dto.container.FuzzySubPageDTO;
import cn.jwis.platform.plm.container.dto.folder.*;
import cn.jwis.platform.plm.container.entity.*;
import cn.jwis.platform.plm.container.entity.response.ContainerDetail;
import cn.jwis.platform.plm.container.entity.response.FolderForExport;
import cn.jwis.platform.plm.container.entity.response.InstanceEntityWithCatalog;
import cn.jwis.platform.plm.container.entity.response.TeamRoleWithUser;
import cn.jwis.platform.plm.container.service.ContainerService;
import cn.jwis.platform.plm.container.service.Impl.FolderHelperImpl;
import cn.jwis.platform.plm.container.service.TeamHelper;
import cn.jwis.platform.plm.container.service.TeamRoleService;
import cn.jwis.platform.plm.datadistribution.annotation.DataDistribution;
import cn.jwis.platform.plm.datadistribution.service.DataDistributionService;
import cn.jwis.platform.plm.dataengine.common.JWICommonService;
import cn.jwis.platform.plm.foundation.classification.able.info.ClassificationInfo;
import cn.jwis.platform.plm.foundation.classification.entity.Classification;
import cn.jwis.platform.plm.foundation.classification.responce.ClassificationTreeNode;
import cn.jwis.platform.plm.foundation.classification.service.ClassificationService;
import cn.jwis.platform.plm.foundation.common.response.InstanceEntity;
import cn.jwis.platform.plm.foundation.common.service.CommonAbilityHelper;
import cn.jwis.platform.plm.foundation.common.service.CommonAbilityService;
import cn.jwis.platform.plm.foundation.common.service.InstanceService;
import cn.jwis.platform.plm.foundation.location.able.info.LocationInfo;
import cn.jwis.platform.plm.foundation.model.service.ModelHelper;
import cn.jwis.platform.plm.foundation.pipeline.manager.entity.ModelPlus;
import cn.jwis.platform.plm.foundation.relationship.Assign;
import cn.jwis.platform.plm.foundation.relationship.Contain;
import cn.jwis.platform.plm.modelexcel.service.ModelExcelExportService;
import cn.jwis.platform.plm.permission.administrativedomain.AdministrativeDomainService;
import cn.jwis.platform.plm.permission.administrativedomain.entity.AdministrativeDomain;
import cn.jwis.platform.plm.permission.administrativedomain.entity.Inherit;
import cn.jwis.platform.plm.permission.administrativedomain.entity.Point;
import cn.jwis.product.pdm.customer.entity.FuzzyExportDTO;
import cn.jwis.product.pdm.customer.entity.FuzzyPageVO;
import cn.jwis.product.pdm.customer.service.dto.FuzzyContainerInstanceExportDTO;
import cn.jwis.product.pdm.customer.service.dto.PdmFolderCreateDTO;
import cn.jwis.product.pdm.customer.service.dto.PermApplyDTO;
import cn.jwis.product.pdm.customer.service.interf.CustomerTeamHelper;
import cn.jwis.product.pdm.customer.service.interf.PDMFolderServiceI;
import cn.jwis.product.pdm.document.entity.DocumentIteration;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import net.sourceforge.pinyin4j.PinyinHelper;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.Collator;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
@Transactional
@Primary
public class CustomFolderHelperImpl extends FolderHelperImpl {

    @Autowired
    private CustomerFolderServiceImpl folderService;

    @Autowired
    private AuthHelper authHelper;

    @Autowired
    private CommonAbilityHelper commonAbilityHelper;
    @Autowired
    private CommonAbilityService commonAbilityService;
    @Autowired
    private AdministrativeDomainService administrativeDomainService;

    @Autowired
    ClassificationService classificationService;

    @Autowired
    PDMFolderServiceI pdmFolderService;

    @Value("${data.distribution.open}")
    private boolean dataDistributionOpen = false;

    @Autowired
    JWICommonService jwiCommonService;

    @Autowired
    InstanceService instanceService;


    @Autowired
    private ModelHelper modelHelper;

    @Autowired
    DataDistributionService dataDistributionService;

    @Autowired
    private ContainerService containerService;

    @Autowired
    private PlatformTransactionManager transactionManager;

    @Autowired
    private ModelExcelExportService modelExcelExportService;

    @Resource(name = "customerTeamHelperImpl")
    private TeamHelper teamHelper;

    @Autowired
    private CustomerTeamHelper customerTeamHelper;

    @Autowired
    private AdministrativeDomainService domainService;

    @Autowired
    SearchHelper searchHelper;

    @Autowired
    RoleHelper roleHelper;
    @Autowired
    private TeamRoleService teamRoleService;


    // @JWIServiceAudit(buzOid = "${result.oid}", bizName = "${result.number}",buzType = "${dto.modelDefinition}",action = Action.ADD,content = "新增文件夹")
    @JWIParam("result")
    @DataDistribution(dataOpt = "create")
    @Override
    public Folder create(FolderCreateDTO dto) throws JWIException {
        ValidationUtil.validate(dto);
        Folder folder = BeanUtil.copyProperties(dto, new Folder());
        //将库信息重新存入对象中
        folderService.setLocation(folder, dto.getLocationInfo());
        // 文件夹增加分类信息
        ClassificationInfo clsInfo = dto.getClassificationInfo();
        if (clsInfo != null) {
            this.folderService.setClassification(folder, clsInfo);
        }
        // 重名校验
        String catalogType = folder.getCatalogType();
        String catalogOid = folder.getCatalogOid();
        Assert.notBlank(catalogOid, "catalogOid can not be null");
        SubFilter subFilter = new SubFilter(catalogType, catalogOid, Contain.TYPE, Folder.TYPE);
        subFilter.setFilter(Condition.where("name").eq(dto.getName()));
        List<Folder> folders = folderService.dynamicQueryByFrom(subFilter);
        Assert.isEmpty(folders, "文件夹名称相同层级已存在:" + dto.getName());
        // 获取当前文件夹的最大值
        ModelAble entity = folderService.findDetailEntity(catalogOid, catalogType);
        String type = entity.getType();
        int orderBy = 0;
        if (Folder.TYPE.equals(type)) {
            Folder lastFolder = (Folder) entity;
            orderBy = lastFolder.getFolderMax();
            orderBy++;
            // 更新文件夹的文件夹最大值
            lastFolder.setFolderMax(orderBy);
            commonAbilityHelper.doUpdate(lastFolder);
        }
        // 文件夹排序字段更新
        folder.setOrderBy(orderBy);

        // 创建
        commonAbilityHelper.doCreate(folder);

        String folderOid = folder.getOid();
        // 8. 创建配置管理域
        AdministrativeDomain administrativeDomain = new AdministrativeDomain();
        String administrativeDomainOid = OidGenerator.newOid();
        administrativeDomain.setOid(administrativeDomainOid);
        administrativeDomain.setName(folder.getName());
        administrativeDomain.setDescription(folder.getDescription());
        administrativeDomain.setContainerModelType(Folder.TYPE);
        administrativeDomain.setContainerOid(folderOid);
        administrativeDomainService.create(administrativeDomain);
        List<RelationAble> rels = new ArrayList<>();
        Point relatedTo = new Point(Folder.TYPE, folderOid, AdministrativeDomain.TYPE, administrativeDomainOid);
        rels.add(relatedTo);

        // 父节点管理域关联 自身管理域
        AdministrativeDomain oneByFrom = administrativeDomainService.findOneByFrom(catalogType, catalogOid, Point.TYPE);
        if (oneByFrom != null) {
            rels.add(new Inherit(AdministrativeDomain.TYPE, oneByFrom.getOid(), AdministrativeDomain.TYPE, administrativeDomainOid));
        }
        commonAbilityService.createInterRelation(rels);
        return folder;
    }

    @Override
    public Folder create(Folder folder) {
        folderService.createEntity(folder);
        folderService.createExtensionContent(folder);
        folderService.createContain(folder);
        //处理管理域
        String folderOid = folder.getOid();
        AdministrativeDomain administrativeDomain = new AdministrativeDomain();
        String administrativeDomainOid = OidGenerator.newOid();
        administrativeDomain.setOid(administrativeDomainOid);
        administrativeDomain.setName(folder.getName());
        administrativeDomain.setDescription(folder.getDescription());
        administrativeDomain.setContainerModelType(Folder.TYPE);
        administrativeDomain.setContainerOid(folderOid);
        administrativeDomainService.create(administrativeDomain);
        List<RelationAble> rels = new ArrayList<>();
        Point relatedTo = new Point(Folder.TYPE, folderOid, AdministrativeDomain.TYPE, administrativeDomainOid);
        rels.add(relatedTo);

        // 父节点管理域关联 自身管理域
        AdministrativeDomain oneByFrom = administrativeDomainService.findOneByFrom(folder.getCatalogType(), folder.getCatalogOid(), Point.TYPE);
        if (oneByFrom != null) {
            rels.add(new Inherit(AdministrativeDomain.TYPE, oneByFrom.getOid(), AdministrativeDomain.TYPE, administrativeDomainOid));
        }
        commonAbilityService.createInterRelation(rels);
        return folder;
    }

    public void createFoldersWithContainerTeam(FolderFromDTO folder) {
        Folder parent = folderService.findByOids(Collections.singletonList(folder.getFolderOid())).stream().findFirst().orElse(new Folder());
        List<Folder> folders = folderService.findByOids(folder.getFromOids());
        List<FolderTreeNode> tree = new ArrayList<>();
        assemblyTree(folders, tree);
        this.createTree(tree, parent, Boolean.TRUE);
        customerTeamHelper.bindContainerTeam(parent,new BindUser());//根据parent.getContainerOid拿到容器oid-》管理域，再去刷新整个容器的团队角色缓存。
    }

    @Override
    public void createFolders(FolderFromDTO folder) {
        Folder parent = folderService.findByOids(Collections.singletonList(folder.getFolderOid())).stream().findFirst().orElse(new Folder());
        List<Folder> folders = folderService.findByOids(folder.getFromOids());
        List<FolderTreeNode> tree = new ArrayList<>();
        assemblyTree(folders, tree);
        this.createTree(tree, parent, Boolean.FALSE);
        //customerTeamHelper.bindContainerTeam(parent,new BindUser());//根据parent.getContainerOid拿到容器oid-》管理域，再去刷新整个容器的团队角色缓存。
    }

    //创建树
    private void createTree(List<FolderTreeNode> tree, Folder parent, boolean isCopy) {
        for (FolderTreeNode node : tree) {
            PdmFolderCreateDTO create = new PdmFolderCreateDTO();
            create.setName(node.getName());
            create.setDescription(node.getDescription());

            LocationInfo info = new LocationInfo();
            info.setContainerOid(parent.getContainerOid());
            info.setContainerType(parent.getContainerType());
            info.setCatalogOid(parent.getOid());
            info.setCatalogType(Folder.TYPE);
            info.setContainerModelDefinition(node.getContainerModelDefinition());

            create.setLocationInfo(info);
            create.setModelIcon(node.getModelIcon());
            create.setExtensionContent(node.getExtensionContent());
            if(StringUtils.isBlank(info.getCatalogOid()) || StringUtils.isBlank(info.getContainerOid()) || StringUtils.isBlank(info.getCatalogType())) {
                log.info("文件夹创建时父级数据缺失,文件夹名称:{},父级文件夹名称:{},oid:{},CatalogOid:{},ContainOid:{},CatalogType:{}",
                        create.getName(), parent.getName(), parent.getOid(), parent.getContainerOid(),
                        parent.getContainerType());
            }
            Folder folder = (Folder)pdmFolderService.create(create);
            //拷贝原文件夹的团队角色-用户到新的文件夹。（参考按照模板导入容器，初始化团队cn.jwis.platform.plm.container.service.Impl.TeamHelperImpl.buildTeam）
            Team team = pdmFolderService.getTeam(node.getOid());
            if(team != null) {
                List<TeamRoleWithUser> teamRoleWithUsers = this.teamHelper.fuzzyContent(team.getOid(), null);
                //创建新folder的团队
                Team newFolderTeam = new Team();
                newFolderTeam.setOid(OidGenerator.newOid());
                newFolderTeam.setName("文件夹默认team");
                newFolderTeam = commonAbilityHelper.doCreate(newFolderTeam);
                Assign assign = new Assign();
                assign.setFromOid(folder.getOid());
                assign.setFromType(Folder.TYPE);
                assign.setToOid(newFolderTeam.getOid());
                assign.setToType(Team.TYPE);
                commonAbilityService.createOutRelation(Collections.singletonList(assign));
                //团队绑定角色和用户,同时绑定权限域
                customerTeamHelper.buildTeamByCopyFolder(newFolderTeam, teamRoleWithUsers,folder.getOid(),isCopy);
            }//没有就跳过不需要拷贝
            //刷新缓存
            if (CollectionUtil.isNotEmpty(node.getChildren())) {
                this.createTree(node.getChildren(), folder, isCopy);
            }
        }
    }

    //组装树
    private void assemblyTree(List<Folder> folders, List<FolderTreeNode> treeNodes) {
        Map<String, FolderTreeNode> map = folders.stream().map(item -> {
            FolderTreeNode node = new FolderTreeNode();
            BeanUtil.copyProperties(item, node);
            node.setChildren(new ArrayList<>());
            return node;
        }).collect(Collectors.toMap(FolderTreeNode::getOid, (o) -> o, (o1, o2) -> o1));

        for (FolderTreeNode value : map.values()) {
            if (StringUtils.isBlank(value.getCatalogOid()) || !map.containsKey(value.getCatalogOid())) {
                treeNodes.add(value);
            } else if (map.containsKey(value.getCatalogOid())) {
                map.get(value.getCatalogOid()).getChildren().add(value);
            }
        }
    }

    @Override
    public List<FolderTreeNode> searchFolders(String containerModel, String containerOid, String searchKey) throws JWIException {
        authHelper.fillUserAuthInfo(SessionHelper.getCurrentUser());
        List<FolderTreeNode> tree = folderService.searchFoldersWithPermisson(Container.TYPE, containerModel, containerOid, searchKey);
        this.sortByName(tree);
        return tree;
    }

    //根据名称排序
    private void sortByName(List<FolderTreeNode> tree) {
        for (FolderTreeNode folderTreeNode : tree) {
            if (CollectionUtil.isNotEmpty(folderTreeNode.getChildren())) {
                this.sortByName(folderTreeNode.getChildren());
            }
        }
        Collator cmp = Collator.getInstance(java.util.Locale.CHINA);
        tree.sort(Comparator.comparing(o -> getPinYin(o.getName()), cmp::compare));
    }

    /**
     * 电子件树结构排序
     * @param tree
     */
    private void sortElePartByName(List<FolderCountNode> tree) {
        for (FolderCountNode folderTreeNode : tree) {
            if (CollectionUtil.isNotEmpty(folderTreeNode.getChildren())) {
                this.sortElePartByName(folderTreeNode.getChildren());
            }
        }
        Collator cmp = Collator.getInstance(java.util.Locale.CHINA);
        tree.sort(Comparator.comparing(o -> getPinYin(o.getName()), cmp::compare));
    }

    //获取拼音首字母
    private String getPinYin(String str) {
        StringBuilder initials = new StringBuilder();
        for (char c : str.toCharArray()) {
            String[] pinyinArray = PinyinHelper.toHanyuPinyinStringArray(c);
            if (pinyinArray != null) {
                initials.append(pinyinArray[0].charAt(0));
            } else {
                initials.append(c); // 如果字符不是中文，则直接添加原字符
            }
        }
        return initials.toString();
    }

    public List<FolderCountNode> searchFoldersAndCount(FolderCountDTO dto) throws JWIException {
        authHelper.fillUserAuthInfo(SessionHelper.getCurrentUser());

        String containerModel = dto.getContainerModel();
        String containerOid = dto.getContainerOid();
        String searchKey = dto.getSearchKey();
        List<String> subTypes = dto.getSubTypes();

        List<FolderTreeNode> folderTreeNodes = folderService.searchFoldersWithPermisson(Container.TYPE, containerModel, containerOid, searchKey);
        // 获取文件夹下数量,统计数量时排除ECAD
        subTypes.remove("ECADIteration");
        List<FolderCountNode> folderSubPart = this.findFolderSubPart(folderTreeNodes, subTypes);
        this.sortElePartByName(folderSubPart);
        return folderSubPart;
    }


    public List<FolderCountNode> findFolderSubPart(List<FolderTreeNode> folderTreeNodes, List<String> subTypes) {
        if (CollectionUtil.isEmpty(folderTreeNodes)) {
            return null;
        }
        List<FolderCountNode> folderCountNodes = new ArrayList<>();
        for (FolderTreeNode folderTreeNode : folderTreeNodes) {

            List<String> oids = new ArrayList<>();
            getAllChildrenOids(folderTreeNode, oids);
            Map<String, Long> countMap = folderService.fuzzyFolderSubCountMap(oids, "", subTypes);
            //复制所有子节点
            FolderCountNode countNode = setCountAndChildren(folderTreeNode, countMap);
            //设置所有数量
            setAllChildrenCount(countNode);
            folderCountNodes.add(countNode);
        }

        return folderCountNodes;
    }

    //设置数量和子节点，不带子节点数量
    private FolderCountNode setCountAndChildren(FolderTreeNode treeNode, Map<String, Long> countMap) {
        FolderCountNode folderCountNode = BeanUtil.copyProperties(treeNode, new FolderCountNode());
        folderCountNode.setCount(countMap.get(treeNode.getOid()));
        folderCountNode.setChildren(new ArrayList<>());
        if (CollectionUtil.isNotEmpty(treeNode.getChildren())) {
            for (FolderTreeNode child : treeNode.getChildren()) {
                FolderCountNode node = setCountAndChildren(child, countMap);
                folderCountNode.getChildren().add(node);
            }
        }
        return folderCountNode;
    }

    //计算和数量，带子节点数量
    private Long setAllChildrenCount(FolderCountNode folderCountNode) {
        Long count = Objects.nonNull(folderCountNode.getCount()) ? folderCountNode.getCount() : 0;
        if (CollectionUtil.isNotEmpty(folderCountNode.getChildren())) {
            for (FolderCountNode child : folderCountNode.getChildren()) {
                count += setAllChildrenCount(child);
            }
        }
        folderCountNode.setCount(count);
        return count;
    }

    //获取所有子节点oid
    private void getAllChildrenOids(FolderTreeNode folder, List<String> childrenOids) {
        childrenOids.add(folder.getOid());
        if (CollectionUtil.isNotEmpty(folder.getChildren())) {
            for (FolderTreeNode child : folder.getChildren()) {
                getAllChildrenOids(child, childrenOids);
            }
        }
    }


    //@JWIServiceAudit(buzOid = "${oid}", buzType = "Folder",action = Action.DELETE,content = "删除文件夹")
//    @JWIParam("result")
//    @Override
//    public Long delete(String oid) throws JWIException {
//        Assert.notBlank(oid, "The folder oid can't not be empty!");
//        Assert.isFalse(folderService.hasContain(oid), "Can not delete: Folder is not empty");
//        if (dataDistributionOpen) {
//            Folder folder = (Folder) folderService.findEntity(oid, Folder.TYPE);
//            JSONObject json = new JSONObject();
//            json.put("oid", oid);
//            json.put("type", Folder.TYPE);
//            json.put("containerOid", folder.getContainerOid());
//            dataDistributionService.sendDataToMq(json, "delete", "delete");
//        }
//
//        List<ModelAble> administrativeDomain = commonAbilityService.findIterByFrom(oid, Folder.TYPE, Point.TYPE);
//        if (CollectionUtil.isNotEmpty(administrativeDomain)) {
//            for (ModelAble modelAble : administrativeDomain) {
//                commonAbilityService.deleteByOid(modelAble.getOid(), modelAble.getType());
//            }
//        }
//        return folderService.delete(oid);
//    }

    @Override
    public PageResult<InstanceEntityWithCatalog> fuzzySubPage(FuzzySubPageDTO dto) throws JWIException {
        ValidationUtil.validate(dto);
        FuzzyContainerInstanceExportDTO newDTO =  BeanUtil.copyProperties(dto,new FuzzyContainerInstanceExportDTO());
        String folderOid = newDTO.getFromOid();
        int index = newDTO.getIndex();
        int size = newDTO.getSize();
        List<Folder> folders = folderService.flapFolderTree(folderOid);
        if (CollectionUtil.isEmpty(folders)) {
            return PageResult.init(0, index, size, new LinkedList<>());
        }
        Map<String, String> folderOid2Name = CollectionUtil.splitToMap(folders, Folder::getOid, Folder::getName);
        FuzzyPageVO pageVO = BeanUtil.copyProperties(newDTO, new FuzzyPageVO());
        pageVO.setFolderOids(new ArrayList<>(folderOid2Name.keySet()));
        PageResult<InstanceEntityWithCatalog> result = folderService.fuzzyFolderContentPage(pageVO);
        List<InstanceEntityWithCatalog> rows = result.getRows();
        if (CollectionUtil.isNotEmpty(rows)) {
            rows.stream().forEach(item -> {
                item.setCatalogName(folderOid2Name.get(item.getCatalogOid()));
            });
        }
        return result;
    }

    @Override
    public Folder findFolderByCLS(String clsOid, String containerOid) {
        return folderService.findFolderByCLS(clsOid, containerOid);
    }

    @Override
    public JSONObject findClsByContainer(String containerOid) {
        UserDTO currentUser = SessionHelper.getCurrentUser();
        Folder folder = folderService.searchRootFolderByContainerOid(containerOid, currentUser.getTenantOid());
        Assert.notNull(folder, "folder not exists");
        JSONObject detailByOid = instanceService.findDetailByOid(folder.getOid(), folder.getType());
        Assert.notNull(detailByOid, "detailByOid not exists");
        return detailByOid;
    }

    @Override
    public Map<String, Folder> getAndCreatePath(List<String> folderPath) {
        //查询所有树数据
        List<FolderTreeNode> treeNodes = folderService.searchFoldersWithPermisson(Container.TYPE, null, null, null);

        List<ContainerDetail> containerListDTOS = containerService.searchAllContainer(null);
        //电子件容器不允许修改
        List<String> disabledContainer = containerListDTOS.stream().filter(item -> Objects.equals(item.getTerritory(), "electron")).map(ContainerDetail::getOid).collect(Collectors.toList());

        Map<String, Folder> allPathFolder = new HashMap<>();
        getFolderPath(allPathFolder, treeNodes, "");
        Map<String, Folder> res = new HashMap<>(folderPath.size());

        Set<String> needCreate = new HashSet<>();
        for (String str : folderPath) {
            //优先全匹配，后模糊匹配
            List<String> keys = allPathFolder.containsKey(str) ? Collections.singletonList(str) : allPathFolder.keySet().stream().filter(key -> key.endsWith(str)).collect(Collectors.toList());
            if (keys.size() > 1) {
                throw new JWIException("文件夹位置[" + str + "]匹配到多个文件夹：[" + String.join(",", keys) + "]，请使用全路径来精确匹配");
            }
            if (keys.size() == 1) {
                res.put(str, allPathFolder.get(keys.get(0)));
            } else {
                //未匹配到时
                needCreate.add(str);
            }
        }
        //根据路径创建文件夹
        createFolderByPath(allPathFolder, needCreate, res, disabledContainer);
        return res;
    }

    //根据path创建文件夹
    private void createFolderByPath(Map<String, Folder> allPathFolder, Set<String> paths, Map<String, Folder> res, List<String> disabledContainer) {
        DefaultTransactionDefinition transactionDefinition = new DefaultTransactionDefinition();
        transactionDefinition.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
        TransactionStatus status = transactionManager.getTransaction(transactionDefinition);

        try {
            for (String path : paths) {
                String[] splitPath = path.split("/");
                String baseNode = path.startsWith("/") ? splitPath[1] : splitPath[0];
                Optional<String> optional = allPathFolder.keySet().stream().filter(item ->
                        Objects.equals(item, "/" + baseNode)).findFirst();
                if (!optional.isPresent()) {
                    throw new JWIException("创建文件夹失败，不存在容器：" + baseNode);
                }
                Folder parentNode = allPathFolder.get(optional.get());
                if (disabledContainer.contains(parentNode.getContainerOid())) {
                    throw new JWIException("未找到文件夹路径：[" + path + "]，电子件容器不能自动创建文件夹，请手动创建");
                }
                StringBuilder parentPath = new StringBuilder("/" + baseNode);
                for (int i = path.startsWith("/") ? 2 : 1; i < splitPath.length; i++) {
                    String folderName = splitPath[i];
                    parentPath.append("/").append(folderName);

                    if (!allPathFolder.containsKey(parentPath.toString())) {
                        FolderCreateDTO createDTO = new FolderCreateDTO();
                        LocationInfo info = new LocationInfo();
                        info.setContainerOid(parentNode.getContainerOid());
                        info.setContainerType(parentNode.getContainerType());
                        info.setCatalogOid(parentNode.getOid());
                        info.setCatalogType(parentNode.getType());
                        info.setContainerModelDefinition(parentNode.getContainerModelDefinition());

                        createDTO.setLocationInfo(info);
                        createDTO.setName(folderName);
                        parentNode = this.create(createDTO);
                        allPathFolder.put(parentPath.toString(), parentNode);
                    } else {
                        parentNode = allPathFolder.get(parentPath.toString());
                    }
                }
                res.put(path, path.startsWith("/") ? allPathFolder.get(path) : allPathFolder.get("/" + path));
            }
            transactionManager.commit(status);
        } catch (JWIException e) {
            transactionManager.rollback(status);
            e.printStackTrace();
            throw e;
        }
    }

    @Override
    public Map<String, String> getFolderPath(List<String> containerOids) {
        Map<String, String> result = new HashMap<>();
        for (String containerOid : containerOids) {
            //查询所有树数据
            List<FolderTreeNode> treeNodes = folderService.searchFoldersWithPermisson(Container.TYPE, null, containerOid, null);

            Map<String, Folder> allPathFolder = new HashMap<>();
            getFolderPath(allPathFolder, treeNodes, "");
            allPathFolder.forEach((k, v) -> {
                result.put(v.getOid(), k);
            });
        }
        return result;
    }

//    @Override
//    public Team getAndCreateTeam(String folderOid) {
//        Optional<ModelAble> modelAble = commonAbilityService.findIterByFrom(folderOid, Folder.TYPE, Assign.TYPE, Team.TYPE).stream().findFirst();
//        Team team = null;
//
//        Folder folder = folderService.findByOids(Collections.singletonList(folderOid)).stream().findFirst().orElse(new Folder());
//        ContainerDetail containerDetail = containerService.findDetail(folder.getContainerOid());
//
//        if (modelAble.isPresent()) {
//            //更新名称
//            team = (Team) modelAble.get();
//            team.setName(containerDetail.getName() + "/" + folder.getName());
//            team = commonAbilityHelper.doUpdate(team);
//        } else {
//            team = new Team();
//            team.setOid(OidGenerator.newOid());
//            team.setName(containerDetail.getName() + "/" + folder.getName());
//            team = commonAbilityHelper.doCreate(team);
//            Assign assign = new Assign();
//            assign.setFromOid(folderOid);
//            assign.setFromType(Folder.TYPE);
//            assign.setToType(Team.TYPE);
//            assign.setToOid(team.getOid());
//            commonAbilityService.createOutRelation(Collections.singletonList(assign));
//        }
//        return team;
//    }

    private void getFolderPath(Map<String, Folder> map, List<FolderTreeNode> tree, String parentPath) {
        for (FolderTreeNode folderTreeNode : tree) {
            String path = parentPath + "/" + folderTreeNode.getName();
            map.put(path, folderTreeNode);
            if (CollectionUtil.isNotEmpty(folderTreeNode.getChildren())) {
                this.getFolderPath(map, folderTreeNode.getChildren(), path);
            }
        }
    }

    @Override
    public boolean refreshFolder(String rootFolderOid, String rootClsOid) {
        // 查询出文件集合
        List<Folder> folders = folderService.flapFolderTree(rootFolderOid);

        Folder one = folders.get(0);
        String containerOid = one.getContainerOid();

        List<Folder> deleteFolders = new ArrayList<>();
        List<Folder> updateFolders = new ArrayList<>();

        Map<String, Folder> clsToFolder = new HashMap<>();
        for (Folder folder : folders) {
            FromToFilter filter = new FromToFilter();
            filter.setToFilter(Condition.where("oid").eq(folder.getOid()));
            filter.setToType(folder.getType());
            filter.setFromType(Classification.TYPE);
            filter.setRelFilter(Condition.where("type").eq(Contain.TYPE));
            List<Classification> classifications = jwiCommonService.dynamicQueryFrom(filter, Classification.class);

            if (CollectionUtil.isEmpty(classifications)) {
                deleteFolders.add(folder);
            } else {
                Classification classification = classifications.get(0);
                clsToFolder.put(classification.getOid(), folder);
                String displayName = classification.getDisplayName();
                String iconUri = classification.getIconUri();
                boolean change = false;
                if (!Objects.equals(displayName, folder.getName())) {
                    change = true;
                    folder.setName(displayName);

                }

                if (!Objects.equals(iconUri, folder.getModelIcon())) {
                    change = true;
                    folder.setModelIcon(iconUri);
                }
                if (change) {
                    updateFolders.add(folder);
                }
            }
        }

        if (CollectionUtil.isNotEmpty(deleteFolders)) {
            this.protectiveDelete(deleteFolders, containerOid);
        }

        if (CollectionUtil.isNotEmpty(updateFolders)) {
            folderService.updateEntity(Folder.TYPE, updateFolders);
        }

        List<ClassificationTreeNode> classificationTreeNodes = classificationService.searchTree(rootClsOid, "");
        this.addFolder(classificationTreeNodes, containerOid, null, clsToFolder);

        return true;
    }

    public void addFolder(List<ClassificationTreeNode> classificationTreeNodes, String containerOid, ClassificationTreeNode parentCls, Map<String, Folder> clsToFolder) {
        if (CollectionUtil.isEmpty(classificationTreeNodes)) {
            return;
        }
        for (ClassificationTreeNode classificationTreeNode : classificationTreeNodes) {
            Folder folderByCLS = clsToFolder.get(classificationTreeNode.getOid());
//            if(Objects.isNull(folderByCLS)){
//                folderByCLS = folderService.findFolderByCLS(classificationTreeNode.getOid(), containerOid);
//            }

            if (ObjectUtils.isEmpty(folderByCLS)) {

                Folder parentFolder = clsToFolder.get(parentCls.getOid());
                if (Objects.isNull(parentFolder)) {
                    parentFolder = folderService.findFolderByCLS(parentCls.getOid(), containerOid);
                }

                Assert.notNull(parentFolder, "分类关联的文件夹不存在");

                // 生成文件夹结构
                LocationInfo locationInfo = new LocationInfo();
                locationInfo.setCatalogType(Folder.TYPE);
                locationInfo.setCatalogOid(parentFolder.getOid());
                locationInfo.setContainerType(parentFolder.getContainerType());
                locationInfo.setContainerOid(parentFolder.getContainerOid());
                locationInfo.setContainerModelDefinition("ResourceContainer");

                FolderCreateDTO folderCreateDTO = new FolderCreateDTO();
                ClassificationInfo classificationInfo = BeanUtil.copyProperties(classificationTreeNode, new ClassificationInfo());
                folderCreateDTO.setClassificationInfo(classificationInfo);
                folderCreateDTO.setLocationInfo(locationInfo);
                folderCreateDTO.setName(classificationTreeNode.getDisplayName());
                folderCreateDTO.setDescription(classificationTreeNode.getDescription());
                folderCreateDTO.setModelIcon(classificationTreeNode.getIconUri());

                Folder folder = this.create(folderCreateDTO);
                clsToFolder.put(classificationTreeNode.getOid(), folder);
            }
            List<ClassificationTreeNode> children = classificationTreeNode.getChildren();
            addFolder(children, containerOid, classificationTreeNode, clsToFolder);
        }
    }

    @Override
    public boolean refreshClsFolder(ClsFolderRefreshDTO dto) {
        List<ClassificationTreeNode> classificationTreeNodes = classificationService.searchTree(dto.getClsOid(), "");
        ClassificationTreeNode rootCls = classificationTreeNodes.get(0);
        List<ClassificationTreeNode> clsChildren = rootCls.getChildren();
        Folder rootFolder = (Folder) folderService.findEntity(dto.getRootOid(), Folder.TYPE);

        Container container = (Container) containerService.findDetailEntity(rootFolder.getContainerOid(), Container.TYPE);
        //顶层树名称跟容器名称走
        rootFolder.setDescription(rootCls.getDescription());
        rootFolder.setModelIcon(rootCls.getIconUri());
        rootFolder.setName(container.getName());
        jwiCommonService.update(rootFolder);

        // 生成文件夹结构
        LocationInfo locationInfo = new LocationInfo();
        locationInfo.setCatalogType(Folder.TYPE);
        locationInfo.setCatalogOid(rootFolder.getOid());
        locationInfo.setContainerType(rootFolder.getContainerType());
        locationInfo.setContainerOid(rootFolder.getContainerOid());
        locationInfo.setContainerModelDefinition("ResourceContainer");
        refreshClsFolder(rootFolder, clsChildren, locationInfo);
        return true;
    }

    private void refreshClsFolder(Folder parentFolder, List<ClassificationTreeNode> clsChildren, LocationInfo locationInfo) {
        // 查子项文件夹
        SubFilter subFilter = new SubFilter(Folder.TYPE, parentFolder.getOid(), Contain.TYPE, Folder.TYPE);
        List<Folder> folderChildren = jwiCommonService.dynamicQueryByFrom(subFilter, Folder.class);
        if (CollectionUtil.isEmpty(clsChildren)) {
            protectiveDelete(folderChildren, locationInfo.getContainerOid());
            return;
        }
        // 有可能分类删了，又建个同名的分类，此时还是当成更新处理
        Map<String, Folder> clsToFolderMap = folderChildren.stream().collect(Collectors.toMap(f -> f.getClsDisplayName(), Function.identity()));
        // 以分类树为准，增删改文件夹树
        for (ClassificationTreeNode clsNode : clsChildren) {
            Folder folderChild = clsToFolderMap.remove(clsNode.getDisplayName());
            if (folderChild == null) {
                // 子文件夹不存在，则创建
                FolderCreateDTO dto = new FolderCreateDTO();
                locationInfo.setCatalogOid(parentFolder.getOid());
                dto.setName(clsNode.getDisplayName());
                dto.setDescription(clsNode.getDescription());
                dto.setModelIcon(clsNode.getIconUri());
                dto.setClassificationInfo(BeanUtil.copyProperties(clsNode, new ClassificationInfo()));
                dto.setLocationInfo(locationInfo);
                folderChild = create(dto);

            } else if (!clsNode.getDisplayName().equals(folderChild.getName())
                    || !Objects.equals(clsNode.getIconUri(), folderChild.getModelIcon())
                    || !Objects.equals(clsNode.getOid(), folderChild.getClsOid())) {
                // 子文件夹存在，但名称变化，则修改
                folderChild.setName(clsNode.getDisplayName());
                folderChild.setDescription(clsNode.getDescription());
                folderChild.setModelIcon(clsNode.getIconUri());
                jwiCommonService.update(folderChild);
            }
            refreshClsFolder(folderChild, clsNode.getChildren(), locationInfo);
        }
        // 子文件夹有多余,则不包含物料时删除
        if (MapUtil.isNotEmpty(clsToFolderMap)) {
            List<Folder> deleteFolders = new ArrayList<>();
            deleteFolders.addAll(clsToFolderMap.values());
            protectiveDelete(deleteFolders, locationInfo.getContainerOid());
        }
    }

    // 保护性删除，如果文件夹内有entity则不处理
    private void protectiveDelete(List<Folder> folderChildren, String containerOid) {
        for (Folder folder : folderChildren) {
            List<FolderTreeNode> folderTreeNodes = folderService.searchFoldersWithPermisson(Container.TYPE,
                    "ResourceContainer", containerOid, "");
            // 获取文件夹下数量,统计数量时排除ECAD
            List<String> subTypes = Arrays.asList("PartIteration", "ECADIteration");
            String oid = folder.getOid();
            List<Folder> folders = folderService.flapFolderTree(oid);
            List<String> oids = CollectionUtil.mapToList(folders, (item) -> item.getOid());
            Long count = folderService.fuzzyFolderSubCount(oids, "", subTypes);
            if (count == 0) {
                folderService.delete(oid);
            }
        }
    }

    @Override
    public boolean copyFolder(String sourceContainerType, String sourceContainerOid, String targetContainerType, String targetContainerOid) {
        List<FolderTreeNode> sourceFolderTree = folderService.searchFolders(sourceContainerType, sourceContainerOid, null);
        FolderTreeNode sourceRoot = sourceFolderTree.get(0);
        List<FolderTreeNode> targetFolderTree = folderService.searchFolders(targetContainerType, targetContainerOid, null);
        FolderTreeNode targetRoot = targetFolderTree.get(0);
        ModelPlus modelDetail = modelHelper.getModelDetail(Folder.TYPE);
        recursioinCopyFolder(sourceRoot, targetRoot, modelDetail);
        return true;
    }

    //@JWIServiceAudit(buzOid = "${result.oid}", bizName = "${result.number}",buzType = "${dto.modelDefinition}",action = Action.ADD,content = "重命名文件夹")
    @JWIParam("result")
    @Override
    public Folder rename(RenameDTO dto) {
        ValidationUtil.validate(dto);
        String oid = dto.getOid();
        Folder byOid = (Folder) folderService.findEntity(oid, Folder.TYPE);

        // 重名校验
        String catalogOid = byOid.getCatalogOid();
        String catalogType = byOid.getCatalogType();
        Assert.isTrue(Folder.TYPE.equals(catalogType), "Can not rename root folder");
        String name = dto.getName();
        Assert.notBlank(catalogOid, "catalogOid can not be null");
        SubFilter subFilter = new SubFilter(byOid.getCatalogType(), catalogOid, Contain.TYPE, Folder.TYPE);
        subFilter.setFilter(Condition.where("name").eq(name).and(Condition.where("oid").neq(oid)));
        List<Folder> folders = folderService.dynamicQueryByFrom(subFilter);
        Assert.isEmpty(folders, "文件夹名称相同层级已存在:" + dto.getName());
        Folder rename = folderService.rename(oid, name);

        // 重命名管理域
        AdministrativeDomain administrativeDomain = administrativeDomainService.findOneByFrom(Folder.TYPE, oid, Point.TYPE);
        if (administrativeDomain != null) {
            administrativeDomain.setName(name);
            administrativeDomainService.update(administrativeDomain);
        }
        //重命名团队名称


        if (dataDistributionOpen) {
            JSONObject json = new JSONObject();
            json.put("oid", oid);
            json.put("type", Folder.TYPE);
            json.put("containerOid", byOid.getContainerOid());
            json.put("name", name);
            dataDistributionService.sendDataToMq(json, "change", "change");
        }

        return rename;
    }

    @Override
    public List<JSONObject> searchSecData(String folderOid, String searchKey) {
        return folderService.searchSecData(folderOid, searchKey);
    }

    @Override
    public List<Folder> searchRootFolder(String searchKey) {
        authHelper.fillUserAuthInfo(SessionHelper.getCurrentUser());
        return folderService.searchRootFolder(Container.TYPE, searchKey);
    }

    // 在指定位置构建文件夹树
    @Override
    public void buildFolderTree(LocationInfo locationInfo, List<FolderForExport> folderTree) {
        if (CollectionUtil.isEmpty(folderTree)) {
            return;
        }
        FolderCreateDTO dto = new FolderCreateDTO();
        List<FolderForExport> children = null;
        LocationInfo subInfo = BeanUtil.copyProperties(locationInfo, new LocationInfo());
        for (FolderForExport folderForExport : folderTree) {
            BeanUtil.copyProperties(folderForExport, dto);
            dto.setLocationInfo(locationInfo);
            Folder folder = create(dto);
            children = folderForExport.getChildren();
            subInfo.setCatalogOid(folder.getOid());
            buildFolderTree(subInfo, children);
        }
    }

    @Override
    public Folder searchRootFolderByContainerOid(String containerOid, String tenantOid) {
        return folderService.searchRootFolderByContainerOid(containerOid, tenantOid);
    }

    private void recursioinCopyFolder(FolderTreeNode source, Folder target, ModelPlus modelDetail) {
        List<FolderTreeNode> children = source.getChildren();
        if (CollectionUtil.isEmpty(children)) {
            return;
        }
        String targetOid = target.getOid();
        String containerType = target.getContainerType();
        String containerOid = target.getContainerOid();
        Folder copy = null;
        for (FolderTreeNode child : children) {
            copy = BeanUtil.copyProperties(child, new Folder());
            copy.setOid(OidGenerator.newOid());
            copy.setCatalogOid(targetOid);
            copy.setContainerOid(containerOid);
            copy.setContainerType(containerType);
            commonAbilityHelper.doCreate(copy, modelDetail);
            recursioinCopyFolder(child, copy, modelDetail);
        }

    }


    @Override
    @DataDistribution(dataOpt = "create")
    public Folder createFolderRootNode(Container container) {
        LocationInfo locationInfo = new LocationInfo();
        locationInfo.setContainerOid(container.getOid());
        locationInfo.setContainerType(Container.TYPE);
        locationInfo.setContainerModelDefinition(container.getModelDefinition());
        locationInfo.setCatalogOid(container.getOid());
        locationInfo.setCatalogType(Container.TYPE);

        FolderCreateDTO folder = new FolderCreateDTO();
        folder.setName(container.getName());
        folder.setLocationInfo(locationInfo);
        return create(folder);
    }

    @Override
    public Folder findFolderByPdmOid(String oid) {
        return folderService.findFolderByPdmOid(oid);
    }

    @Override
    @DataDistribution(dataOpt = "create")
    public Folder createFolderRootNode(Container container, ClassificationInfo classificationInfo) {
        LocationInfo locationInfo = new LocationInfo();
        locationInfo.setContainerOid(container.getOid());
        locationInfo.setContainerType(Container.TYPE);
        locationInfo.setContainerModelDefinition(container.getModelDefinition());
        locationInfo.setCatalogOid(container.getOid());
        locationInfo.setCatalogType(Container.TYPE);

        FolderCreateDTO folder = new FolderCreateDTO();
        folder.setName(container.getName());
        folder.setLocationInfo(locationInfo);
        folder.setClassificationInfo(classificationInfo);
        FolderLinkClassify folderLinkClassify = (FolderLinkClassify) classificationInfo;
        folder.setModelIcon(folderLinkClassify.getModelIcon());
        return create(folder);
    }

    public void export(HttpServletResponse response, FuzzyExportDTO dto) throws IOException {
        List<InstanceEntityWithCatalog> datas = dto.getDatas();
        FuzzySubPageDTO fdto = dto.getDto();
        if(fdto==null) {
            return;
        }
        List<InstanceEntityWithCatalog> res = new ArrayList<>();
        if(CollectionUtil.isEmpty(datas)) {
           datas = queryExportData(fdto);
           if(CollectionUtil.isNotEmpty(datas)) {
               Map<String,List<String>> dataOidMap = CollectionUtil.splitToMapList(datas,InstanceEntityWithCatalog::getType,item->item.getOid());
               dataOidMap.forEach((key,value)->{
                  List<JSONObject> list = instanceService.findDetailByOid(value,key);
                  if(CollectionUtil.isNotEmpty(list)) {
                      res.addAll(JSON.parseArray(JSONArray.toJSONString(list),InstanceEntityWithCatalog.class));
                  }
               });
           }
        } else {
            Map<String,List<String>> dataOidMap = CollectionUtil.splitToMapList(datas,InstanceEntityWithCatalog::getType,item->item.getOid());
            dataOidMap.forEach((key,value)->{
                List<JSONObject> list = instanceService.findDetailByOid(value,key);
                if(CollectionUtil.isNotEmpty(list)) {
                    res.addAll(JSON.parseArray(JSONArray.toJSONString(list),InstanceEntityWithCatalog.class));
                }
            });
            ///datas = datas!=null?datas.stream().filter(item->oids.contains(item.getOid())).collect(Collectors.toList()) : null;
        }
        if(CollectionUtil.isNotEmpty(res)) {
            modelExcelExportService.export(response,res);
        }
    }

    private List<InstanceEntityWithCatalog> queryExportData(FuzzySubPageDTO fdto) {
        String folderOid = fdto.getFromOid();
        List<Folder> folders = folderService.flapFolderTree(folderOid);
        if (CollectionUtil.isEmpty(folders)) {
            return null;
        }
        List<String> folderOids = CollectionUtil.mapToList(folders,Folder::getOid);
        FuzzyPageVO pageVO = BeanUtil.copyProperties(fdto, new FuzzyPageVO());
        pageVO.setFolderOids(folderOids);
        pageVO.setSize(0);
        return folderService.fuzzyFolderContent(pageVO);
    }

    public void teamCreate(PermApplyDTO dto) {

        InstanceEntity instance = instanceService.findInstance(dto.getOid(), DocumentIteration.TYPE);
        log.info("权限申请instance信息:{}", JSONUtil.toJsonStr(instance));
        Team team = pdmFolderService.getInstanceTeam(instance);
        log.info("权限申请team信息:{}", JSONUtil.toJsonStr(team));
        String instanceOid = instance.getOid();
        AdministrativeDomain domain = domainService.searchByContainerOid(instanceOid);
        if (null == domain) {
            AdministrativeDomain administrativeDomain = new AdministrativeDomain();
            String administrativeDomainOid = OidGenerator.newOid();
            administrativeDomain.setOid(administrativeDomainOid);
            administrativeDomain.setName(instance.getName());
            administrativeDomain.setDescription(instance.getDescription());
            administrativeDomain.setContainerModelType(instance.getType());
            administrativeDomain.setContainerOid(instanceOid);
            AdministrativeDomain administrativeDomain1 = administrativeDomainService.create(administrativeDomain);
            log.info("创建结果:{}", JSONUtil.toJsonStr(administrativeDomain1));
            List<RelationAble> rels = new ArrayList<>();
            Point relatedTo = new Point(DocumentIteration.TYPE, instanceOid, AdministrativeDomain.TYPE, administrativeDomainOid);
            rels.add(relatedTo);
            // 父节点管理域关联 自身管理域
            AdministrativeDomain oneByFrom = administrativeDomainService.findOneByFrom(instance.getCatalogType(), instance.getCatalogOid(), Point.TYPE);
            if (oneByFrom!=null){
                rels.add(new Inherit(AdministrativeDomain.TYPE, oneByFrom.getOid(), AdministrativeDomain.TYPE, administrativeDomainOid));
            }
            Long interRelation = commonAbilityService.createInterRelation(rels);

            log.info("rels创建结果：" + interRelation);
        }
        SearchKeywordPageDTO<Object> objectSearchKeywordPageDTO = new SearchKeywordPageDTO<>();
        objectSearchKeywordPageDTO.setContainerOid(team.getTenantOid());
        objectSearchKeywordPageDTO.setContainerType("Tenant");
        objectSearchKeywordPageDTO.setKeyword("");
        objectSearchKeywordPageDTO.setPageSize(100);
        objectSearchKeywordPageDTO.setPageNum(1);
        objectSearchKeywordPageDTO = this.roleHelper.buildSearchKeywordPage(objectSearchKeywordPageDTO);
        List list = this.searchHelper.keywordPage(objectSearchKeywordPageDTO);
        log.info("权限申请list信息:{}", JSONUtil.toJsonStr(list));

        // 将Team 和角色绑定
        BindRole bindRole = new BindRole();
        bindRole.setTeamOid(team.getOid());
        List<TeamRole> teamRoleList = new ArrayList<>();

        // 遍历 list 并筛选出 displayName 为“访客”的对象
        list.stream()
                .filter(Objects::nonNull) // 过滤掉 null
                .filter(obj -> {
                    Map<String, Object> map = cn.hutool.core.bean.BeanUtil.beanToMap(obj);
                    return "访客".equals(map.get("displayName")); // 判断 displayName 是否为“访客”
                })
                .forEach(visitorObj -> {
                    // 将筛选出的对象转换为 TeamRole 并添加到 teamRoleList
                    cn.hutool.json.JSONObject jsonObject = JSONUtil.parseObj(visitorObj);
                    TeamRole teamRole = new TeamRole();
                    cn.hutool.core.bean.BeanUtil.copyProperties(jsonObject,teamRole);
                    teamRoleList.add(teamRole);
                });

        bindRole.setContainerOid(instance.getContainerOid());
        bindRole.setTeamRoleList(teamRoleList);
        this.teamHelper.bindRole(bindRole);

        // 记录日志
        log.info("添加到 teamRoleList 的访客角色数量: {}", teamRoleList.size());
    }


    private void initUser(){
        /*UserDTO userDTO = new UserDTO();
        userDTO.setAccount(team.getCreateBy());
        userDTO.setTenantOid(team.getTenantOid());
        this.authHelper.fillUserAuthInfo(userDTO);
        String accessToken = SessionHelper.getAccessToken();
        SessionHelper.setAccessToken(accessToken);
        SessionHelper.addCurrentUser(userDTO);*/
   }

}
