package cn.jwis.product.pdm.customer.service.delegate;

import cn.jwis.framework.base.dto.UserDTO;
import cn.jwis.framework.base.response.PageResult;
import cn.jwis.framework.base.util.CollectionUtil;
import cn.jwis.framework.base.util.MapUtil;
import cn.jwis.framework.base.util.SpringContextUtil;
import cn.jwis.framework.base.web.session.SessionHelper;
import cn.jwis.framework.database.core.entity.EntityPageFilter;
import cn.jwis.framework.database.core.query.dynamic.Condition;
import cn.jwis.framework.database.core.query.dynamic.Order;
import cn.jwis.platform.plm.account.user.service.UserHelper;
import cn.jwis.platform.plm.foundation.common.response.InstanceEntity;
import cn.jwis.platform.plm.workflow.engine.order.entity.ProcessOrder;
import cn.jwis.platform.plm.workflow.engine.service.interf.ProcessOrderHelper;
import cn.jwis.platform.plm.workflow.engine.workflow.service.interf.ProcessModelDomainService;
import cn.jwis.product.pdm.customer.service.dto.U9InventoryResponse;
import cn.jwis.product.pdm.customer.service.dto.U9Transit;
import cn.jwis.product.pdm.customer.service.impl.DingTalkServiceImpl;
import cn.jwis.product.pdm.customer.service.interf.DingTalkService;
import cn.jwis.product.pdm.customer.service.interf.IntegrationMonitorService;
import cn.jwis.product.pdm.partbom.part.entity.PartIteration;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.RuntimeService;
import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.JavaDelegate;
import org.activiti.engine.impl.persistence.entity.VariableInstance;
import org.activiti.engine.repository.Model;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 工作流执行代理：收集U9单据的责任人，用于PDM流程的通知
 * @date 2023/9/11 11:44
 * @Email <EMAIL>
 */
@Slf4j
public class CollectionU9NotifierDelegate implements JavaDelegate {

    @Override
    public void execute(DelegateExecution execution) {
        String processInstanceId = execution.getProcessInstanceId();
        log.info("CollectionU9NotifierDelegate execute procId={}", processInstanceId);
        RuntimeService runtimeService = SpringContextUtil.getBean(RuntimeService.class);
        initContext(runtimeService,execution.getId());
        try {
            // 获取原通知人
            Object notifiers = runtimeService.getVariable(execution.getId(),"cn_jwis_notifier");
            List<String> accounts = new ArrayList<>();
            if(!ObjectUtils.isEmpty(notifiers)){
                if(notifiers instanceof Collection){
                    accounts.addAll((Collection<? extends String>) notifiers);
                }else{
                    accounts.add(notifiers.toString());
                }
            }
            log.info("CollectionU9NotifierDelegate origin accounts={}", JSON.toJSONString(accounts));
            ProcessOrder processOrder = getProcessOrder(execution);
            ProcessOrderHelper processOrderHelper = (ProcessOrderHelper) SpringContextUtil.getBean("processOrderHelperImpl");
            List<InstanceEntity> instanceEntityList = processOrderHelper.findBizObject(processOrder.getOid());
            // 增加u9通知人
            List<String> needU9 = Arrays.asList("cn_jwis_ECR","cn_jwis_ECA");
            EntityPageFilter entityPageFilter = new EntityPageFilter();
            entityPageFilter.setFilter(Condition.where("id").eq(processOrder.getProcessModelId()));
            entityPageFilter.setIndex(1);
            entityPageFilter.setSize(20);
            ProcessModelDomainService processModelDomainService = (ProcessModelDomainService) SpringContextUtil.getBean("processModelDomainServiceImpl");
            PageResult<Model> modelPageResult = processModelDomainService.dynamicQueryLatestPage(entityPageFilter);
            String modelKey = modelPageResult.getRows().get(0).getKey();
            if(needU9.contains(modelKey)) {
                List<String> numbers = instanceEntityList.stream()
                        .filter(i -> PartIteration.TYPE.equals(i.getType()))
                        .map(i -> i.getNumber()).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(numbers)) {
                    IntegrationMonitorService integrationService = (IntegrationMonitorService) SpringContextUtil.getBean("integrationMonitorServiceImpl");
                    U9InventoryResponse response = integrationService.queryInventoryFromU9(String.join(",", numbers));
                    if (CollectionUtil.isNotEmpty(response.getTransit())) {
                        List<U9Transit> transit = response.getTransit();
                        List<String> u9Creator = transit.stream().map(t -> t.getCreatedBy()).collect(Collectors.toList());
                        accounts.addAll(u9Creator);
                    }
                }
                log.info("CollectionU9NotifierDelegate new accounts={}", JSON.toJSONString(accounts));
                runtimeService.setVariable(execution.getId(), "cn_jwis_notifier", accounts);
            }
            // 发布通知
            DingTalkService dingTalkService = (DingTalkServiceImpl) SpringContextUtil.getBean("dingTalkServiceImpl");
            dingTalkService.createNoticeTask(accounts,processOrder,instanceEntityList);
        } catch (Exception e) {
            log.error("CollectionU9NotifierDelegate execute error==>", e);
        }
    }

    private void initContext(RuntimeService runtimeService, String id) {
        Object owner = runtimeService.getVariable(id,"owner");
        UserHelper userHelper = (UserHelper)SpringContextUtil.getBean("defaultUserHelperImpl");
        UserDTO user = userHelper.findByAccount(owner.toString());
        Object tenantOid = runtimeService.getVariable(id,"tenantOid");
        user.setAccount(owner.toString());
        user.setOid(owner.toString());
        user.setTenantOid(tenantOid.toString());
        SessionHelper.addCurrentUser(user);
    }

    private ProcessOrder getProcessOrder(DelegateExecution delegateTask) {
        ProcessOrder processOrder = null;
        Map<String, VariableInstance> variableInstances = delegateTask.getVariableInstances();
        ProcessOrderHelper processOrderHelper = (ProcessOrderHelper) SpringContextUtil.getBean("processOrderHelperImpl");
        if (MapUtil.isEmpty(variableInstances)){
            String processInstanceId = delegateTask.getProcessInstanceId();
            processOrder = processOrderHelper.findByProcessInstanceId(processInstanceId);
        }else {
            VariableInstance processOrderOid = variableInstances.get("processOrderOid");
            if (processOrderOid == null){
                String processInstanceId = delegateTask.getProcessInstanceId();
                processOrder = processOrderHelper.findByProcessInstanceId(processInstanceId);
            }else {
                String processOrderOidStr = processOrderOid.getValue().toString();
                processOrder = processOrderHelper.findByOid(processOrderOidStr);
            }
        }
        return processOrder;
    }

}
