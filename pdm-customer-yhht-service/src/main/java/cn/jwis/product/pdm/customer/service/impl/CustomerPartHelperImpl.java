package cn.jwis.product.pdm.customer.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.jwis.audit.annotation.JWIParam;
import cn.jwis.audit.annotation.JWIServiceAudit;
import cn.jwis.audit.enums.Action;
import cn.jwis.framework.base.bean.util.BeanUtil;
import cn.jwis.framework.base.domain.able.RelationAble;
import cn.jwis.framework.base.domain.able.info.ModelInfo;
import cn.jwis.framework.base.dto.UserDTO;
import cn.jwis.framework.base.exception.JWIException;
import cn.jwis.framework.base.exception.JWIServiceException;
import cn.jwis.framework.base.util.*;
import cn.jwis.framework.base.web.session.SessionHelper;
import cn.jwis.framework.database.core.entity.CopyRel;
import cn.jwis.framework.database.core.query.dynamic.Condition;
import cn.jwis.platform.plm.datadistribution.dto.DataDistributionDTO;
import cn.jwis.platform.plm.dataengine.common.JWICommonService;
import cn.jwis.platform.plm.foundation.classification.able.info.ClassificationInfo;
import cn.jwis.platform.plm.foundation.classification.entity.Classification;
import cn.jwis.platform.plm.foundation.classification.service.ClassificationService;
import cn.jwis.platform.plm.foundation.common.dto.RelatedFuzzyDTO;
import cn.jwis.platform.plm.foundation.common.response.InstanceEntity;
import cn.jwis.platform.plm.foundation.common.service.CommonAbilityHelper;
import cn.jwis.platform.plm.foundation.common.service.CommonAbilityService;
import cn.jwis.platform.plm.foundation.common.service.InstanceHelper;
import cn.jwis.platform.plm.foundation.lifecycle.entity.LifecycleTemplate;
import cn.jwis.platform.plm.foundation.lifecycle.service.LifecycleTemplateService;
import cn.jwis.platform.plm.foundation.relationship.Copy;
import cn.jwis.platform.plm.foundation.relationship.Reference;
import cn.jwis.platform.plm.foundation.relationship.Use;
import cn.jwis.platform.plm.foundation.versionrule.able.LockAble;
import cn.jwis.platform.plm.foundation.versionrule.able.info.LockInfo;
import cn.jwis.platform.plm.foundation.versionrule.service.VersionRuleHelper;
import cn.jwis.platform.plm.permission.helper.PermissionHelper;
import cn.jwis.platform.plm.permission.permission.enums.PermissionKeyEnum;
import cn.jwis.platform.plm.sysconfig.entity.ConfigItem;
import cn.jwis.platform.plm.sysconfig.preferences.PreferencesService;
import cn.jwis.platform.plm.sysconfig.preferences.helper.SysConfigHelper;
import cn.jwis.product.pdm.customer.repo.neo4j.CustomerPartIterationRepoImpl;
import cn.jwis.product.pdm.customer.service.dto.CustomerPartLinkDTO;
import cn.jwis.product.pdm.customer.service.excel.CustomerPartLinkExcelListener;
import cn.jwis.product.pdm.customer.service.interf.PartScreenAutoHelper;
import cn.jwis.product.pdm.partbom.part.able.info.ViewInfo;
import cn.jwis.product.pdm.partbom.part.dto.*;
import cn.jwis.product.pdm.partbom.part.dto.effectivity.EffectivityCreateDTO;
import cn.jwis.product.pdm.partbom.part.entity.Effectivity;
import cn.jwis.product.pdm.partbom.part.entity.PartIteration;
import cn.jwis.product.pdm.partbom.part.service.*;
import cn.jwis.product.pdm.partbom.sysconfig.PartSysConfigRemote;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.text.MessageFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 重写DefaultPartHelperImpl
 * @date 2023/8/16 9:38
 * @Email <EMAIL>
 */
@Service
@Primary
@Transactional
@Slf4j
public class CustomerPartHelperImpl extends DefaultPartHelperImpl {

    private List<String> checkOutExcludeTypes = Arrays.asList("IMPACT", "REVIEW_FOR", "LOCAL_SUBSTITUTE", "ATTRIBUTE", "GENERATE", "COPY", "USE");


    @Autowired
    PartService partService;

    @Autowired
    PartHelper partHelper;

    @Autowired
    CommonAbilityHelper commonAbilityHelper;

    @Autowired
    CommonAbilityService commonAbilityService;

    @Resource
    private EffectivityService effectivityService;

    @Autowired
    VersionRuleHelper versionRuleHelper;

    @Resource
    private EffectivityHelper effectivityHelper;

    @Autowired
    LifecycleTemplateService lifecycleTemplateService;

    @Value("${revise.default.status:Design}")
    private String reviseDefaultStatus;

    @Autowired
    ClassificationService classificationService;

    @Autowired
    PreferencesService preferencesService;

    @Autowired
    CustomerCommonAbilityHelperImpl customerCommonAbilityHelper;

    @Resource
    TriggerAuditServiceImpl triggerAuditService;

    @Resource
    JWICommonService jwiCommonService;

    @Value("${part.autoScreen:false}")
    private Boolean autoScreen;
    @Autowired
    private CustomerPartIterationRepoImpl customerPartIterationRepo;

    static String Identification_suffix = "_Identity_Rule";
    static String Default_Identity_Rule = "Default_Identity_Rule";


    @JWIServiceAudit(
            buzOid = "${result.oid}",
            bizName = "${result.name}",
            bizNumber = "${result.number}",
            bizContainerOid = "${result.containerOid}",
            buzType = "${result.type}",
            action = Action.ADD,
            content = "创建Part,对象的名称为：${result.name}"
    )
    @JWIParam("result")
    @Override
    public PartIteration create(PartCreateDTO dto) throws JWIException {
        return create(dto,false);
    }

    @Override
    public List<SimplePartBOMNode> findSimpleUseTree(String parentOid, String rootOid, Integer maxLevel) throws JWIException {
        Assert.notBlank(rootOid, "rootOid can not be null");
        if (maxLevel != null && maxLevel < 1) {
            maxLevel = 1;
        }

        String[] rule = this.getRule("PartIteration");
        List<SimplePartBOMNode> simpleUseTree = this.customerPartIterationRepo.findUseTree(parentOid, rootOid, maxLevel, SimplePartBOMNode.class, rule);
        sortByLineNumber(simpleUseTree);
        log.info("findSimpleUseTree:{}", JSON.toJSONString(simpleUseTree));
        return simpleUseTree;
    }

    public List<SimplePartBOMNode> fuzzySimpleUseTree(String parentOid, String rootOid, Integer maxLevel, String searchKey) throws JWIException {
        Assert.notBlank(rootOid, "rootOid can not be null");
        if (maxLevel != null && maxLevel < 1) {
            maxLevel = 1;
        }

        Condition condition = null;
        if (StringUtil.isNotBlank(searchKey)) {
            condition = this.searchCondition(searchKey);
        }

        return this.customerPartIterationRepo.fuzzyUseTree(parentOid, rootOid, maxLevel, condition, SimplePartBOMNode.class);
    }



    private String[] getRule(String type) {
        String key = type + Identification_suffix;
        String rule = StringUtils.isEmpty(SysConfigHelper.queryConfigByCode(key)) ? SysConfigHelper.queryConfigByCode(Default_Identity_Rule) : SysConfigHelper.queryConfigByCode(key);
        if (StringUtil.isEmpty(rule)) {
            throw new JWIServiceException(MessageFormat.format("请配置显示规则  {0}", key));
        } else {
            String[] split = rule.split("[;]");
            return split;
        }
    }

    /**
     * 根据Use扩展属性中的行号进行排序（升序），并递归对子节点进行排序。
     */
    private void sortByLineNumber(List<SimplePartBOMNode> nodes) {
        if (nodes == null || nodes.isEmpty()) {
            return;
        }

        nodes.sort(Comparator.comparingInt(node -> {
            JSONObject extension = node.getUse() != null ? node.getUse().getExtensionContent() : null;
            if (extension != null && extension.containsKey("lineNumber")) {
                Object lineNumberObj = extension.get("lineNumber");
                try {
                    return lineNumberObj != null ? Integer.parseInt(lineNumberObj.toString()) : Integer.MAX_VALUE;
                } catch (NumberFormatException e) {
                    return Integer.MAX_VALUE; // 非法数字，当作没有行号处理
                }
            }
            return Integer.MAX_VALUE; // 没有行号，排最后
        }));

        // 递归对子节点排序
        for (SimplePartBOMNode node : nodes) {
            sortByLineNumber(node.getChildren());
        }
    }

    public PartIteration create(PartCreateDTO dto,boolean fromWeb) throws JWIException {
        // 如果未指定小类，则使用默认小类
        if(StringUtil.isBlank(dto.getModelDefinition()) ||  "Part".equals(dto.getModelDefinition())) {
            // 部件发布流程中，检查部件子项是不是已发放，或者子项也在此次发布流程中
            ConfigItem item = preferencesService.queryConfigValue("partDefaultModelDefinition");
            if (item != null && StringUtil.isNotBlank(item.getValue())) {
                dto.setModelDefinition(item.getValue());
            }
        }
        // 如果未指定分类，则使用默认分类
        ClassificationInfo clsInfo = dto.getClassificationInfo();
        if (clsInfo == null) {
            addTempCls(dto);
        }

        if(clsInfo != null && "RM03元器件".equals(clsInfo.getDisplayName())){
            throw new JWIException("RM03元器件 不是最小分类！");
        }

        // 计量单位 默认值个
        if(StringUtil.isBlank(dto.getDefaultUnit())) {
            dto.setDefaultUnit("个");
        }
        // 料品形态 默认值采购件
        if(StringUtil.isBlank(dto.getSource())) {
            dto.setSource("采购件");
        }
        String catalogOid = dto.getLocationInfo().getCatalogOid();
        String catalogType = dto.getLocationInfo().getCatalogType();
        Assert.notNull(this.partService.findEntity(catalogOid, catalogType), "catalog does not exists");
        ValidationUtil.validate(dto);
        PartIteration partIteration = BeanUtil.copyProperties(dto, new PartIteration());
        this.partService.setLocation(partIteration, dto.getLocationInfo());
        clsInfo = dto.getClassificationInfo();
        if (clsInfo != null) {
            this.partService.setClassification(partIteration, clsInfo);
        }

        ViewInfo viewInfo = dto.getViewInfo();
        this.partService.setView(partIteration, viewInfo);
        PartIteration result = this.commonAbilityHelper.doCreate(partIteration, fromWeb);
        DataDistributionDTO dataDistributionDTO = new DataDistributionDTO(PartIteration.TYPE, result.getOid(), "create",
                SessionHelper.getAccessToken(), SessionHelper.getCurrentUser(), SessionHelper.getAppId());
        dataDistributionDTO.setBody(result);
        this.partHelper.dataDistribution(dataDistributionDTO);
        List<String> partOids = Arrays.asList(result.getOid());
        this.dealPartAndCadLink(partOids, "create");
        Map<PartIteration, JSONObject> ecadFileMap = new HashMap();
        ecadFileMap.put(result, dto.getExtensionContent());
        this.dealECADData(ecadFileMap);

        JSONObject clsJson;
        if(autoScreen && (clsJson = result.getClsProperty()) != null)
            createPartScreen(dto, result, clsJson.getString("cn_jwis_sxzldj"),fromWeb);

        return result;
    }

    @JWIParam("result")
    public String rename(RenameDTO dto) throws JWIException {
        PartIteration byOid = this.partService.findByOid(dto.getOid());
        String result = super.rename(dto);
        triggerAuditService.partRename(byOid, dto.getName());
        return result;
    }

    void createPartScreen(PartCreateDTO dto, PartIteration part, String cn_jwis_sxzldj, boolean fromWeb){
        if(cn_jwis_sxzldj != null && !"否".equals(cn_jwis_sxzldj)){
            BatchCopyPartDTO batchCopyPartDTO = new BatchCopyPartDTO();
            batchCopyPartDTO.setCurrentStage("");
            batchCopyPartDTO.setLinkRelationship(Collections.emptyList());
            batchCopyPartDTO.setLocationInfo(dto.getLocationInfo());

            BatchCopyPartDocumentBatchItemDTO batchCopyPartDocumentBatchItemDTO = new BatchCopyPartDocumentBatchItemDTO();
            batchCopyPartDocumentBatchItemDTO.setSourceOid(part.getOid());

            batchCopyPartDTO.setSourceOids(Arrays.asList(part.getOid()));
            batchCopyPartDTO.setUnLinkRelationship(Collections.emptyList());

            if("筛选级".equals(cn_jwis_sxzldj)){
                batchCopyPartDTO.setType("screen");
                batchCopyPartDocumentBatchItemDTO.setNewName(part.getName() + "（筛选级）");
                batchCopyPartDTO.setNames(Arrays.asList(batchCopyPartDocumentBatchItemDTO));
                partScreenAutoHelper.partBatchCopy(batchCopyPartDTO,fromWeb);
            }else if("成型".equals(cn_jwis_sxzldj)){
                batchCopyPartDTO.setType("forming");
                batchCopyPartDocumentBatchItemDTO.setNewName(part.getName() + "（未成型）");
                batchCopyPartDTO.setNames(Arrays.asList(batchCopyPartDocumentBatchItemDTO));
                partScreenAutoHelper.partBatchCopy(batchCopyPartDTO,fromWeb);
            }

        }
    }

    @Autowired
    PartScreenAutoHelper partScreenAutoHelper;

    @Autowired
    private InstanceHelper instanceHelper;

    @Autowired
    private PartSysConfigRemote partSysConfigRemote;

    @Override
    public void confirmChange(String oid, String changeOid) {
        log.info("开始进行修订，入参：" + oid + "&&&" + changeOid);
        this.partService.updateLatest(oid, false);
        String entityOid = UUID.randomUUID().toString();
        PartIteration partIteration = this.partService.findDetailByOid(changeOid);
        partIteration.setLatest(true);
        ModelInfo modelInfo = (ModelInfo)BeanUtil.clone(partIteration, ModelInfo.class);
        List<String> nextVersion = this.versionRuleHelper.getNextVersion(modelInfo);
        this.partService.nextVersion(partIteration, (String)nextVersion.get(0));
        partIteration.setOid(entityOid);
        // 处理升级后的状态
        //this.partService.initLifecycleStatus(partIteration);
        String lifecycleOid = partIteration.getLifecycleOid();
        LifecycleTemplate lifecycleTemplate = lifecycleTemplateService.findByOid(lifecycleOid);
        partIteration.setLifecycleStatus(getReviseDefaultStatus(lifecycleTemplate));
        this.partService.createCopy(partIteration);
        CopyRel copyRel = new CopyRel();
        copyRel.setSourceType("PartIteration");
        copyRel.setSourceOid(changeOid);
        copyRel.setTargetType("PartIteration");
        copyRel.setTargetOid(entityOid);
        List<String> excludeTypes = Arrays.asList("USE");
        copyRel.setRelationTypes(excludeTypes);
        this.commonAbilityService.copyOuterRelIncludes(copyRel);
        List<PartIteration> list = this.partService.findAddPart(changeOid);
        list.forEach((n) -> {
            this.partService.updateLatest(n.getOid(), true);
            this.partService.createContain(n);
        });
        this.partService.copyInterUseForConfirmChange(oid, entityOid);
        copyRel.setSourceOid(oid);
        copyRel.setTargetOid(entityOid);
        excludeTypes = Arrays.asList("IMPACT", "REVIEW_FOR", "LOCAL_SUBSTITUTE", "ATTRIBUTE", "GENERATE", "COPY", "USE", "HAS");
        copyRel.setRelationTypes(excludeTypes);
        this.commonAbilityService.copyIORelExcludes(copyRel);
        List<Effectivity> oldEffectivity = this.effectivityService.findByPartBomOid(changeOid);
        oldEffectivity = (List)oldEffectivity.stream().filter((item) -> {
            return !item.isMarkForDelete();
        }).collect(Collectors.toList());
        List<EffectivityCreateDTO> createEff = new ArrayList();
        oldEffectivity.forEach((i) -> {
            EffectivityCreateDTO item = (EffectivityCreateDTO)BeanUtil.copyProperties(i, new EffectivityCreateDTO());
            item.setPartbomOid(entityOid);
            createEff.add(item);
        });
        this.effectivityHelper.batchCreate(createEff);
        this.commonAbilityService.deleteUseForHistory(oid, "PartIteration", false);
    }

    @Override
    public boolean addTo(AddToDTO dto) {
        // 校验权限
        PartIteration father = partHelper.findByOid(dto.getTargetOid());
        UserDTO user = SessionHelper.getCurrentUser();
        boolean hasAccess = PermissionHelper.validate(father, PermissionKeyEnum.EDIT, user);
        Assert.isTrue(hasAccess, "你没有修改【" + father.getName() + "】的权限！");
        return super.addTo(dto);
    }

    @Override
    public boolean moveInBOM(MoveInBomDTO dto) {
        PartIteration oldFather = partHelper.findByOid(dto.getParentOid());
        PartIteration newFather = partHelper.findByOid(dto.getTargetOid());
        UserDTO user = SessionHelper.getCurrentUser();
        boolean hasAccess = PermissionHelper.validate(oldFather, PermissionKeyEnum.EDIT, user);
        Assert.isTrue(hasAccess, "你没有修改【" + oldFather.getName() + "】的权限！");
        hasAccess = PermissionHelper.validate(newFather, PermissionKeyEnum.EDIT, user);
        Assert.isTrue(hasAccess, "你没有修改【" + newFather.getName() + "】的权限！");
        return super.moveInBOM(dto);
    }

    @Override
    public List<LockAble> doCheckOut(Collection<LockAble> lockAbles) {
        if (CollectionUtil.isEmpty(lockAbles)) {
            return new LinkedList();
        } else {
            String type = ((LockAble)lockAbles.iterator().next()).getType();
            List<PartIteration> copies = null;

            try {
                copies = BeanUtil.cloneList(lockAbles, PartIteration.class);
            } catch (Exception var10) {
                throw new JWIServiceException(var10.getMessage());
            }

            Map<String, LockAble> source2CopyNode = new HashMap(copies.size());
            Map<String, Copy> source2Rel = new HashMap(copies.size());
            Iterator var6 = copies.iterator();

            while(var6.hasNext()) {
                PartIteration copy = (PartIteration)var6.next();
                String lockSourceOid = copy.getOid();
                copy.setLockSourceOid(lockSourceOid);
                String newOid = OidGenerator.newOid();
                copy.setOid(newOid);
                copy.setDisplayVersion(copy.getDisplayVersion() + "(Working Copy)");
                source2Rel.put(lockSourceOid, new Copy(type, newOid, type, lockSourceOid));
                source2CopyNode.put(lockSourceOid, copy);
            }

            List<LockAble> lockAbleCopies = this.partService.createCopy(source2Rel, source2CopyNode);
            List<String> sourceOids = CollectionUtil.mapToList(lockAbleCopies, LockAble::getLockSourceOid);
            this.commonAbilityService.copyIORelExcludesForCheckOut(type, sourceOids, this.checkOutExcludeTypes);
            this.commonAbilityService.copyUseForCheckOut(type, sourceOids);
            List<String> newOids = (List)source2CopyNode.values().stream().map((c) -> {
                return c.getOid();
            }).collect(Collectors.toList());
            return lockAbleCopies;
        }
    }

    @Override
    public LockAble doCheckOut(LockAble lockAble) {
        PartIteration copy = null;
        String sourceOid = lockAble.getOid();
        String modelType = lockAble.getType();

        try {
            copy = (PartIteration) BeanUtil.copyProperties(lockAble, new PartIteration());
        } catch (Exception var7) {
            throw new JWIServiceException(var7.getMessage());
        }

        String copyOid = OidGenerator.newOid();
        copy.setOid(copyOid);
        copy.setLockSourceOid(sourceOid);
        copy.setDisplayVersion(copy.getDisplayVersion() + "(Working Copy)");
        this.partService.createCopy(new Copy(modelType, copyOid, modelType, sourceOid), copy);
        CopyRel copyRel = new CopyRel();
        copyRel.setSourceType("PartIteration");
        copyRel.setSourceOid(sourceOid);
        copyRel.setTargetType("PartIteration");
        copyRel.setTargetOid(copyOid);
        copyRel.setRelationTypes(this.checkOutExcludeTypes);
        this.commonAbilityService.copyIORelExcludes(copyRel);
        copyRel.setRelationTypes(Collections.singletonList("USE"));
        this.commonAbilityService.copyUseForCheckOut(copyRel);
        return copy;
    }

    @Override
    public List<LockAble> doCheckIn(Collection<LockAble> lockAbles) throws JWIException {
        for(LockAble lockAble : lockAbles){
            customerCommonAbilityHelper.initNumberForUpdate(lockAble);
        }
        return super.doCheckIn(lockAbles);
    }

    @Override
    public LockAble doCheckIn(LockAble copyNode) throws JWIException {
        customerCommonAbilityHelper.initNumberForUpdate(copyNode);
        return super.doCheckIn(copyNode);
    }

    private String getReviseDefaultStatus(LifecycleTemplate lifecycleTemplate) {
        List<String> statusList = lifecycleTemplate.getContext().getStates().stream()
                .map(s->s.getCode()).collect(Collectors.toList());
        if (statusList.contains(reviseDefaultStatus)) {
            return reviseDefaultStatus;
        } else {
            return lifecycleTemplate.findStartStatus();
        }
    }

    // 因为目前所有类型的Part都加了分类必填的校验，所以此处赋予一个临时分类，保证CAD转Part时通过校验，后续在起流程时校验部件必须为正式分类
    private void addTempCls(PartCreateDTO dto) {
        Classification classification = classificationService.findByClsCode("CADCHECKIN");
        ClassificationInfo clsInfo = BeanUtil.copyProperties(classification,new ClassificationInfo());
        dto.setClassificationInfo(clsInfo);
    }

    @SneakyThrows
    public void importPartLink(MultipartFile file) {
        EasyExcel.read(file.getInputStream(), CustomerPartLinkDTO.class, new CustomerPartLinkExcelListener()).sheet().doRead();
    }

    @Override
    public BatchCopyResultDTO batchCopy(BatchCopyPartDTO dto) {
        //新旧oid对应关系
        Map<String, String[]> oidCorrespond = new HashMap<>();
        List<PartIteration> parts = commonAbilityHelper.findDetailEntity(dto.getSourceOids(), PartIteration.TYPE)
                .stream().map(item -> (PartIteration) item).collect(Collectors.toList());
        //保存基本数据
        List<PartIteration> newParts = this.savePartList(parts, dto, oidCorrespond);
        //保存结构关系和文档关系
        this.savePartRel(dto, oidCorrespond, parts);

        //保存bom关系
        copyBOMRelation(newParts, parts, oidCorrespond);

        BatchCopyResultDTO resultDTO = new BatchCopyResultDTO();
        resultDTO.setList(newParts);
        return resultDTO;
    }

    private void copyBOMRelation(List<PartIteration> newParts, List<PartIteration> parts, Map<String, String[]> oidCorrespond) {
        List<Use> newUseList = new ArrayList<>();

        Map<String, PartIteration> newPartMap = newParts.stream()
                .collect(Collectors.toMap(PartIteration::getNumber, Function.identity()));

        //  仅收集有BOM的part number
        Set<String> partNumbersWithBOM = new HashSet<>();
        Map<String, List<SimplePartBOMNode>> bomNodeMap = new HashMap<>();

        for (PartIteration oldPart : parts) {
            String oldFromOid = oldPart.getOid();
            List<SimplePartBOMNode> bomNodes = this.findSimpleUseTree("", oldFromOid, 1);
            if (bomNodes != null && !bomNodes.isEmpty() && !bomNodes.get(0).getChildren().isEmpty()) {
                String newCopiedOid = oidCorrespond.get(oldFromOid)[0];
                PartIteration newCopiedPart = newParts.stream()
                        .filter(p -> p.getOid().equals(newCopiedOid))
                        .findFirst()
                        .orElseThrow(() -> new JWIException("未找到新复制Part: " + oldFromOid));
                partNumbersWithBOM.add(newCopiedPart.getNumber());
                bomNodeMap.put(oldFromOid, bomNodes);
            }
        }

        //  执行检出（仅对有BOM的Part）
        Map<String, PartIteration> checkoutPartIterationMap = checkOutParts(
                newPartMap.entrySet().stream()
                        .filter(e -> partNumbersWithBOM.contains(e.getKey()))
                        .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue))
        );

        Map<String, PartIteration> checkinPartMap = new HashMap<>();
        for (PartIteration oldPart : parts) {
            String oldFromOid = oldPart.getOid();
            if (!bomNodeMap.containsKey(oldFromOid)) {
                continue; // 没有BOM结构，不处理
            }

            String newCopiedOid = oidCorrespond.get(oldFromOid)[0];
            PartIteration newCopiedPart = newParts.stream()
                    .filter(p -> p.getOid().equals(newCopiedOid))
                    .findFirst()
                    .orElseThrow(() -> new JWIException("未找到新复制Part: " + oldFromOid));
            String newNumber = newCopiedPart.getNumber();
            PartIteration newCheckedOutPart = checkoutPartIterationMap.get(newNumber);
            if (newCheckedOutPart == null) {
                throw new JWIException("未找到检出后的新Part：" + newNumber);
            }

            String newFromOid = newCheckedOutPart.getOid();
            List<SimplePartBOMNode> bomNodes = bomNodeMap.get(oldFromOid);
            List<SimplePartBOMNode> children = bomNodes.get(0).getChildren();

            for (SimplePartBOMNode bomNode : children) {
                String oldToOid = bomNode.getOid();

                Use use = new Use();
                use.setOid(OidGenerator.newOid());
                use.setFromOid(newFromOid);
                use.setFromType(PartIteration.TYPE);
                use.setToOid(oldToOid);
                use.setToType(PartIteration.TYPE);
                use.setQuantity(bomNode.getUse().getQuantity() != null ? bomNode.getUse().getQuantity() : 1.0);
                use.setUnit(bomNode.getUse().getUnit());
                if (CollUtil.isNotEmpty(bomNode.getUse().getExtensionContent())) {
                    use.setExtensionContent(bomNode.getUse().getExtensionContent());
                }
                newUseList.add(use);
            }

            checkinPartMap.put(newCopiedPart.getNumber(), newCopiedPart);
        }

        if (CollUtil.isNotEmpty(newUseList)) {
            log.info("复制BOM结构共 {} 条", newUseList.size());
            partHelper.addUse(newUseList);
        }

        //  最后统一检入（只检入真正处理过的）
        if (!checkoutPartIterationMap.isEmpty()) {
            log.info("part check in start, part 数量: {}", checkoutPartIterationMap.size());
            PartIteration topCheckInPart = checkInParts(checkoutPartIterationMap, parts.get(0).getNumber());
            log.info("part check in complete!");
        }
    }



    private List<PartIteration> savePartList(List<PartIteration> parts, BatchCopyPartDTO dto, Map<String, String[]> oidCorrespond) {
        List<PartIteration> list = new ArrayList<>(dto.getSourceOids().size());
        Map<String, String> newNames = dto.getNames().stream().collect(Collectors.toMap(BatchCopyPartDocumentBatchItemDTO::getSourceOid, BatchCopyPartDocumentBatchItemDTO::getNewName));
        UserDTO currentUser = SessionHelper.getCurrentUser();
        String account = currentUser.getAccount();
        for (PartIteration partIteration : parts) {
            String sourceOid = partIteration.getOid();
            PartIteration copyPartDTO = BeanUtil.copyProperties(partIteration, new PartIteration());
            if (newNames.containsKey(sourceOid)) {
                copyPartDTO.setName(newNames.get(sourceOid));
            }
            copyPartDTO.setCurrentStage(dto.getCurrentStage());
            copyPartDTO.setNumber(null);
            copyPartDTO.setThumbnailOid(null);
            partService.setLocation(copyPartDTO, dto.getLocationInfo());
            copyPartDTO.setOid(OidGenerator.newOid());
            copyPartDTO.setMasterOid(OidGenerator.newOid());
            copyPartDTO.setOwner(account);
            copyPartDTO.setCreateBy(account);
            JSONObject extensionContent = copyPartDTO.getExtensionContent();
            if (extensionContent == null) {
                extensionContent = new JSONObject();
                copyPartDTO.setExtensionContent(extensionContent);
            }
            JSONObject requirementUser = new JSONObject();
            requirementUser.put("account", account);
            requirementUser.put("name", currentUser.getName());
            extensionContent.put("cn_jwis_xqr", requirementUser);


            //存储新旧数据对应关系
            String[] str = new String[]{copyPartDTO.getOid(), copyPartDTO.getMasterOid(), partIteration.getMasterOid()};
            oidCorrespond.put(sourceOid, str);
            list.add(copyPartDTO);
        }
        commonAbilityHelper.doCreate(list);
        return list;
    }

    private void savePartRel(BatchCopyPartDTO dto, Map<String, String[]> oidCorrespond, List<PartIteration> oldList) {
        //bom结构关系Use复制
        List<RelationAble> rel = partService.getPartBomRelation(dto.getSourceOids()).stream().map(use -> {
            Use newUse = BeanUtil.copyProperties(use, new Use());
            newUse.setOid(OidGenerator.newOid());
            newUse.setFromOid(oidCorrespond.get(use.getFromOid())[0]);
            newUse.setToOid(oidCorrespond.get(use.getToOid())[0]);
            return newUse;
        }).collect(Collectors.toList());
        //bom关联文档复制
        //历史masteroid 和 oid 对应关系
        Map<String, String> masterAndOid = oldList.stream().collect(Collectors.toMap(PartIteration::getMasterOid, PartIteration::getOid, (o1, o2) -> o1));
        List<Reference> references = partService.findDocumentRel(dto.getSourceOids()).stream().map(reference -> {
            Reference newReference = BeanUtil.copyProperties(reference, new Reference());
            newReference.setOid(OidGenerator.newOid());
            String oldSourceOid = masterAndOid.get(reference.getFromOid());
            //新masteroid
            newReference.setFromOid(oidCorrespond.get(oldSourceOid)[1]);
            return newReference;
        }).collect(Collectors.toList());
        rel.addAll(references);
        jwiCommonService.createRelation(rel);
    }

//    @Override
//    public List collectionList(String oid) {
//        //配置中心开关状态
//        List<Map<String, String>> onoff = partSysConfigRemote.queryConfigValue("auto_collect_state");
//        //未配置开关，或状态是非on的返回空
//        if (onoff.isEmpty() || !Objects.equals(CollectionUtils.firstElement(onoff).get("name"), "on")) {
//            return new ArrayList<>();
//        }
//
//        List<SimplePartBOMNode> bomNodes = this.findSimpleUseTree(null, oid, null);
//        List<SimplePartBOMNode> bomList = getAllList(bomNodes);
//        bomList.addAll(bomNodes);
//
//        String bomStatus;
//        //过滤bom状态
//        List<Map<String, String>> bomStatuses = partSysConfigRemote.queryConfigValue("collect_bom_status");
//        if (!bomStatuses.isEmpty()) {
//            bomStatus = CollectionUtils.firstElement(bomStatuses).get("name");
//        } else {
//            bomStatus = "";
//        }
//
//        PartIteration pi = findByOid(oid);
//
//        //过滤需要收集的相关对象
//        List<Map<String, String>> labelList = partSysConfigRemote.queryConfigValue("collect_obj_list");
//
//        //关系名称
//        List<RelatedFuzzyNameDTO> relNames = partSysConfigRemote.findByAppliedType(pi.getModelDefinition()).stream().map(item -> {
//            RelatedFuzzyNameDTO map = new RelatedFuzzyNameDTO();
//            map.setName(item.get("relationDisplayName").toString());
//            map.setRelated(JSON.parseObject(JSON.toJSONString(item), RelatedFuzzyDTO.class));
//            return map;
//        }).collect(Collectors.toList());
//
//        bomList = bomList.stream().filter(item -> StringUtil.isNotBlank(bomStatus) && !Objects.equals(item.getLifecycleStatus(), bomStatus)).collect(Collectors.toList());
//        List res = new ArrayList();
//        res.addAll(bomList);
//        for (RelatedFuzzyNameDTO relName : relNames) {
//            Optional<Map<String, String>> nameDTO = labelList.stream().filter(item -> Objects.equals(item.get("name"), relName.getName())).findFirst();
//            if (nameDTO.isPresent()) {
//                String statues = nameDTO.get().get("value");
//                for (SimplePartBOMNode simplePartBOMNode : bomList) {
//                    if (StringUtil.isNotBlank(bomStatus) && !Objects.equals(simplePartBOMNode.getLifecycleStatus(), bomStatus)) {
//                        relName.getRelated().setMainObjectOid(simplePartBOMNode.getOid());
//                        List<InstanceEntity> entities = instanceHelper.fuzzyRelated(relName.getRelated());
//                        if (StringUtils.isNotBlank(statues)) {
//                            res.addAll(entities.stream().filter(item -> !Objects.equals(item.getLifecycleStatus(), statues)).collect(Collectors.toList()));
//                        } else {
//                            res.addAll(entities);
//                        }
//                    }
//                }
//
//            }
//        }
//
//        return res;
//    }

    @Override
    public List<Object> collectionList(String oid) {
        // 获取 auto_collect_state 配置，未开启则返回空
        if (!isAutoCollectEnabled()) {
            return new ArrayList<>();
        }

        // 获取 BOM 组件
        List<SimplePartBOMNode> bomList = getBOMList(oid);
        if (bomList.isEmpty()) {
            return new ArrayList<>();
        }

        // 获取 BOM 过滤状态
        String bomStatus = getConfigValue("collect_bom_status");

        // 获取 PartIteration 实例
        PartIteration pi = findByOid(oid);

        // 处理 BOM 过滤逻辑
        bomList = filterBOMByStatus(bomList, pi);

        // 获取关联对象
        List<Object> res = new ArrayList<>(bomList);
        res.addAll(getRelatedObjects(pi, bomList));

        return res;
    }

    private List<SimplePartBOMNode> filterBOMByStatus(List<SimplePartBOMNode> bomList, PartIteration pi) {
        String bomStatus = getConfigValue("collect_bom_status");

        if ("Released".equalsIgnoreCase(pi.getLifecycleStatus())) {
            // 仅保留状态是 Released 的 BOM
            return bomList.stream()
                    .filter(item -> "Released".equalsIgnoreCase(item.getLifecycleStatus()))
                    .collect(Collectors.toList());
        } else {
            // 按照 bomStatus 过滤
            return bomList.stream()
                    .filter(item -> StringUtil.isNotBlank(bomStatus) && !Objects.equals(item.getLifecycleStatus(), bomStatus))
                    .collect(Collectors.toList());
        }
    }

    private boolean isAutoCollectEnabled() {
        List<Map<String, String>> onoff = partSysConfigRemote.queryConfigValue("auto_collect_state");
        return !onoff.isEmpty() && "on".equalsIgnoreCase(onoff.get(0).get("name"));
    }

    private List<SimplePartBOMNode> getBOMList(String oid) {
        List<SimplePartBOMNode> bomNodes = findSimpleUseTree(null, oid, null);
        List<SimplePartBOMNode> bomList = getAllList(bomNodes);
        bomList.addAll(bomNodes);
        return bomList;
    }

    private String getConfigValue(String key) {
        List<Map<String, String>> config = partSysConfigRemote.queryConfigValue(key);
        return config.isEmpty() ? "" : config.get(0).get("name");
    }

    private List<SimplePartBOMNode> filterBOMByStatus(List<SimplePartBOMNode> bomList, String bomStatus) {
        return bomList.stream()
                .filter(item -> StringUtil.isNotBlank(bomStatus) && !Objects.equals(item.getLifecycleStatus(), bomStatus))
                .collect(Collectors.toList());
    }

    private List<Object> getRelatedObjects(PartIteration pi, List<SimplePartBOMNode> bomList) {
        List<Object> res = new ArrayList<>();
        List<Map<String, String>> labelList = partSysConfigRemote.queryConfigValue("collect_obj_list");
        List<RelatedFuzzyNameDTO> relNames = partSysConfigRemote.findByAppliedType(pi.getModelDefinition()).stream()
                .map(item -> {
                    RelatedFuzzyNameDTO map = new RelatedFuzzyNameDTO();
                    map.setName(item.get("relationDisplayName").toString());
                    map.setRelated(JSON.parseObject(JSON.toJSONString(item), RelatedFuzzyDTO.class));
                    return map;
                }).collect(Collectors.toList());

        for (RelatedFuzzyNameDTO relName : relNames) {
            Optional<Map<String, String>> nameDTO = labelList.stream()
                    .filter(item -> Objects.equals(item.get("name"), relName.getName()))
                    .findFirst();
            if (nameDTO.isPresent()) {
                String statues = nameDTO.get().get("value");

                for (SimplePartBOMNode bomNode : bomList) {
                    relName.getRelated().setMainObjectOid(bomNode.getOid());
                    List<InstanceEntity> entities = instanceHelper.fuzzyRelated(relName.getRelated());

                    if (StringUtils.isNotBlank(statues)) {
                        res.addAll(entities.stream()
                                .filter(item -> !Objects.equals(item.getLifecycleStatus(), statues))
                                .collect(Collectors.toList()));
                    } else {
                        res.addAll(entities);
                    }
                }
            }
        }
        return res;
    }

    /**
     * 对目标部件集合执行检出操作，返回检出后的映射关系
     */
    private Map<String, PartIteration> checkOutParts(Map<String, PartIteration> partMap) {
        Map<String, PartIteration> resultMap = new HashMap<>();
        String currentUserOid = SessionHelper.getCurrentUser().getOid();

        for (Map.Entry<String, PartIteration> entry : partMap.entrySet()) {
            String number = entry.getKey();
            PartIteration part = entry.getValue();

            ModelInfo modelInfo = new ModelInfo();
            modelInfo.setType(part.getType());
            modelInfo.setOid(part.getOid());

            if (StringUtils.isEmpty(part.getLockOwnerOid())) {
                PartIteration checkoutPart = (PartIteration) partHelper.checkOut(modelInfo);
                resultMap.put(number, checkoutPart);
            } else if (currentUserOid.equals(part.getLockOwnerOid())) {
                resultMap.put(number, part);
            } else {
                throw new JWIException(String.format("部件 %s 已被用户 %s 检出，无法操作！", number, part.getLockOwnerAccount()));
            }
        }

        return resultMap;
    }

    /**
     * 对检出的部件集合执行检入操作，返回顶层部件
     */
    private PartIteration checkInParts(Map<String, PartIteration> checkoutPartMap, String topNumber) {
        PartIteration topCheckInPart = null;

        for (Map.Entry<String, PartIteration> entry : checkoutPartMap.entrySet()) {
            PartIteration part = entry.getValue();
            LockInfo lockInfo = new LockInfo();
            lockInfo.setOid(part.getOid());
            lockInfo.setType(part.getType());

            partHelper.checkIn(lockInfo);

            if (topNumber.equals(entry.getKey())) {
                topCheckInPart = part;
            }
        }

        return topCheckInPart;
    }



}
