package cn.jwis.product.pdm.customer.service.impl;

import cn.jwis.framework.base.util.Assert;
import cn.jwis.framework.base.util.StringUtil;
import cn.jwis.platform.plm.dataengine.common.JWICommonService;
import cn.jwis.platform.plm.foundation.classification.responce.ClsPropertyWithRel;
import cn.jwis.platform.plm.foundation.classification.service.ClassificationPropertyHelper;
import cn.jwis.platform.plm.foundation.common.response.InstanceEntity;
import cn.jwis.platform.plm.foundation.common.service.InstanceHelper;
import cn.jwis.platform.plm.foundation.model.response.ModelPropertyWithRel;
import cn.jwis.platform.plm.foundation.model.service.ModelPropertyHelper;
import cn.jwis.platform.plm.foundation.utils.ModelPropUtil;
import cn.jwis.product.pdm.cad.ecad.dto.SyncToAccessDTO;
import cn.jwis.product.pdm.cad.ecad.service.EdaIntegrationHelper;
import cn.jwis.product.pdm.customer.entity.DingTaskRecord;
import cn.jwis.product.pdm.customer.service.delegate.BatchSendERP;
import cn.jwis.product.pdm.customer.service.interf.CustomerPartCompHelper;
import cn.jwis.product.pdm.partbom.part.dto.CompareBomParamDTO;
import cn.jwis.product.pdm.partbom.part.entity.PartIteration;
import cn.jwis.product.pdm.partbom.part.service.PartHelper;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CustomerPartCompHelperImpl implements CustomerPartCompHelper {

    @Resource
    InstanceHelper instanceHelper;

    @Resource
    PartHelper partHelper;

    @Resource
    ModelPropertyHelper modelPropertyHelper;

    @Resource
    ClassificationPropertyHelper classificationPropertyHelper;

    @Override
    public List<PartIteration> compareAttribute(CompareBomParamDTO dto) {
        String primaryOid = dto.getPrimaryOid();
        String targetOid = dto.getTargetOid();

        PartIteration primaryData = partHelper.findByOid(primaryOid);

        if(StringUtil.isNotBlank(primaryData.getClsOid())){
            JSONObject clsProperty = primaryData.getClsProperty();
            List<ClsPropertyWithRel> clsProps = classificationPropertyHelper.findAllPropByClsOid(primaryData.getClsOid());
            for (ClsPropertyWithRel clsProp : clsProps) {
                Object txt = ModelPropUtil.getPropTxt(clsProp, clsProperty);
                clsProperty.put(clsProp.getCode(),txt);
            }
        }

        //重新处理数据字典code和val
        List<ModelPropertyWithRel> extendProps = modelPropertyHelper.findAllByModel(primaryData.getModelDefinition())
                .stream().filter(item -> !item.isSystemDefault()).collect(Collectors.toList());
        JSONObject extensionContent = Optional.ofNullable(primaryData.getExtensionContent()).orElse(new JSONObject());
        for (ModelPropertyWithRel extendProp : extendProps) {
            Object txt = ModelPropUtil.getPropTxt(extendProp, extensionContent);
            extensionContent.put(extendProp.getCode(), txt);
        }
        primaryData.setExtensionContent(extensionContent);

        PartIteration targetData = partHelper.findByOid(targetOid);

        if(StringUtil.isNotBlank(targetData.getClsOid())){
            JSONObject clsProperty = targetData.getClsProperty();
            List<ClsPropertyWithRel> clsProps = classificationPropertyHelper.findAllPropByClsOid(targetData.getClsOid());
            for (ClsPropertyWithRel clsProp : clsProps) {
                Object txt = ModelPropUtil.getPropTxt(clsProp, clsProperty);
                clsProperty.put(clsProp.getCode(),txt);
            }
        }

        //重新处理数据字典code和val
        List<ModelPropertyWithRel> extendPropsTarget = modelPropertyHelper.findAllByModel(targetData.getModelDefinition())
                .stream().filter(item -> !item.isSystemDefault()).collect(Collectors.toList());
        JSONObject extensionContentTarget = Optional.ofNullable(targetData.getExtensionContent()).orElse(new JSONObject());
        for (ModelPropertyWithRel extendProp : extendPropsTarget) {
            Object txt = ModelPropUtil.getPropTxt(extendProp, extensionContentTarget);
            extensionContentTarget.put(extendProp.getCode(), txt);
        }
        targetData.setExtensionContent(extensionContentTarget);

        List<PartIteration> list = new ArrayList<>();
        list.add(primaryData);
        list.add(targetData);
        //测试提交
        return list;
    }

    @Resource
    JWICommonService jwiCommonService;

    @Resource
    EdaIntegrationHelper edaIntegrationHelper;

    @Resource
    private BatchSendERP batchSendERP;
    @Override
    public boolean partCallBack(String processId) {
        DingTaskRecord dingTaskRecord = jwiCommonService.findByOid(DingTaskRecord.TYPE,processId,DingTaskRecord.class);
        Assert.notNull(dingTaskRecord,"无钉钉流程记录");
        SyncToAccessDTO dto = new SyncToAccessDTO();
        dto.setOids(dingTaskRecord.getPartInterationOidList());
        this.edaIntegrationHelper.saveBatch(dto);
        List<InstanceEntity> instanceList = jwiCommonService.findByOid(PartIteration.TYPE,
                dingTaskRecord.getPartInterationOidList(),InstanceEntity.class);
        this.batchSendERP.batchSendERP(instanceList, Boolean.FALSE);
        return true;
    }
}
