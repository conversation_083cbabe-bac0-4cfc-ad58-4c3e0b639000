package cn.jwis.product.pdm.customer.service.dto;

import cn.jwis.platform.plm.file.entity.FileMetadata;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2023/8/22 20:17
 * @Email <EMAIL>
 */
@Data
@EqualsAndHashCode
public class DingTaskReviewEntity {

    //编码
    String name;
    //、名称
    String number;
    //、版本
    String version;
    //、状态
    String status;
    //、规格
    String gg;
    //、生产厂家
    String sccj;
    //、质量等级
    String zldj;
    //、封装形式
    String fzxs;
    //、创建时间
    String createTime;
    //、创建人
    String creator;
    //、datasheet下载链接
    FileMetadata datasheet;
    //、材质
    String cz;
    //、表面处理
    String bmcl;

}
