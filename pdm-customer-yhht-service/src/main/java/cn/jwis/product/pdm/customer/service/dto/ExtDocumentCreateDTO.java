package cn.jwis.product.pdm.customer.service.dto;

import cn.jwis.product.pdm.document.dto.DocumentCreateDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> yefei
 * @Date ： 2024/3/4
 * @Description : 自定义文件创建用DTO交付清单id用于广联指定交付清单
 */

@Data
@EqualsAndHashCode
public class ExtDocumentCreateDTO extends DocumentCreateDTO {

    @ApiModelProperty("要关联的交付清单oid")
    private String deliveryOid;

    @ApiModelProperty("用于接收外协类文件手动填写的编码")
    private String number;
}
