package cn.jwis.product.pdm.customer.service.delegate;

import cn.hutool.json.JSONUtil;
import cn.jwis.framework.base.exception.JWIServiceException;
import cn.jwis.framework.base.util.*;
import cn.jwis.framework.base.web.session.SessionHelper;
import cn.jwis.platform.plm.workflow.engine.dto.HistoricTaskDTO;
import cn.jwis.platform.plm.workflow.engine.order.entity.ProcessOrder;
import cn.jwis.platform.plm.workflow.engine.service.interf.HistoryHelper;
import cn.jwis.platform.plm.workflow.engine.service.interf.ProcessOrderHelper;
import cn.jwis.platform.plm.workflow.engine.util.HttpClientUtil;
import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.RuntimeService;
import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.Expression;
import org.activiti.engine.delegate.JavaDelegate;
import org.activiti.engine.impl.persistence.entity.VariableInstance;
import org.apache.commons.lang3.StringUtils;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR> hsy
 * @Date ： Created in 2022/9/19 19:33
 * @Email ： <EMAIL>
 * @Description :
 */
@Slf4j
@Data
@EqualsAndHashCode
public class DocSignDelegate implements JavaDelegate {

    static SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy/MM/dd");

    static SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyyMMdd");

    private Expression url;

    private Expression textParams;

    private Expression dateParams;

    private Expression signVar2TaskName; // 签名的变量 --> 人工服务节点的名称，

    private Expression data; // 其他数据，模版信息，插入方式， 插入页码

    @Override
    synchronized public void execute(DelegateExecution execution) {
        RuntimeService runtimeService = SpringContextUtil.getBean(RuntimeService.class);
        try {
            // 获取所有变量
            String urlValue = url.getExpressionText();
            Assert.notBlank(urlValue, "url can not be null");

            ProcessOrder processOrder = getProcessOrder(execution);
            String processInstanceId = processOrder.getProcessInstanceId();

            Map<String, VariableInstance> variableInstances = execution.getVariableInstances();
            Assert.isFalse(MapUtil.isEmpty(variableInstances), "VariableInstances of this workflow can not be empty");

            String gateway = fetchGateway(variableInstances); // geteway
            String accesstoken = fetchAccessToken(variableInstances); // accesstoken
            String tenantAlias = fetchTenantAlias(variableInstances); // tenantAlias

            JSONObject dataJSON = fetchExpressionParamJSON(this.data); // 解析 data 内容
            JSONObject textJSON = fetchExpressionParamJSON(this.textParams); // 解析 文本参数 内容
            JSONObject dateParamJSON = fetchExpressionParamJSON(this.dateParams); // 解析 文本参数 内容
            JSONObject signVar2TaskNameJSON = fetchExpressionParamJSON(this.signVar2TaskName); // 解析 签名参数 内容

            // 准备请求体内容
            JSONObject requestBody = new JSONObject();
            requestBody.put("tenantOid", execution.getTenantId());
            requestBody.put("processOrderType", ProcessOrder.TYPE);
            requestBody.put("processInstanceId", processInstanceId);
            requestBody.put("processOrderOid", processOrder.getOid());
            requestBody.put("textParams", textJSON);
            requestBody.put("dateParams", dateParamJSON);
            requestBody.put("signVar2TaskNameJSON", signVar2TaskNameJSON);
            requestBody.put("data", dataJSON); // 其他参数

            log.info("签名环节入参requestBody1：",requestBody);

            // 解析
            rebuildRequestBody(requestBody);
            log.info("签名环节入参requestBody2：",requestBody);
            System.out.println(requestBody);
            String url = buildRequestURL(urlValue, gateway); // 请求 API
            if(StringUtil.isBlank(SessionHelper.getAppId())){
                SessionHelper.setAppId("pdm");
            }
            System.out.println(JSONUtil.toJsonStr(requestBody));
            JSONObject post = HttpClientUtil.sendHttpPost(requestBody, url, accesstoken, tenantAlias);
            String str = post.getString("responseContent");
            JSONObject postResult = JSONObject.parseObject(str);

            // 下面这段没啥用，服务节点暂时没有做异常处理
            if (postResult.getInteger("code") == 0) {
                runtimeService.setVariable(execution.getId(), "errorMsg", null);
            } else {
                JSONObject errorMsgObj = new JSONObject();
                errorMsgObj.put("error.callback.url", url);
                errorMsgObj.put("error.callback.params", requestBody);
                errorMsgObj.put("error.callback.msg", postResult.getString("msg"));
                runtimeService.setVariable(execution.getId(), "errorMsg", errorMsgObj.toJSONString());
            }
        } catch (NullPointerException e) {
            StringBuffer buffer = new StringBuffer();
            StackTraceElement[] stackTrace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : stackTrace) {
                buffer.append(stackTraceElement.toString() + "\n");
            }
            runtimeService.setVariable(execution.getId(), "errorMsg", buffer.toString());
        } catch (Exception e) {
            log.info("execute error==>",e);
            runtimeService.setVariable(execution.getId(), "errorMsg", e.getMessage());
        }
    }


    private void rebuildRequestBody(JSONObject requestBody) {
        String processInstanceId = requestBody.getString("processInstanceId");
        JSONObject signVar2TaskNameJSON = requestBody.getJSONObject("signVar2TaskNameJSON");
        JSONObject dateParams = requestBody.getJSONObject("dateParams");
        JSONObject textJSON = requestBody.getJSONObject("textParams");
        JSONObject accountParamJSON = new JSONObject();
        JSONObject textParamJSON = new JSONObject();

        HistoryHelper historyHelper = SpringContextUtil.getBean(HistoryHelper.class);
        HistoricTaskDTO dto = new HistoricTaskDTO();
        dto.setProcessInstanceId(processInstanceId);
        List<JSONObject> userTasks = historyHelper.getHistoricTasktaskComment(dto);

        Assert.notEmpty(userTasks, "User task does not exists");
        Map<String, JSONObject> taskName2Task = CollectionUtil.splitToMap(userTasks, row->row.getString("name"));

        // 解析签名数据
        for (String key : signVar2TaskNameJSON.keySet()) {
            String taskName = signVar2TaskNameJSON.getString(key);
            JSONObject task = taskName2Task.get(taskName);
            if(task == null && "会签".equals(taskName)){
                accountParamJSON.put(key, "");
                accountParamJSON.put(key + "_time", "");
                task = new JSONObject();
            }else{
                Assert.notNull(task, "Task named " +taskName +" does not exists!");
                String assignee = task.getString("assignee");
                Date endTime = task.getDate("endTime");
                Assert.notBlank(assignee, "Doc Sign workflow call failed: Prepare request body failed: Can not find the assignee of task named " + taskName);
                accountParamJSON.put(key, assignee);
                accountParamJSON.put(key + "_time", endTime == null? "" : DATE_FORMAT.format(endTime));
            }

        }
        requestBody.put("accountParams", accountParamJSON);

        // 解析文本数据
        for (String key : textJSON.keySet()) {
            String taskName = textJSON.getString(key);
            JSONObject task = taskName2Task.get(taskName);
            Assert.notNull(task, "Task named " +taskName +" does not exists!");
            JSONObject comment = task.getJSONObject("comment");
            if (MapUtil.isEmpty(comment)) {
                textParamJSON.put(key, "");
                continue;
            }
            String commentContent = comment.getString("comment");
            textParamJSON.put(key, StringUtil.isBlank(commentContent)? Optional.ofNullable(comment.getString("selectRouting")).orElse(""): commentContent);
        }

        // 解析 日期数据
        for (String key : dateParams.keySet()) {
            String taskName = dateParams.getString(key);
            JSONObject task = taskName2Task.get(taskName);
            Assert.notNull(task, "Task named " +taskName +" does not exists!");
            Date endTime = task.getDate("endTime");
            textParamJSON.put(key, endTime == null? "" : dateFormat.format(endTime));
        }
        requestBody.put("textParams", textParamJSON);

    }

    private String buildRequestURL(String urlValue, String gateway) {
        String url = gateway + urlValue;
        if (!url.contains("http")) {
            url = "http://" + url;
        }
        System.out.println("*************** Document Sign Delegate URL : "+url);
        return url;
    }

    private JSONObject fetchExpressionParamJSON(Expression expression) {
        if (expression == null) {
            return new JSONObject();
        }
        String content = null;
        if (expression.getExpressionText() != null) {
            content = expression.getExpressionText();
        }
        return StringUtils.isBlank(content)? new JSONObject() : JSONObject.parseObject(content);

    }


    private String fetchTenantAlias(Map<String, VariableInstance> variableInstances) {
        String alias = null;
        if (variableInstances.get("tenantAlias") != null) {
            VariableInstance tenantAliasInstance = variableInstances.get("tenantAlias");
            alias = tenantAliasInstance.getTextValue();
            if (StringUtil.isBlank(alias)){
                Object value = tenantAliasInstance.getValue();
                if (value == null){
                    alias = SessionHelper.getCurrentUser().getTenantAlias();
                }else {
                    alias = String.valueOf(value);
                }
            }
        }
        return alias;
    }

    private String fetchAccessToken(Map<String, VariableInstance> variableInstances) {
        String accesstoken = null;
        if (variableInstances.get("accesstoken") != null) {
            VariableInstance accesstokenInstance = variableInstances.get("accesstoken");
            accesstoken = accesstokenInstance.getTextValue();
            if (StringUtil.isBlank(accesstoken)){
                Object value = accesstokenInstance.getValue();
                if (value == null){
                    accesstoken = SessionHelper.getAccessToken();
                }else {
                    accesstoken = String.valueOf(value);
                }
            }
        }
        return accesstoken;
    }

    private String fetchGateway(Map<String, VariableInstance> variableInstances) {
        String gateway;
        VariableInstance variableInstance = variableInstances.get("gateway");
        if (variableInstance == null) {
            throw new JWIServiceException("gateway url can not be null.");
        } else {
            gateway = variableInstance.getTextValue();
            if (StringUtil.isBlank(gateway)){
                Object value = variableInstance.getValue();
                if (value == null){
                    gateway = SpringContextUtil.getProperty("workflow.callback.gateway.path");
                }else {
                    gateway = String.valueOf(value);
                }
            }
        }
        return gateway;
    }

    private ProcessOrder getProcessOrder(DelegateExecution delegateTask) {
        ProcessOrder processOrder = null;
        Map<String, VariableInstance> variableInstances = delegateTask.getVariableInstances();
        ProcessOrderHelper processOrderHelper = SpringContextUtil.getBean(ProcessOrderHelper.class);
        if (MapUtil.isEmpty(variableInstances)){
            String processInstanceId = delegateTask.getProcessInstanceId();
            processOrder = processOrderHelper.findByProcessInstanceId(processInstanceId);
        }else {
            VariableInstance processOrderOid = variableInstances.get("processOrderOid");
            if (processOrderOid == null){
                String processInstanceId = delegateTask.getProcessInstanceId();
                processOrder = processOrderHelper.findByProcessInstanceId(processInstanceId);
            }else {
                String processOrderOidStr = processOrderOid.getValue().toString();
                processOrder = processOrderHelper.findByOid(processOrderOidStr);
            }
        }
        return processOrder;
    }
}
