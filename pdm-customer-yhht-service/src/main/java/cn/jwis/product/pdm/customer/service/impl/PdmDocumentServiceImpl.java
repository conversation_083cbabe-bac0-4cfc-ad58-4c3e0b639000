package cn.jwis.product.pdm.customer.service.impl;

import cn.jwis.framework.base.bean.util.BeanUtil;
import cn.jwis.framework.base.domain.able.ModelAble;
import cn.jwis.framework.base.domain.able.RelationAble;
import cn.jwis.framework.base.domain.able.info.ModelInfo;
import cn.jwis.framework.base.util.StringUtil;
import cn.jwis.framework.base.util.ValidationUtil;
import cn.jwis.platform.plm.container.entity.Container;
import cn.jwis.platform.plm.dataengine.common.JWICommonService;
import cn.jwis.platform.plm.foundation.common.service.CommonAbilityHelper;
import cn.jwis.platform.plm.foundation.common.service.CommonAbilityService;
import cn.jwis.platform.plm.foundation.location.able.LocationAble;
import cn.jwis.platform.plm.foundation.location.able.info.LocationInfo;
import cn.jwis.platform.plm.foundation.model.entity.VertexDef;
import cn.jwis.platform.plm.foundation.model.service.ModelHelper;
import cn.jwis.platform.plm.foundation.numberrule.able.NumberAble;
import cn.jwis.platform.plm.foundation.relationship.Contain;
import cn.jwis.platform.plm.foundation.versionrule.able.info.LockInfo;
import cn.jwis.product.pdm.cad.mcad.entity.MCADIteration;
import cn.jwis.product.pdm.customer.service.dto.PdmDocBorrowWorkflowDTO;
import cn.jwis.product.pdm.customer.service.interf.PdmDocumentService;
import cn.jwis.product.pdm.document.entity.DocumentIteration;
import cn.jwis.product.pdm.document.service.DocumentService;
import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> lhm
 * @Date ： Created in 2023/9/19 10:38
 * @Description :
 */
@Service
@Transactional
public class PdmDocumentServiceImpl implements PdmDocumentService {

    @Autowired
    private DocumentService documentService;

    @Autowired
    private ModelHelper modelHelper;

    @Autowired
    private CommonAbilityHelper commonAbilityHelper;

    @Autowired
    private JWICommonService jwiCommonService;

    @Autowired
    CommonAbilityService commonAbilityService;

    @Autowired
    CustomerCommonAbilityHelperImpl customerCommonAbilityHelper;


    @ApiModelProperty("项目文档产品化类型 ")
    @Value("${doc.borrow.Workflow:JWIProductionDocument}")
    private String JWIProductionDocument;

    @Override
    public boolean docBorrowWorkflow(PdmDocBorrowWorkflowDTO dto) {
        // 1.更新文档
        ValidationUtil.validate(dto);
        String oid = dto.getOid();
        DocumentIteration byDocItera = documentService.findByOid(oid);
        if (Objects.isNull(byDocItera)) {
            return false;
        }
        DocumentIteration bean = (DocumentIteration) checkOut(BeanUtil.copyProperties(byDocItera, new ModelInfo()));
        // 更新名称+位置+modelDefinition
        bean.setName(dto.getName());
        bean.setModelDefinition(JWIProductionDocument);
        bean.setContainerOid(dto.getContainerOid());
        bean.setContainerType(dto.getContainerType());
        bean.setCatalogOid(dto.getCatalogOid());
        bean.setCatalogType(dto.getCatalogType());
//        String number = numberRuleHelper.nextNumber(bean);
//        bean.setNumber(number);
        bean.setNumber(null);
        customerCommonAbilityHelper.initNumber(bean);
        VertexDef vertexDef = modelHelper.findByName(JWIProductionDocument);
        String icon = vertexDef.getIcon();
        if(StringUtils.isNotEmpty(icon) ){
            bean.setModelIcon(icon);
        }
        commonAbilityHelper.doUpdate(bean);
        // 2.源位置更新关系
        LocationInfo source = dto.getSource();
        List<RelationAble> relShouldBeCreate = new ArrayList();
        relShouldBeCreate.add(new Contain(source.getCatalogType(), source.getCatalogOid(), dto.getType(), bean.getOid(), true));

        // 3.新位置关联关系
        if(!dto.getCatalogOid().equals(source.getCatalogOid())){
            relShouldBeCreate.add(new Contain(dto.getCatalogType(), dto.getCatalogOid(), dto.getType(), bean.getOid()));
        }

        // 4. 修改

        jwiCommonService.mergeRelation(relShouldBeCreate);

        // 检入
        checkIn(BeanUtil.copyProperties(bean, new LockInfo()));

        return true;
    }
}
