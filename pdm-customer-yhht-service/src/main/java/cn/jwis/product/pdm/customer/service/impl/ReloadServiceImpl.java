package cn.jwis.product.pdm.customer.service.impl;

import cn.jwis.product.pdm.customer.service.interf.ReloadService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

@Service
public class ReloadServiceImpl implements ReloadService {

    private static final Logger logger = LoggerFactory.getLogger(ReloadServiceImpl.class);

    public void test(){
        logger.info("ttttttttttttttttttttttttttttttt");
    }

}
