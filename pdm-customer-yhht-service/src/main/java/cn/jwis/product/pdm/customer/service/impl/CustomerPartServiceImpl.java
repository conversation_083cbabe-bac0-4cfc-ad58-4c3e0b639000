package cn.jwis.product.pdm.customer.service.impl;

import cn.jwis.framework.base.util.StringUtil;
import cn.jwis.platform.plm.foundation.classification.responce.ClsPropertyWithRel;
import cn.jwis.platform.plm.foundation.classification.service.ClassificationPropertyHelper;
import cn.jwis.platform.plm.foundation.utils.ModelPropUtil;
import cn.jwis.product.pdm.customer.repo.CustomerCommonRepo;
import cn.jwis.product.pdm.partbom.part.dto.ConfigDTO;
import cn.jwis.product.pdm.partbom.part.entity.PartIteration;
import cn.jwis.product.pdm.partbom.part.service.DefaultPartServiceImpl;
import com.alibaba.fastjson.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2023/9/14 13:56
 * @Email <EMAIL>
 */
@Service
@Primary
@Transactional
public class CustomerPartServiceImpl extends DefaultPartServiceImpl {

    @Autowired
    CustomerCommonRepo customerCommonRepo;

    @Resource
    private ClassificationPropertyHelper classificationPropertyHelper;

    @Override
    public List<PartIteration> querySearch(List<ConfigDTO> systemProp, List<ConfigDTO> extendProp, List<ConfigDTO> classificationProp, JSONObject partIteration) {
        List<PartIteration> partIterationList = customerCommonRepo.querySearch(systemProp, extendProp, classificationProp, partIteration);

        List<String> clsOidList = partIterationList.stream().map((item) -> item.getClsProperty() != null ?
                item.getClsProperty().getString("clsOid") : null).filter(org.apache.commons.lang3.StringUtils::isNotBlank).collect(Collectors.toList());
        Map<String, List<ClsPropertyWithRel>> clsMap = this.classificationPropertyHelper.fuzzyByClsOidsRel(clsOidList);

        for (PartIteration iteration : partIterationList) {
            String cls = iteration.getClsProperty() != null ? iteration.getClsProperty().getString("clsOid") : null;
            if(StringUtil.isNotBlank(cls)){
                JSONObject clsProp = iteration.getClsProperty() != null ? iteration.getClsProperty() : new JSONObject();
                List<ClsPropertyWithRel> props = clsMap.get(cls) != null ? clsMap.get(cls) : new ArrayList<>();
                for (ClsPropertyWithRel prop : props) {
                    if(clsProp.containsKey(prop.getCode())){
                        Object str = ModelPropUtil.getPropTxt(prop, clsProp);
                        clsProp.put(prop.getCode(), str);
                    }
                }
            }
        }
        return partIterationList;
    }
}
