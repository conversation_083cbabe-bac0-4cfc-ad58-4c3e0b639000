package cn.jwis.product.pdm.customer.service.dto;

import cn.jwis.product.pdm.document.dto.DocumentCreateDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @author: 汪江
 * @data: 2023-12-23 15:04
 * @description:
 **/
@Data
@EqualsAndHashCode
public class CustomerDocumentCreateDTO extends DocumentCreateDTO {
    @ApiModelProperty("编码")
    private String number;

    @ApiModelProperty("版本")
    private String version;
    private int iteratedVersion;

    @ApiModelProperty("生命周期")
    private String lifecycleStatus;

    @ApiModelProperty("数据是否新建标识符")
    private boolean createFlag ;

    @ApiModelProperty("所有者")
    private String owner;
}
