package cn.jwis.product.pdm.customer.service.impl;

import cn.hutool.json.JSONUtil;
import cn.jwis.audit.annotation.JWIParam;
import cn.jwis.audit.annotation.JWIServiceAudit;
import cn.jwis.audit.enums.Action;
import cn.jwis.framework.base.bean.util.BeanUtil;
import cn.jwis.framework.base.domain.able.RelationAble;
import cn.jwis.framework.base.domain.able.info.ModelInfo;
import cn.jwis.framework.base.domain.entity.BaseEntity;
import cn.jwis.framework.base.dto.UserDTO;
import cn.jwis.framework.base.exception.JWIException;
import cn.jwis.framework.base.exception.JWIServiceException;
import cn.jwis.framework.base.util.*;
import cn.jwis.framework.base.web.session.SessionHelper;
import cn.jwis.framework.database.core.entity.CopyRel;
import cn.jwis.framework.database.core.entity.SubFilter;
import cn.jwis.framework.database.core.query.dynamic.Condition;
import cn.jwis.platform.plm.account.user.service.UserHelper;
import cn.jwis.platform.plm.container.entity.ContainerTemplate;
import cn.jwis.platform.plm.datadistribution.annotation.DataDistribution;
import cn.jwis.platform.plm.dataengine.common.JWICommonService;
import cn.jwis.platform.plm.file.entity.FileMetadata;
import cn.jwis.platform.plm.file.service.FileService;
import cn.jwis.platform.plm.foundation.attachment.entity.File;
import cn.jwis.platform.plm.foundation.classification.able.info.ClassificationInfo;
import cn.jwis.platform.plm.foundation.common.service.CommonAbilityHelper;
import cn.jwis.platform.plm.foundation.common.service.CommonAbilityService;
import cn.jwis.platform.plm.foundation.lifecycle.able.LifecycleAble;
import cn.jwis.platform.plm.foundation.lifecycle.entity.LifecycleTemplate;
import cn.jwis.platform.plm.foundation.lifecycle.service.LifecycleTemplateService;
import cn.jwis.platform.plm.foundation.model.service.ModelHelper;
import cn.jwis.platform.plm.foundation.relationship.*;
import cn.jwis.platform.plm.foundation.versionrule.able.VersionAble;
import cn.jwis.platform.plm.foundation.versionrule.util.VersionUtil;
import cn.jwis.platform.plm.modelexcel.dto.ModelExcelAllImportDTO;
import cn.jwis.platform.plm.modelexcel.dto.ModelExcelAllPropDTO;
import cn.jwis.platform.plm.modelexcel.service.ModelDataSaveService;
import cn.jwis.platform.plm.modelexcel.service.ModelExcelActService;
import cn.jwis.platform.plm.modelexcel.utils.ZipUtil;
import cn.jwis.platform.plm.signtemplate.SignTemplateFileRemote;
import cn.jwis.platform.plm.signtemplate.entity.SignTemplateContentNormalText;
import cn.jwis.platform.plm.signtemplate.entity.SignTemplateContentPicture;
import cn.jwis.platform.plm.signtemplate.service.CheckUtil;
import cn.jwis.product.pdm.cad.ecad.entity.ECADIteration;
import cn.jwis.product.pdm.cad.ecad.service.ECADHelper;
import cn.jwis.product.pdm.cad.mcad.entity.CADFile;
import cn.jwis.product.pdm.cad.mcad.entity.MCADIteration;
import cn.jwis.product.pdm.cad.mcad.service.MCADHelper;
import cn.jwis.product.pdm.cad.repo.mcad.MCADIterationRepo;
import cn.jwis.product.pdm.change.response.ChangeInfo;
import cn.jwis.product.pdm.change.service.ChangeService;
import cn.jwis.product.pdm.change.service.ECAService;
import cn.jwis.product.pdm.customer.entity.DingTaskRecord;
import cn.jwis.product.pdm.customer.entity.GenerateSignDocumentRemote;
import cn.jwis.product.pdm.customer.entity.assistant.SafeWrapAssist;
import cn.jwis.product.pdm.customer.repo.CustomerCommonRepo;
import cn.jwis.product.pdm.customer.service.dto.CheckImportDTO;
import cn.jwis.product.pdm.customer.service.dto.ExtDocumentCreateDTO;
import cn.jwis.product.pdm.customer.service.interf.CustomerDocumentHelper;
import cn.jwis.product.pdm.customer.service.signtemplate.CustomerSignTemplateImpl;
import cn.jwis.product.pdm.delivery.entity.Delivery;
import cn.jwis.product.pdm.document.dto.CopyDocumentDTO;
import cn.jwis.product.pdm.document.dto.DocSignWorkflowDTO;
import cn.jwis.product.pdm.document.dto.DocumentUpdateDTO;
import cn.jwis.product.pdm.document.dto.DwgSignWorkflowDTO;
import cn.jwis.product.pdm.document.entity.Document;
import cn.jwis.product.pdm.document.entity.DocumentIteration;
import cn.jwis.product.pdm.document.remote.container.DocMgmtContainerRemote;
import cn.jwis.product.pdm.document.remote.container.dto.TemplateFindDTO;
import cn.jwis.product.pdm.document.remote.delivery.DocDeliveryRemote;
import cn.jwis.product.pdm.document.remote.sign.DocMgmtSignTemplateRemote;
import cn.jwis.product.pdm.document.remote.sign.dto.GenerateSignDocumentDTO;
import cn.jwis.product.pdm.document.remote.sign.dto.SignPersonDTO;
import cn.jwis.product.pdm.document.service.DefaultDocumentHelperImpl;
import cn.jwis.product.pdm.document.service.DocumentService;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import feign.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.util.IOUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.text.MessageFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 重写DefaultPartHelperImpl
 * @date 2023/8/16 9:38
 * @Email <EMAIL>
 */
@Slf4j
@Service
@Primary
@Transactional
public class CustomerDocHelperImpl extends DefaultDocumentHelperImpl implements CustomerDocumentHelper, SafeWrapAssist {

    private static final Logger logger = LoggerFactory.getLogger(CustomerDocHelperImpl.class);

    private static final String KEY_DELIVERY_OID = "deliveryOid";

    @Autowired
    private DocumentService documentService;
    @Autowired
    private MCADIterationRepo mcadIterationRepo;
    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private CommonAbilityHelper commonAbilityHelper;
    @Autowired
    private CommonAbilityService commonAbilityService;
    @Autowired
    private DocDeliveryRemote docDeliveryRemote;
    @Resource
    private DocMgmtSignTemplateRemote signTemplateRemote;
    @Resource
    private DocMgmtContainerRemote containerRemote;
    @Autowired
    ModelHelper modelHelper;
    @Autowired
    private CustomerCommonServiceImpl customerCommonService;

    @Autowired
    LifecycleTemplateService lifecycleTemplateService;

    @Autowired
    MCADHelper mcadHelper;

    @Autowired
    UserHelper userHelper;
    @Autowired
    FileService fileService;
    @Resource
    ChangeService changeService;

    @Resource
    ECAService ecaService;

    @Resource
    private JWICommonService jwiCommonService;

    @Value("${revise.default.status:Design}")
    private String reviseDefaultStatus;

    @Resource
    private ModelExcelActService modelExcelActService;

    @Resource
    private TriggerAuditServiceImpl triggerAuditService;

    @Resource(name = "customerDocumentDataSaveServiceImpl")
    private ModelDataSaveService modelDataSaveService;

    @Autowired
    private CustomerCommonRepo customerCommonRepo;

    @Autowired
    CustomerSignTemplateImpl customerSignTemplate;
    @Autowired
    private VersionUtil versionUtil;

    @Override
    public void importExcelTemp(ModelExcelAllImportDTO importDTO, HttpServletRequest request) {
        MultipartFile importFile = importDTO.getFile();
        if(ObjectUtils.isNotEmpty(importFile)) {
            triggerAuditService.importDoc(importFile.getOriginalFilename());
        }
        modelExcelActService.importExcelAllModel(importDTO, modelDataSaveService, request);
    }

    @Override
    public void importUnzipFile(JSONObject jsonObject) {
        if(ObjectUtils.isEmpty(jsonObject)) {
            throw new JWIException("参数不为json结构不合法");
        }
        String unZipFileDir = jsonObject.getString("filePath");
        if(StringUtils.isBlank(unZipFileDir)) {
            throw new JWIException("文件路径不存在");
        }
        log.info("开始导入已解压文件清单目录路径:{}",unZipFileDir);
        java.io.File dirFile = FileUtils.getFile(unZipFileDir);
//        try {
            Map<String, java.io.File> filemap = ZipUtil.getFileMapPath(dirFile);
            java.io.File excelFile = filemap.remove("excelfile");
            log.info("去读文件列表结果当前文件总数:{}", filemap.size());
            EasyExcel.read(excelFile, ModelExcelAllPropDTO.class, new CustomerModelExcelAllListener(filemap, dirFile,
                    modelDataSaveService, StringUtils.EMPTY,true)).sheet().doRead();
//        } finally {
//            ZipUtil.deleteAllFile(dirFile);
//        }
    }

    @Override
    public String checkImportHisData(MultipartFile file) {
        String originalFilename = file.getOriginalFilename();
        originalFilename = originalFilename.toLowerCase();
        Assert.isTrue((originalFilename.endsWith("xlsx") || originalFilename.endsWith("xls")), "import.file.format");
        // 读数据
        List<CheckImportDTO> rows;
        try {
            rows = ExcelUtils.read(file.getInputStream(), CheckImportDTO.class);
        } catch (IOException e) {
            throw new JWIServiceException("Excel file parse error:" + e.getMessage());
        }
        rows = rows.subList(2, rows.size() - 1);

        Set<String> numberSet = rows.stream().map(CheckImportDTO::getNumber).collect(Collectors.toSet());

        Map<String, DocumentIteration> numberMap = jwiCommonService.dynamicQuery(DocumentIteration.TYPE,
                Condition.where("number").in(numberSet), DocumentIteration.class).stream().collect(Collectors.toMap(DocumentIteration::getNumber, v -> v));
        ;
        List<String> resultList = new ArrayList<>();
        for (CheckImportDTO row : rows) {
            StringBuffer stringBuffer = new StringBuffer();
            if (!numberMap.containsKey(row.getNumber())) {
                stringBuffer.append("指定编码数据未创建:" + row.getNumber());
            } else {
                DocumentIteration documentIteration = numberMap.get(row.getNumber());
                if (!StringUtils.equals(documentIteration.getName(), row.getName())) {
                    stringBuffer.append("导入数据名称不符:" + row.getNumber() + " ImportName:" + row.getName() + " " +
                            "CurrentName:" + documentIteration.getName());
                }

                if (StringUtils.isNotBlank(row.getPrimaryFile())) {
                    String fileName = row.getPrimaryFile().split("/")[1];
                    if (ObjectUtils.isEmpty(documentIteration.getPrimaryFile())) {
                        Set<String> nameList =
                                documentIteration.getPrimaryFile().stream().map(File::getName).collect(Collectors.toSet());
                        if (!nameList.contains(fileName)) {
                            stringBuffer.append("主文件未找到:" + row.getNumber() + " pFile" + fileName);
                        }
                    }
                }

                if (StringUtils.isNotBlank(row.getSecondaryFile())) {
                    String fileName = row.getSecondaryFile().split("/")[1];
                    if (ObjectUtils.isEmpty(documentIteration.getSecondaryFile())) {
                        Set<String> nameList =
                                documentIteration.getSecondaryFile().stream().map(File::getName).collect(Collectors.toSet());
                        if (!nameList.contains(fileName)) {
                            stringBuffer.append("附件未找到:" + row.getNumber() + " pFile" + fileName);
                        }
                    }
                }
            }
            if (stringBuffer.length() > 0) {
                resultList.add(stringBuffer.toString());
            }
        }
        return StringUtils.join(resultList, "\n");
    }

    @Override
    public void confirmChange(String oid, String changeOid) {
        //将原始数据的latest的标记修改为false
        documentService.updateLatest(oid, false);
        logger.info("updateProject updateLatest oid:{}",oid);
        //将方案查询出来并复制一份副本(升级大版本，latest标记修改为true，重新和库以及master，变更对象建立关系)
        String entityOid = UUID.randomUUID().toString();
        DocumentIteration documentIteration =  (DocumentIteration)documentService.findDetailEntity(changeOid, DocumentIteration.TYPE);
        documentIteration.setLatest(true);
        documentIteration.setOid(entityOid);
        logger.info("updateProject documentIteration info:{}",documentIteration);
        //升级大版本
        documentService.nextVersion(documentIteration);
        logger.info("updateProject nextVersion entityOid:{}",entityOid);
        // 处理升级后的状态
        //documentService.initLifecycleStatus(documentIteration);
        String lifecycleOid = documentIteration.getLifecycleOid();
        LifecycleTemplate lifecycleTemplate = lifecycleTemplateService.findByOid(lifecycleOid);
        documentIteration.setLifecycleStatus(getReviseDefaultStatus(lifecycleTemplate));
        //创建新的工序信息和库，master创建关系
        logger.info("updateProject createCopy");
        documentService.createCopy(documentIteration);
        logger.info("updateProject createContain");
        documentService.createContain(documentIteration);
        logger.info("updateProject createMasterNotCreate");
        documentService.createMasterNotCreate(documentIteration.getMasterOid(),documentIteration.getMasterType(),entityOid,DocumentIteration.TYPE);
        // 执行变更方案后，对于已修改的交付清单进行变更
        DocumentIteration byOid = jwiCommonService.findByOid(DocumentIteration.TYPE,oid,DocumentIteration.class);
        logger.info("byOid has {}",byOid == null);
        if(byOid != null) {
            logger.info("confirmChange start update Delivery befJson:{};newVersion:{}",byOid.getExtensionContent(), JSONObject.toJSONString(documentIteration));
            updateBindDelivery(documentIteration.getExtensionContent(),byOid);
        }
    }

    public String confirmChangeAndReturn(String oid, String changeOid) {
        //将原始数据的latest的标记修改为false
        documentService.updateLatest(oid, false);
        logger.info("updateProject updateLatest oid:{}",oid);
        //将方案查询出来并复制一份副本(升级大版本，latest标记修改为true，重新和库以及master，变更对象建立关系)
        String entityOid = UUID.randomUUID().toString();
        DocumentIteration documentIteration =  (DocumentIteration)documentService.findDetailEntity(changeOid, DocumentIteration.TYPE);
        documentIteration.setLatest(true);
        documentIteration.setOid(entityOid);
        logger.info("updateProject documentIteration info:{}",documentIteration);
        //升级大版本
        documentService.nextVersion(documentIteration);
        logger.info("updateProject nextVersion entityOid:{}",entityOid);
        // 处理升级后的状态
        //documentService.initLifecycleStatus(documentIteration);
        String lifecycleOid = documentIteration.getLifecycleOid();
        LifecycleTemplate lifecycleTemplate = lifecycleTemplateService.findByOid(lifecycleOid);
        documentIteration.setLifecycleStatus(getReviseDefaultStatus(lifecycleTemplate));
        //创建新的工序信息和库，master创建关系
        logger.info("updateProject createCopy");
        documentService.createCopy(documentIteration);
        logger.info("updateProject createContain");
        documentService.createContain(documentIteration);
        logger.info("updateProject createMasterNotCreate");
        documentService.createMasterNotCreate(documentIteration.getMasterOid(),documentIteration.getMasterType(),entityOid,DocumentIteration.TYPE);
        return entityOid;
    }

    private Map<String,String> queryChangeSchemaDocOidMap(String ecrOid) {
        List<ChangeInfo> changeInfo = changeService.findChangeInfo(ecrOid, null);

        HashMap<String,String> map = new HashMap();
        changeInfo.forEach(n -> {
            String changeSchemeOid = ecaService.findChangeSchemeOid(n.getOid(), n.getType());
            if (StringUtil.isEmpty(changeSchemeOid)) {
                throw new JWIException(n.getNumber() + "不存在变更方案无法变更，请填写变更方案");
            }
            map.put(changeSchemeOid, n.getOid());
        });
        return map;
    }
    /**
     *
     * @param dto  word签名
     * @return
     */
    @Override
    public boolean docSignWorkflow(DocSignWorkflowDTO dto) {
        //ValidationUtil.validate(dto);

        log.info("DocSignWorkflowDTO dto info:{}",JSONObject.toJSON(dto));

        JSONObject data = dto.getData();
        Assert.isFalse(MapUtil.isEmpty(data), "data is empty");
        String signTemplateCode = data.getString("signTemplateCode");
        String operateType = data.getString("operateType");
        int signPageNum = data.getInteger("pageNum");
        Assert.notBlank(signTemplateCode, "Param signTemplateCode can not be null");
        boolean isChange = data.getBooleanValue("isChange");

        Map<String,String> docSchemaOidMap;
        if (isChange) {
            docSchemaOidMap = queryChangeSchemaDocOidMap(data.getString("ecrOid"));
        } else {
            docSchemaOidMap = new HashMap<>();
        }
        log.info("docSignWorkflow docSchemaOidMap {} ", docSchemaOidMap);

        List<String> documentOidList = data.getObject("documentOidList", List.class);
        String processInstanceId = dto.getProcessInstanceId();
        // 查询流程中的文档对象
        List<DocumentIteration> docs =
                commonAbilityService.findDetailEntity(documentOidList, DocumentIteration.TYPE).stream().map(item -> (DocumentIteration) item).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(docs)) {
            return true;
        }
        // 查询 签名模版
        TemplateFindDTO templateFindDTO = new TemplateFindDTO();
        templateFindDTO.setCategory("signTemplate");
        templateFindDTO.setName(signTemplateCode);
        templateFindDTO.setContainerOid(dto.getTenantOid());
        ContainerTemplate signTemplate = containerRemote.findTemplateByName(templateFindDTO);
        Assert.notNull(signTemplate, "Sign template does not exists");
        File signTemplateFile = signTemplate.getFile();
        Assert.notNull(signTemplateFile, "Sign template does not exists");

        //需要签名转换PDF上传附件，文件清单
        List<File> listSign = new ArrayList<>();
        //需要转换pdf上传附件，文件清单（外协类的）
        List<File> listToPdf = new ArrayList<>();
        //主文件上传附件，文件清单（主文件是zip，表格）
        List<File> listToUpload = new ArrayList<>();

        for (DocumentIteration doc : docs) {
            log.info("docSignWorkflow docFile Detail:{}", JSONObject.toJSONString(doc));
            List<File> primaryFileList = doc.getPrimaryFile();
            Assert.notEmpty(primaryFileList, "Primary File does not exists");
            File primaryFile=primaryFileList.stream().findFirst().get();
            DocumentIteration detail=documentService.findByOid(doc.getOid());

            if(primaryFile.getName().endsWith("doc") || primaryFile.getName().endsWith("docx") ){

                if(detail.getExtensionContent() != null && "外协类文件".equals(detail.getExtensionContent().getString("source"))){
                    listToPdf.add(primaryFile);
                }else {
                    listSign.add(primaryFile);
                }
            }else {
                //把主文件上传附件
                listToUpload.add(primaryFile) ;
            }

        }

        Map<String, File> toUploadMap = listToUpload.stream().collect(Collectors.toMap(f -> f.getOid(), v -> v));
        // 准备签名参数
        GenerateSignDocumentRemote generateSignDocumentDTO = new GenerateSignDocumentRemote();
        generateSignDocumentDTO.setProcessInstanceId(processInstanceId);
        generateSignDocumentDTO.setSignTemplateFileOid(signTemplateFile.getOid()); // 签名模版 DFS 文件oid
        generateSignDocumentDTO.setPageNum(signPageNum);  // 页码
        generateSignDocumentDTO.setOperateType(operateType); // 签名页织入方式
        List<SignTemplateContentPicture> list = new ArrayList<>();
        generateSignDocumentDTO.setPictureCollection(list);
        Map<String, FileMetadata> oldFileOid2NewFile =new HashMap<>();
        buildSignWorkflowTextParam(generateSignDocumentDTO, dto.getAccountParams()); // 文本参数
        buildSignWorkflowPictureParam(generateSignDocumentDTO, dto.getAccountParams()); // 签名参数
        if("0".equals(data.getString("checkInOut")))
            generateSignDocumentDTO.setDocumentIterationOidList(documentOidList); // 手动生成pdf修改为查询的documentOidList
        if(CollectionUtil.isNotEmpty(listSign)){
            generateSignDocumentDTO.setOperateDocList(new ArrayList<>(listSign)); // 文档对象 oid
            // 生成签名内容
            oldFileOid2NewFile = signTemplateRemote.toGenerateSignDocument(generateSignDocumentDTO);
        }

        if(CollectionUtil.isNotEmpty(listToPdf)){
            Map<String, FileMetadata> collect = listToPdf.stream().collect(Collectors.toMap(f -> f.getOid(), v -> fileService.findByOid(v.getOid())));
            oldFileOid2NewFile.putAll(collect);
        }

        Map<String, DocumentIteration> oldFileOidDocOidMap = new HashMap<>();
        docs.stream().collect(() -> oldFileOidDocOidMap, (m, t) -> safeWrapList(t.getPrimaryFile()).stream().forEach(file -> m.put(file.getOid(), t)), (m1, m2) -> {});
        // word转pdf
        oldFileOid2NewFile =
                oldFileOid2NewFile.entrySet().parallelStream().collect(Collectors.toConcurrentMap(entry -> entry.getKey(), entry -> customerCommonService.doc2PdfAddCode(entry.getValue(), oldFileOidDocOidMap.get(entry.getKey()), isChange), (v1, v2) -> v1));

        // 填充新文件， 并检入
        for (DocumentIteration documentIteration : docs) {
            List<File> primaryFileList = documentIteration.getPrimaryFile();
            List<String> fileNameList =
                    primaryFileList.stream().map(f -> getFileNameIgnoreSuffix(f.getName()) + "_" + documentIteration.getVersion() + "_会签归档版").collect(Collectors.toList());
            File primaryFile = primaryFileList.stream().findFirst().get();
            List<File> secondaryFile = documentIteration.getSecondaryFile();
            if (CollectionUtil.isEmpty(secondaryFile)) {
                secondaryFile = new ArrayList<>();
            }
            //若为变更流程 需要按照名称匹配 清理上个版本的 pdf文件
            if (isChange && docSchemaOidMap.containsKey(documentIteration.getOid())) {
                log.info("start to clean oid:{}", docSchemaOidMap.get(documentIteration.getOid()));
                cleanOldSecondaryFile(secondaryFile,docSchemaOidMap.get(documentIteration.getOid()));
            }
            FileMetadata entry = oldFileOid2NewFile.get(primaryFile.getOid());
            if (entry != null) {
                File newFile = new File();
                newFile.setName(entry.getFileOriginalName());
                newFile.setOid(entry.getOid());
                secondaryFile.add(newFile);
            } else if (toUploadMap.containsKey(primaryFile.getOid())) {
                File newFile = new File();
                newFile.setName(toUploadMap.get(primaryFile.getOid()).getName());
                newFile.setOid(toUploadMap.get(primaryFile.getOid()).getOid());
                secondaryFile.add(newFile);
            }
            if (secondaryFile != null) {
                documentIteration.setSecondaryFile(secondaryFile);
            }


            log.info("toUpdate documentIteration {} ", JSONObject.toJSON(documentIteration));
            //设置流程实例ID
            setInstanceData(documentIteration,dto);
            // 更新
            commonAbilityHelper.doUpdate(documentIteration);
            if (isChange && docSchemaOidMap.containsKey(documentIteration.getOid())) {
                String entityOid = confirmChangeAndReturn(docSchemaOidMap.get(documentIteration.getOid()),
                        documentIteration.getOid());
                customerCommonRepo.updateLifeStatus(DocumentIteration.TYPE,
                        new ArrayList<String>() {{
                            add(entityOid);
                        }}, "Released");
            }
        }
        return true;
    }

    private void cleanOldSecondaryFile(List<File> secondaryFile,String docOid) {
        if(StringUtils.isBlank(docOid)) {
            return;
        }
        DocumentIteration documentIteration = jwiCommonService.findByOid(DocumentIteration.TYPE, docOid, DocumentIteration.class);
        if (ObjectUtils.isEmpty(documentIteration)) {
            return;
        }
        if (ObjectUtils.isEmpty(documentIteration.getPrimaryFile())) {
            return;
        }
        for (Iterator<File> iterator = secondaryFile.iterator(); iterator.hasNext(); ) {
            File file = iterator.next();
            for (File primaryFile : documentIteration.getPrimaryFile()) {
                // 提取主文件名称和后缀
                String primaryFileName = primaryFile.getName();
                String primaryFileExtension = getFileExtension(primaryFileName);

                // 获取 PDF 名称
                String pdfName = customerCommonService.getDocPdfFileName(primaryFileName, documentIteration);

                // 条件1：当前附件名称与生成的 PDF 名称匹配
                boolean matchesPdfName = StringUtils.equalsIgnoreCase(file.getName(), pdfName);

                // 条件2：主文件后缀不是 doc/docx 且附件名称与主文件名称相同
                boolean matchesPrimaryFileName = !isDocOrDocx(primaryFileExtension) && StringUtils.equalsIgnoreCase(file.getName(), primaryFileName);

                // 如果满足任一条件，则移除附件
                if (matchesPdfName || matchesPrimaryFileName) {
                    iterator.remove();
                }
            }
        }
    }

    /**
     * 判断文件后缀是否为 doc 或 docx
     */
    private boolean isDocOrDocx(String extension) {
        return "doc".equalsIgnoreCase(extension) || "docx".equalsIgnoreCase(extension);
    }

    /**
     * 获取文件的扩展名
     */
    private String getFileExtension(String fileName) {
        if (StringUtils.isBlank(fileName) || !fileName.contains(".")) {
            return StringUtils.EMPTY;
        }
        return fileName.substring(fileName.lastIndexOf(".") + 1);
    }

    private void setInstanceData(DocumentIteration documentIteration, DocSignWorkflowDTO dto) {
        try {
            JSONObject data = dto.getData();
            if (ObjectUtils.isEmpty(data)) {
                return;
            }
            JSONObject extensionContent = documentIteration.getExtensionContent();
            if (ObjectUtils.isEmpty(extensionContent)) {
                extensionContent = new JSONObject();
                documentIteration.setExtensionContent(extensionContent);
            }
            extensionContent.put("processInstanceId", data.getString("processInstanceId"));
            extensionContent.put("processBusinessId", data.getString("processBusinessId"));
            extensionContent.put("subTime", data.getLongValue("subTime"));
            extensionContent.put("storageTime", data.getLongValue("storageTime"));
            if(ObjectUtils.isNotEmpty(data.getLongValue("subTime"))) {
                documentIteration.setCreateDate(data.getLongValue("subTime"));
            }
        } catch (JWIException e) {
            log.info("setInstanceData error", e);
        }
    }


    public File getSignTemplateFile(String signTemplateCode, String containerOid){
        // 查询签名模板
        TemplateFindDTO templateFindDTO = new TemplateFindDTO();
        templateFindDTO.setCategory("signTemplate");
        templateFindDTO.setName(signTemplateCode);
        templateFindDTO.setContainerOid(containerOid);

        ContainerTemplate signTemplate = containerRemote.findTemplateByName(templateFindDTO);
        Assert.notNull(signTemplate, "Sign template does not exists");
        File signTemplateFile = signTemplate.getFile();
        Assert.notNull(signTemplateFile, "Sign template does not exists");
        return signTemplateFile;
    }

    @Autowired
    private SignTemplateFileRemote fileRemote;

    public byte[] transformResponseInput2Bytes(String fileOid) throws IOException {
        Response response = this.fileRemote.downloadByFileOid(fileOid);
        CheckUtil.check(response.status() == 200, String.format(Locale.ENGLISH, "获取文件内容失败 fileOid:%s", fileOid));
        return IOUtils.toByteArray(response.body().asInputStream());
    }

    /**
     *
     * @param dto  pdf签名
     * @return
     */
    @Override
    public boolean degSignWorkflow(DwgSignWorkflowDTO dto) {
        try {
            log.info("DwgSignWorkflowDTO dto info:{}",JSONObject.toJSON(dto));
            ValidationUtil.validate(dto);
            JSONObject data = dto.getData();
            Assert.isFalse(MapUtil.isEmpty(data), "data is empty");
            JSONObject processTemplateData = dto.getProcessTemplateData();
            String processOrderType = dto.getProcessOrderType();
            String processOrderOid = dto.getProcessOrderOid();

            File signTemplateFile = getSignTemplateFile(processTemplateData.getString("signTemplateCode"), dto.getTenantOid());
            processTemplateData.put("signTemplateOid", signTemplateFile.getOid());
            // 查询流程中的结构CAD对象
            SubFilter subFilter = new SubFilter(processOrderType, processOrderOid, Review.TYPE, MCADIteration.TYPE);
            subFilter.setFilter(Condition.where("modelDefinition").eq("CADDrawing"));
            List<MCADIteration> docs = mcadIterationRepo.dynamicQueryByFrom(subFilter);
            log.info("subFilter:----->>>{}", JSONUtil.toJsonStr(subFilter));
            log.info("docs:----->>>{}", JSONUtil.toJsonStr(docs));
//        List<MCADIteration> docs = mcadService.dynamicQueryByFrom(subFilter);
            if (CollectionUtil.isEmpty(docs)) {
                return true;
            }
            List<File> files = new LinkedList<>();
            for (MCADIteration doc : docs) {
                String thumbnailOid = doc.getThumbnailOid();
                if (StringUtils.isNotEmpty(thumbnailOid)) {
                    File file = new File();
                    file.setOid(thumbnailOid);
                    file.setName(doc.getName());
                    files.add(file);
                }
            }
            if (CollectionUtil.isEmpty(files)) {
                return true;
            }
            // 准备签名参数
            SignPersonDTO signPersonDTO = new SignPersonDTO();
            signPersonDTO.setOperateDocList(new ArrayList<>(files));
            fillRatifier(data); //批准者信息缺失时补充完整
            signPersonDTO.setProcessNode(data);
            signPersonDTO.setProcessTemplateData(processTemplateData);
            log.info("degSign insId:{} requestBody: {}", dto.getProcessInstanceId(),
                    JSONObject.toJSON(signPersonDTO));
            // 生成签名内容
            Map<String, FileMetadata> oldFileOid2NewFile = signTemplateRemote.toGenerateSignPerson(signPersonDTO);
            log.info("degSign insId:{} toGenerateSignPerson response: {}",
                    dto.getProcessInstanceId(), JSONObject.toJSON(oldFileOid2NewFile));
            for (MCADIteration updateNode : docs) {
                String thumbnailOid = updateNode.getThumbnailOid();
                log.info("degSign insId:{} thumbnailOid:{} oid:{}", dto.getProcessInstanceId(), thumbnailOid,
                        updateNode.getOid());
                FileMetadata fileMetadata = oldFileOid2NewFile.get(thumbnailOid);
                if (fileMetadata == null) {
                    log.info("degSign insId:{} fileMetadata isNull continue", dto.getProcessInstanceId());
                    continue;
                }
                updateNode.setThumbnailOid(fileMetadata.getOid());
                //检入时设置生命周期为Released
                updateNode.setLifecycleStatus("Released");
                updateSignCadFile(updateNode, fileMetadata);
                log.info("degSign insId:{} doUpdate", dto.getProcessInstanceId());
                // 更新
                commonAbilityHelper.doUpdate(updateNode);
            }
        }catch (Exception e) {
            log.error(e.getMessage(),e);
        } finally {
            saveRequestBodyInRecord(JSONObject.toJSONString(dto), dto.getProcessInstanceId());
        }
        return true;
    }

    /**
     * 通过钉钉 为流程中MCAD的pdf签名
     * @param dto 入参
     * @return
     */
    @Override
    public boolean degSignByDingTalk(DwgSignWorkflowDTO dto) {
        log.info("degSignByDingTalk start");
        try {
            log.info("degSignByDingTalk dto info:{}",JSONObject.toJSON(dto));
            JSONObject data1 = dto.getData();
            Assert.isFalse(MapUtil.isEmpty(data1), "审批信息为空");

            JSONObject data = data1.getJSONObject("dataJson");
            Assert.isFalse(MapUtil.isEmpty(data), "data is empty");
            JSONArray documentOidList = data1.getJSONArray("documentOidList");
            Assert.isFalse(documentOidList == null || documentOidList.isEmpty(), "documentOidList is empty or null");

            log.info("degSignByDingTalk.data---->>>{}", JSONObject.toJSONString(data));
            JSONObject processTemplateData = dto.getProcessTemplateData();

            File signTemplateFile = getSignTemplateFile(processTemplateData.getString("signTemplateCode"), dto.getTenantOid());
            processTemplateData.put("signTemplateOid", signTemplateFile.getOid());
            // 查询流程中的结构CAD对象
            List<MCADIteration> docs = new ArrayList<>();
            for (int i = 0; i < documentOidList.size(); i++) {
                String oid = documentOidList.getString(i);
                MCADIteration mcadIteration = mcadHelper.findByOid(oid);
                if (mcadIteration != null && mcadIteration.getModelDefinition().equals("CADDrawing")) {
                    docs.add(mcadIteration);
                }
            }
            log.info("degSignByDingTalk.docs:----->>>{}", JSONUtil.toJsonStr(docs));
            if (CollectionUtil.isEmpty(docs)) {
                return true;
            }
            List<File> files = new LinkedList<>();
            for (MCADIteration doc : docs) {
                String thumbnailOid = doc.getThumbnailOid();
                if (StringUtils.isNotEmpty(thumbnailOid)) {
                    File file = new File();
                    file.setOid(thumbnailOid);
                    file.setName(doc.getName());
                    files.add(file);
                }
            }
            if (CollectionUtil.isEmpty(files)) {
                return true;
            }
            // 准备签名参数
            SignPersonDTO signPersonDTO = new SignPersonDTO();
            signPersonDTO.setOperateDocList(new ArrayList<>(files));
            fillRatifier(data); //批准者信息缺失时补充完整
            signPersonDTO.setProcessNode(data);
            signPersonDTO.setProcessTemplateData(processTemplateData);
            log.info("degSignByDingTalk insId:{} requestBody: {}", dto.getProcessInstanceId(), JSONObject.toJSON(signPersonDTO));
            // 生成签名内容
            Map<String, FileMetadata> oldFileOid2NewFile = signTemplateRemote.toGenerateSignPerson(signPersonDTO);
            log.info("degSignByDingTalk insId:{} toGenerateSignPerson response: {}",
                    dto.getProcessInstanceId(), JSONObject.toJSON(oldFileOid2NewFile));
            for (MCADIteration updateNode : docs) {
                String thumbnailOid = updateNode.getThumbnailOid();
                log.info("degSignByDingTalk insId:{} thumbnailOid:{} oid:{}", dto.getProcessInstanceId(), thumbnailOid,
                        updateNode.getOid());
                FileMetadata fileMetadata = oldFileOid2NewFile.get(thumbnailOid);
                if (fileMetadata == null) {
                    log.info("degSignByDingTalk insId:{} fileMetadata isNull continue", dto.getProcessInstanceId());
                    continue;
                }
                updateNode.setThumbnailOid(fileMetadata.getOid());
                //检入时设置生命周期为Released
                updateNode.setLifecycleStatus("Released");
                updateSignCadFile(updateNode, fileMetadata);
                log.info("degSignByDingTalk insId:{} doUpdate", dto.getProcessInstanceId());
                // 更新
                commonAbilityHelper.doUpdate(updateNode);
                log.info("degSignByDingTalk end");
            }
        }catch (Exception e) {
            log.error(e.getMessage(),e);
        } finally {
            saveBomSignRequestInRecord(JSONObject.toJSONString(dto), dto.getProcessInstanceId());
        }
        return true;
    }

    public void saveBomSignRequestInRecord(String string,String processInstanceId) {
        List<DingTaskRecord> dingTaskRecordList = jwiCommonService.dynamicQuery(DingTaskRecord.TYPE, Condition.where(
                "dingProcessInstanceId").eq(processInstanceId),DingTaskRecord.class);
        DingTaskRecord dingTaskRecord =
                dingTaskRecordList.stream().sorted(Comparator.comparing(DingTaskRecord::getCreateDate).reversed()).findFirst().orElse(null);
        log.info("当前要保存的dingTaskRecord:{}", JSONUtil.toJsonStr(dingTaskRecord));
        if(dingTaskRecord != null) {
            dingTaskRecord.setSignRequest(string);
            jwiCommonService.update(dingTaskRecord);
        }
    }

    public void saveRequestBodyInRecord(String string,String processInstanceId) {
        List<DingTaskRecord> dingTaskRecordList = jwiCommonService.dynamicQuery(DingTaskRecord.TYPE, Condition.where(
                "pdmProcessInstanceId").eq(processInstanceId),DingTaskRecord.class);
        DingTaskRecord dingTaskRecord =
                dingTaskRecordList.stream().sorted(Comparator.comparing(DingTaskRecord::getCreateDate).reversed()).findFirst().orElse(null);
        if(dingTaskRecord != null) {
            dingTaskRecord.setSignRequest(string);
            jwiCommonService.update(dingTaskRecord);
        }
    }
    /**
     *
     * @param dto ecad pdf签名
     * @return
     */
    @Override
    public boolean ecadDegSignWorkflow(JSONObject dto) {
        ValidationUtil.validate(dto);
        JSONObject data = dto.getJSONObject("data");
        Assert.isFalse(MapUtil.isEmpty(data), "data is empty");
        try {
            JSONObject processTemplateData = dto.getJSONObject("processTemplateData");

            File signTemplateFile = getSignTemplateFile(processTemplateData.getString("signTemplateCode"), dto.getString("tenantOid"));
            processTemplateData.put("signTemplateOid",signTemplateFile.getOid());

            List<ECADIteration> docs = ecadHelper.findByOid(dto.getJSONArray("ecadOidList"));

            if (CollectionUtil.isEmpty(docs)) {
                return true;
            }
            List<File> files = new LinkedList<>();
//        for (ECADIteration doc : docs) {
//            for (File secFile : doc.getPrimaryFile()) {
//                String secFileOid = secFile.getOid();
//                if(StringUtils.isNotEmpty(secFileOid) && secFile.getName().endsWith(".pdf")){
//                    File file = new File();
//                    file.setOid(secFileOid);
//                    file.setName(secFile.getName());
//                    files.add(file);
//                }
//            }
//        }

            if (CollectionUtil.isEmpty(files)) {
                for (ECADIteration doc : docs) {
                    for (File secFile : doc.getSecondaryFile()) {
                        String secFileOid = secFile.getOid();
                        if(StringUtils.isNotEmpty(secFileOid) && secFile.getName().endsWith(".pdf")){
                            File file = new File();
                            file.setOid(secFileOid);
                            file.setName(secFile.getName());
                            files.add(file);
                        }
                    }
                    JSONObject extension = doc.getExtensionContent();
                    data.fluentPut("版本节点", new JSONObject().fluentPut("name", doc.getVersion()).fluentPut("assignee", doc.getVersion()).fluentPut("date", ""));
                    data.fluentPut("密级节点", new JSONObject().fluentPut("name", extension.getOrDefault("cn_jwis_secrecy", "")).fluentPut("assignee", extension.getOrDefault("cn_jwis_secrecy", "")).fluentPut("date", ""));
                }
            }

            if (CollectionUtil.isEmpty(files)) {
                return true;
            }
            // 检出
            List<ECADIteration> checkOutNode = new ArrayList<>(docs.size());
            for (ECADIteration doc : docs) {
                ModelInfo modelInfo = new ModelInfo();
                modelInfo.setOid(doc.getOid());
                modelInfo.setType(doc.getType());
                //2024-09-11 暂时取消检出、取消检入
//            checkOutNode.add((ECADIteration)checkOut(modelInfo));
                checkOutNode.add(doc);
            }
            // 准备签名参数
            SignPersonDTO signPersonDTO = new SignPersonDTO();
            signPersonDTO.setOperateDocList(new ArrayList<>(files));
            fillRatifier(data); //批准者信息缺失时补充完整
            signPersonDTO.setProcessNode(data);
            signPersonDTO.setProcessTemplateData(processTemplateData);

            // 生成签名内容
            Map<String, FileMetadata> oldFileOid2NewFile = signTemplateRemote.toGenerateSignPerson(signPersonDTO);
            log.info("checkOutNode.before: {}", JSONUtil.toJsonStr(checkOutNode));
            log.info("oldFileOid2NewFile: {}", JSONUtil.toJsonStr(oldFileOid2NewFile));
            A: for (ECADIteration checkNode : checkOutNode) {
                AtomicReference<Boolean> checkOut = new AtomicReference<>(Boolean.FALSE);
                List<File> secFileNewList = checkNode.getSecondaryFile().stream().map(secFile -> {
                    if(secFile.getName().endsWith(".pdf")){
                        FileMetadata fileMetadata = oldFileOid2NewFile.get(secFile.getOid());
                        if(fileMetadata != null){
                            File file = new File();

//                        file.setName(fileMetadata.getFileOriginalName());
                            String fileName = secFile.getName();
                            int dotIndex = fileName.lastIndexOf(".");
                            String baseName = fileName.substring(0, dotIndex);
                            String extension = fileName.substring(dotIndex);
                            // 拼接新文件名
                            String newFilename = baseName + "_" + checkNode.getVersion() + "_会签归档版" + extension;
                            file.setName(newFilename);
                            file.setOid(fileMetadata.getOid());
                            checkNode.setThumbnailOid(fileMetadata.getOid());
                            checkOut.set(Boolean.TRUE);
                            return file;
                        }
                    }
                    return secFile;
                }).collect(Collectors.toList());
                if (checkOut.get() == Boolean.FALSE) {
                    cancelCheckOut(BeanUtil.copyProperties(checkNode, new ModelInfo()));
                    continue A;
                }else {
                    checkNode.setSecondaryFile(secFileNewList);
//                updateEcadSignCadFile(checkNode,fileMetadata); // TODO
                    // 更新
                    checkNode.setLifecycleStatus("Released");
                    commonAbilityHelper.doUpdate(checkNode);
                    // 检入
//                checkIn(BeanUtil.copyProperties(checkNode, new LockInfo()));

                }
            }
            log.info("checkOutNode.after: {}", JSONUtil.toJsonStr(checkOutNode));
        }catch  (Exception e) {
            log.error(e.getMessage(),e);
        }finally {
            saveBomSignRequestInRecord(JSONObject.toJSONString(dto), dto.getString("processInstanceId"));
        }
        return true;
    }

    @JWIServiceAudit(
            buzOid = "${result.oid}",
            bizNumber = "${result.number}",
            bizName = "${result.name}",
            buzType = "${result.type}",
            action = Action.SAVE_AS,
            content = "复制文档,复制后名称为${result.name}"
    )
    @JWIParam("result")
    @DataDistribution(
            dataOpt = "create"
    )
    @Override
    public DocumentIteration copy(CopyDocumentDTO dto) throws JWIException {
        DocumentIteration copy = super.copy(dto);
        ClassificationInfo classificationInfo = dto.getClassificationInfo();
        if (classificationInfo != null) {
            BaseEntity result = this.docDeliveryRemote.findByClsOid(copy.getContainerOid(), classificationInfo.getOid());
            if (result != null) {
                String deliveryOid = result.getOid();
                String deliveryType = result.getType();
                List<RelationAble> rels = new ArrayList();
                Reference reference = new Reference();
                reference.setFromOid(deliveryOid);
                reference.setFromType(deliveryType);
                reference.setToOid(copy.getMasterOid());
                reference.setToType(copy.getMasterType());
                rels.add(reference);
                this.commonAbilityService.createOutRelation(rels);
            }
        }
        return copy;
    }

    private void fillRatifier(JSONObject data) {
        if(!data.containsKey("批准")){
            UserDTO userDTO = SessionHelper.getCurrentUser();
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("assignee", userDTO.getAccount());
            jsonObject.put("name", userDTO.getName());
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy/M/d");
            String currentTime = dateFormat.format(new Date());
            jsonObject.put("date", currentTime);
            data.put("批准", jsonObject);
        }
    }

    private void updateSignCadFile(MCADIteration checkNode, FileMetadata fileMetadata) {
        List<CADFile> cadFiles = mcadHelper.findCADFile(checkNode.getOid());
        cadFiles = cadFiles.stream().filter(cadFile -> "thumbnail".equals(cadFile.getFileType())).collect(Collectors.toList());
        CADFile cadFile = CollectionUtil.getFirst(cadFiles);
        if(cadFile != null){
            cadFile.setUrl(fileMetadata.getOid());
            String version = checkNode.getVersion();
            String fileName = cadFile.getFileName();
            // 去掉文件名的扩展名
            int dotIndex = fileName.lastIndexOf(".");
            String baseName = fileName.substring(0, dotIndex);
            String extension = fileName.substring(dotIndex);
            // 拼接新文件名
            String newFilename = baseName + "_" + version + "_会签归档版" + extension;
            cadFile.setFileName(newFilename);
            log.info("updateSignCadFile.fileName{}", fileName);
            log.info("updateSignCadFile.newFilename{}", newFilename);
        }
        commonAbilityHelper.doUpdate(cadFile);
    }

    @Autowired
    private ECADHelper ecadHelper;

    private void updateEcadSignCadFile(ECADIteration checkNode, FileMetadata fileMetadata) {
//        List<CADFile> cadFiles = ecadHelper.find(checkNode.getOid());
//        cadFiles = cadFiles.stream().filter(cadFile -> "thumbnail".equals(cadFile.getFileType())).collect(Collectors.toList());
//        CADFile cadFile = CollectionUtil.getFirst(cadFiles);
//        if(cadFile != null){
//            cadFile.setUrl(fileMetadata.getOid());
//        }
//        commonAbilityHelper.doUpdate(cadFile);
    }

    private String getFileNameIgnoreSuffix(String fileName){
        String[] nameUnits = fileName.split("\\.");
        StringBuffer result = new StringBuffer();
        for(int i = 0; i<nameUnits.length-1; i++){
            result.append(nameUnits[i]);
        }
        return result.toString();
    }

    private String getReviseDefaultStatus(LifecycleTemplate lifecycleTemplate) {
        List<String> statusList = lifecycleTemplate.getContext().getStates().stream()
                .map(s->s.getCode()).collect(Collectors.toList());
        if (statusList.contains(reviseDefaultStatus)) {
            return reviseDefaultStatus;
        } else {
            return lifecycleTemplate.findStartStatus();
        }
    }

    private void buildSignWorkflowTextParam(GenerateSignDocumentDTO generateSignDocumentDTO, JSONObject textParam) {
        if (MapUtil.isEmpty(textParam)) {
            generateSignDocumentDTO.setTextCollection(new LinkedList<>());
        }
        Collection<SignTemplateContentNormalText> result = generateSignDocumentDTO.getTextCollection();
        if (result == null) {
            result = new ArrayList<>(textParam.size());
            generateSignDocumentDTO.setTextCollection(result);
        }
        for (String key : textParam.keySet() ) {
            if(!"cs".equals(key) && textParam.containsKey(key +"_time")){
                result.add(new SignTemplateContentNormalText(key, textParam.getString(key) + "" +textParam.getString(key +"_time")));
            }else {
                result.add(new SignTemplateContentNormalText(key, textParam.getString(key)));
            }
        }
        generateSignDocumentDTO.setTextCollection(result);
    }

    private void buildSignWorkflowPictureParam(GenerateSignDocumentDTO generateSignDocumentDTO, JSONObject accountParams) {
        logger.info("accountParams {}",accountParams);
        if (MapUtil.isEmpty(accountParams)) {
            generateSignDocumentDTO.setPictureCollection(new LinkedList<>());
        }

    }

    private String getJsonStringValue(JSONObject jsonObject,String key) {
        String value = jsonObject.getString(key);
        return StringUtils.isBlank(value) ? StringUtils.EMPTY : value;
    }

    @Override
    @JWIServiceAudit(buzOid = "${result.oid}",
            bizNumber = "${result.number}",
            bizName = "${result.name}",
            buzType = "${dto.type}",action = Action.ADD,content = "创建文档,名称为：${result.name}")
    @JWIParam("result")
    @DataDistribution(dataOpt = "create")
    public DocumentIteration create(ExtDocumentCreateDTO dto) throws JWIException {
        ValidationUtil.validate(dto);

        // 确定 所在目录
        String catalogOid = dto.getLocationInfo().getCatalogOid();
        String catalogType = dto.getLocationInfo().getCatalogType();
        //检查指定编码是否重复
        checkNumberRepeat(dto);
        // 校验目录下重名，获取目录下的名称锁
        String name = dto.getName();
        RLock nameLock = redissonClient.getLock(MessageFormat.format(REDISSON_DOCUMENT_NAME_KEY, catalogOid, name));
        nameLock.tryLock();
        Assert.isTrue(nameLock.isHeldByCurrentThread(), "同名文件夹正在创建中:" + name);
        try {
            List<DocumentIteration> byName = findByNameAndCatalog(catalogType, catalogOid, name);
            Assert.isEmpty(byName, "目录下已存在同名文档:" + name);

            DocumentIteration documentIteration = BeanUtil.copyProperties(dto, new DocumentIteration());
            documentService.setLocation(documentIteration, dto.getLocationInfo());
            ClassificationInfo classificationInfo = dto.getClassificationInfo();
            if (classificationInfo != null) {
                documentService.setClassification(documentIteration, classificationInfo);
            }
            DocumentIteration resultDoc = commonAbilityHelper.doCreate(documentIteration);
            //根据创建时的deliveryOid进行交付清单关联
            if (StringUtils.isNotBlank(getDeliveryOidFromJson(resultDoc.getExtensionContent()))) {
                bindToDelivery(resultDoc.getMasterOid(), getDeliveryOidFromJson(resultDoc.getExtensionContent()));
            }
            return resultDoc;
        } finally {
            if (nameLock.isHeldByCurrentThread()) {
                nameLock.unlock();
            }
        }
    }

    @JWIServiceAudit(
            buzOid = "${result.oid}",
            buzType = "${result.type}",
            bizNumber = "${result.number}",
            bizName = "${result.name}",
            action = Action.UPDATE,
            content = "更新文档,对象的名称为：${result.name}"
    )
    @JWIParam("result")
    @DataDistribution(
            dataOpt = "change"
    )
    public DocumentIteration update(DocumentUpdateDTO dto) throws JWIException {
        ValidationUtil.validate(dto);
        String oid = dto.getOid();
        DocumentIteration byDocIteration = this.documentService.findByOid(oid);
        updateBindDelivery(dto.getExtensionContent(),byDocIteration);
        if (Objects.isNull(byDocIteration)) {
            return byDocIteration;
        } else {
            String lockSourceOid = byDocIteration.getLockSourceOid();
            Assert.notBlank(lockSourceOid, "Only check-out nodes are allowed to be updated");
            Assert.isTrue(this.documentService.hasLock("Document", lockSourceOid), "Please check it out first!");
            BeanUtil.copyProperties(dto, byDocIteration);
            ClassificationInfo classificationInfo = dto.getClassificationInfo();
            if (classificationInfo != null) {
                this.documentService.setClassification(byDocIteration, classificationInfo);
            } else {
                byDocIteration.setClsOid("");
            }
            return this.commonAbilityHelper.doUpdate(byDocIteration);
        }
    }

    private String getDeliveryOidFromJson(JSONObject jsonObject) {
        if(jsonObject == null) {
            return StringUtils.EMPTY;
        } else {
            return jsonObject.getString(KEY_DELIVERY_OID);
        }
    }

    private void updateBindDelivery(JSONObject updateExtensionContent,DocumentIteration befUpdateDoc) {
        String befDeliveryOid = getDeliveryOidFromJson(befUpdateDoc.getExtensionContent());
        String updateDeliveryOid = getDeliveryOidFromJson(updateExtensionContent);
        if(StringUtils.isBlank(updateDeliveryOid)) {
            return;
        }
        if(StringUtils.isNotBlank(updateDeliveryOid) && StringUtils.equalsAnyIgnoreCase(updateDeliveryOid,befDeliveryOid)) {
            return;
        }
        if(StringUtils.isNotBlank(befDeliveryOid)) {
            unBindLinkDelivery(befUpdateDoc.getMasterOid(),befDeliveryOid);
        }
        bindToDelivery(befUpdateDoc.getMasterOid(), updateDeliveryOid);
    }

    private void bindToDelivery(String masterOid, String deliveryOid) {
        commonAbilityService.createOutRelation(createRelation(masterOid,deliveryOid));
    }

    private void unBindLinkDelivery(String masterOid, String deliveryOid) {
        commonAbilityService.deleteOutRelation(createRelation(masterOid,deliveryOid));
    }

    private List<Reference> createRelation(String masterOid, String deliveryOid) {
        BaseEntity result = jwiCommonService.findByOid(Delivery.TYPE, deliveryOid,Delivery.class);
        if (ObjectUtils.isNotEmpty(result)) {
            Reference reference = new Reference();
            reference.setFromOid(result.getOid());
            reference.setFromType(result.getType());
            reference.setToOid(masterOid);
            reference.setToType(Document.TYPE);
            return Lists.newArrayList(reference);
        } else {
            return Lists.newArrayList();
        }
    }

    private void checkNumberRepeat(ExtDocumentCreateDTO dto) {
        if(StringUtils.isEmpty(dto.getNumber())) {
            return;
        }
        Assert.isNull(documentService.findByNumber(dto.getNumber()), "创建文档编码重复:" + dto.getNumber());
    }

    private void setDeliveryOid(DocumentIteration documentIteration,String deliveryOid) {
        if (ObjectUtils.isEmpty(deliveryOid)) {
            return;
        }
        JSONObject extensionContent = documentIteration.getExtensionContent();
        if(extensionContent == null) {
            extensionContent = new JSONObject();
            documentIteration.setExtensionContent(extensionContent);
        }
        extensionContent.put("deliveryOid", deliveryOid);
    }

    @Override
    public String rename(String oid, String name) throws JWIException {
        // 校验存在
        DocumentIteration byOid = documentService.findByOid(oid);
        // 校验名称是否存在
        List<DocumentIteration> byNameAndCatalog = findByNameAndCatalog(byOid.getCatalogType(), byOid.getCatalogOid(), name);
        Assert.isTrue(CollectionUtil.isEmpty(byNameAndCatalog) ||
                        (byNameAndCatalog.size() == 0 && byNameAndCatalog.get(0).getOid().equals(oid)),
                "目录下存在相同名称文档:" + name);
        // 重命名 所有的 iteartion
        documentService.renameAllIteration(oid, name);
        triggerAuditService.docRename(byOid,name);
        return name;
    }


    @Override
    public DocumentIteration deleteByOid(String oid) {
        DocumentIteration documentIteration = jwiCommonService.findByOid(DocumentIteration.TYPE,oid,
                DocumentIteration.class);
        triggerAuditService.deleteDoc(documentIteration);
        return super.deleteByOid(oid);
    };

    @Override
    public Long deleteByOid(List<String> oids) {
        List<DocumentIteration> docList = jwiCommonService.findByOid(DocumentIteration.TYPE,oids,
                DocumentIteration.class);
        Long result = super.deleteByOid(oids);
        docList.forEach(item -> triggerAuditService.deleteDoc(item));
        return result;
    };

    @Override
    @JWIServiceAudit(buzOid = "${node.lifecycleOid}",buzType = DocumentIteration.TYPE,action = Action.REVISE,content = "修订文档,修订后的状态为${result.lifecycleStatus}")
    @JWIParam("result")
    public LifecycleAble doRevise(LifecycleAble node) throws JWIException {
        DocumentIteration partIteration = (DocumentIteration) node;
        String sourceOid = node.getOid();
        String modelType = node.getType();
        // 获取生命周期模板
        LifecycleTemplate lifecycleTemplate = documentService.getLifecycleTemplate(node);
        Assert.notNull(lifecycleTemplate, "Lifecycle template does not exists!");

        // 复制成 新的 lifecycleAble 对象
        DocumentIteration copy = null;
        DocumentIteration newDoc = null;
        try {
            copy = BeanUtil.copyProperties(node, new DocumentIteration());
        }catch (Exception e){
            throw new JWIServiceException(e.getMessage());
        }
        // 设置 修订对象 的属性
        String copyOid = OidGenerator.newOid();
        copy.setOid(copyOid);
        // 设置生命周期状态
        copy.setLifecycleStatus(documentService.findLifecycleStartStatus(lifecycleTemplate));
        // 设置 版本
        documentService.nextVersion(copy);
        // 创建镜像
        newDoc = documentService.createCopy(copy);

        // 复制关系，除 Attribute、Generate、copy， LocalSubstitute 关系外
        CopyRel copyRel = new CopyRel();
        copyRel.setSourceType(DocumentIteration.TYPE);
        copyRel.setSourceOid(sourceOid);
        copyRel.setTargetType(DocumentIteration.TYPE);
        copyRel.setTargetOid(copyOid);
        List<String> excludeTypes = Arrays.asList(Attribute.TYPE, Generate.TYPE, Copy.TYPE, Describe.TYPE, Impact.TYPE);
        copyRel.setRelationTypes(excludeTypes);
        commonAbilityService.copyIORelExcludes(copyRel);
        commonAbilityService.deleteOuterLink(sourceOid,DocumentIteration.TYPE,"REVIEW_FOR");
        // 清除 传入历史对象 入的  非历史Use关系(即from节点必须是新的或检出的)
        // 清除 被检出对象 出的  与传入历史对象 的Use关系
        // 将 源对象 设置为非 latest 状态
        documentService.asHistory((VersionAble) node);
        return newDoc;
    }

    /**
     * ids文档不走钉钉直接归档
     * @param dto  word签名
     * @return
     */
    @Override
    public boolean docSignWorkflowForIds(DocSignWorkflowDTO dto) {

        log.info("docSignWorkflowForIds dto info:{}",JSONObject.toJSON(dto));

        JSONObject data = dto.getData();
        Assert.isFalse(MapUtil.isEmpty(data), "data is empty");
        String signTemplateCode = data.getString("signTemplateCode");
        String operateType = data.getString("operateType");
        int signPageNum = data.getInteger("pageNum");
        Assert.notBlank(signTemplateCode, "Param signTemplateCode can not be null");
        boolean isChange = data.getBooleanValue("isChange");

        Map<String,String> docSchemaOidMap;
        if (isChange) {
            docSchemaOidMap = queryChangeSchemaDocOidMap(data.getString("ecrOid"));
        } else {
            docSchemaOidMap = new HashMap<>();
        }
        log.info("docSignWorkflow docSchemaOidMap {} ", docSchemaOidMap);

        List<String> documentOidList = data.getObject("documentOidList", List.class);
        String processInstanceId = dto.getProcessInstanceId();
        // 查询流程中的文档对象
        List<DocumentIteration> docs =
                commonAbilityService.findDetailEntity(documentOidList, DocumentIteration.TYPE).stream().map(item -> (DocumentIteration) item).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(docs)) {
            return true;
        }
        // 查询 签名模版
        TemplateFindDTO templateFindDTO = new TemplateFindDTO();
        templateFindDTO.setCategory("signTemplate");
        templateFindDTO.setName(signTemplateCode);
        templateFindDTO.setContainerOid(dto.getTenantOid());
        ContainerTemplate signTemplate = containerRemote.findTemplateByName(templateFindDTO);
        Assert.notNull(signTemplate, "Sign template does not exists");
        File signTemplateFile = signTemplate.getFile();
        Assert.notNull(signTemplateFile, "Sign template does not exists");

        //需要签名转换PDF上传附件，文件清单
        List<File> listSign = new ArrayList<>();
        //需要转换pdf上传附件，文件清单（外协类的）
        List<File> listToPdf = new ArrayList<>();
        //主文件上传附件，文件清单（主文件是zip，表格）
        List<File> listToUpload = new ArrayList<>();

        for (DocumentIteration doc : docs) {
            log.info("docSignWorkflow docFile Detail:{}", JSONObject.toJSONString(doc));
            List<File> primaryFileList = doc.getPrimaryFile();
            Assert.notEmpty(primaryFileList, "Primary File does not exists");
            File primaryFile=primaryFileList.stream().findFirst().get();
            DocumentIteration detail=documentService.findByOid(doc.getOid());

            if(primaryFile.getName().endsWith("doc") || primaryFile.getName().endsWith("docx") ){

                if(detail.getExtensionContent() != null && "外协类文件".equals(detail.getExtensionContent().getString("source"))){
                    listToPdf.add(primaryFile);
                }else {
                    listSign.add(primaryFile);
                }
            }else {
                //把主文件上传附件
                listToUpload.add(primaryFile) ;
            }

        }

        Map<String, File> toUploadMap = listToUpload.stream().collect(Collectors.toMap(f -> f.getOid(), v -> v));
        // 准备签名参数
        GenerateSignDocumentRemote generateSignDocumentDTO = new GenerateSignDocumentRemote();
        generateSignDocumentDTO.setProcessInstanceId(processInstanceId);
        generateSignDocumentDTO.setSignTemplateFileOid(signTemplateFile.getOid()); // 签名模版 DFS 文件oid
        generateSignDocumentDTO.setPageNum(signPageNum);  // 页码
        generateSignDocumentDTO.setOperateType(operateType); // 签名页织入方式
        List<SignTemplateContentPicture> list = new ArrayList<>();
        generateSignDocumentDTO.setPictureCollection(list);
        Map<String, FileMetadata> oldFileOid2NewFile =new HashMap<>();
        buildSignWorkflowTextParam(generateSignDocumentDTO, dto.getAccountParams()); // 文本参数
        buildSignWorkflowPictureParam(generateSignDocumentDTO, dto.getAccountParams()); // 签名参数
        log.info("data----->>>>>{}", JSONUtil.toJsonStr(data));
        if("0".equals(data.getString("checkInOut"))){
            generateSignDocumentDTO.setDocumentIterationOidList(documentOidList); // 手动生成pdf修改为查询的documentOidList
        }
        log.info("documentOidList----->>>>>{}", JSONUtil.toJsonStr(documentOidList));
        generateSignDocumentDTO.setDocumentIterationOidList(documentOidList);
        if(CollectionUtil.isNotEmpty(listSign)){
            generateSignDocumentDTO.setOperateDocList(new ArrayList<>(listSign)); // 文档对象 oid
            // 生成签名内容
            try {
                log.info("generateSignDocumentDTO入参----->>>>>{}", JSONUtil.toJsonStr(generateSignDocumentDTO));
                oldFileOid2NewFile = customerSignTemplate.toGenerateSignDocumentForIds(generateSignDocumentDTO,documentOidList);
            } catch (IOException e) {
                log.error("Error generating {}", e.getMessage(), e);
                throw new RuntimeException(e);
            }
        }

        if(CollectionUtil.isNotEmpty(listToPdf)){
            Map<String, FileMetadata> collect = listToPdf.stream().collect(Collectors.toMap(f -> f.getOid(), v -> fileService.findByOid(v.getOid())));
            oldFileOid2NewFile.putAll(collect);
        }

        Map<String, DocumentIteration> oldFileOidDocOidMap = new HashMap<>();
        docs.stream().collect(() -> oldFileOidDocOidMap, (m, t) -> safeWrapList(t.getPrimaryFile()).stream().forEach(file -> m.put(file.getOid(), t)), (m1, m2) -> {});
        log.info("oldFileOidDocOidMap内容：{}", JSONUtil.toJsonStr(oldFileOidDocOidMap));
        log.info("oldFileOid2NewFile内容：{}", JSONUtil.toJsonStr(oldFileOid2NewFile));

        // word转pdf
        oldFileOid2NewFile =
                oldFileOid2NewFile.entrySet().parallelStream().collect(Collectors.toConcurrentMap(entry -> entry.getKey(), entry -> customerCommonService.doc2PdfAddCode(entry.getValue(), oldFileOidDocOidMap.get(entry.getKey()), isChange), (v1, v2) -> v1));

        // 填充新文件， 并检入
        for (DocumentIteration documentIteration : docs) {
            List<File> primaryFileList = documentIteration.getPrimaryFile();
            List<String> fileNameList =
                    primaryFileList.stream().map(f -> getFileNameIgnoreSuffix(f.getName()) + "_" + documentIteration.getVersion() + "_会签归档版").collect(Collectors.toList());
            File primaryFile = primaryFileList.stream().findFirst().get();
            List<File> secondaryFile = documentIteration.getSecondaryFile();
            if (CollectionUtil.isEmpty(secondaryFile)) {
                secondaryFile = new ArrayList<>();
            }
            FileMetadata entry = oldFileOid2NewFile.get(primaryFile.getOid());
            if (entry != null) {
                File newFile = new File();
                newFile.setName(entry.getFileOriginalName());
                newFile.setOid(entry.getOid());
                secondaryFile.add(newFile);
            } else if (toUploadMap.containsKey(primaryFile.getOid())) {
                File newFile = new File();
                newFile.setName(toUploadMap.get(primaryFile.getOid()).getName());
                newFile.setOid(toUploadMap.get(primaryFile.getOid()).getOid());
                secondaryFile.add(newFile);
            }
            if (secondaryFile != null) {
                documentIteration.setSecondaryFile(secondaryFile);
            }
            //若为变更流程 需要按照名称匹配 清理上个版本的 pdf文件
            if (isChange && docSchemaOidMap.containsKey(documentIteration.getOid())) {
                log.info("start to clean oid:{}", docSchemaOidMap.get(documentIteration.getOid()));
                cleanOldSecondaryFile(secondaryFile,docSchemaOidMap.get(documentIteration.getOid()));
            }

            log.info("toUpdate documentIteration {} ", JSONObject.toJSON(documentIteration));
            //设置流程实例ID
            setInstanceData(documentIteration,dto);
            // 更新
            commonAbilityHelper.doUpdate(documentIteration);
            if (isChange && docSchemaOidMap.containsKey(documentIteration.getOid())) {
                String entityOid = confirmChangeAndReturn(docSchemaOidMap.get(documentIteration.getOid()),
                        documentIteration.getOid());
                customerCommonRepo.updateLifeStatus(DocumentIteration.TYPE,
                        new ArrayList<String>() {{
                            add(entityOid);
                        }}, "Released");
            }
        }
        return true;
    }
}
