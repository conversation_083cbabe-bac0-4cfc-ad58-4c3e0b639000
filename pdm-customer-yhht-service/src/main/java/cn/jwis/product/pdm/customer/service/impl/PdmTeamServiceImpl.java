package cn.jwis.product.pdm.customer.service.impl;

import cn.jwis.framework.base.response.PageResult;
import cn.jwis.framework.base.web.session.SessionHelper;
import cn.jwis.platform.plm.account.auth.service.AuthHelper;
import cn.jwis.platform.plm.account.entity.team.TeamTemplate;
import cn.jwis.product.pdm.customer.entity.CustomerUnBindUserDTO;
import cn.jwis.product.pdm.customer.repo.PdmTeamRepo;
import cn.jwis.product.pdm.customer.service.interf.PdmTeamService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR> lhm
 * @Date ： Created in 2023/9/13 10:56
 * @Description :
 */
@Service
@Transactional
public class PdmTeamServiceImpl implements PdmTeamService {

    @Autowired
    PdmTeamRepo pdmTeamRepo;

    @Autowired
    AuthHelper authHelper;


    @Override
    public List<TeamTemplate> findProcessTeam(String searchKey) {
        this.authHelper.fillUserAuthInfo(SessionHelper.getCurrentUser());
        return pdmTeamRepo.findProcessTeam(searchKey);
    }

    @Override
    public PageResult<TeamTemplate> findProcessTeamPage(String searchKey, int index, int size) {
        this.authHelper.fillUserAuthInfo(SessionHelper.getCurrentUser());
        return pdmTeamRepo.findProcessTeamPage(searchKey,index,size);
    }

    @Override
    public Object searchAllTeamRole(String containerOid, String searchKey, int index, int size) {
        this.authHelper.fillUserAuthInfo(SessionHelper.getCurrentUser());
        return pdmTeamRepo.searchAllTeamRole(containerOid,searchKey,index,size);
    }

    @Override
    public void deleteAllTeamRoleUser(CustomerUnBindUserDTO dto) {
        this.authHelper.fillUserAuthInfo(SessionHelper.getCurrentUser());
        pdmTeamRepo.deleteAllTeamRoleUser(dto);
    }

    @Override
    public void batchHandleTeamRoleUser(CustomerUnBindUserDTO dto) {
        this.authHelper.fillUserAuthInfo(SessionHelper.getCurrentUser());
        pdmTeamRepo.batchHandleTeamRoleUser(dto);
    }

}
