package cn.jwis.product.pdm.customer.service.listener;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.jwis.framework.base.annotation.Description;
import cn.jwis.framework.base.exception.JWIServiceException;
import cn.jwis.framework.base.util.CollectionUtil;
import cn.jwis.framework.base.util.MapUtil;
import cn.jwis.framework.base.util.SpringContextUtil;
import cn.jwis.platform.plm.dataengine.common.JWICommonService;
import cn.jwis.platform.plm.foundation.common.response.InstanceEntity;
import cn.jwis.platform.plm.workflow.engine.dto.ProcessOrderDetail;
import cn.jwis.platform.plm.workflow.engine.order.entity.ProcessOrder;
import cn.jwis.platform.plm.workflow.engine.service.interf.ProcessOrderHelper;
import cn.jwis.product.pdm.change.response.ChangeInfo;
import cn.jwis.product.pdm.change.service.ChangeService;
import cn.jwis.product.pdm.customer.service.interf.DingTalkService;
import cn.jwis.product.pdm.partbom.part.entity.PartIteration;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.ExecutionListener;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Description("ECR变更流程开始节点发布钉钉任务监听器")
@Slf4j
public class ECRReleaseStartListener implements ExecutionListener {

    @Override
    public void notify(DelegateExecution execution) {
        try {
            log.info("ECRReleaseStartListener start processing...");
            
            // 获取流程变量
            JSONObject delegateTask = new JSONObject();
            delegateTask.put("processInstanceId", execution.getProcessInstanceId());
            delegateTask.put("variableInstances", execution.getVariables());
            
            this.doNotify(0, delegateTask);
            
        } catch (Exception e) {
            log.error("ECRReleaseStartListener error", e);
            throw new JWIServiceException("ECR钉钉流程发起失败: " + e.getMessage());
        }
    }

    private void doNotify(int times, JSONObject delegateTask) {
        // 尝试 5 次
        if (times == 5) {
            log.info("ECRReleaseStartListener failed");
            throw new JWIServiceException("ECRReleaseStartListener failed: System busy");
        }
        
        ProcessOrder processOrder = getProcessOrder(delegateTask);
        if (processOrder == null) {
            try {
                Thread.sleep(500);
                times++;
                doNotify(times, delegateTask);
                return;
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
        
        String oid = processOrder.getOid();
        ProcessOrderHelper processOrderHelper = SpringContextUtil.getBean(ProcessOrderHelper.class);
        ProcessOrderDetail detail = processOrderHelper.findDetail(oid);
        String processInstanceId = delegateTask.getString("processInstanceId");
        if (StrUtil.isNotBlank(processInstanceId)) {
            detail.setProcessInstanceId(processInstanceId.trim());
        }
        log.info("ECR ProcessOrderDetail: {}", JSONUtil.parseObj(detail));

        // 获取审批对象
        List<InstanceEntity> instanceList = detail.getBizObjects();
        if (CollectionUtil.isEmpty(instanceList)) {
            log.warn("ECR流程中未找到审批对象");
            return;
        }

        // 检查是否包含ECR对象
        boolean hasECR = instanceList.stream().anyMatch(instance -> "ECR".equals(instance.getType()));
        if (!hasECR) {
            log.warn("流程中未找到ECR对象，跳过ECR钉钉流程处理");
            return;
        }

        log.info("ECRReleaseStartListener.processOrder---->>>{}", JSONUtil.toJsonStr(processOrder));
        log.info("ECRReleaseStartListener.instanceList---->>>{}", JSONUtil.toJsonStr(instanceList));

        DingTalkService dingTalkService = (DingTalkService) SpringContextUtil.getBean("dingTalkServiceImpl");
        
        // 检查任务是否已发起
        Boolean isTaskSent = delegateTask.getBooleanValue("isTaskSent");
        log.info("ECRReleaseStartListener.isTaskSent---->>>{}", isTaskSent);
        
        if (isTaskSent == null || !isTaskSent) {
            try {
                log.info("ECRReleaseStartListener.发起钉钉流程入参 detail---->>>{}", JSONObject.toJSONString(detail));
                
                // 构建适配的ProcessOrderDetail
//                ProcessOrderDetail adaptedDetail = buildAdaptedDetailForECR(detail);

                // 调用ECR专用的钉钉流程创建方法
                dingTalkService.createSendTaskForEcrRelease(detail);
//                dingTalkService.createSendTaskForBomRelease(adaptedDetail);
                delegateTask.put("isTaskSent", true); // 标记任务已发起
                
            } catch (Exception e) {
                log.error("ECRReleaseStartListener.delegateTask error", e);
                throw new JWIServiceException("发起ECR钉钉流程失败: " + e.getMessage());
            }
        }
    }

    private ProcessOrder getProcessOrder(JSONObject delegateTask) {
        ProcessOrder processOrder = null;
        JSONObject variableInstances = delegateTask.getJSONObject("variableInstances");
        ProcessOrderHelper processOrderHelper = (ProcessOrderHelper) SpringContextUtil.getBean("processOrderHelperImpl");
        
        if (MapUtil.isEmpty(variableInstances)) {
            String processInstanceId = delegateTask.getString("processInstanceId");
            processOrder = processOrderHelper.findByProcessInstanceId(processInstanceId);
        } else {
//            JSONObject processOrderVar = variableInstances.getJSONObject("processOrderOid");
            String string = variableInstances.getString("processOrderOid");
            if (StrUtil.isEmpty(string)) {
                String processInstanceId = delegateTask.getString("processInstanceId");
                processOrder = processOrderHelper.findByProcessInstanceId(processInstanceId);
            } else {
                String processOrderOid = string;
                processOrder = processOrderHelper.findByOid(processOrderOid);
            }
        }
        return processOrder;
    }

    /**
     * 构建适配ECR的ProcessOrderDetail
     * 将ECR中的变更对象转换为bizObjects，以复用现有的钉钉流程逻辑
     */
    private ProcessOrderDetail buildAdaptedDetailForECR(ProcessOrderDetail originalDetail) {
        try {
            log.info("开始构建ECR适配的ProcessOrderDetail");
            
            // 获取ECR对象
            List<InstanceEntity> ecrList = originalDetail.getBizObjects().stream()
                    .filter(instance -> "ECR".equals(instance.getType()))
                    .collect(ArrayList::new, (list, item) -> list.add(item), ArrayList::addAll);
            
            if (ecrList.isEmpty()) {
                throw new JWIServiceException("未找到ECR对象");
            }
            
            InstanceEntity ecrInstance = ecrList.get(0);
            String ecrOid = ecrInstance.getOid();
            log.info("处理ECR对象，OID: {}", ecrOid);
            
            // 获取变更对象列表
            ChangeService changeService = SpringContextUtil.getBean(ChangeService.class);
            List<ChangeInfo> changeInfoList = changeService.findChangeInfo(ecrOid, null);
            log.info("ECR关联的变更对象数量: {}", changeInfoList.size());
            
            if (CollectionUtil.isEmpty(changeInfoList)) {
                log.warn("ECR [{}] 中未找到变更对象", ecrOid);
                // 如果没有变更对象，仍然可以发起流程，但bizObjects为空
                originalDetail.setBizObjects(new ArrayList<>());
                return originalDetail;
            }
            
            // 将ChangeInfo转换为InstanceEntity
            List<InstanceEntity> adaptedBizObjects = new ArrayList<>();
            for (ChangeInfo changeInfo : changeInfoList) {
                InstanceEntity instanceEntity = convertChangeInfoToInstanceEntity(changeInfo);
                adaptedBizObjects.add(instanceEntity);
                log.info("转换变更对象: {} -> {}", changeInfo.getName(), instanceEntity.getOid());
            }
            
            // 设置适配后的bizObjects
            originalDetail.setBizObjects(adaptedBizObjects);
            
            log.info("ECR适配完成，转换后的bizObjects数量: {}", adaptedBizObjects.size());
            return originalDetail;
            
        } catch (Exception e) {
            log.error("构建ECR适配的ProcessOrderDetail失败", e);
            throw new JWIServiceException("ECR流程适配失败: " + e.getMessage());
        }
    }

    /**
     * 将ChangeInfo转换为InstanceEntity
     */
    private InstanceEntity convertChangeInfoToInstanceEntity(ChangeInfo changeInfo) {
        InstanceEntity instanceEntity = new InstanceEntity();
        JWICommonService jwiCommonService = SpringContextUtil.getBean(JWICommonService.class);
        List<InstanceEntity> instanceList = jwiCommonService.findByOid(PartIteration.TYPE,
                Arrays.asList(changeInfo.getOid()),InstanceEntity.class);

        if(CollectionUtil.isNotEmpty(instanceList)){
            instanceEntity = instanceList.get(0);
        }

        return instanceEntity;
    }
}
