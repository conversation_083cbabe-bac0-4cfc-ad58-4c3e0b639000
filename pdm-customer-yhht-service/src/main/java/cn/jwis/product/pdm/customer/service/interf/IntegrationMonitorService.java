package cn.jwis.product.pdm.customer.service.interf;

import cn.jwis.product.pdm.cad.mcad.param.Page;
import cn.jwis.product.pdm.customer.entity.IntegrationRecord;
import cn.jwis.product.pdm.customer.entity.IntegrationFeedback;
import cn.jwis.product.pdm.customer.service.dto.SendByNumDTO;
import cn.jwis.product.pdm.customer.service.dto.U9InventoryResponse;
import cn.jwis.product.pdm.customer.service.dto.U9ProductionOrder;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2023/8/9 22:23
 * @Email <EMAIL>
 */
public interface IntegrationMonitorService {

    IntegrationRecord findByBusinessOid(String oid);

    List<IntegrationRecord> findRecord(Page page);

    String reSend(IntegrationRecord record);

    String feedback(IntegrationFeedback dto,int count) throws InterruptedException;

    List<U9ProductionOrder> queryProductionOrderFromU9(String number);

    U9InventoryResponse queryInventoryFromU9(String number);

    String queryIntegrationResult(String unReleaseEntity);

    String sendByNumber(SendByNumDTO dto);
}
