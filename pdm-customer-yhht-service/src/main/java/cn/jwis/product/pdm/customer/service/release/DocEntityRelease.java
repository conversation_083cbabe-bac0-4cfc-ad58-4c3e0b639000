package cn.jwis.product.pdm.customer.service.release;

import cn.jwis.framework.base.bean.util.BeanUtil;
import cn.jwis.framework.base.domain.able.ModelAble;
import cn.jwis.framework.base.domain.able.info.ModelInfo;
import cn.jwis.framework.base.dto.UserDTO;
import cn.jwis.framework.base.util.CollectionUtil;
import cn.jwis.framework.base.web.session.SessionHelper;
import cn.jwis.platform.plm.account.repo.user.response.UserWithPositionAndOrg;
import cn.jwis.platform.plm.account.user.service.UserHelper;
import cn.jwis.platform.plm.account.user.service.UserService;
import cn.jwis.platform.plm.datadistribution.service.DataDistributionService;
import cn.jwis.platform.plm.dataengine.common.JWICommonService;
import cn.jwis.platform.plm.file.entity.FileMetadata;
import cn.jwis.platform.plm.file.service.FileService;
import cn.jwis.platform.plm.foundation.attachment.entity.File;
import cn.jwis.platform.plm.foundation.classification.entity.Classification;
import cn.jwis.platform.plm.foundation.classification.service.ClassificationService;
import cn.jwis.platform.plm.foundation.common.service.CommonAbilityHelper;
import cn.jwis.platform.plm.foundation.common.service.CommonAbilityService;
import cn.jwis.platform.plm.foundation.lifecycle.able.LifecycleAble;
import cn.jwis.platform.plm.sysconfig.entity.ConfigItem;
import cn.jwis.platform.plm.sysconfig.preferences.PreferencesService;
import cn.jwis.platform.plm.workflow.engine.order.entity.ProcessOrder;
import cn.jwis.product.pdm.customer.entity.IntegrationRecord;
import cn.jwis.product.pdm.customer.service.dto.BaseEntityDTO;
import cn.jwis.product.pdm.customer.service.dto.DocReleaseDataDTO;
import cn.jwis.product.pdm.customer.service.dto.IntegrationFile;
import cn.jwis.product.pdm.customer.service.interf.CommonService;
import cn.jwis.product.pdm.document.entity.DocumentIteration;
import cn.jwis.product.pdm.document.service.DocumentHelper;
import cn.jwis.product.pdm.partbom.part.entity.PartIteration;
import com.alibaba.fastjson.JSONObject;
import jodd.util.StringUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:文档发布能力提供者
 * @date 2023/8/15 11:13
 * @Email <EMAIL>
 */
@Data
@EqualsAndHashCode
@Component
@Transactional
@Slf4j
public class DocEntityRelease extends EntityRelease{

    private final List<String> targetLifecycleStatus = Arrays.asList("Released","Inwork","Design","Draft","UnderReview");

    @Autowired
    DataDistributionService dataDistributionService;

    @Autowired
    DocumentHelper docHelper;

    @Autowired
    JWICommonService jwiCommonService;

    @Autowired
    CommonAbilityHelper commonAbilityHelper;

    @Autowired
    CommonAbilityService commonAbilityService;

    @Autowired
    CommonService commonService;

    @Autowired
    FileService fileService;

    @Autowired
    UserHelper userHelper;

    @Autowired
    UserService userService;

    @Autowired
    PreferencesService preferencesService;

    @Autowired
    ClassificationService classificationService;

    /**
     * 真正的doc发布实现，在此处推送发放的doc到任意外部接口，当前只发MQ。
     * @return
     */
    @Override
    @Async
    public IntegrationRecord release(ProcessOrder processOrder,String entityOid, String recordOid, int isFirst) {
        DocumentIteration dbDoc = docHelper.findByOid(entityOid);
        if(dbDoc == null) return null;
        // 非指定分类，不发SMIS
        if(!needSendSMIS(dbDoc)){
            log.info(dbDoc.getNumber() + "不需要发SMIS！");
            return null;
        }
        //Assert.isTrue(targetLifecycleStatus.contains(dbDoc.getLifecycleStatus()),"doc[" + dbDoc.getNumber() + "] lifecycleStatus is not match!");
        DocReleaseDataDTO docReleaseData = BeanUtil.copyProperties(dbDoc,new DocReleaseDataDTO());
        docReleaseData.setBusinessOid(type + "_" + dbDoc.getNumber() + "_" + dbDoc.getVersion());
        docReleaseData.setUpdateTime(dbDoc.getUpdateDate());
        // 分类路径
        docReleaseData.setClsPath(getClsPath(dbDoc.getType(),dbDoc.getModelDefinition(),dbDoc.getClsProperty()));
        // 产品型号
        docReleaseData.setProductName(initProductName(dbDoc.getContainerOid()));
        // 发布人
        UserWithPositionAndOrg user = userHelper.findDetailByAccount(dbDoc.getOwner());
        initPublisher(user,docReleaseData);
        // 初始化文档实体文件
        initFile(dbDoc,docReleaseData);
        // 初始化参考部件（已发放的部件）
        initReference(dbDoc,docReleaseData);
        // 初始化描述部件（已发放的部件）
        initDescribe(dbDoc,docReleaseData);
        JSONObject releaseData = JSONObject.parseObject(JSONObject.toJSONString(docReleaseData));
        // 获取集成记录
        IntegrationRecord record = getIntegrationRecord(recordOid,type,processOrder,releaseData);
        // 发MQ
        String result = releaseMQ(record,dataDistributionService);
        record.setMqSuccess(StringUtil.isBlank(result) ? true : false);
        // U9处理结果，预设为 waiting
        record.setConsumer("smis");
        record.setIsSuccess(false);
        record.setMsg("waiting");
        return jwiCommonService.update(record);
    }

    private boolean needSendSMIS(DocumentIteration doc) {
        JSONObject clsProperty = doc.getClsProperty();
        String code = clsProperty.getString("code");
        if(StringUtil.isBlank(code)){
            String clsOid = clsProperty.getString("clsOid");
            Classification classification = classificationService.findByOid(clsOid);
            code = classification.getCode();
        }
        ConfigItem item = preferencesService.queryConfigValue("needSendSmisDocType");
        List<String> needSendSmis = new ArrayList<>();
        if(item != null) {
            needSendSmis.addAll(Arrays.asList(item.getValue().split(",")));
        }
        return needSendSmis.contains(code);
    }


    private void initPublisher(UserWithPositionAndOrg user, DocReleaseDataDTO docReleaseData) {
        UserDTO u = SessionHelper.getCurrentUser();
        if(user == null){
            docReleaseData.setUpdatorAccount("sys_admin");
            docReleaseData.setUpdatorNameAndDepartment("PDM系统管理员");
            return;
        }
        docReleaseData.setUpdatorAccount(user.getAccount());
        List<String> orgList = userService.getOrgTreeNameByAccount(user.getAccount());
        StringBuffer orgTree = new StringBuffer(user.getName() + ",");
        for(int i = orgList.size()-2; i > -1 ; i--){
            orgTree.append(orgList.get(i));
            if(i > 0){
                orgTree.append("-");
            }
        }
        docReleaseData.setUpdatorNameAndDepartment(orgTree.toString());
    }

    private void initFile(DocumentIteration dbDoc,DocReleaseDataDTO docReleaseData) {
        docReleaseData.setPrimaryFile(new ArrayList<>());
        docReleaseData.setSecondaryFile(new ArrayList<>());
        List<File> primary = dbDoc.getPrimaryFile();
        if(CollectionUtil.isNotEmpty(primary)){
            primary.stream().forEach(f->{
                FileMetadata fileMetadata = fileService.findByOid(f.getOid());
                IntegrationFile integrationFile = new IntegrationFile(fileMetadata);
                docReleaseData.getPrimaryFile().add(integrationFile);
            });
        }
        List<File> secondary = dbDoc.getSecondaryFile();
        if(CollectionUtil.isNotEmpty(secondary)){
            secondary.stream().forEach(f->{
                FileMetadata fileMetadata = fileService.findByOid(f.getOid());
                IntegrationFile integrationFile = new IntegrationFile(fileMetadata);
                docReleaseData.getSecondaryFile().add(integrationFile);
            });
        }
    }

    private void initDescribe(DocumentIteration dbDoc,DocReleaseDataDTO docReleaseData) {
        ModelInfo doc = BeanUtil.copyProperties(dbDoc,new ModelInfo());
        List<ModelAble> modelAbles = commonAbilityHelper.findDescribe(doc);
        List<PartIteration> PartList = new ArrayList<>();
        for(ModelAble modelAble : modelAbles) {
            PartIteration part = BeanUtil.copyProperties(modelAble,new PartIteration());
            PartList.add(part);
        }
        PartList = withMaxVersionConstraint(PartList);
//        List<String> partOids = modelAbles.stream().map(part->part.getOid()).collect(Collectors.toList());
//        modelAbles = commonAbilityService.findDetailEntity(partOids, PartIteration.TYPE);
        for(PartIteration modelAble : PartList){
            if(targetLifecycleStatus.contains(((LifecycleAble)modelAble).getLifecycleStatus())) {
                BaseEntityDTO dto = BeanUtil.copyProperties(modelAble,new BaseEntityDTO());
                dto.setType("Describe");
                docReleaseData.getRelationPart().add(dto);
            }
        }
    }

    private void initReference(DocumentIteration dbDoc,DocReleaseDataDTO docReleaseData) {
        ModelInfo docMaster = new ModelInfo();
        docMaster.setOid(dbDoc.getMasterOid());
        docMaster.setType(dbDoc.getMasterType());
        List<ModelAble> modelAbles = commonAbilityHelper.findBeReferenced(docMaster);
        List<PartIteration> PartList = new ArrayList<>();
        for(ModelAble modelAble : modelAbles) {
            PartIteration part = BeanUtil.copyProperties(modelAble,new PartIteration());
            PartList.add(part);
        }
        PartList = withMaxVersionConstraint(PartList);
//        List<String> partOids = PartList.stream().map(part->part.getOid()).collect(Collectors.toList());
//        modelAbles = commonAbilityService.findDetailEntity(partOids, PartIteration.TYPE);
        for(ModelAble modelAble : PartList){
            if(targetLifecycleStatus.contains(((LifecycleAble)modelAble).getLifecycleStatus())) {
                BaseEntityDTO dto = BeanUtil.copyProperties(modelAble,new BaseEntityDTO());
                dto.setType("Reference");
                docReleaseData.getRelationPart().add(dto);
            }
        }
    }

    public List<PartIteration> withMaxVersionConstraint(List<PartIteration> rows) {
        if (CollectionUtil.isEmpty(rows)) {
            return rows;
        }
        Map<String, PartIteration> record = new LinkedHashMap<>(rows.size());
        String masterOid = null;
        Integer versionSortId = null;
        PartIteration holder = null;
        for (PartIteration row : rows) {
            masterOid = row.getMasterOid();
            if (cn.jwis.framework.base.util.StringUtil.isBlank(masterOid)) {
                record.put(row.getOid(), row);
                continue;
            }
            versionSortId = row.getVersionSortId();
            holder = record.get(masterOid);
            if (holder == null || holder.getVersionSortId() < versionSortId) {
                record.put(masterOid, row);
            }
        }
        return new ArrayList<>(record.values());
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        type = EntityReleaseFactory.BusinessType.doc.name();
        EntityReleaseFactory.register(type,this);
    }
}
