package cn.jwis.product.pdm.customer.service.util;

import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;
import java.util.function.Predicate;

public class HttpAssistant {

    private static final Logger logger = LoggerFactory.getLogger(HttpAssistant.class);

    static final String APPLICATION_JSON = "application/json", ERR_CODE = "errCode", ERR = "error", CODE = "code", SUCCESS_CODE = "200";
    static final Integer SUCCESS_CODE_I = 200;
    static final int TIMEOUT = 30000;

    public static <T> T post(String url, Object data, Class<T> expectClazz, Map<String, String> headerMap, Predicate<JSONObject>... errorPredicate) {
        T res = null;
        String bodyDataStr = JSONUtil.toJsonStr(data);
        try {
            String respStr;
            if(headerMap == null) respStr = HttpRequest.post(url).timeout(TIMEOUT).body(bodyDataStr, APPLICATION_JSON).execute().body();
            else respStr = HttpRequest.post(url).timeout(TIMEOUT).addHeaders(headerMap).body(bodyDataStr, APPLICATION_JSON).execute().body();

            if(JSONObject.class.equals(expectClazz)){
                JSONObject jsonRes = new JSONObject(respStr);
                if(errorPredicate != null && errorPredicate.length > 0 && errorPredicate[0].test(jsonRes))
                    logger.error("\nPOST接口调用返回错误==> " + "url==>" + url + "\n" + "data==>" + bodyDataStr + "\nrespStr==>" + respStr);
                res = (T) jsonRes;
            } else
                res = (T) respStr;
        } catch (Exception e){
            logger.error("\nPOST接口调用异常==> " + "url==>" + url + "\n" + "data==>" + bodyDataStr + "\nrespStr==>" + e.getMessage(), e);
        }
        return res;
    }

    public static <T> T get(String url, Class<T> expectClazz, Map<String, String> headerMap, Predicate<JSONObject> errorPredicate, String... headers) {
        T res = null;
        try {
            String respStr;
            if(headerMap == null) respStr = HttpRequest.get(url).timeout(TIMEOUT).execute().body();
            else respStr = HttpRequest.get(url).addHeaders(headerMap).timeout(TIMEOUT).execute().body();

            if(JSONObject.class.equals(expectClazz)){
                JSONObject jsonRes = new JSONObject(respStr);
                if(errorPredicate != null && errorPredicate.test(jsonRes))
                    logger.error("\nGET接口调用返回错误==> " + "url==>" + url + "\n" + "\nrespStr==>" + respStr);
                res = (T) jsonRes;
            } else
                res = (T) respStr;
        } catch (Exception e){
            logger.error("\nGET接口调用异常==> " + "url==>" + url + "\nrespStr==>" + e.getMessage(), e);
        }
        return res;
    }

}
