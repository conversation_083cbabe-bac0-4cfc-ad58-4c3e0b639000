package cn.jwis.product.pdm.customer.service.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2023/8/30 20:14
 * @Email <EMAIL>
 */
@Data
@EqualsAndHashCode
public class BaseEntityDTO {

    private String type;
    private String number;
    private String name;
    private String version;
    private List<IntegrationFile> file = new ArrayList<>();

}
