package cn.jwis.product.pdm.customer.service.delegate;

import cn.hutool.json.JSONUtil;
import cn.jwis.framework.base.dto.UserDTO;
import cn.jwis.framework.base.util.CollectionUtil;
import cn.jwis.framework.base.util.SpringContextUtil;
import cn.jwis.framework.base.util.StringUtil;
import cn.jwis.framework.base.web.session.SessionHelper;
import cn.jwis.platform.plm.foundation.common.response.InstanceEntity;
import cn.jwis.platform.plm.foundation.common.service.InstanceHelper;
import cn.jwis.product.pdm.change.entity.ECA;
import cn.jwis.product.pdm.change.response.ChangeInfo;
import cn.jwis.product.pdm.change.service.ECAService;
import cn.jwis.product.pdm.change.service.ECAServiceImpl;
import cn.jwis.product.pdm.customer.entity.IntegrationRecord;
import cn.jwis.product.pdm.customer.repo.CustomerCommonRepo;
import cn.jwis.product.pdm.customer.repo.IntegrationMonitorRepo;
import cn.jwis.product.pdm.customer.service.release.EntityRelease;
import cn.jwis.product.pdm.customer.service.release.EntityReleaseFactory;
import cn.jwis.product.pdm.customer.service.release.PartEntityRelease;
import cn.jwis.product.pdm.partbom.part.service.PartHelper;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class BatchSendHZZ {

    @Autowired
    ECAService ecaService;

    @Autowired
    CustomerCommonRepo customerCommonRepo;

    @Autowired
    IntegrationMonitorRepo monitorRepo;

    @Value("${part.autoScreen:false}")
    private Boolean autoScreen;

    @Autowired
    PartHelper partHelper;

    @Autowired
    PartEntityRelease partEntityRelease;

    public String batchSendHZZ(List<InstanceEntity> originInstanceEntityList, Boolean checkPermission) {
        log.info("batchSendHZZ当前入参信息：{}", JSONUtil.toJsonStr(originInstanceEntityList));


        UserDTO currUser = SessionHelper.getCurrentUser();
        if(checkPermission && currUser == null || (checkPermission && !"sys_admin".equals(currUser.getAccount()) && !customerCommonRepo.batchSendERPPermission(currUser.getAccount())))
            throw new RuntimeException("只有【组织管理员】和【数据管理员】才能批量发送!");


        try {
            ECAServiceImpl ecaService = (ECAServiceImpl)SpringContextUtil.getBean("ECAServiceImpl");
            // instanceEntityList：评审对象集合（部件，文档等）
            List<InstanceEntity> instanceEntityList = new ArrayList<>();
            for(InstanceEntity instanceEntity : originInstanceEntityList){
                if(ECA.TYPE.equals(instanceEntity.getType())){
                    InstanceHelper instanceHelper = (InstanceHelper) SpringContextUtil.getBean("instanceHelperImpl");
                    List<ChangeInfo> list = ecaService.findChangeInfo(instanceEntity.getOid(),new ArrayList<>());
                    list.stream().forEach(changeInfo -> {
                        JSONObject jsonObject = instanceHelper.findLatest(changeInfo.getOid(), changeInfo.getType());
                        InstanceEntity newEntity = jsonObject.toJavaObject(InstanceEntity.class);
                        instanceEntityList.add(newEntity);
                    });
                }else {
                    instanceEntityList.add(instanceEntity);
                }
            }
            // 评审对象为空则不处理
            if(CollectionUtil.isEmpty(instanceEntityList)){
                log.info("BatchSendHZZ  instanceEntity is empty! ");
                return "BatchSendHZZ  instanceEntity is empty! ";
            }
            // 发放数据,返回结果为此次需要发布的数据
            String unReleaseData = releaseData(instanceEntityList);
            log.info("BatchSendHZZ instanceEntityList=" + instanceEntityList.stream().map(it -> it.getNumber()).collect(Collectors.joining(",")));
            log.info("BatchSendHZZ release=" + unReleaseData);
        } catch (Exception e) {
            log.error("BatchSendHZZ execute error==>", e);
            StringBuffer buffer = new StringBuffer();
            StackTraceElement[] stackTrace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : stackTrace) {
                buffer.append(stackTraceElement.toString() + "\n");
            }
            throw new RuntimeException(e.getMessage());
        }
        return "BatchSendHZZ success";
    }

    private String releaseData(List<InstanceEntity> instanceEntityList) {
        List<String> needPublish = new ArrayList<>(); // 需要发布的
        // 排序，非筛选或未成型物料排在前面发布

        StringBuffer result = new StringBuffer();
        String businessTypes = "hzzBOM";
        for (String businessType : businessTypes.split(",")) {
            instanceEntityList.stream().forEach(instanceEntity -> {
                // 根据流程类型或者评审对象类型找到对应的发布能力提供者
                EntityRelease entityRelease = EntityReleaseFactory.match(businessType);
                //全部发布
                //如果是不属于该businessType的数据，会返回null，不处理
                if(entityRelease != null){
                    IntegrationRecord record = entityRelease.release(null, instanceEntity.getOid(), "",3);
                    if (record != null) {
                        result.append(record.getBusinessOid()).append(",");
                        needPublish.add(instanceEntity.getOid());
                    }
                }
            });
        }

        return StringUtil.isBlank(result.toString()) ? "none" : result.toString() ;
    }


}
