package cn.jwis.product.pdm.customer.service.impl;

import cn.jwis.framework.base.bean.util.BeanUtil;
import cn.jwis.framework.base.domain.able.info.ModelInfo;
import cn.jwis.framework.base.exception.JWIException;
import cn.jwis.framework.base.util.Assert;
import cn.jwis.framework.base.util.CollectionUtil;
import cn.jwis.framework.base.util.StringUtil;
import cn.jwis.platform.plm.dataengine.common.JWICommonService;
import cn.jwis.platform.plm.foundation.classification.able.info.ClassificationInfo;
import cn.jwis.platform.plm.foundation.common.service.CommonAbilityHelper;
import cn.jwis.platform.plm.foundation.lifecycle.entity.LifecycleStatus;
import cn.jwis.platform.plm.foundation.versionrule.able.LockAble;
import cn.jwis.platform.plm.foundation.versionrule.able.info.LockInfo;
import cn.jwis.platform.plm.modelexcel.constants.ModelSystemPropKey;
import cn.jwis.platform.plm.modelexcel.dto.ModelExcelAllCreateDTO;
import cn.jwis.platform.plm.modelexcel.service.ModelDataSaveService;
import cn.jwis.product.pdm.customer.service.dto.CustomerDocumentCreateDTO;
import cn.jwis.product.pdm.customer.service.dto.CustomerDocumentUpdateDTO;
import cn.jwis.product.pdm.document.entity.DocumentIteration;
import cn.jwis.product.pdm.document.service.DocumentHelper;
import cn.jwis.product.pdm.document.service.DocumentService;
import cn.jwis.product.pdm.partbom.part.service.PartService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 保存文档的回调
 */
@Slf4j
@Component
public class CustomerDocumentDataSaveServiceImpl implements ModelDataSaveService {
    @Resource
    private CommonAbilityHelper commonAbilityHelper;

    @Autowired
    JWICommonService jwiCommonService;

    @Resource
    PartService partService;

    @Autowired
    private DocumentHelper documentHelper;

    @Autowired
    private DocumentService documentService;

    @Override
    public void batchSave(List<ModelExcelAllCreateDTO> res) {
        log.info("开始批量导入文件数量:{}",res.size());
        List<CustomerDocumentUpdateDTO> batchDocument = new ArrayList<>(res.size());

        for (ModelExcelAllCreateDTO re : res) {
            CustomerDocumentUpdateDTO customerDocumentUpdateDTO = new CustomerDocumentUpdateDTO();

            BeanUtil.copyProperties(re, customerDocumentUpdateDTO);

            //系统属性
            Map<String, String> partExcelDTO = re.getAllPropData();

            String owner = partExcelDTO.get("owner");
            if (!StringUtils.isEmpty(owner))
                customerDocumentUpdateDTO.setOwner(owner);

            if (re.getLifecycleStatus() != null) {
                LifecycleStatus status = re.getLifecycleStatus();
                customerDocumentUpdateDTO.setLifecycleStatus(status.getCode());
            }

            customerDocumentUpdateDTO.setCurrentStage(partExcelDTO.get(ModelSystemPropKey.currentStage));


            //基本属性
            customerDocumentUpdateDTO.setNumber(partExcelDTO.get(ModelSystemPropKey.number));
            customerDocumentUpdateDTO.setName(partExcelDTO.get(ModelSystemPropKey.name));


            customerDocumentUpdateDTO.setDescription(partExcelDTO.get(ModelSystemPropKey.description));

            customerDocumentUpdateDTO.setExcelVersion(partExcelDTO.get("excelVersion"));
            customerDocumentUpdateDTO.setExcelIteratedVersion(partExcelDTO.get("excelIteratedVersion"));

            batchDocument.add(customerDocumentUpdateDTO);
        }

        //检查数据正确性
        checkImportData(batchDocument);
        this.save(batchDocument);
    }

    private void checkImportData(List<CustomerDocumentUpdateDTO> batchDocument) {
        // 校验编码重复
        Map<String,List<String>> numberNameMap = new HashMap<>();
        for (CustomerDocumentUpdateDTO dto : batchDocument) {
            if(StringUtils.isNotBlank(dto.getNumber())) {
                numberNameMap.computeIfAbsent(dto.getNumber(),k->new ArrayList<>()).add(dto.getName());
            }
        }
        List<String> errorList = numberNameMap.entrySet().stream().filter(item->item.getValue().size()>1).map(entry ->
                "编码重复:" + entry.getKey() + " 文档名称:" + StringUtils.join(entry.getValue(),",")).collect(Collectors.toList());


        Assert.isEmpty(errorList,StringUtils.join(errorList,";"));
    }

    private void save(List<CustomerDocumentUpdateDTO> batchDocument) {

        List<String> codes = batchDocument.stream().map(CustomerDocumentUpdateDTO::getNumber)
                .filter(StringUtil::isNotBlank).collect(Collectors.toList());

        List<DocumentIteration> dbDocument = commonAbilityHelper.findDetailEntityByNumber(codes, DocumentIteration.TYPE)
                .stream().map(item -> (DocumentIteration) item).collect(Collectors.toList());

        Map<String, DocumentIteration> numberDbDocumentMap = dbDocument.stream().collect(Collectors.toMap(DocumentIteration::getNumber, part -> part, (o1, o2) -> o2));

        //更新数据

        List<CustomerDocumentUpdateDTO> updateExcelDocuments = batchDocument.stream().filter(t -> !t.isCreateFlag()).collect(Collectors.toList());
        log.info("导入文档更新数量:{}",updateExcelDocuments.size());
        if (CollectionUtil.isNotEmpty(updateExcelDocuments)) {
            Map<String, CustomerDocumentUpdateDTO> numberExcelDocumentMap = new HashMap<>();
            List<ModelInfo> checkOutData = updateExcelDocuments.stream()
                    .map(item -> {
                        ModelInfo modelInfo = new ModelInfo();
                        String number = item.getNumber();
                        numberExcelDocumentMap.put(number, item);
                        DocumentIteration partIteration = numberDbDocumentMap.get(number);
                        modelInfo.setOid(partIteration.getOid());
                        modelInfo.setType(partIteration.getType());
                        modelInfo.setModelDefinition(partIteration.getModelDefinition());
                        return modelInfo;
                    })
                    .collect(Collectors.toList());

            List<LockAble> lockAbles = documentHelper.checkOut(checkOutData);

            List<DocumentIteration> updateDocuments = new ArrayList<>();
            for (LockAble lockAble : lockAbles) {
                DocumentIteration byCheckOut = (DocumentIteration) lockAble;
                String oid = byCheckOut.getOid();
                String number = byCheckOut.getNumber();
                CustomerDocumentUpdateDTO customerDocumentUpdateDTO = numberExcelDocumentMap.get(number);
                BeanUtil.copyProperties(customerDocumentUpdateDTO, byCheckOut);

                DocumentIteration documentIteration = numberDbDocumentMap.get(number);
                if (StringUtil.isNotBlank(customerDocumentUpdateDTO.getExcelVersion())) {
                    byCheckOut.setVersion(customerDocumentUpdateDTO.getExcelVersion());
                    if (customerDocumentUpdateDTO.getExcelVersion().equalsIgnoreCase(documentIteration.getVersion())) {
                        byCheckOut.setIteratedVersion(documentIteration.getIteratedVersion()); // 大版本一样，小版本填了也不用，自动加一就行
                    } else {
                        if (StringUtil.isNotBlank(customerDocumentUpdateDTO.getExcelIteratedVersion())) {
                            int iteratedVersion = Integer.parseInt(customerDocumentUpdateDTO.getExcelIteratedVersion()) - 1;
                            byCheckOut.setIteratedVersion(iteratedVersion);
                        }else{
                            byCheckOut.setIteratedVersion(0); // 因为更新的时候小版本会自动+1，所以这里减个1
                        }
                    }

                }else{
                    if ("已发布".equalsIgnoreCase(documentIteration.getLifecycleStatus()) || "released".equalsIgnoreCase(documentIteration.getLifecycleStatus())){
                        this.documentService.nextVersion(byCheckOut); // 不写版本且为已发布则升级大版本，小版本置为1
                        byCheckOut.setIteratedVersion(0);
                    }else{
                        byCheckOut.setVersion(documentIteration.getVersion()); // 不写版本不是已发布，大版本不变，小版本+1
                        byCheckOut.setIteratedVersion(documentIteration.getIteratedVersion());
                    }
                }

                byCheckOut.setOid(oid);
                ClassificationInfo clsInfo = customerDocumentUpdateDTO.getClassificationInfo();
                if (clsInfo != null) {
                    documentService.setClassification(byCheckOut, clsInfo);
                } else {
                    byCheckOut.setClsOid("");
                }
                updateDocuments.add(byCheckOut);
            }
            try {
                commonAbilityHelper.doUpdate(updateDocuments);
                //检入
                List<LockInfo> needCheckIn = updateDocuments.stream().map(item -> {
                    LockInfo lockInfo = new LockInfo();
                    lockInfo.setOid(item.getOid());
                    lockInfo.setType(item.getType());
                    return lockInfo;
                }).collect(Collectors.toList());

                documentHelper.checkIn(needCheckIn);
            } catch (JWIException e) {
                checkOutData.forEach(item-> this.commonAbilityHelper.removeRedisCheckOutLock(item.getOid()));
                throw e;
            }
        }

        log.info("导入数据更新完毕");

        //创建数据
        List<CustomerDocumentUpdateDTO> createExcelDocuments = batchDocument.stream().filter(t -> t.isCreateFlag()).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(createExcelDocuments)) {
//            List<DocumentIteration> createDocuments=new ArrayList<>();
//            for (CustomerDocumentUpdateDTO createExcelDocument : createExcelDocuments) {
//                DocumentIteration documentIteration = BeanUtil.copyProperties(createExcelDocument, new DocumentIteration());
//                documentService.setLocation(documentIteration, createExcelDocument.getLocationInfo());
//                ClassificationInfo clsInfo = createExcelDocument.getClassificationInfo();
//                if (clsInfo != null) {
//                    documentService.setClassification(documentIteration, clsInfo);
//
//                }
//                createDocuments.add(documentIteration);
//
//            }
//            commonAbilityHelper.doCreate(createDocuments);
            log.info("导入数据新增数量:{}",createExcelDocuments.size());
            for (CustomerDocumentUpdateDTO createExcelDocument : createExcelDocuments) {
                CustomerDocumentCreateDTO customerDocumentCreateDTO = new CustomerDocumentCreateDTO();
                BeanUtil.copyProperties(createExcelDocument, customerDocumentCreateDTO);

                if (StringUtil.isNotBlank(createExcelDocument.getExcelVersion())) {
                    customerDocumentCreateDTO.setVersion(createExcelDocument.getExcelVersion());
                }

                if (StringUtil.isNotBlank(createExcelDocument.getExcelIteratedVersion())) {
                    customerDocumentCreateDTO.setIteratedVersion(Integer.parseInt(createExcelDocument.getExcelIteratedVersion()));
                }

                DocumentIteration documentIteration = documentHelper.create(customerDocumentCreateDTO);
                resetCreateData(documentIteration);
                if (StringUtil.isNotBlank(createExcelDocument.getExcelVersion())) {
                    documentIteration.setVersion(createExcelDocument.getExcelVersion());
                    if (StringUtil.isNotBlank(createExcelDocument.getExcelIteratedVersion())) {
                        documentIteration.setIteratedVersion(Integer.parseInt(createExcelDocument.getExcelIteratedVersion()));
                        documentIteration.setDisplayVersion(documentIteration.getVersion() + "." + documentIteration.getIteratedVersion());
                    }else{
                        documentIteration.setDisplayVersion(documentIteration.getVersion() + ".1");
                    }
                    jwiCommonService.update(documentIteration); // 既然创建的时候创不进去，那就更新吧
                }

                //更新状态
                if (StringUtil.isNotBlank(createExcelDocument.getLifecycleStatus())) {
                    ModelInfo modelInfo = new ModelInfo();
                    modelInfo.setOid(documentIteration.getOid());
                    modelInfo.setType(documentIteration.getType());
                    documentHelper.setStatus(modelInfo, createExcelDocument.getLifecycleStatus());
                }
            }
            log.info("导入新增数据处理完毕",createExcelDocuments.size());

        }
    }

    // 若导入数据包含入库 时间使用入库
    private void resetCreateData(DocumentIteration documentIteration) {
        if(ObjectUtils.isNotEmpty(documentIteration.getExtensionContent()) && StringUtils.isNotBlank(documentIteration.getExtensionContent().getString("storageTime"))) {
            try {
                Long storageTime = Long.parseLong(documentIteration.getExtensionContent().getString("storageTime"));
                documentIteration.setCreateDate(storageTime);
                jwiCommonService.update(documentIteration);
            } catch (JWIException e) {
                log.info("resetCreateData error ", e);
            }
        }
    }

}
