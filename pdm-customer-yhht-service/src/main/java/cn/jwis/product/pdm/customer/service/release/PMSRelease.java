package cn.jwis.product.pdm.customer.service.release;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.ContentType;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONUtil;
import cn.jwis.framework.base.exception.JWIException;
import cn.jwis.platform.plm.foundation.common.service.CommonAbilityHelper;
import cn.jwis.platform.plm.sysconfig.entity.ConfigItem;
import cn.jwis.platform.plm.sysconfig.preferences.PreferencesService;
import cn.jwis.platform.plm.workflow.engine.order.entity.ProcessOrder;
import cn.jwis.product.pdm.customer.entity.DeliveryDocumentOperateDTO;
import cn.jwis.product.pdm.customer.entity.IntegrationRecord;
import cn.jwis.product.pdm.delivery.entity.Delivery;
import cn.jwis.product.pdm.delivery.helper.DeliveryHelper;
import cn.jwis.product.pdm.document.entity.DocumentIteration;
import cn.jwis.product.pdm.document.service.DocumentHelper;
import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

@Data
@Component
@Slf4j
public class PMSRelease extends EntityRelease{

    @Resource
    private CommonAbilityHelper commonAbilityHelper;
    @Resource
    private DocumentHelper documentHelper;
    @Resource
    DeliveryHelper deliveryHelper;
    @Resource
    PreferencesService preferencesService;



    @Override
    public IntegrationRecord release(ProcessOrder processOrder, String entityOid, String recordOid, int isFirst) {
        pushDocumentInfoToPMS(entityOid, ObjectUtil.toString(isFirst));
        return new IntegrationRecord();
    }


    /**
     *
     * @param oid 文档oid
     * @param flag 标志位，表⽰⽂档操作 1：上传、2：删除、3：版本变更
     *
     */
    public void pushDocumentInfoToPMS(String oid, String flag) {
        // PMS接口地址
        String pmsApiUrl;
        String pmsTestApiUrl = "http://10.60.10.80:8090/openapi/YinheDocument/SyncFile"; //测试环境地址
        ConfigItem keyItem = preferencesService.queryConfigValue("pmsApiUrl");
        pmsApiUrl = (keyItem == null || StrUtil.isBlank(keyItem.getValue()))
                ? pmsTestApiUrl
                : keyItem.getValue();
        log.info("PMS接口推送地址:{}", pmsApiUrl);

        // 校验oid
        if (StrUtil.isBlank(oid)) {
            throw new JWIException("oid不能为空");
        }

        // 查询DocumentIteration
        DocumentIteration doc = (DocumentIteration) commonAbilityHelper.findDetailEntity(oid, DocumentIteration.TYPE);
        if (doc == null) {
            log.warn("没有查询到文档信息, oid={}", oid);
            throw new JWIException("没有查询到文档信息");
        }

        JSONObject extensionContent = doc.getExtensionContent();
        if (extensionContent == null) {
            throw new JWIException("文档扩展信息(extensionContent)为空");
        }

        String deliveryOid = extensionContent.getString("deliveryOid");
        if (StrUtil.isBlank(deliveryOid)) {
            throw new JWIException("文档未关联交付清单(缺少deliveryOid)");
        }

        // 查询Delivery
        Delivery delivery = (Delivery) commonAbilityHelper.findDetailEntity(deliveryOid, Delivery.TYPE);
        if (delivery == null) {
            throw new JWIException("未找到对应交付清单: " + deliveryOid);
        }

        JSONObject deliveryExt = delivery.getExtensionContent();
        if (deliveryExt == null) {
            throw new JWIException("交付清单扩展信息(extensionContent)为空");
        }

        String pmsId = deliveryExt.getString("pms_id");
        if (StrUtil.isBlank(pmsId)) {
            log.warn("跳过处理：交付清单缺少 pms_id 属性，deliveryExt={}", deliveryExt.toJSONString());
            return;
        }

        // 构造DTO
        DeliveryDocumentOperateDTO dto = new DeliveryDocumentOperateDTO();
        dto.setExpectDocumentId(pmsId);
        dto.setFileId(doc.getOid());
        dto.setFileName(doc.getName());
        dto.setFileUrl(StrUtil.format(
                "http://plm.plmtest.yhroot.com/#/detailPage/object-details?oid={}&type=DocumentIteration&masterType=Document&modelDefinition=JWIGeneralDocument&tabActive=product",
                doc.getOid()
        ));
        dto.setUploaderId(doc.getOwner());
        dto.setStaus(doc.getLifecycleStatus());
        dto.setActualFinishTime(DateUtil.today());
        dto.setVersion(doc.getVersion());
        dto.setFlag(flag); // 上传

        List<DeliveryDocumentOperateDTO> requestList = Collections.singletonList(dto);
        log.info("准备推送文档到PMS: {}", JSONUtil.toJsonStr(requestList));




        HttpResponse response;
        try {
            response = HttpRequest.post(pmsTestApiUrl)
                    .header("Content-Type", ContentType.JSON.getValue())
                    .body(JSONUtil.toJsonStr(requestList))
                    .timeout(10000)
                    .execute();
        } catch (Exception ex) {
            log.error("推送PMS接口异常: {}", ex.getMessage(), ex);
            throw new RuntimeException("推送PMS接口发生异常: " + ex.getMessage(), ex);
        }
        // 返回内容
        String body = response.body();
        log.info("PMS接口返回: {}", body);

        // 先看http状态
        if (!response.isOk()) {
            throw new RuntimeException("推送PMS接口失败: HTTP状态码=" + response.getStatus() + ", 内容=" + body);
        }

        // 再解析JSON
        cn.hutool.json.JSONObject json = JSONUtil.parseObj(body);
        Boolean success = json.getBool("success");
        String msg = json.getStr("msg");

        if (Boolean.TRUE.equals(success)) {
            log.info("PMS推送成功");
        } else {
            throw new RuntimeException("PMS推送失败: " + msg);
        }

        if (response.isOk()) {
            log.info("文档推送结果: {}", response.body());
        } else {
            log.error("文档推送失败: HTTP {} - {}", response.getStatus(), response.body());
            throw new RuntimeException("推送PMS接口失败: " + response.body());
        }
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        type = EntityReleaseFactory.BusinessType.PMS.name();
        EntityReleaseFactory.register(type,this);
    }



}
