package cn.jwis.product.pdm.customer.service.release;

import cn.jwis.framework.base.exception.JWIException;
import cn.jwis.product.pdm.customer.service.util.HttpClientUtil;
import cn.jwis.product.pdm.document.entity.DocumentIteration;
import cn.jwis.product.pdm.document.service.DocumentHelper;
import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2023/11/2 9:22
 * @Email <EMAIL>
 */
@Data
@EqualsAndHashCode
@Component
@Transactional
@Slf4j
public class DocReleaseIDS {

    @Autowired
    DocumentHelper docHelper;


    // IDS更新文档状态接口
    @Value("${ids.callback.url}")
    private String ids_callback_url;


    public void sendIDSWithStatus(String entityOid, long status) {
        DocumentIteration dbDoc = docHelper.findByOid(entityOid);
        if (dbDoc == null) return;
        if (!"IDS".equals(dbDoc.getClsCode())) {
            log.info(dbDoc.getNumber() + " 不是IDS类型 不需要发送IDS");
            return;
        }
        JSONObject body = new JSONObject();
        body.put("encode", dbDoc.getNumber());
        //0=已发布 1=取消
        body.put("status", status);
        log.info("sendIDS.url:" + ids_callback_url);
        log.info("sendIDS.request:" + body.toJSONString());
        String responseStr = HttpClientUtil.postJson(ids_callback_url, null, new HashMap<>(), body.toJSONString(), false);
        log.info("sendIDS.result:" + responseStr);
    }

    public void sendIDS(String entityOid){
        DocumentIteration dbDoc = docHelper.findByOid(entityOid);
        if(dbDoc == null) return;
        if(!"IDS".equals(dbDoc.getClsCode())){
            log.info(dbDoc.getNumber() + " 不是IDS类型 不需要发送IDS");
            return;
        }
        JSONObject body = new JSONObject();
        body.put("encode", dbDoc.getNumber());
        body.put("status", 0);
        log.info("sendIDS.url:" + ids_callback_url);
        log.info("sendIDS.request:" + body.toJSONString());
        String responseStr = HttpClientUtil.postJson(ids_callback_url, null, new HashMap<>(), body.toJSONString(), false);
        log.info("sendIDS.result:" + responseStr);
    }

    public String sendIDSEdit(List<String> oidList){
        StringBuilder stringBuilder = new StringBuilder();
        oidList.parallelStream().forEach(entityOid -> {
            try {
                DocumentIteration dbDoc = docHelper.findByOid(entityOid);
                if(dbDoc == null) return;
                if(!"IDS".equals(dbDoc.getClsCode())){
                    return;
                }
                JSONObject body = new JSONObject();
                body.put("encode", dbDoc.getNumber());
                body.put("status", 1);
                log.info("sendIDS.url:" + ids_callback_url);
                log.info("sendIDS.request:" + body.toJSONString());
                String responseStr = HttpClientUtil.postJson(ids_callback_url, null, new HashMap<>(), body.toJSONString(), false);
                log.info("sendIDS.result:" + responseStr);
            }catch (Exception e){
                log.error(e.getMessage(), e);
                stringBuilder.append(e.getMessage());
            }
        });
        if(stringBuilder.length() > 0)
            throw new JWIException(stringBuilder.toString());
        return "SUCCESS";
    }

}
