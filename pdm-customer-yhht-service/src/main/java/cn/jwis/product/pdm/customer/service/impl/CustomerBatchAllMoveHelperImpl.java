package cn.jwis.product.pdm.customer.service.impl;

import cn.jwis.framework.base.domain.able.info.ModelInfo;
import cn.jwis.framework.base.dto.UserDTO;
import cn.jwis.framework.base.util.Assert;
import cn.jwis.framework.base.util.CollectionUtil;
import cn.jwis.framework.base.web.session.SessionHelper;
import cn.jwis.platform.plm.foundation.location.able.info.LocationInfo;
import cn.jwis.platform.plm.permission.filter.service.PermissionFilterHelper;
import cn.jwis.platform.plm.permission.helper.PermissionHelper;
import cn.jwis.platform.plm.permission.permission.dto.FilterParameterDTO;
import cn.jwis.platform.plm.permission.permission.enums.PermissionKeyEnum;
import cn.jwis.product.pdm.cad.batchmove.service.BatchAllMoveHelperImpl;
import cn.jwis.product.pdm.cad.mcad.entity.MCAD;
import cn.jwis.product.pdm.cad.mcad.entity.MCADIteration;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2023/10/9 11:01
 * @Email <EMAIL>
 */
@Service
@Primary
@Transactional
public class CustomerBatchAllMoveHelperImpl extends BatchAllMoveHelperImpl {

    @Autowired
    PermissionFilterHelper permissionFilterHelper;

    @Resource
    TriggerAuditServiceImpl triggerAuditService;

    @Override
    public long move(List<ModelInfo> entitys, LocationInfo target) {
        // 校验部件、文档、ECAD的权限
        Map<String,Boolean> hasAccessMap = hasPermissionOnFolder(
                "NOINSTANCE",target.getCatalogOid(),target.getCatalogType(),"create");
        // 校验MCAD的权限
        hasAccessMap.put("MCAD",hasMcadPermissionOnFolder(entitys,target));
        for(ModelInfo modelInfo : entitys){
            String modelType = modelInfo.getType();
            modelType = modelType.replace("Iteration","");
            Assert.isTrue(hasAccessMap.get(modelType), "你对目标位置没有" + modelType + "的创建权限，不能移动！");
        }
        long result = super.move(entitys, target);
        entitys.forEach(item -> triggerAuditService.move(item, target));
        return result;
    }

    private Boolean hasMcadPermissionOnFolder(List<ModelInfo> entitys,LocationInfo target) {
        List<ModelInfo> mcadModels = entitys.stream().filter(m->MCADIteration.TYPE.equals(m.getType())).collect(Collectors.toList());
        if(CollectionUtil.isEmpty(mcadModels)){
            return true;
        }
        UserDTO user = SessionHelper.getCurrentUser();
        JSONObject param = new JSONObject();
        param.put("tenantOid", user.getTenantOid());
        param.put("containerOid", target.getContainerOid());
        param.put("catalogOid",target.getCatalogOid());
        param.put("catalogType",target.getCatalogType());
        param.put("masterType", MCAD.TYPE);
        Set<String> modelTypes = CollectionUtil.mapToSet(mcadModels, ModelInfo::getModelDefinition);
        modelTypes.add(MCAD.TYPE);
        for (String modelType : modelTypes) {
            param.put("modelDefinition", modelType);
            boolean hasAccess = PermissionHelper.hasCreatePermission(param, PermissionKeyEnum.CREATE, user);
            if(hasAccess){
                return true;
            }
        }
        return false;
    }

    public Map<String,Boolean> hasPermissionOnFolder(String viewCode, String objectOid, String objectType, String permissionType) {
        Map<String,Boolean> result = new HashMap<>();
        result.put("Document",true);
        result.put("Part",true);
        result.put("ECAD",true);
        FilterParameterDTO dto = new FilterParameterDTO();
        dto.setViewCode(viewCode);
        dto.setObjectOid(objectOid);
        dto.setObjectType(objectType);
        List<JSONObject> permissionList = permissionFilterHelper.executeFilters(dto);
        if(CollectionUtils.isEmpty(permissionList)){
            return result;
        }
        for(Object permission : permissionList){
            JSONObject jsonObject = JSONObject.parseObject(JSON.toJSONString(permission));
            String modelType = jsonObject.getString("modelType");
            String permissionKey = jsonObject.getString("permissionKey");
            String status = jsonObject.getString("status");
            boolean hasAccess= "enable".equals(status) ? true : false;
            result.put(modelType,hasAccess);
        }
        return result;
    }

}
