package cn.jwis.product.pdm.customer.service.delegate;

import cn.jwis.framework.base.domain.able.ModelAble;
import cn.jwis.framework.base.dto.UserDTO;
import cn.jwis.framework.base.util.CollectionUtil;
import cn.jwis.framework.base.util.SpringContextUtil;
import cn.jwis.framework.base.util.StringUtil;
import cn.jwis.framework.base.web.session.SessionHelper;
import cn.jwis.platform.plm.account.user.service.UserHelper;
import cn.jwis.platform.plm.foundation.common.response.InstanceEntity;
import cn.jwis.platform.plm.foundation.common.service.CommonAbilityService;
import cn.jwis.platform.plm.foundation.common.service.InstanceHelper;
import cn.jwis.platform.plm.workflow.engine.order.entity.ProcessOrder;
import cn.jwis.platform.plm.workflow.engine.service.interf.ProcessOrderHelper;
import cn.jwis.product.pdm.change.entity.ECA;
import cn.jwis.product.pdm.change.response.ChangeInfo;
import cn.jwis.product.pdm.change.service.ECAService;
import cn.jwis.product.pdm.change.service.ECAServiceImpl;
import cn.jwis.product.pdm.customer.entity.Forming;
import cn.jwis.product.pdm.customer.entity.IntegrationRecord;
import cn.jwis.product.pdm.customer.entity.Screen;
import cn.jwis.product.pdm.customer.repo.CustomerCommonRepo;
import cn.jwis.product.pdm.customer.repo.IntegrationMonitorRepo;
import cn.jwis.product.pdm.customer.service.interf.IntegrationMonitorService;
import cn.jwis.product.pdm.customer.service.release.EntityRelease;
import cn.jwis.product.pdm.customer.service.release.EntityReleaseFactory;
import cn.jwis.product.pdm.partbom.part.entity.PartIteration;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.RepositoryService;
import org.activiti.engine.RuntimeService;
import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.Expression;
import org.activiti.engine.delegate.JavaDelegate;
import org.activiti.engine.repository.Model;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 工作流执行代理：实现部件/文档/EC等实体发放时推送数据到MQ的功能
 * @date 2023/8/15 10:44
 * @Email <EMAIL>
 */
@Slf4j
public class EntityReleaseDelegate implements JavaDelegate {

    // 流程模板中配置的代理类参数，按需使用即可
    private Expression businessType;

    @Autowired
    ECAService ecaService;

    @Override
    public void execute(DelegateExecution execution) {
        String processInstanceId = execution.getProcessInstanceId();
        log.info("EntityReleaseDelegate execute procId={}", processInstanceId);
        RuntimeService runtimeService = SpringContextUtil.getBean(RuntimeService.class);
        initContext(runtimeService,execution.getId());
        try {
            ProcessOrderHelper processOrderHelper = (ProcessOrderHelper) SpringContextUtil.getBean("processOrderHelperImpl");
            ECAServiceImpl ecaService = (ECAServiceImpl)SpringContextUtil.getBean("ECAServiceImpl");
            // processOrder：流程申请单对象
            ProcessOrder processOrder = processOrderHelper.findByProcessInstanceId(processInstanceId);
            log.info("EntityReleaseDelegate  processOrder==>{}", JSONObject.toJSONString(processOrder));
            // instanceEntityList：评审对象集合（部件，文档等）
            List<InstanceEntity> originInstanceEntityList = processOrderHelper.findBizObject(processOrder.getOid());
            List<InstanceEntity> instanceEntityList = new ArrayList<>();
            for(InstanceEntity instanceEntity : originInstanceEntityList){
                if(ECA.TYPE.equals(instanceEntity.getType())){
                    InstanceHelper instanceHelper = (InstanceHelper) SpringContextUtil.getBean("instanceHelperImpl");
                    List<ChangeInfo> list = ecaService.findChangeInfo(instanceEntity.getOid(),new ArrayList<>());
                    list.stream().forEach(changeInfo -> {
                        JSONObject jsonObject = instanceHelper.findLatest(changeInfo.getOid(), changeInfo.getType());
                        InstanceEntity newEntity = jsonObject.toJavaObject(InstanceEntity.class);
                        instanceEntityList.add(newEntity);
                    });
                }else {
                    instanceEntityList.add(instanceEntity);
                }
            }
            // 评审对象为空则不处理
            if(CollectionUtil.isEmpty(instanceEntityList)){
                log.info("EntityReleaseDelegate  instanceEntity is empty! ");
                runtimeService.setVariable(execution.getId(), "unReleaseEntity", "");
                return;
            }
            // 获取上一轮发布失败的数据
            Object lastUnReleaseObj = runtimeService.getVariable(execution.getId(),"unReleaseEntity");
            // 第一次发布认为是全部未发放
            String lastUnReleaseEntity = "all";
            if(lastUnReleaseObj != null){
                lastUnReleaseEntity = lastUnReleaseObj.toString();
            }
            processOrder.setCurrExecuteId(execution.getId());
            // 发放数据,返回结果为此次需要发布的数据
            String unReleaseData = releaseData(processOrder, instanceEntityList,lastUnReleaseEntity);
            // 对走完 物料BOM及图纸发布流程的直接已发布
            List<String> oidList = instanceEntityList.stream()
                    .map(InstanceEntity::getOid)
                    .collect(Collectors.toList());
            CustomerCommonRepo customerCommonRepo = SpringContextUtil.getBean(CustomerCommonRepo.class);
            customerCommonRepo.updateLifeStatus(PartIteration.TYPE, oidList, "Released");

            log.info("EntityReleaseDelegate instanceEntityList=" + instanceEntityList.stream().map(it -> it.getNumber()).collect(Collectors.joining(",")));
            log.info("EntityReleaseDelegate release=" + unReleaseData);
            // 查询发布结果
            //IntegrationMonitorService integrationService = (IntegrationMonitorService) SpringContextUtil.getBean("integrationMonitorServiceImpl");
            //String result = analysisIntegrationResult(unReleaseData,integrationService,2);
            //log.info("EntityReleaseDelegate analysisIntegrationResult=" + result);
            // 记录发布的数据
            runtimeService.setVariable(execution.getId(), "unReleaseEntity", unReleaseData);
        } catch (Exception e) {
            log.error("EntityReleaseDelegate execute error==>", e);
            StringBuffer buffer = new StringBuffer();
            StackTraceElement[] stackTrace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : stackTrace) {
                buffer.append(stackTraceElement.toString() + "\n");
            }
            runtimeService.setVariable(execution.getId(), "releaseError", "处理集成数据异常，请联系IT！\r\n" + buffer);
            runtimeService.setVariable(execution.getId(), "unReleaseEntity", "all");
        }
    }

    private void initContext(RuntimeService runtimeService, String id) {
        Object owner = runtimeService.getVariable(id,"owner");
        UserHelper userHelper = (UserHelper)SpringContextUtil.getBean("defaultUserHelperImpl");
        UserDTO user = userHelper.findByAccount(owner.toString());
        Object tenantOid = runtimeService.getVariable(id,"tenantOid");
        user.setAccount(owner.toString());
        user.setOid(owner.toString());
        user.setTenantOid(tenantOid.toString());
        SessionHelper.addCurrentUser(user);
    }

    private String analysisIntegrationResult(String unReleaseData,IntegrationMonitorService integrationService,int count) throws Exception {
        if(count == 0 || StringUtil.isBlank(unReleaseData)){
            return unReleaseData;
        }
        Thread.sleep(3000);
        StringBuffer currResult = new StringBuffer();
        for(String unReleased : unReleaseData.split(",")){
            IntegrationRecord record = integrationService.findByBusinessOid(unReleased);
            if(record == null){
                continue;
            }
            if(!record.getIsSuccess()) {
                currResult.append(unReleased).append(",");
            }
        }
        return analysisIntegrationResult(currResult.toString(),integrationService,count-1);
    }

    private String releaseData(ProcessOrder processOrder, List<InstanceEntity> instanceEntityList,String lastUnReleaseEntity) {
        if (StringUtil.isBlank(lastUnReleaseEntity)) {
            log.info("lastUnReleaseEntity is empty!");
            return ""; // 此时认为上一轮已经全部发布成功了，直接返回。
        }
        List<String> needPublish = new ArrayList<>(); // 需要发布的
        // 排序，非筛选或未成型物料排在前面发布
        List<InstanceEntity> sortedEntity = sortEntity(instanceEntityList);
        StringBuffer result = new StringBuffer();
        String businessTypes = businessType.getExpressionText();
        for (String businessType : businessTypes.split(",")) {
            sortedEntity.stream().forEach(instanceEntity -> {
                // 根据流程类型或者评审对象类型找到对应的发布能力提供者
                EntityRelease entityRelease = EntityReleaseFactory.match(businessType);
                //把前一轮未发布的数据再发布一次
                //第一次全部发布
                //如果是不属于该businessType的数据，会返回null，不处理
                if ("all".equals(lastUnReleaseEntity)
                        || lastUnReleaseEntity.contains(entityRelease.getType() + "_" + instanceEntity.getNumber() + "_" + instanceEntity.getVersion())) {
                    IntegrationRecord record = entityRelease.release(processOrder, instanceEntity.getOid(), "",0);
                    if (record != null) {
                        result.append(record.getBusinessOid()).append(",");
                        needPublish.add(instanceEntity.getOid());
                    }
                }
            });
        }
        // 未发布的数据在此处设置为已发放状态，已发布的数据在回调函数中设置为已发放状态
        List<InstanceEntity> needUpdateStatus = instanceEntityList.stream().filter(
                i->!needPublish.contains(i.getOid())).collect(Collectors.toList());
        updateStatus(processOrder,needUpdateStatus);
        return StringUtil.isBlank(result.toString()) ? "none" : result.toString() ;
    }

    private void updateStatus(ProcessOrder processOrder,List<InstanceEntity> needUpdateStatus) {
        if(CollectionUtil.isEmpty(needUpdateStatus) || processOrder == null){
            return;
        }
        RepositoryService repositoryService = SpringContextUtil.getBean(RepositoryService.class);
        IntegrationMonitorRepo monitorRepo = (IntegrationMonitorRepo) SpringContextUtil.getBean("integrationMonitorNeo4jRepoImpl");
        // 是否是停用流程
        boolean isInactive = false;
        Model model = repositoryService.getModel(processOrder.getProcessModelId());
        if("cn_jwis_wlstop".equals(model.getKey())){
            isInactive = true;
        }
        String lifeCycleStatus = isInactive ? "Deactivated" : "Released";
        for(InstanceEntity instanceEntity : needUpdateStatus) {
            monitorRepo.updateStatusByOid(instanceEntity.getOid(), instanceEntity.getType(), lifeCycleStatus);
        }




    }

    private List<InstanceEntity> sortEntity(List<InstanceEntity> instanceEntityList) {
        List<InstanceEntity> first = new ArrayList<>();
        List<InstanceEntity> second = new ArrayList<>();
        CommonAbilityService commonAbilityService = (CommonAbilityService) SpringContextUtil.getBean("commonAbilityServiceImpl");
        for(InstanceEntity entity : instanceEntityList){
            List<ModelAble> modelAbleList = commonAbilityService.findMIByTo(entity.getMasterOid(),entity.getMasterType(), Forming.TYPE);
            // 如果关联了成型物料，则标识它本身是未成型的，要先发布
            if(CollectionUtil.isNotEmpty(modelAbleList)){
                first.add(entity);
                continue;
            }
            modelAbleList = commonAbilityService.findMIByFrom(entity.getMasterOid(),entity.getMasterType(), Screen.TYPE);
            // 如果关联了筛选物料，则标识它本身是未成型的，要先发布
            if(CollectionUtil.isNotEmpty(modelAbleList)){
                first.add(entity);
                continue;
            }
            second.add(entity);
        }
        first.addAll(second);
        return first;
    }

}
