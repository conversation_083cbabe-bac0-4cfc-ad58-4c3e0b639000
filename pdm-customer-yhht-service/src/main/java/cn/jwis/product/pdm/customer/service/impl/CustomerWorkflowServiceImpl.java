package cn.jwis.product.pdm.customer.service.impl;

import cn.hutool.json.JSONUtil;
import cn.jwis.framework.base.util.StringUtil;
import cn.jwis.platform.plm.sysconfig.entity.ConfigItem;
import cn.jwis.platform.plm.sysconfig.preferences.PreferencesService;
import cn.jwis.platform.plm.workflow.engine.service.interf.ProcessModelHelper;
import cn.jwis.product.pdm.customer.service.dto.ModelParam;
import cn.jwis.product.pdm.customer.service.dto.WorkflowQueryParam;
import cn.jwis.product.pdm.customer.service.interf.CustomerWorkflowService;
import org.activiti.rest.service.api.repository.ModelResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Primary
@Transactional
public class CustomerWorkflowServiceImpl implements CustomerWorkflowService {

    private static final Logger log = LoggerFactory.getLogger(CustomerWorkflowServiceImpl.class);
    @Autowired
    private PreferencesService preferencesService;

    @Autowired
    private ProcessModelHelper processModelHelper;

    public List<ModelResponse> latest(WorkflowQueryParam param) {
        List<ModelResponse> modelResponses = processModelHelper.fuzzyLatestDeployedModel(param.getSearchKey(), param.getContainerType());
        if (param.getModels().isEmpty()) {
            return modelResponses;
        }

        /**
         * 项目文档产品化流程;;;DocumentIteration|物料BOM及图纸发布流程;;;PartIteration,MCADIteration,ECADIteration|技术文档发布流程;;;DocumentIteration|PCB评审流程;;;PartIteration,ECADIteration|物料停用流程;;;PartIteration|新器件引入流程;;;PartIteration|物料编码申请流程;;;PartIteration
         */

        List<ModelResponse> responses = modelResponses;
        ConfigItem reviewType = this.preferencesService.queryConfigValue("review_type_Verification");
        ConfigItem LifecycleState = this.preferencesService.queryConfigValue("Lifecycle_State_Verification");
        ConfigItem btnConfigItem = this.preferencesService.queryConfigValue("btn_type_Verification");

        log.info("reviewType:{}", JSONUtil.toJsonStr(reviewType));
        log.info("LifecycleState:{}", JSONUtil.toJsonStr(LifecycleState));
        log.info("btnConfigItem:{}", JSONUtil.toJsonStr(btnConfigItem));

        for (ModelParam modelParam : param.getModels()) {
            if (reviewType != null && StringUtil.isNotBlank(modelParam.getModelType())) {
                List<String> names = getNames(reviewType, modelParam.getModelType());
                responses = responses.stream().filter(r -> names.contains(r.getName())).collect(Collectors.toList());
            }

            if (LifecycleState != null && StringUtil.isNotBlank(modelParam.getStatus())) {
                List<String> names = getNames(LifecycleState, modelParam.getStatus());
                responses = responses.stream().filter(r -> names.contains(r.getName())).collect(Collectors.toList());
            }

            //只对已发布的做再次过滤
            if (modelParam.getStatus().equals("Released")) {
                //增加外发BOM btn来再次根据 btnType过滤可以发起的流程
                if (btnConfigItem != null && StringUtil.isNotBlank(modelParam.getBtnType())) {
                    List<String> names = getNames(btnConfigItem, modelParam.getBtnType());
                    responses = responses.stream().filter(r -> names.contains(r.getName())).collect(Collectors.toList());
                }
            }

        }

        if (!responses.isEmpty()) {
            return responses;
        }

        return modelResponses;
    }

    private List<String> getNames(ConfigItem configItem, String key) {
        List<String> names = new ArrayList<>();

        String configStr = configItem.getValue();
        String[] split = configStr.split("\\|");
        for (String ss : split) {
            String[] split1 = ss.split(";;;");
            if (split1[1].contains(",")) {
                String[] split2 = split1[1].split(",");
                for (String sss : split2) {
                    if (key.equalsIgnoreCase(sss)) {
                        names.add(split1[0]);
                    }
                }

            } else {
                if (key.equalsIgnoreCase(split1[1])) {
                    names.add(split1[0]);
                }
            }
        }
        return names;
    }
}
