package cn.jwis.product.pdm.customer.service.impl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.resource.InputStreamResource;
import cn.hutool.core.io.resource.MultiResource;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.jwis.framework.base.domain.able.ModelAble;
import cn.jwis.framework.base.domain.able.info.ModelInfo;
import cn.jwis.framework.base.domain.entity.BaseEntity;
import cn.jwis.framework.base.dto.UserDTO;
import cn.jwis.framework.base.exception.JWIException;
import cn.jwis.framework.base.response.PageResult;
import cn.jwis.framework.base.util.CollectionUtil;
import cn.jwis.framework.base.web.session.SessionHelper;
import cn.jwis.framework.database.core.entity.EntityFilter;
import cn.jwis.platform.plm.container.entity.Folder;
import cn.jwis.platform.plm.dataengine.common.JWICommonService;
import cn.jwis.platform.plm.file.entity.FileMetadata;
import cn.jwis.platform.plm.file.utils.MinioUtil;
import cn.jwis.platform.plm.foundation.common.dto.InstanceBasicDTO;
import cn.jwis.platform.plm.foundation.context.entity.Tenant;
import cn.jwis.platform.plm.foundation.versionrule.service.VersionRuleHelper;
import cn.jwis.platform.plm.permission.dto.DynamicPermissionDTO;
import cn.jwis.platform.plm.permission.dynamic.service.IDynamicPermissionService;
import cn.jwis.platform.plm.permission.filter.service.factory.instance.ProcessFilter;
import cn.jwis.platform.plm.permission.helper.PermissionHelper;
import cn.jwis.platform.plm.permission.permission.enums.PermissionKeyEnum;
import cn.jwis.platform.plm.permissions.util.RedisUtil;
import cn.jwis.product.pdm.change.entity.Issue;
import cn.jwis.product.pdm.customer.entity.DeliveryReport;
import cn.jwis.product.pdm.customer.entity.DeliveryReportCustom;
import cn.jwis.product.pdm.customer.entity.PermApplyEntity;
import cn.jwis.product.pdm.customer.entity.assistant.CheckedAssist;
import cn.jwis.product.pdm.customer.repo.CustomerCommonRepo;
import cn.jwis.product.pdm.customer.service.interf.CommonService;
import cn.jwis.product.pdm.customer.service.interf.DingTalkService;
import cn.jwis.product.pdm.customer.service.interf.ExcelExportService;
import cn.jwis.product.pdm.customer.service.util.CustomerMinioUtil;
import cn.jwis.product.pdm.customer.service.util.Doc2PdfUtil;
import cn.jwis.product.pdm.document.entity.DocumentIteration;
import cn.jwis.product.pdm.document.service.DocumentService;
import com.aliyun.dingtalkworkflow_1_0.models.GetProcessInstanceResponse;
import com.aliyun.dingtalkworkflow_1_0.models.GetProcessInstanceResponseBody;
import com.aliyun.tea.TeaException;
import com.aliyun.teautil.models.RuntimeOptions;
import com.aspose.words.*;
import com.itextpdf.text.Element;
import com.itextpdf.text.Phrase;
import com.itextpdf.text.Rectangle;
import com.itextpdf.text.pdf.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.StandardCopyOption;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.List;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2023/8/11 17:09
 * @Email <EMAIL>
 */
@Service
@Transactional
@Slf4j
public class CustomerCommonServiceImpl implements CommonService, CheckedAssist {

    private static final Logger logger = LoggerFactory.getLogger(CustomerCommonServiceImpl.class);

    @Autowired
    JWICommonService jwiCommonService;

    @Autowired
    CustomerCommonRepo customerCommonRepo;

    @Autowired
    DocumentService documentService;

    @Resource
    VersionRuleHelper versionRuleHelper;


    @Autowired
    private MinioUtil minioUtil;
    @Autowired
    private CustomerMinioUtil customerMinioUtil;

    @Value("${file.service.gateway.url}")
    private String fileServiceGatewayUrl;

    @Value("${minio.default.bucket}")
    private String defaultBucket;

    @Autowired
    DingTalkService dingTalkService;
    @Autowired
    private RedisUtil redisUtil;

    private static final int MAX_RETRIES = 5;

    @Value("${tenantOid:6deb5dde-aa39-46fb-962d-a5951f8fab5e}")
    private String tenantOid;

    @Override
    public String getAloneTenantOid() {
        EntityFilter filter = new EntityFilter();
        filter.setType(Tenant.TYPE);
        ModelAble tenant= CollectionUtil.getFirst(jwiCommonService.dynamicQuery(filter));
        if(tenant != null){
            return tenant.getOid();
        }
        return null;
    }

    @Override
    public List<Folder> getFolderPath(String type, String oid) throws Exception {
        return customerCommonRepo.getFolderPath(type,oid);
    }

    @Override
    public List<Issue> findIssueByEntity(List<InstanceBasicDTO> instances, boolean onlyUnClosed) throws Exception {
        List<Issue> result = new ArrayList<>();
        Map<String, List<String>> type2Entity = CollectionUtil.splitToMapList(
                instances,InstanceBasicDTO::getType,InstanceBasicDTO::getOid);
        for(Map.Entry<String,List<String>> entry : type2Entity.entrySet()){
            result.addAll(customerCommonRepo.findIssueByEntity(entry.getKey(),entry.getValue(),onlyUnClosed));
        }
        return result;
    }

    private final static Boolean isWindows = System.getProperty("os.name").toLowerCase().contains("windows");
    public final static String fileSeparator = File.separator, tmpPath = isWindows ? ("C:" + fileSeparator + "tmp" + fileSeparator) : (fileSeparator + "tmp" + fileSeparator);

    public void download2TmpDir(FileMetadata fileMetadata, String fileSaveCompletePath){
        try {
            HttpUtil.downloadFile(customerMinioUtil.getPresignedDownloadUrl(defaultBucket, fileMetadata.getFileName(), 30), FileUtil.file(fileSaveCompletePath));
        } catch (Exception e){
            throw new JWIException(e.getMessage(), e);
        }
    }
    public static String copyImageToTmpDir(String fileOriginalName, String tmpPath) {
        URL url = CustomerCommonServiceImpl.class.getClassLoader().getResource("image/logo.png");
        if (url == null) {
            System.err.println("图片路径未找到");
            return null;
        }

        // 临时目录下的目标文件路径
        String targetPath = tmpPath + fileOriginalName;

        logger.info("logo图片路径为--->>>>: " + targetPath);

        // 创建临时目录文件对象
        File targetFile = new File(targetPath);
        targetFile.getParentFile().mkdirs(); // 确保目录存在

        for (int attempt = 1; attempt <= MAX_RETRIES; attempt++) {
            try (InputStream inputStream = url.openStream()) {
                // 将图片文件复制到临时目录
                Files.copy(inputStream, targetFile.toPath(), StandardCopyOption.REPLACE_EXISTING);
                return targetFile.getAbsolutePath();
            } catch (Exception e) {
                e.printStackTrace();
                logger.info("图片复制失败，正在重试第 " + attempt + " 次...");
            }
        }

        // 如果所有重试都失败，返回null
        logger.error("图片复制失败，已尝试 " + MAX_RETRIES + " 次");
        return null;
    }
    /**
     * 在word页眉和页脚处增加 名称、编号、版本等属性，并保留原word内容
     * @param fileMetadata
     * @param doc
     * @param isChange
     * @return
     */
    public FileMetadata doc2PdfAddCode(FileMetadata fileMetadata, DocumentIteration doc, boolean isChange) {
        logger.info("doc2PdfAddCode start");
        String fileOriginalName = null;
        String originalName = fileMetadata.getFileOriginalName();
        try {
            String hashedFileName = generateHashedFileName(originalName);
            fileOriginalName = URLEncoder.encode(hashedFileName , "UTF-8");
        } catch (UnsupportedEncodingException e) {
            logger.error("Unable to encode file",e);
            throw new JWIException("文件名转码失败");
        }

        String docFileTmpSavePath = tmpPath + "docTempPath" + fileSeparator + fileOriginalName, pdfPath, pdfFileName, pdfPathNew;
        logger.info("docFileTmpSavePath---->>" + docFileTmpSavePath);
        download2TmpDir(fileMetadata, docFileTmpSavePath);
        URL url = CustomerCommonServiceImpl.class.getClassLoader().getResource("image/logo.png");
        String logoTempPath = tmpPath + "logoTempPath" + fileSeparator + UUID.fastUUID() + fileSeparator;
        String logoPath = copyImageToTmpDir("logo.png", logoTempPath);
        if (logoPath != null) {
            logger.info("图片复制成功");
        } else {
            logger.info("图片复制失败");
        }

        String copyDocPath = tmpPath + "copyDoc" + fileSeparator + UUID.fastUUID() + fileSeparator + fileOriginalName;
        String headerText = doc.getName();
        String headerRightText = doc.getNumber();
        com.alibaba.fastjson.JSONObject extensionContent = doc.getExtensionContent();
        String footerText = "版本号/修订码：" + StrUtil.SPACE + doc.getVersion();
        String footerCenterText = "银河航天" + "  " + "商密：" + extensionContent.get("cn_jwis_secrecy");
        try {
            Doc2PdfUtil.registerWord_v_22_5();
            logger.info("Loading document from path: {}", docFileTmpSavePath);
            Document docSource = new Document(docFileTmpSavePath);
            Document copyDoc = docSource.deepClone();
            DocumentBuilder builder = new DocumentBuilder(copyDoc);
            SectionCollection sections = copyDoc.getSections();
            double cmToPoints = 28.35;
            double fontSize = 7.5;
            for (int i = 1; i < sections.getCount(); i++) {

                // 清除节中的页眉和页脚
                for (HeaderFooter headerFooter : sections.get(i).getHeadersFooters()) {
                    if (headerFooter.getHeaderFooterType() == HeaderFooterType.HEADER_PRIMARY ||
                            headerFooter.getHeaderFooterType() == HeaderFooterType.FOOTER_PRIMARY ||
                            headerFooter.getHeaderFooterType() == HeaderFooterType.HEADER_FIRST ||
                            headerFooter.getHeaderFooterType() == HeaderFooterType.FOOTER_FIRST ||
                            headerFooter.getHeaderFooterType() == HeaderFooterType.HEADER_EVEN ||
                            headerFooter.getHeaderFooterType() == HeaderFooterType.FOOTER_EVEN) {
                        headerFooter.removeAllChildren(); // 清除页眉和页脚的内容
                    }
                }
                builder.moveToSection(i);
                builder.moveToHeaderFooter(HeaderFooterType.HEADER_PRIMARY);
//                builder.getParagraphFormat().setAlignment(ParagraphAlignment.LEFT);

                Border borderHeader = builder.getParagraphFormat().getBorders().getBottom();
                borderHeader.setLineStyle(LineStyle.SINGLE);
                borderHeader.setDistanceFromText(1.5);
                builder.getPageSetup().setHeaderDistance(0.8 * cmToPoints);
                // 设置页眉内容...
                builder.writeln();
                builder.getParagraphFormat().getBorders().clearFormatting();
                builder.getParagraphFormat().setSpaceAfter(10);  // 设置段落的间距，单位为磅，可以根据需要调整

                builder.startTable();
                // 第一行，第一列插入图片
                builder.insertCell();
                builder.getCellFormat().getBorders().setLineStyle(LineStyle.NONE);
                builder.getParagraphFormat().getBorders().clearFormatting();
                builder.getParagraphFormat().setAlignment(ParagraphAlignment.LEFT);
                builder.getParagraphFormat().getBorders().clearFormatting();
                builder.getParagraphFormat().setSpaceAfter(0);
                builder.getParagraphFormat().setSpaceBefore(0);
                String imagePath = logoPath; // 图片的路径
//            double widthInCm = 2.66;
                double widthInCm = 1.77;
//            double heightInCm = 0.85;
                double heightInCm = 0.56;
                double widthInPoints = widthInCm * cmToPoints;
                double heightInPoints = heightInCm * cmToPoints;
                logger.info("Inserting image from path: {}", imagePath);
                builder.insertImage(imagePath, widthInPoints, heightInPoints);

                // 第一行，第二列插入headerText
                builder.insertCell();
                builder.getCellFormat().getBorders().setLineStyle(LineStyle.NONE);
                builder.getCellFormat().getBorders().clearFormatting();
                builder.getCellFormat().setVerticalAlignment(CellVerticalAlignment.CENTER);
                builder.getParagraphFormat().setAlignment(ParagraphAlignment.CENTER); // 设置居中对齐
                builder.getFont().setSize(fontSize);
                builder.getFont().setName("宋体");
                builder.write(headerText);

                // 第一行，第三列插入编号，右对齐
                builder.insertCell();
                builder.getCellFormat().getBorders().setLineStyle(LineStyle.NONE);
                builder.getParagraphFormat().getBorders().clearFormatting();
                builder.getCellFormat().setVerticalAlignment(CellVerticalAlignment.CENTER);
                builder.getParagraphFormat().setAlignment(ParagraphAlignment.RIGHT); // 设置右对齐
                builder.write(headerRightText);

                builder.endRow();
                builder.endTable();
                // 移除表格边框
                builder.getCellFormat().getBorders().setLineStyle(LineStyle.NONE);

                // 页脚
                builder.moveToHeaderFooter(HeaderFooterType.FOOTER_PRIMARY);

                // 创建一个形状对象，用于水平线
                Border borderFooter = builder.getParagraphFormat().getBorders().getTop();
                borderFooter.setLineStyle(LineStyle.SINGLE);
                borderFooter.setDistanceFromText(0);
                builder.getPageSetup().setFooterDistance(1.1 * cmToPoints);

                builder.startTable();
                // 页脚表格第一列插入footerText
                builder.insertCell();

                builder.getCellFormat().getBorders().setLineStyle(LineStyle.NONE);
                builder.getParagraphFormat().getBorders().clearFormatting();
                builder.getParagraphFormat().setAlignment(ParagraphAlignment.LEFT);
                builder.getFont().setSize(fontSize);
                builder.getFont().setName("宋体");
                builder.write(footerText);

                // 页脚表格第二列插入xxx2，居中对齐
                builder.insertCell();
                builder.getCellFormat().getBorders().setLineStyle(LineStyle.NONE);
                builder.getParagraphFormat().getBorders().clearFormatting();
                builder.getParagraphFormat().setAlignment(ParagraphAlignment.LEFT);
                builder.write(footerCenterText);

                // 页脚表格第三列插入页码和总页数
                builder.insertCell();
                builder.getCellFormat().getBorders().setLineStyle(LineStyle.NONE);
                builder.getParagraphFormat().getBorders().clearFormatting();
                builder.getParagraphFormat().setAlignment(ParagraphAlignment.RIGHT);

                builder.endRow();
                builder.endTable();
                // 移除表格边框
                builder.getCellFormat().getBorders().setLineStyle(LineStyle.NONE);
            }
            logger.info("Saving document to path: {}", copyDocPath);
            copyDoc.save(copyDocPath);
            docFileTmpSavePath = copyDocPath;
        } catch (Exception e) {
            log.info(e.getMessage(), e);
            throw new JWIException("文件添加页眉文字出错");
        }

        if(fileOriginalName.endsWith(".doc") || fileOriginalName.endsWith(".docx"))
            pdfPath = Doc2PdfUtil.doc2pdf(docFileTmpSavePath);
        else{
            throw new JWIException("文件不是word格式");
        }
        pdfPathNew = tmpPath + "copyPdf" + fileOriginalName;

        pdfPath = signPageByItext(pdfPath, pdfPathNew);

        FileItem item = new DiskFileItemFactory().createItem("file", MediaType.MULTIPART_FORM_DATA_VALUE, true
                , pdfFileName = getDocPdfFileName(originalName, doc,isChange));
        try (InputStream pdfInputStream = new FileInputStream(pdfPath)){
            IOUtils.copy(pdfInputStream, item.getOutputStream());
            MultiResource multiResource = new MultiResource(Stream.of(new CommonsMultipartFile(item))
                    .map(applyNoThrow(multipartFile -> new InputStreamResource(multipartFile.getInputStream(), multipartFile.getOriginalFilename()))).collect(Collectors.toList()));

            String fileUploadUrl = fileServiceGatewayUrl + "/file/upload", accesstoken;
            accesstoken = (accesstoken = SessionHelper.getAccessToken()) != null ? accesstoken : "eyJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJKV0lTIiwiaWF0IjoxNjk4ODQwOTI4LCJzdWIiOiJzeXNfYWRtaW4iLCJleHAiOjE2OTk0NDU3MjgsInVzZXIiOnsib2lkIjoic3lzX2FkbWluIiwidGVuYW50T2lkIjoiIiwidGVuYW50TmFtZSI6bnVsbCwiYWNjb3VudCI6InN5c19hZG1pbiIsIm5hbWUiOiLns7vnu5_nrqHnkIblkZgiLCJwYXNzd29yZCI6bnVsbCwicGhvbmUiOm51bGwsImVtYWlsIjpudWxsLCJnZW5kZXIiOjAsImRlc2NyaXB0aW9uIjpudWxsLCJhdmF0YXIiOm51bGwsImRpc2FibGUiOjAsInNlY3VyaXR5TGV2ZWwiOjk5OSwiaWNvbiI6bnVsbCwib2xkUGFzc3dvcmQiOm51bGwsImNvbmZpcm1QYXNzd29yZCI6bnVsbCwibmV3UGFzc3dvcmQiOm51bGwsInRpcHMiOm51bGwsImludmFsaWRUaW1lIjpudWxsLCJjcmVhdGVCeSI6bnVsbCwic2lnbmF0dXJlIjpudWxsLCJjcmVhdGVEYXRlIjowLCJ1cGRhdGVCeSI6bnVsbCwidXBkYXRlRGF0ZSI6MH0sImFjY291bnQiOiJzeXNfYWRtaW4iLCJ1c2VyT2lkIjoic3lzX2FkbWluIn0.Mk5efWzWg03MUpHkw1YAHVJEcqC2RqDWIjC43U0ZSnI";
            Map<String, String> headerMap = new HashMap<>();
            headerMap.put("accesstoken", accesstoken);
            String respStr = HttpRequest.post(fileUploadUrl).timeout(30000).addHeaders(headerMap).form("file", multiResource).execute().body();
            logger.info("upload resp==>" + respStr);
            logger.info("pdfFileName==>" + pdfFileName);
            JSONObject respJson = new JSONObject(respStr);
            if(respJson.getInt("code") == 0)
                return JSONUtil.toBean(respJson.getJSONObject("result").getJSONObject(pdfFileName), FileMetadata.class);
            throw new JWIException(respStr);

        } catch (Exception e){
            logger.error(e.getMessage(), e);
            throw new JWIException(e.getMessage(), e);
        }
    }

    private static String signPageByItext(String src, String targetPath) {
        String dest = targetPath;
        logger.info("copyPdf路径=" + targetPath);

        try {
            PdfReader reader = new PdfReader(src);
            int totalPageCount = reader.getNumberOfPages();
            PdfStamper stamper = new PdfStamper(reader, new FileOutputStream(dest));

            // 使用 BaseFont 创建字体对象
            float size = 7.5f;
            BaseFont bf = BaseFont.createFont(BaseFont.HELVETICA, BaseFont.WINANSI, BaseFont.EMBEDDED);
            com.itextpdf.text.Font font = new com.itextpdf.text.Font(bf, size);

            for (int pageIndex = 2; pageIndex <= totalPageCount; pageIndex++) {
                PdfContentByte canvas = stamper.getOverContent(pageIndex);
                Rectangle pageSize = reader.getPageSize(pageIndex);
                // 创建页码和总页数的字符串
                String pageNumberText = String.format(" %d/%d", pageIndex - 1, totalPageCount - 1);

                // 使用 Phrase 创建文本new Phrase
                Phrase phrase = new Phrase(pageNumberText, font);

                // 计算文本的X坐标，使其右对齐
                float xPosition = pageSize.getRight() - 100; // 右对齐，50可以根据需要调整
                float yPosition = pageSize.getBottom() + 58; // 页脚位置

                // 添加文本到页面
                ColumnText.showTextAligned(canvas, Element.ALIGN_RIGHT, phrase, xPosition, yPosition, 0);
            }

            stamper.close();
            reader.close();
            logger.info("pdf写入页码完成");
        } catch (Exception e) {
            logger.error("pdf写入页码失败", e);
            e.printStackTrace();
        }
        return dest;

    }

    public FileMetadata doc2PdfUpload(FileMetadata fileMetadata, DocumentIteration doc,boolean isChange) {
        String fileOriginalName = fileMetadata.getFileOriginalName(), docFileTmpSavePath = tmpPath + fileOriginalName, pdfPath, pdfFileName;
        download2TmpDir(fileMetadata, docFileTmpSavePath);

        if(fileOriginalName.endsWith(".doc") || fileOriginalName.endsWith(".docx"))
            pdfPath = Doc2PdfUtil.doc2pdf(docFileTmpSavePath);
        else
            throw new JWIException("文件不是word格式");

        FileItem item = new DiskFileItemFactory().createItem("file", MediaType.MULTIPART_FORM_DATA_VALUE, true
                , pdfFileName = getDocPdfFileName(fileOriginalName, doc,isChange));
        try (InputStream pdfInputStream = new FileInputStream(pdfPath)){
            IOUtils.copy(pdfInputStream, item.getOutputStream());
            MultiResource multiResource = new MultiResource(Stream.of(new CommonsMultipartFile(item))
                    .map(applyNoThrow(multipartFile -> new InputStreamResource(multipartFile.getInputStream(), multipartFile.getOriginalFilename()))).collect(Collectors.toList()));

            String fileUploadUrl = fileServiceGatewayUrl + "/file/upload", accesstoken;
            accesstoken = (accesstoken = SessionHelper.getAccessToken()) != null ? accesstoken : "eyJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJKV0lTIiwiaWF0IjoxNjk4ODQwOTI4LCJzdWIiOiJzeXNfYWRtaW4iLCJleHAiOjE2OTk0NDU3MjgsInVzZXIiOnsib2lkIjoic3lzX2FkbWluIiwidGVuYW50T2lkIjoiIiwidGVuYW50TmFtZSI6bnVsbCwiYWNjb3VudCI6InN5c19hZG1pbiIsIm5hbWUiOiLns7vnu5_nrqHnkIblkZgiLCJwYXNzd29yZCI6bnVsbCwicGhvbmUiOm51bGwsImVtYWlsIjpudWxsLCJnZW5kZXIiOjAsImRlc2NyaXB0aW9uIjpudWxsLCJhdmF0YXIiOm51bGwsImRpc2FibGUiOjAsInNlY3VyaXR5TGV2ZWwiOjk5OSwiaWNvbiI6bnVsbCwib2xkUGFzc3dvcmQiOm51bGwsImNvbmZpcm1QYXNzd29yZCI6bnVsbCwibmV3UGFzc3dvcmQiOm51bGwsInRpcHMiOm51bGwsImludmFsaWRUaW1lIjpudWxsLCJjcmVhdGVCeSI6bnVsbCwic2lnbmF0dXJlIjpudWxsLCJjcmVhdGVEYXRlIjowLCJ1cGRhdGVCeSI6bnVsbCwidXBkYXRlRGF0ZSI6MH0sImFjY291bnQiOiJzeXNfYWRtaW4iLCJ1c2VyT2lkIjoic3lzX2FkbWluIn0.Mk5efWzWg03MUpHkw1YAHVJEcqC2RqDWIjC43U0ZSnI";
            Map<String, String> headerMap = new HashMap<>();
            headerMap.put("accesstoken", accesstoken);
            String respStr = HttpRequest.post(fileUploadUrl).timeout(30000).addHeaders(headerMap).form("file", multiResource).execute().body();
            logger.info("upload resp==>" + respStr);

            JSONObject respJson = new JSONObject(respStr);
            if(respJson.getInt("code") == 0)
                return JSONUtil.toBean(respJson.getJSONObject("result").getJSONObject(pdfFileName), FileMetadata.class);
            throw new JWIException(respStr);

        } catch (Exception e){
            logger.error(e.getMessage(), e);
            throw new JWIException(e.getMessage(), e);
        }
    }

    public FileMetadata doc2PdfUpload(FileMetadata fileMetadata, DocumentIteration doc) {
       return doc2PdfUpload(fileMetadata,doc,false);
    }

    public String getDocPdfFileName(String fileOriginalName, DocumentIteration doc) {
        return getDocPdfFileName(fileOriginalName, doc, false);
    }
    public String getDocPdfFileName(String fileOriginalName, DocumentIteration doc,boolean isChange) {
        if (StringUtils.isBlank(fileOriginalName)) {
            return StringUtils.EMPTY;
        }
        // 打印提取前的原始文件名
        log.info("提取前的原始文件名: {}", fileOriginalName);
        // 提取最后一个 `/` 后的文件名
        int lastSlashIndex = fileOriginalName.lastIndexOf("/");
        if (lastSlashIndex != -1) {
            String extractedFileName = fileOriginalName.substring(lastSlashIndex + 1);
            log.info("检测到路径分隔符 `/`，提取后的文件名: {}", extractedFileName);
            fileOriginalName = extractedFileName;
        } else {
            log.info("未检测到路径分隔符 `/`，文件名无需提取: {}", fileOriginalName);
        }

        String version;
        if (ObjectUtils.isEmpty(doc)) {
            version = StringUtils.EMPTY;
        } else if (isChange) {
            try {
                ModelInfo modelInfo = new ModelInfo();
                modelInfo.setOid(doc.getOid());
                modelInfo.setType(doc.getType());
                modelInfo.setModelDefinition(doc.getModelDefinition());
                log.info("ModelInfo:{}", JSONUtil.toJsonStr(modelInfo));
                UserDTO currUser = SessionHelper.getCurrentUser();
                if(currUser == null || (currUser != null && currUser.getTenantOid() == null)){
                    initUser();
                }
                version = versionRuleHelper.getNextVersion(modelInfo).get(0);
            } catch (Exception e) {
                log.error("获取版本号失败:", e);
                version = getNextVersionFromCurrent(doc.getVersion());
            }
        } else {
            version = doc.getVersion();
        }
        return fileOriginalName.substring(0, fileOriginalName.lastIndexOf(".")) + ("_" + version + "_" + "会签归档版") +
                ".pdf";
    }

    private String getNextVersionFromCurrent(String currentVersion) {
        if (StringUtils.isBlank(currentVersion)) {
            log.warn("当前版本号为空，无法推算下一个版本");
            return ""; // 或者抛出异常，取决于你的业务逻辑
        }

        if (!currentVersion.matches("^[A-Z]\\d{2}$")) {
            log.warn("当前版本号格式不正确 (应为 A\\d{{2}} 格式): {}", currentVersion);
            return ""; // 或者抛出异常
        }

        char prefix = currentVersion.charAt(0);
        int number = Integer.parseInt(currentVersion.substring(1));

        number++;

        if (number > 99) {
            prefix++;
            number = 1;
            if (prefix > 'Z') {
                log.error("版本号前缀已超出范围");
                return ""; //或者抛出异常
            }
        }

        return String.format("%c%02d", prefix, number);
    }

    @Autowired
    private IDynamicPermissionService dynamicPermissionService;

    @Autowired
    private ProcessFilter processFilter;

    public boolean checkDownloadAccess(String oid,String type) {
        ModelAble doc = jwiCommonService.findEntity(type,oid);

        try {
            String masterOid = this.dynamicPermissionService.queryMasterOidByObjectOid(oid, doc.getType());
            DynamicPermissionDTO dynamicPermissionDTO = new DynamicPermissionDTO();
            dynamicPermissionDTO.setTargetUserAccount(SessionHelper.getCurrentUser().getAccount());
            dynamicPermissionDTO.setTargetUserOid(SessionHelper.getCurrentUser().getOid());
            dynamicPermissionDTO.setBusinessObjectOid(masterOid);
            dynamicPermissionDTO.setBusinessObjectType(doc.getType());
            List<String> permissionCodeList = processFilter.checkDynamicPermission(dynamicPermissionDTO);
            if(permissionCodeList != null && permissionCodeList.contains("download"))
                return Boolean.TRUE;
        } catch (Exception var8) {
            log.error("getDynamicPermission error==>", var8);
        }

        return PermissionHelper.validate((BaseEntity)doc, PermissionKeyEnum.DOWNLOAD,SessionHelper.getCurrentUser());
    }

    @Resource
    private ExcelExportService excelExportService;

    public List<DeliveryReport> getDeliveryData(String containerOid){
        return customerCommonRepo.getDeliveryData(containerOid);
    }

    public void exportDeliveryData(String containerOid, HttpServletResponse response){
        List<DeliveryReport> data = customerCommonRepo.getDeliveryData(containerOid);
        Map map = new HashMap();
        map.put("交付文档报表", data);
        excelExportService.cvtDataExportWeb("交付文档报表", map, response);
    }

    public PageResult<PermApplyEntity> queryPerm(PermApplyEntity permApplyEntity){
        return customerCommonRepo.queryPerm(permApplyEntity);
    }


    public List<DeliveryReportCustom> getDeliveryDataForExport(String containerOid, boolean isNormal) {
        return customerCommonRepo.getDeliveryDataForExport(containerOid,isNormal);
    }

    public Object dingUserByProcessInstanceId(String processInstanceId, String dingCorPid) {
        try {
            String tokenNew = dingTalkService.getTokenNew();
            String userName = SessionHelper.getCurrentUser().getName();
            try {
                //根据token 调用 获取单个审批实例的查询接口
                com.aliyun.teaopenapi.models.Config config = new com.aliyun.teaopenapi.models.Config();
                config.protocol = "https";
                config.regionId = "central";
                com.aliyun.dingtalkworkflow_1_0.Client client = new com.aliyun.dingtalkworkflow_1_0.Client(config);
                com.aliyun.dingtalkworkflow_1_0.models.GetProcessInstanceHeaders getProcessInstanceHeaders = new com.aliyun.dingtalkworkflow_1_0.models.GetProcessInstanceHeaders();
                getProcessInstanceHeaders.xAcsDingtalkAccessToken = tokenNew;
                com.aliyun.dingtalkworkflow_1_0.models.GetProcessInstanceRequest getProcessInstanceRequest = new com.aliyun.dingtalkworkflow_1_0.models.GetProcessInstanceRequest()
                        .setProcessInstanceId(processInstanceId);
                GetProcessInstanceResponse processInstanceWithOptions = client.getProcessInstanceWithOptions(getProcessInstanceRequest, getProcessInstanceHeaders, new RuntimeOptions());
                GetProcessInstanceResponseBody.GetProcessInstanceResponseBodyResult result = processInstanceWithOptions.getBody().getResult();
                List<GetProcessInstanceResponseBody.GetProcessInstanceResponseBodyResultTasks> tasks = result.getTasks();
                String title = result.getTitle();
                for (GetProcessInstanceResponseBody.GetProcessInstanceResponseBodyResultTasks task : tasks) {
                    String result1 = task.getResult();

                    if (Objects.equals(result1, "NONE")) {
                        String userId = task.getUserId();
                        String key = "SEND_ROBOT_DING:" + processInstanceId + ":" + userId;

                        if (redisUtil.get(key) == null) {
                            //根据userID 使用robot发送 DING一下
                            //构造消息内容
                            String approvalUrl = "https://aflow.dingtalk.com/dingtalk/mobile/homepage.htm?corpid="
                                    + URLEncoder.encode(dingCorPid, StandardCharsets.UTF_8.toString())
                                    + "&dd_share=false&showmenu=false&dd_progress=false&back=native&procInstId="
                                    + URLEncoder.encode(processInstanceId, StandardCharsets.UTF_8.toString())
                                    + "&taskId=&swfrom=isv&dinghash=approval&dtaction=os&dd_from=corp#approval";

                            // 构建deeplink URL
                            String encodedUrl = URLEncoder.encode(approvalUrl, StandardCharsets.UTF_8.toString());
                            String content = userName + "提醒您查看: " + title + "dingtalk://dingtalkclient/page/link?url=" + encodedUrl + "&pc_slide=true";
                            dingTalkService.sendRobotDING(tokenNew, Arrays.asList(userId), content);
                            logger.info("当前审批人的钉钉userID为：---->>>> " + userId + " 发送消息体------>>>>" + content);
                            redisUtil.set(key, "sent", 60 * 60);
                            break;
                        }else {
                            logger.info("已在过去一小时内向用户 " + userId + " 发送过消息，跳过发送。");
                        }
                    }
                }
            } catch (TeaException err) {
                if (!com.aliyun.teautil.Common.empty(err.code) && !com.aliyun.teautil.Common.empty(err.message)) {
                    logger.warn("查询钉钉当前审批流程有误：---->>>>." + err.message);
                }

            } catch (Exception _err) {
                TeaException err = new TeaException(_err.getMessage(), _err);
                if (!com.aliyun.teautil.Common.empty(err.code) && !com.aliyun.teautil.Common.empty(err.message)) {
                    logger.warn("查询钉钉当前审批流程有误：---->>>>." + err.message);
                }

            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }

        return null;
    }


    public String generateHashedFileName(String originalName) {
        try {
            // 提取文件名后缀
            String extension = "";
            int dotIndex = originalName.lastIndexOf('.');
            if (dotIndex > 0 && dotIndex < originalName.length() - 1) {
                extension = originalName.substring(dotIndex);  // 包括点在内的后缀
            }

            // 生成哈希值
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hash = digest.digest(originalName.getBytes(StandardCharsets.UTF_8));
            StringBuilder hexString = new StringBuilder();
            for (byte b : hash) {
                hexString.append(String.format("%02x", b));
            }

            // 返回带有原始后缀的哈希文件名
            return hexString.toString() + extension;
        } catch (NoSuchAlgorithmException e) {
            logger.error("Error generating hash for file name", e);
            throw new JWIException("文件名哈希生成失败");
        }
    }

    private void initUser() {
        UserDTO userDto = new UserDTO();
        userDto.setTenantOid(tenantOid);
        userDto.setOid("sys_admin");
        userDto.setAccount("sys_admin");
        userDto.setSystemAdmin(Boolean.TRUE);
        userDto.setIpAddress("127.0.0.1");
        SessionHelper.addCurrentUser(userDto);
    }

}
