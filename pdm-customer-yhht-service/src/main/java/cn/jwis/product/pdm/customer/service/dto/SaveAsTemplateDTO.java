package cn.jwis.product.pdm.customer.service.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * @author: 汪江
 * @data: 2023-12-29 11:14
 * @description:
 **/
@Data
@EqualsAndHashCode
public class SaveAsTemplateDTO {
    @NotBlank(
            message = "容器oid不能为空"
    )
    @ApiModelProperty("容器oid")
    private String containerOid;
    @NotBlank(
            message = "容器模板名称不能为空"
    )
    @ApiModelProperty("容器模板名称")
    private String templateName;
    private String description;
    private Boolean isAddPer;
}
