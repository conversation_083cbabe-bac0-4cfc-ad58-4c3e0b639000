package cn.jwis.product.pdm.customer.service.dto;

import cn.jwis.platform.plm.foundation.classification.able.info.ClassificationInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * @author: 汪江
 * @data: 2024-01-02 10:00
 * @description:
 **/
@Data
@EqualsAndHashCode
public class FolderForExportDTO {
    @ApiModelProperty("名称")
    private String name;
    @ApiModelProperty("描述")
    private String description;
    @ApiModelProperty("分类信息")
    private ClassificationInfo classificationInfo;
    private List<FolderForExportDTO> children;
    @ApiModelProperty("modelIcon")
    private String modelIcon;
    private List<TeamRoleForExportDTO> teamRoles;
}
