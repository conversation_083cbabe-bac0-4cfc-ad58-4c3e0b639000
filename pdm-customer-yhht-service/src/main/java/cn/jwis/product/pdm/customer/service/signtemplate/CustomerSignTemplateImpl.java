package cn.jwis.product.pdm.customer.service.signtemplate;

import cn.hutool.json.JSONUtil;
import cn.jwis.framework.base.domain.able.info.ModelInfo;
import cn.jwis.framework.base.dto.UserDTO;
import cn.jwis.framework.base.web.session.SessionHelper;
import cn.jwis.framework.database.core.query.dynamic.Condition;
import cn.jwis.platform.plm.account.user.service.UserHelper;
import cn.jwis.platform.plm.dataengine.common.JWICommonService;
import cn.jwis.platform.plm.file.entity.FileMetadata;
import cn.jwis.platform.plm.foundation.attachment.entity.File;
import cn.jwis.platform.plm.foundation.versionrule.service.VersionRuleHelper;
import cn.jwis.platform.plm.signtemplate.SignTemplateFileRemote;
import cn.jwis.platform.plm.signtemplate.entity.*;
import cn.jwis.platform.plm.signtemplate.enums.DocOperateType;
import cn.jwis.platform.plm.signtemplate.repo.SignTemplateRepo;
import cn.jwis.platform.plm.signtemplate.service.CheckUtil;
import cn.jwis.platform.plm.signtemplate.service.SignTemplateImpl;
import cn.jwis.platform.plm.signtemplate.service.factory.SignDelegateFactory;
import cn.jwis.product.pdm.customer.entity.CustomerGenerateSignDocument;
import cn.jwis.product.pdm.customer.entity.DingTaskRecord;
import cn.jwis.product.pdm.customer.entity.GenerateSignDocumentRemote;
import cn.jwis.product.pdm.document.entity.DocumentIteration;
import cn.jwis.product.pdm.document.service.DocumentService;
import com.alibaba.excel.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.data.Includes;
import com.deepoove.poi.data.Pictures;
import feign.Response;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.RuntimeService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.poi.util.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.FileInputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2023/10/11 11:42
 * @Email <EMAIL>
 */
@Service
@Primary
@Transactional
@Slf4j
public class CustomerSignTemplateImpl extends SignTemplateImpl {

    Logger logger = LoggerFactory.getLogger(CustomerSignTemplateImpl.class);

    @Autowired
    SignTemplateRepo signTemplateRepo;

    @Autowired
    SignDelegateFactory signDelegateFactory;

    @Autowired
    RuntimeService runtimeService;

    @Autowired
    DocumentService documentService;

    @Autowired
    JWICommonService jwiCommonService;

    @Resource
    VersionRuleHelper versionRuleHelper;

    @Resource
    UserHelper userHelper;

    private static final String SPLIT_STR = " ";

    static SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyyMMdd");

    private static final String REPLACE_KEY = "include";

    @Autowired
    private SignTemplateFileRemote fileRemote;

    @Value("${sign-template.picture.default.size.width}")
    private int DEFAULT_PICTURE_WIDTH;
    @Value("${sign-template.picture.default.size.height}")
    private int DEFAULT_PICTURE_HEIGHT;
    @Value("${sign-template.out.file.suffix}")
    private String DOC_FILE_SUFFIX;
    @Value("${sign-template.out.file.key}")
    private String DOC_FILE_KEY;
    @Value("#{'${workflow.process.node.type:提交,提交,主设计审核,主管审核,经理审核}'.split(',')}")
    private List<String> PROCESS_NODE;


    /**
     * 生成ids直接归档文件
     * @param dtoParam
     * @param documentOidList
     * @return
     * @throws IOException
     */
    public Map<String, FileMetadata> toGenerateSignDocumentForIds(GenerateSignDocumentRemote dtoParam, List<String> documentOidList) throws IOException {
        logger.info("GenerateSignDocumentRemote----->>>>>{}", JSONUtil.toJsonStr(dtoParam));
        logger.info("documentOidList----->>>>>{}", JSONUtil.toJsonStr(documentOidList));
        Map<String, BeSignDocWithFileData> beSignDocWithFileDataMap = new HashMap<>();
        BeSignDocParam beSignDocParam = this.initBeSignDocParam(dtoParam);
        byte[] templateBytes;
        CustomerGenerateSignDocument dto = new CustomerGenerateSignDocument();
        dto.setOperateType(DocOperateType.APPEND);

        BeanUtils.copyProperties(dtoParam, dto);
        // 对模板内容进行填充
        Map<String, Object> dataMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(dto.getPictureCollection())) {
            // 按照图片清单 将模板中的占位符进行替换
            supplementPicKey(dto.getPictureCollection(), dataMap);
            try (ByteArrayInputStream byteArrayInputStream =
                         new ByteArrayInputStream(beSignDocParam.getSignTemplateFileBytes());
                 ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream()) {
                XWPFTemplate.compile(byteArrayInputStream).render(dataMap).write(byteArrayOutputStream);
                templateBytes = byteArrayOutputStream.toByteArray();
            }
            dataMap.clear();
            putPictureContent(dto.getPictureCollection(), dataMap);
        } else {
            templateBytes =  beSignDocParam.getSignTemplateFileBytes();
        }
        putTextContent(dto.getTextCollection(), dataMap);
        // value包含,时为会签进行数据转换
        putCounterSign(dto.getTextCollection(),dataMap);

        // 查询流程中的文档对象  fileOid---doc
        Map<String,DocumentIteration> fileToDocMap = new HashMap<>();
        boolean isChange = Boolean.FALSE;

        for (String  oid : documentOidList) {
            DocumentIteration detail = documentService.findByOid(oid);
            List<File> primaryFile = detail.getPrimaryFile();
            primaryFile.forEach(item->fileToDocMap.put(item.getOid(),detail));
        }

        // 逐个文件处理
        List<BeSignDocWithFileData> beSignDocWithFileDatas = beSignDocParam.getBeSignFileDataList();
        for(BeSignDocWithFileData data : beSignDocWithFileDatas){
            // 填充相关文档的信息
            long createDate = System.currentTimeMillis();
            log.info("dingTaskRecord.getCreateDate()：" + createDate);

            DocumentIteration doc = fileToDocMap.get(data.getOid());
            putDocInfo(doc, isChange, dataMap, createDate);
            log.info("toGenerateSignDocument.finalDataMap：" + JSON.toJSONString(dataMap));
            // 设置为追加内容数据
            Map<String, Object> replaceData = new HashMap<String, Object>() {
                {
                    put(REPLACE_KEY, Includes.ofBytes(templateBytes).setRenderModel(dataMap).create());
                }
            };
            beSignDocWithFileDataMap.putAll(
                    signDelegateFactory.createSignDelegate(dto.getOperateType())
                            .startToSign(Arrays.asList(data), replaceData, REPLACE_KEY));
        }

        Map<String, FileMetadata> uploadDocFiles = toUploadDocFiles(beSignDocWithFileDataMap);
        return uploadDocFiles;
    }

    @Override
    public Map<String, BeSignDocWithFileData> toGenerateSignDocument(GenerateSignDocumentDTO dtoParam, BeSignDocParam beSignDocParam) throws IOException {
        Map<String, BeSignDocWithFileData> beSignDocWithFileDataMap = new HashMap<>();
        byte[] templateBytes;
        CustomerGenerateSignDocument dto = new CustomerGenerateSignDocument();
        BeanUtils.copyProperties(dtoParam, dto);
        // 对模板内容进行填充
        Map<String, Object> dataMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(dto.getPictureCollection())) {
            // 按照图片清单 将模板中的占位符进行替换
            supplementPicKey(dto.getPictureCollection(), dataMap);
            try (ByteArrayInputStream byteArrayInputStream =
                         new ByteArrayInputStream(beSignDocParam.getSignTemplateFileBytes());
                 ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream()) {
                XWPFTemplate.compile(byteArrayInputStream).render(dataMap).write(byteArrayOutputStream);
                templateBytes = byteArrayOutputStream.toByteArray();
            }
            dataMap.clear();
            putPictureContent(dto.getPictureCollection(), dataMap);
        } else {
            templateBytes =  beSignDocParam.getSignTemplateFileBytes();
        }
        putTextContent(dto.getTextCollection(), dataMap);
        // value包含,时为会签进行数据转换
        putCounterSign(dto.getTextCollection(),dataMap);

        // 查询流程中的文档对象  fileOid---doc
        Map<String,DocumentIteration> fileToDocMap = new HashMap<>();
      /*  ProcessOrderHelper processOrderHelper = SpringContextUtil.getBean(ProcessOrderHelper.class);
        String processInstanceId = dto.getProcessInstanceId();
        ProcessOrder processOrder = processOrderHelper.findByProcessInstanceId(processInstanceId);*/
        //List<DocumentIteration> docs = documentService.findByOid(dto.getOperateDocList().stream().map(File::getOid).collect(Collectors.toList()));
        //TODO 查询钉钉的DingTaskRecord记录
        DingTaskRecord dingTaskRecord =
                jwiCommonService.dynamicQueryOne(DingTaskRecord.TYPE, Condition.where(
                        "dingProcessInstanceId").eq(dto.getProcessInstanceId()), DingTaskRecord.class);

        Boolean createPdfManually = dto.getDocumentIterationOidList() != null, isChange = createPdfManually ? Boolean.FALSE : dingTaskRecord.getIsChange();
        List<String> documentOidList = createPdfManually ? dto.getDocumentIterationOidList() : dingTaskRecord.getDocumentIterationOidList();

        if(isChange) {
            String owner = dingTaskRecord.getOwner();
            UserDTO byAccount = userHelper.findByAccount(owner);
            byAccount.setTenantOid(dingTaskRecord.getTenantOid());
            SessionHelper.addCurrentUser(byAccount);
        }
        log.info("当前dingTaskRecord:{}", JSONUtil.toJsonStr(dingTaskRecord));
        log.info("当前documentOidList:{}", JSONUtil.toJsonStr(documentOidList));
        for (String  oid : documentOidList) {
            DocumentIteration detail = documentService.findByOid(oid);
            List<File> primaryFile = detail.getPrimaryFile();
            log.info("当前文档detail:{}", JSONUtil.toJsonStr(detail));
            primaryFile.forEach(item->fileToDocMap.put(item.getOid(),detail));
        }
        log.info("当前fileToDocMap信息:{}", JSONUtil.toJsonStr(fileToDocMap));
        // 逐个文件处理
        List<BeSignDocWithFileData> beSignDocWithFileDatas = beSignDocParam.getBeSignFileDataList();
        for(BeSignDocWithFileData data : beSignDocWithFileDatas){
             // 填充相关文档的信息
            log.info("dingTaskRecord.getCreateDate()：" + dingTaskRecord.getCreateDate());

            DocumentIteration doc = fileToDocMap.get(data.getOid());
            log.info("当前doc信息:{}", JSONUtil.toJsonStr(doc));
            putDocInfo(doc, isChange, dataMap, dingTaskRecord.getCreateDate());
            log.info("toGenerateSignDocument.finalDataMap：" + JSON.toJSONString(dataMap));
            // 设置为追加内容数据
            Map<String, Object> replaceData = new HashMap<String, Object>() {
                {
                    put(REPLACE_KEY, Includes.ofBytes(templateBytes).setRenderModel(dataMap).create());
                }
            };
            beSignDocWithFileDataMap.putAll(
                    signDelegateFactory.createSignDelegate(dto.getOperateType())
                            .startToSign(Arrays.asList(data), replaceData, REPLACE_KEY));
        }
        return beSignDocWithFileDataMap;
    }

    private void putDocInfo(DocumentIteration doc,boolean isChange, Map<String, Object> dataMap,
                            long processCreateDate) {
        if(isChange) {
            ModelInfo modelInfo = new ModelInfo();
            modelInfo.setOid(doc.getOid());
            modelInfo.setType(doc.getType());
            modelInfo.setModelDefinition(doc.getModelDefinition());
            dataMap.put("version",versionRuleHelper.getNextVersion(modelInfo).get(0));
            log.info("Sign getNextVersion:{}",dataMap.get("version"));
        } else {
            dataMap.put("version",doc.getVersion());
        }

        dataMap.put("number",doc.getNumber());
        String name = dealDocName(doc);
        log.info("转换后的name---->>>{}", name);
        dataMap.put("name",name);
        UserDTO userDTO = userHelper.findByAccount(doc.getOwner());
        dataMap.put("owner",
                (ObjectUtils.isEmpty(userDTO) ? doc.getOwner() : userDTO.getName()) + " " + DATE_FORMAT.format(processCreateDate));
        JSONObject extensionContent = doc.getExtensionContent();
        if(extensionContent != null) {
            dataMap.put("cn_jwis_secrecy", extensionContent.getString("cn_jwis_secrecy"));
            dataMap.put("cn_jwis_yzjd", extensionContent.getString("cn_jwis_yzjd"));
        }
    }

    private static String dealDocName(DocumentIteration doc) {
        String name = doc.getName();
        // 将每15个字符添加一个换行符
        StringBuilder formattedName = new StringBuilder();

        int length = name.length();
        for (int i = 0; i < length; i++) {
            formattedName.append(name.charAt(i));
            // 每15个字符添加一个换行符，但不在字符串末尾添加
            if ((i + 1) % 15 == 0 && (i + 1) != length) {
                formattedName.append("\n");
            }
        }

        // 将格式化后的字符串重新赋值给name
        name = formattedName.toString();
        return name;
    }

    private SignTemplateContentNormalText initUserFromProcess(GenerateSignDocumentDTO dto, String keyName, String roleName) {
        // 重新获取责任人
        Collection<SignTemplateContentNormalText> currVariables = dto.getTextCollection();
        Map<String, Object> processVariables = runtimeService.getVariables(dto.getProcessInstanceId());
        processVariables.put(keyName,processVariables.get(roleName));
        for(SignTemplateContentNormalText v : currVariables){
//            if(processVariables.containsKey(v.getKeyName())){
//                v.setContent(processVariables.get(v.getKeyName()).toString());
//            }
            if(keyName.equals(v.getKeyName())){
                v.setContent(processVariables.get(v.getKeyName()).toString());
                return v;
            }
        }
        return null;
    }

    private void supplementPicKey(Collection<SignTemplateContentPicture> signTemplateContentPictures, Map<String,
            Object> dataMap) {
        signTemplateContentPictures.forEach(pic -> {
            dataMap.put(pic.getKeyName(), String.format(Locale.ENGLISH, "{{@%s}}", pic.getKeyName()));
        });
    }

    private void putTextContent(Collection<SignTemplateContentNormalText> textCollection,
                                Map<String, Object> dataMap) {
        textCollection.forEach(dto -> dataMap.put(dto.getKeyName(), dto.getContent()));
    }

    private void putPictureContent(Collection<SignTemplateContentPicture> textCollection,
                                   Map<String, Object> dataMap) {
        textCollection.forEach(dto -> {
            dataMap.put(dto.getKeyName(), Pictures.ofBytes(dto.getPictureBytes(),
                    dto.getPicType().transform(dto.getPicType().type())).size(dto.getWidth(), dto.getHeight()).create());
        });
    }

    private void putCounterSign(Collection<SignTemplateContentNormalText> testList, Map<String, Object> dataMap) {
        if (CollectionUtils.isEmpty(testList)) {
            return;
        }

        for (SignTemplateContentNormalText signTemplateContentNormalText : testList) {
            String content = signTemplateContentNormalText.getContent();
            if ("cs".equals(signTemplateContentNormalText.getKeyName())) {
                List<Map<String, String>> userMapList = new ArrayList<>();
                List<String> userList =
                        Arrays.stream(content.split(SPLIT_STR)).filter(user -> StringUtils.isNotBlank(user)).collect(Collectors.toList());
                userList.stream().filter(user -> StringUtils.isNotBlank(user)).forEach(user -> {
                    Map<String, String> map = new HashMap<>();
                    map.put("user_name", user);
                    userMapList.add(map);
                });
                dataMap.put(signTemplateContentNormalText.getKeyName(), userMapList);
            }
        }
    }

    private Map<String, FileMetadata> toUploadDocFiles(Map<String, BeSignDocWithFileData> filePathMap) throws IOException {
        try {
            DiskFileItemFactory factory = new DiskFileItemFactory();
            Map<String, FileMetadata> result = new HashMap();
            Iterator var4 = filePathMap.entrySet().iterator();

            while(var4.hasNext()) {
                Map.Entry<String, BeSignDocWithFileData> fileOidPathEntity = (Map.Entry)var4.next();
                this.logger.info(String.format(Locale.ENGLISH, "start upload file path %s", fileOidPathEntity.getValue()));
                java.io.File file = FileUtils.getFile(new String[]{((BeSignDocWithFileData)fileOidPathEntity.getValue()).getFilePath()});
                FileItem fileItem = factory.createItem(this.DOC_FILE_KEY, "text/plain", true, ((BeSignDocWithFileData)fileOidPathEntity.getValue()).getName());
                this.outPutToFileItem(file, fileItem);
                MultipartFile multiPartFile = new CommonsMultipartFile(fileItem);
                MultipartFile[] uploadFiles = new MultipartFile[]{multiPartFile};
                Map<String, FileMetadata> updateResult = this.fileRemote.upload(uploadFiles);
                CheckUtil.check(!updateResult.isEmpty(), String.format(Locale.ENGLISH, "文件上传无返回值 %s", fileOidPathEntity.getValue()));
                result.put(fileOidPathEntity.getKey(), updateResult.values().stream().findFirst().get());
            }

            HashMap var14 = (HashMap) result;
            return var14;
        } finally {
            filePathMap.values().forEach((docFileData) -> {
                this.logger.info(String.format(Locale.ENGLISH, "start to delete temp file %s", docFileData.getFilePath()));
                java.io.File file = FileUtils.getFile(new String[]{docFileData.getFilePath()});
                file.deleteOnExit();
            });
        }
    }
    private BeSignDocParam initBeSignDocParam(GenerateSignDocumentRemote dto) throws IOException {
        BeSignDocParam beSignDocParam = new BeSignDocParam();
        beSignDocParam.setSignTemplateFileBytes(this.transformResponseInput2Bytes(dto.getSignTemplateFileOid()));
        this.setPic(dto.getPictureCollection());
        CheckUtil.checkEmpty(dto.getOperateDocList(), "operateDocFileOidList can not be empty");
        beSignDocParam.setBeSignFileDataList(this.createBeSignFileData(dto.getOperateDocList()));
        return beSignDocParam;
    }
    private byte[] transformResponseInput2Bytes(String fileOid) throws IOException {
        Response response = this.fileRemote.downloadByFileOid(fileOid);
        CheckUtil.check(response.status() == 200, String.format(Locale.ENGLISH, "获取文件内容失败 fileOid:%s", fileOid));
        return IOUtils.toByteArray(response.body().asInputStream());
    }
    private void setPic(Collection<SignTemplateContentPicture> signTemplateContentPictureList) throws IOException {
        Iterator var2 = signTemplateContentPictureList.iterator();

        while(var2.hasNext()) {
            SignTemplateContentPicture dto = (SignTemplateContentPicture)var2.next();
            CheckUtil.checkEmpty(dto.getKeyName(), String.format(Locale.ENGLISH, "无对应key值 请检查参数 fileName:%s", dto.getFileName()));
            if (ObjectUtils.isEmpty(dto.getPictureBytes())) {
                dto.setPictureBytes(this.transformResponseInput2Bytes(dto.getFileOid()));
            }

            dto.setWidth(dto.getWidth() != 0 ? dto.getWidth() : this.DEFAULT_PICTURE_WIDTH);
            dto.setHeight(dto.getHeight() != 0 ? dto.getHeight() : this.DEFAULT_PICTURE_HEIGHT);
            this.logger.info("one sign picture param : {}", dto);
        }

    }
    private List<BeSignDocWithFileData> createBeSignFileData(List<File> beSignDocList) throws IOException {
        List<BeSignDocWithFileData> beSignFileDataList = new ArrayList();
        Iterator var3 = beSignDocList.iterator();

        while(var3.hasNext()) {
            File beSignDoc = (File)var3.next();
            CheckUtil.checkEmpty(beSignDoc.getOid(), "beSignDoc fileOid can not be empty");
            CheckUtil.checkEmpty(beSignDoc.getName(), "beSignDoc fileName can not be empty");
            BeSignDocWithFileData beSignFileData = new BeSignDocWithFileData();
            beSignFileData.setName(beSignDoc.getName());
            beSignFileData.setOid(beSignDoc.getOid());
            beSignFileData.setFileBytes(this.transformResponseInput2Bytes(beSignDoc.getOid()));
            beSignFileData.setFileSuffix(this.DOC_FILE_SUFFIX);
            beSignFileDataList.add(beSignFileData);
        }

        return beSignFileDataList;
    }
    private void outPutToFileItem(java.io.File localFile, FileItem fileItem) throws IOException {
        FileInputStream fis = new FileInputStream(localFile);
        Throwable var4 = null;

        try {
            IOUtils.copy(fis, fileItem.getOutputStream());
        } catch (Throwable var13) {
            var4 = var13;
            throw var13;
        } finally {
            if (fis != null) {
                if (var4 != null) {
                    try {
                        fis.close();
                    } catch (Throwable var12) {
                        var4.addSuppressed(var12);
                    }
                } else {
                    fis.close();
                }
            }

        }

    }
}
