package cn.jwis.product.pdm.customer.service.dto;

import cn.jwis.platform.plm.foundation.location.able.info.LocationInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2023/10/18 10:25
 * @Email <EMAIL>
 */
@Data
@EqualsAndHashCode
public class IDSDocCreateDTO {
    //文档编码
    String number;
    //文档名称
    String name;
    //文档来源
    String source;
    //文档密级
    String secrecy;
    //文档入库描述
    String msg;
    //位置信息
    LocationInfo locationInfo;

}
