package cn.jwis.product.pdm.customer.service.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

@Data
@EqualsAndHashCode
public class DataDictionaryExcelDTO {

    /**
     * 唯一标识符
     */
    @NotBlank(message = "code不能为空")
    @ExcelProperty("唯一标识符")
    private String code;

    /**
     * 显示名称
     */
    @NotBlank(message = "显示名称不能为空")
    @ExcelProperty("名称")
    private String displayName;

    /**
     * 是否启用
     */
    @ExcelProperty("启用")
    private Boolean enable = true;



    /**
     * 数据类型，group,dictionary,data
     */
    @ExcelProperty("数据类型")
    @Pattern(regexp = "(group|dictionary|data)", message = "数据类型只能是group,dictionary,data等值，分组，字典，数据")
    private String dataType;

    /**
     * 上级节点code
     */
    @ExcelProperty("上级节点唯一标识")
    private String parentCode;

    @ExcelProperty("上级节点名称")
    private String parentName;


}
