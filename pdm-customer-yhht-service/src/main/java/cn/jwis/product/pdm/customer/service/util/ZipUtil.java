package cn.jwis.product.pdm.customer.service.util;

import cn.jwis.framework.base.exception.JWIException;
import cn.jwis.framework.base.util.StringUtil;
import net.lingala.zip4j.core.ZipFile;
import net.lingala.zip4j.exception.ZipException;
import net.lingala.zip4j.model.FileHeader;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.tools.ant.Project;
import org.apache.tools.ant.taskdefs.Expand;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.nio.charset.Charset;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

public class ZipUtil {
    private static final Logger log = LoggerFactory.getLogger(ZipUtil.class);
    private static int BUFFER = 1024;
    private static final String tmpDir = "tmp";
    public static final String excelMapName = "excelfile";
    public static final String templateDir = "templateDir";

    public ZipUtil() {
    }

    public static String zipBaseUrl() {
        String url = System.getProperty("user.dir") + File.separator + "tmp" + File.separator;
        File dir = new File(url);
        if (!dir.exists()) {
            dir.mkdirs();
        }

        return url;
    }

    public static boolean isZip(MultipartFile file) {
        return file != null && file.getOriginalFilename() != null && file.getOriginalFilename().endsWith(".zip") ? Boolean.TRUE : Boolean.FALSE;
    }

    public static boolean isExcel(MultipartFile file) {
        return file == null || file.getOriginalFilename() == null || !file.getOriginalFilename().endsWith(".xlsx") && !file.getOriginalFilename().endsWith(".xls") ? Boolean.FALSE : Boolean.TRUE;
    }

    public static String templateUrl() {
        String url = System.getProperty("user.dir") + File.separator + "tmp" + File.separator + "templateDir" + File.separator;
        File file = new File(url);
        if (!file.exists()) {
            file.mkdirs();
        }

        return url;
    }

    public static Optional<File> getTemplateFile(String type) {
        File file = new File(templateUrl() + type + ".zip");
        if (file.exists()) {
            return Optional.of(file);
        } else {
            File dir = new File(templateUrl() + type);
            dir.mkdirs();
            return Optional.empty();
        }
    }

    public static String getTemplateFileUrl(String type) {
        return templateUrl() + type + File.separator;
    }

    public static void sendFileToWeb(HttpServletResponse response, File file) {
        response.setContentType("application/octet-stream");
        response.setHeader("Content-Disposition", "attachment;filename=文档导出信息.zip");

        try {
            OutputStream out = response.getOutputStream();
            Throwable var3 = null;

            try {
                FileInputStream in = new FileInputStream(file);
                Throwable var5 = null;

                try {
                    byte[] by = new byte[1024];

                    int n;
                    while((n = in.read(by)) != -1) {
                        out.write(by, 0, n);
                    }
                } catch (Throwable var31) {
                    var5 = var31;
                    throw var31;
                } finally {
                    if (in != null) {
                        if (var5 != null) {
                            try {
                                in.close();
                            } catch (Throwable var30) {
                                var5.addSuppressed(var30);
                            }
                        } else {
                            in.close();
                        }
                    }

                }
            } catch (Throwable var33) {
                var3 = var33;
                throw var33;
            } finally {
                if (out != null) {
                    if (var3 != null) {
                        try {
                            out.close();
                        } catch (Throwable var29) {
                            var3.addSuppressed(var29);
                        }
                    } else {
                        out.close();
                    }
                }

            }

        } catch (IOException var35) {
            throw new RuntimeException(var35);
        }
    }

    public static File downloadZipFile(MultipartFile multipartFile, String name) {
        InputStream inputStream = null;
        String originalFileName = multipartFile.getOriginalFilename();
        String zipName = name + originalFileName.substring(originalFileName.lastIndexOf("."));
        File zipFile = new File(zipBaseUrl() + zipName);

        try {
            FileOutputStream outputStream = new FileOutputStream(zipFile);
            Throwable var7 = null;

            try {
                zipFile.createNewFile();
                inputStream = multipartFile.getInputStream();
                byte[] by = new byte[1024];

                int n;
                while((n = inputStream.read(by)) > 0) {
                    outputStream.write(by, 0, n);
                }

                File var10 = zipFile;
                return var10;
            } catch (Throwable var33) {
                var7 = var33;
                throw var33;
            } finally {
                if (outputStream != null) {
                    if (var7 != null) {
                        try {
                            outputStream.close();
                        } catch (Throwable var31) {
                            var7.addSuppressed(var31);
                        }
                    } else {
                        outputStream.close();
                    }
                }

            }
        } catch (IOException var35) {
            log.error("文件读取异常：{}", var35.getMessage());
            throw new RuntimeException(var35);
        } finally {
            try {
                if (inputStream != null) {
                    inputStream.close();
                }
            } catch (IOException var32) {
                log.error("关闭流异常：{}", var32.getMessage());
                throw new RuntimeException(var32);
            }

        }
    }

    public static File downloadUnzip(MultipartFile file) {
        String fileName = RandomStringUtils.randomAlphanumeric(5);
        File zipFile = downloadZipFile(file, fileName);
        File dir = decompressZip(zipFile, fileName, (HttpServletRequest)null);
        return dir;
    }

    public static File downloadUnzip(MultipartFile file, HttpServletRequest request) {
        String fileName = RandomStringUtils.randomAlphanumeric(5);
        File zipFile = downloadZipFile(file, fileName);
        File dir = decompressZip(zipFile, fileName, request);
        return dir;
    }

    public static Map<String, File> getFileMap(File dir) {
        List<File> allFile = new ArrayList();
        getAllFile(allFile, dir);
        Map<String, File> fileMap = (Map)allFile.stream().filter((item) -> {
            return !item.getName().startsWith(".~");
        }).collect(Collectors.toMap(File::getName, Function.identity(), (item1, item2) -> {
            return item1;
        }));
        File excelFile = getExcelFile(dir);
        if (excelFile != null) {
            fileMap.put("excelfile", excelFile);
            return fileMap;
        } else {
            throw new JWIException("", new Object[]{"未找到excel,请使用标准模板"});
        }
    }

    public static Map<String, File> getFileMapPath(File dir) {
        Map<String, File> fileMap = new HashMap();
        getAllFilePath(fileMap, dir, "");
        List<String> excelFile = (List)fileMap.keySet().stream().filter((fileName) -> {
            return fileName.endsWith(".xlsx") || fileName.endsWith(".xls");
        }).collect(Collectors.toList());
        if (excelFile.size() == 0) {
            throw new JWIException("压缩包内未找到Excel文件，数据Excel应放在压缩包顶层路径");
        } else {
            Optional<String> minExcel = excelFile.stream().min(Comparator.comparingInt((o) -> {
                return o.split("/").length;
            }));
            String dataExcelStr = (String)minExcel.get();
            int excelLength = dataExcelStr.split("/").length;
            List<String> minExcels = (List)excelFile.stream().filter((item) -> {
                return Objects.equals(excelLength, item.split("/").length);
            }).collect(Collectors.toList());
            if (minExcels.size() > 1) {
                minExcels = (List)minExcels.stream().map((item) -> {
                    return item.replaceFirst(dir.getName() + "/", "");
                }).collect(Collectors.toList());
                throw new JWIException("顶层路径存在多个Excel，请保留一个数据Excel：" + String.join(",", minExcels));
            } else {
                File dataExcelFile = (File)fileMap.get(minExcels.get(0));
                String preHead = dataExcelStr.substring(0, dataExcelStr.lastIndexOf("/") + 1);
                Map<String, File> res = new HashMap();
                fileMap.forEach((k, v) -> {
                    res.put(k.replaceFirst(preHead, ""), v);
                });
                res.put("excelfile", dataExcelFile);
                return res;
            }
        }
    }

    public static void getAllFilePath(Map<String, File> map, File dir, String path) {
        path = path + dir.getName() + "/";
        File[] var3 = (File[])Objects.requireNonNull(dir.listFiles());
        int var4 = var3.length;

        for(int var5 = 0; var5 < var4; ++var5) {
            File file = var3[var5];
            if (file.isFile() && !file.getName().startsWith(".~")) {
                map.put(path + file.getName(), file);
            } else if (file.isDirectory() && !Objects.equals(file.getName(), "__MACOSX")) {
                getAllFilePath(map, file, path);
            }
        }

    }

    public static void getAllFile(List<File> files, File dir) {
        File[] var2 = (File[])Objects.requireNonNull(dir.listFiles());
        int var3 = var2.length;

        for(int var4 = 0; var4 < var3; ++var4) {
            File file = var2[var4];
            if (file.isFile()) {
                files.add(file);
            } else if (file.isDirectory() && !Objects.equals(file.getName(), "__MACOSX")) {
                getAllFile(files, file);
            }
        }

    }

    public static void deleteAllFile(File dir) {
        if (dir.isDirectory()) {
            File[] var1 = (File[])Objects.requireNonNull(dir.listFiles());
            int var2 = var1.length;

            for(int var3 = 0; var3 < var2; ++var3) {
                File file = var1[var3];
                deleteAllFile(file);
            }

            dir.delete();
        } else {
            dir.delete();
        }

    }

    public static File getExcelFile(File dir) {
        if (dir.isFile()) {
            if (!dir.getName().startsWith(".~") && (dir.getName().lastIndexOf(".xlsx") != -1 || dir.getName().lastIndexOf(".xls") != -1)) {
                return dir;
            }
        } else if (dir.isDirectory() && !Objects.equals(dir.getName(), "__MACOSX")) {
            File[] var1 = (File[])Objects.requireNonNull(dir.listFiles());
            int var2 = var1.length;

            for(int var3 = 0; var3 < var2; ++var3) {
                File file = var1[var3];
                File file1 = getExcelFile(file);
                if (file1 != null) {
                    return file1;
                }
            }
        }

        return null;
    }

    public static File decompressZip(File zipFile, String dirName, HttpServletRequest request) {
        File dir = new File(zipBaseUrl() + dirName);
        unCompress(zipFile.getPath(), dir.getPath(), request);
        zipFile.delete();
        return dir;
    }

    public static void unCompress(String zipFilePaht, String targetDir, HttpServletRequest request) {
        String fileType = zipFilePaht.substring(zipFilePaht.lastIndexOf("."));
        byte var5 = -1;
        switch(fileType.hashCode()) {
        case 46033:
            if (fileType.equals(".7z")) {
                var5 = 1;
            }
            break;
        case 1490995:
            if (fileType.equals(".zip")) {
                var5 = 0;
            }
        }

        switch(var5) {
        case 0:
            unZip(zipFilePaht, targetDir, request);
        case 1:
            return;
        default:
            throw new JWIException("不支持压缩格式" + fileType);
        }
    }

    public static String getEncoding(String path) {
        String encoding = "GBK";

        try {
            ZipFile zipFile = new ZipFile(path);
            zipFile.setFileNameCharset(encoding);
            List<FileHeader> list = zipFile.getFileHeaders();

            for(int i = 0; i < list.size(); ++i) {
                FileHeader fileHeader = (FileHeader)list.get(i);
                String fileName = fileHeader.getFileName();
                if (fileName.contains("__MACOSX")) {
                    encoding = "UTF-8";
                    break;
                }
            }

            return encoding;
        } catch (ZipException var7) {
            throw new RuntimeException(var7);
        }
    }

    public static String getEncoding(HttpServletRequest request) {
        String userAgent = request.getHeader("User-Agent");
        if (StringUtil.isNotBlank(userAgent)) {
            if (userAgent.contains("Mac")) {
                return "UTF-8";
            }

            if (userAgent.contains("Windows")) {
                return "GBK";
            }
        }

        return "GBK";
    }

    public static void unZip(String zipFilePart, String targetDir, HttpServletRequest request) {
        File srcZipFile = new File(zipFilePart);
        if (!srcZipFile.exists()) {
            throw new RuntimeException("压缩文件未创建");
        } else {
            Project project = new Project();
            Expand expand = new Expand();
            expand.setProject(project);
            expand.setSrc(srcZipFile);
            expand.setOverwrite(true);
            expand.setEncoding(request == null ? getEncoding(zipFilePart) : getEncoding(request));
            File file = new File(targetDir);
            if (!file.exists()) {
                file.mkdirs();
            }

            expand.setDest(file);
            expand.execute();
        }
    }

    public static void zipFile(File srcDir, File targetDir) {
        try {
            zipFile(srcDir, new BufferedOutputStream(new FileOutputStream(targetDir)), true);
        } catch (FileNotFoundException var3) {
            var3.printStackTrace();
        }

    }

    public static void zipFile(File srcDir, OutputStream out, boolean maintainStructure) {
        if (!srcDir.exists()) {
            throw new RuntimeException("未找到文件");
        } else {
            try {
                ZipOutputStream zos = new ZipOutputStream(out, Charset.forName("GBK"));
                Throwable var4 = null;

                try {
                    compress(srcDir, zos, srcDir.getName(), maintainStructure);
                } catch (Throwable var14) {
                    var4 = var14;
                    throw var14;
                } finally {
                    if (zos != null) {
                        if (var4 != null) {
                            try {
                                zos.close();
                            } catch (Throwable var13) {
                                var4.addSuppressed(var13);
                            }
                        } else {
                            zos.close();
                        }
                    }

                }

            } catch (Exception var16) {
                throw new RuntimeException(var16);
            }
        }
    }

    private static void compress(File srcDir, ZipOutputStream zos, String entryName, boolean maintainStructure) {
        int len;
        if (srcDir.isFile()) {
            try {
                FileInputStream in = new FileInputStream(srcDir);
                Throwable var5 = null;

                try {
                    zos.putNextEntry(new ZipEntry(entryName));
                    byte[] bytes = new byte[1024];

                    while((len = in.read(bytes)) != -1) {
                        zos.write(bytes, 0, len);
                    }

                    zos.closeEntry();
                } catch (Throwable var18) {
                    var5 = var18;
                    throw var18;
                } finally {
                    if (in != null) {
                        if (var5 != null) {
                            try {
                                in.close();
                            } catch (Throwable var16) {
                                var5.addSuppressed(var16);
                            }
                        } else {
                            in.close();
                        }
                    }

                }
            } catch (IOException var20) {
                throw new RuntimeException(var20);
            }
        }

        if (srcDir.isDirectory()) {
            File[] listFiles = srcDir.listFiles();
            if (ArrayUtils.isEmpty(listFiles)) {
                try {
                    zos.putNextEntry(new ZipEntry(entryName + "/"));
                    zos.closeEntry();
                } catch (IOException var17) {
                    throw new RuntimeException(var17);
                }
            }

            File[] var22 = listFiles;
            len = listFiles.length;

            for(int var23 = 0; var23 < len; ++var23) {
                File srcFile = var22[var23];
                if (maintainStructure) {
                    compress(srcFile, zos, entryName + File.separator + srcFile.getName(), maintainStructure);
                } else {
                    compress(srcFile, zos, srcFile.getName(), maintainStructure);
                }
            }
        }

    }
}
