package cn.jwis.product.pdm.customer.service.listener;

import cn.jwis.framework.base.annotation.Description;
import cn.jwis.framework.base.util.CollectionUtil;
import cn.jwis.framework.base.util.MapUtil;
import cn.jwis.framework.base.util.SpringContextUtil;
import cn.jwis.platform.plm.foundation.common.response.InstanceEntity;
import cn.jwis.platform.plm.foundation.common.service.CommonAbilityHelper;
import cn.jwis.platform.plm.workflow.engine.order.entity.ProcessOrder;
import cn.jwis.platform.plm.workflow.engine.service.interf.ProcessOrderHelper;
import cn.jwis.product.pdm.cad.mcad.entity.MCADIteration;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.delegate.DelegateTask;
import org.activiti.engine.delegate.TaskListener;
import org.springframework.stereotype.Component;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Component
@Description("回写MCAD关联的partNumber,和审核信息")
public class McadCallBackDataListener implements TaskListener {

    @Override
    public void notify(DelegateTask delegateTask) {
        log.info("回写MCAD关联的partNumber,和审核信息");
        JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(delegateTask));
        ProcessOrder processOrder = getProcessOrder(jsonObject);
        List<InstanceEntity> entities = getInstanceList(processOrder);
        List<String> mcadOids = entities.stream().filter(item -> Objects.equals(item.getType(), MCADIteration.TYPE))
                .map(InstanceEntity::getOid).collect(Collectors.toList());

        if(CollectionUtil.isNotEmpty(mcadOids)){
            CommonAbilityHelper commonAbilityHelper = SpringContextUtil.getBean(CommonAbilityHelper.class);
            List<MCADIteration> mcadIterations = commonAbilityHelper.findDetailEntity(mcadOids, MCADIteration.TYPE).stream().map(item -> (MCADIteration)item).collect(Collectors.toList());
            for (MCADIteration mcadIteration : mcadIterations) {
                mcadIteration.setProcessInstanceId(processOrder.getProcessInstanceId());
                mcadIteration.setProcessCreateTime(processOrder.getCreateDate());
            }
            commonAbilityHelper.doUpdate(mcadIterations);
        }
    }

    private ProcessOrder getProcessOrder(JSONObject delegateTask) {
        ProcessOrder processOrder = null;
        JSONObject variableInstances = delegateTask.getJSONObject("variableInstances");
        ProcessOrderHelper processOrderHelper = (ProcessOrderHelper) SpringContextUtil.getBean("processOrderHelperImpl");
        if (MapUtil.isEmpty(variableInstances)){
            String processInstanceId = delegateTask.getString("processInstanceId");
            processOrder = processOrderHelper.findByProcessInstanceId(processInstanceId);
        }else {
            JSONObject processOrderVar = variableInstances.getJSONObject("processOrderOid");
            if (MapUtil.isEmpty(processOrderVar)){
                String processInstanceId = delegateTask.getString("processInstanceId");
                processOrder = processOrderHelper.findByProcessInstanceId(processInstanceId);
            }else {
                String processOrderOid = processOrderVar.getString("value");
                processOrder = processOrderHelper.findByOid(processOrderOid);
            }
        }
        return processOrder;
    }



    private List<InstanceEntity> getInstanceList(ProcessOrder processOrder) {
        ProcessOrderHelper processOrderHelper = SpringContextUtil.getBean(ProcessOrderHelper.class);
        return processOrderHelper.findBizObject(processOrder.getOid());
    }
}
