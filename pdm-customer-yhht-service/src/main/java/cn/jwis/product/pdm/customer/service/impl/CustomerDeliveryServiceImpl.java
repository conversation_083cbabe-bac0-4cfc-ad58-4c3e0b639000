//package cn.jwis.product.pdm.customer.service.impl;
//
//import cn.jwis.platform.plm.foundation.pipeline.manager.AbleWorkCellPipelineManager;
//import cn.jwis.product.pdm.customer.repo.DeliveryServiceRepo;
//import cn.jwis.product.pdm.customer.service.interf.DeliveryService;
//import cn.jwis.product.pdm.delivery.entity.Delivery;
//import cn.jwis.product.pdm.delivery.entity.DeliveryTreeNode;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.context.annotation.Primary;
//import org.springframework.stereotype.Service;
//import org.springframework.transaction.annotation.Transactional;
//
//import java.util.List;
//
//@Service
//@Transactional
//@Primary
//public class CustomerDeliveryServiceImpl implements DeliveryService {
//
//    {
//        AbleWorkCellPipelineManager.registerActuatorService(Delivery.TYPE, this);
//    }
//    @Autowired
//    DeliveryServiceRepo deliveryServiceRepo;
//
//    @Override
//    public Long delete(String oid,String level) {
//        return deliveryServiceRepo.delete(oid, level);
//    }
//
//    @Override
//    public Long delete(List<String> oids) {
//        return deliveryServiceRepo.deleteByOid(oids, null);
//    }
//
//    @Override
//    public List<DeliveryTreeNode> findByProductCatalogOid(String productCatalogOid) {
//        return deliveryServiceRepo.findByProductCatalogOid(productCatalogOid);
//    }
//
//    @Override
//    public List<DeliveryTreeNode> findStructureTree(String catalogType, String catalogOid, Integer level, String searchKey) {
//        return deliveryServiceRepo.findStructureTree(catalogType,catalogOid,level,searchKey, null, true);
//    }
//
//    @Override
//    public List<DeliveryTreeNode> findNodesByCatalogOid(String catalogType, String catalogOid, Integer level) {
//        return deliveryServiceRepo.findNodesByCatalogOid(catalogType,catalogOid,level);
//    }
//
//    @Override
//    public List<DeliveryTreeNode> findChildrenByDeliveryOid(String oid) {
//        return deliveryServiceRepo.findChildrenByDeliveryOid(oid);
//    }
//
//    @Override
//    public void createContainChild(String parentOid, String oid) {
//        deliveryServiceRepo.createContainChild(parentOid,oid);
//    }
//
//    @Override
//    public Delivery findByOid(String oid) {
//        return deliveryServiceRepo.findByOid(oid);
//    }
//
//    @Override
//    public Delivery findByClsOidAndName(String catalogOid, String oid, String clsOid, String name, String modelDefinition) {
//        return deliveryServiceRepo.findByClsOidAndName(catalogOid,oid,clsOid,name,modelDefinition);
//    }
//
//    @Override
//    public Delivery findByClsOid(String catalogOid, String clsOid) {
//        return deliveryServiceRepo.findByClsOid( catalogOid, clsOid);
//    }
//
//    @Override
//    public List<Delivery> findLeafNodes(String containerOid, String searchKey) {
//        return deliveryServiceRepo.findLeafNodes(containerOid, searchKey);
//    }
//
//    @Override
//    public List<DeliveryTreeNode> findAllChildrenObjByDeliveryOid(String oid, String level) {
//        return deliveryServiceRepo.findAllChildrenObjByDeliveryOid(oid, level);
//    }
//
//    @Override
//    public Delivery findByParentOidAndName(String name, String parentOid, Boolean isRoot) {
//        return deliveryServiceRepo.findByParentOidAndName(name,parentOid,isRoot);
//    }
//}