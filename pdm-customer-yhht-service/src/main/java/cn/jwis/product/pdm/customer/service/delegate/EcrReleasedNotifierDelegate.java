package cn.jwis.product.pdm.customer.service.delegate;

import cn.jwis.framework.base.util.Assert;
import cn.jwis.framework.base.util.MapUtil;
import cn.jwis.framework.base.util.SpringContextUtil;
import cn.jwis.framework.database.core.entity.SubFilter;
import cn.jwis.platform.plm.dataengine.common.JWICommonService;
import cn.jwis.platform.plm.foundation.common.response.InstanceEntity;
import cn.jwis.platform.plm.foundation.common.service.CommonAbilityService;
import cn.jwis.platform.plm.foundation.relationship.Impact;
import cn.jwis.platform.plm.workflow.engine.order.entity.ProcessOrder;
import cn.jwis.platform.plm.workflow.engine.service.interf.ProcessOrderHelper;
import cn.jwis.product.pdm.change.entity.ECR;
import cn.jwis.product.pdm.customer.service.impl.DingTalkServiceImpl;
import cn.jwis.product.pdm.customer.service.interf.CustomerChangeHelper;
import cn.jwis.product.pdm.customer.service.interf.DingTalkService;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.RuntimeService;
import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.JavaDelegate;
import org.activiti.engine.impl.persistence.entity.VariableInstance;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

/**
 * <AUTHOR> lhm
 * @Date ： Created in 2024/1/5 9:36
 * @Description :ECR流程结束：发送钉钉消息 通知流程发起人
 */
@Slf4j
public class EcrReleasedNotifierDelegate implements JavaDelegate {

    private static final ExecutorService executorService = Executors.newFixedThreadPool(1);

    @Override
    public void execute(DelegateExecution execution) {
        String processInstanceId = execution.getProcessInstanceId();
        log.info("EcrReleasedNotifierDelegate execute procId={}", processInstanceId);
        RuntimeService runtimeService = SpringContextUtil.getBean(RuntimeService.class);

        try {
            // 获取ECR流程发起人
            Object notifiers = runtimeService.getVariable(execution.getId(),"owner");
            Assert.notNull(notifiers,"流程发起人变量owner不存在");
            List<String> accounts = new ArrayList<>();
            if(!ObjectUtils.isEmpty(notifiers)){
                if(notifiers instanceof Collection){
                    accounts.addAll((Collection<? extends String>) notifiers);
                }else{
                    accounts.add(notifiers.toString());
                }
            }
            log.info("EcrReleasedNotifierDelegate origin accounts={}", JSON.toJSONString(accounts));
            ProcessOrder processOrder = getProcessOrder(execution);
            ProcessOrderHelper processOrderHelper = (ProcessOrderHelper) SpringContextUtil.getBean("processOrderHelperImpl");
            List<InstanceEntity> instanceEntityList = processOrderHelper.findBizObject(processOrder.getOid());
            //获取ECR变更对象
            Assert.notNull(instanceEntityList,"ECR变更单对象为空");
            InstanceEntity ecr = instanceEntityList.get(0);
            ((CommonAbilityService) SpringContextUtil.getBean("commonAbilityServiceImpl")).findByOid(ecr.getOid(), ECR.TYPE);
            List<InstanceEntity> changeInfo = this.getChangeInfo(ecr);
            Assert.notNull(changeInfo,"ECR变更单关联的变更对象为空");

            String subject = changeInfo.stream().map(t -> t.getName()).collect(Collectors.joining(","));
            subject="请对【"+subject+"】对象进行修改";;
            // 发布通知
            DingTalkService dingTalkService = (DingTalkServiceImpl) SpringContextUtil.getBean("dingTalkServiceImpl");
            dingTalkService.createNoticeTask(accounts,processOrder,changeInfo,subject);

            CustomerChangeHelper customerChangeHelper = (CustomerChangeHelper) SpringContextUtil.getBean("customerChangeHelperImpl");
            executorService.submit(() -> {
                try {
                    customerChangeHelper.closeIssue(ecr.getOid());
                }catch (Exception e){
                    log.error(e.getMessage(), e);
                }
            });

        } catch (Exception e) {
            log.error("EcrReleasedNotifierDelegate execute error==>", e);
        }
    }

    private ProcessOrder getProcessOrder(DelegateExecution delegateTask) {
        ProcessOrder processOrder = null;
        Map<String, VariableInstance> variableInstances = delegateTask.getVariableInstances();
        ProcessOrderHelper processOrderHelper = (ProcessOrderHelper) SpringContextUtil.getBean("processOrderHelperImpl");
        if (MapUtil.isEmpty(variableInstances)){
            String processInstanceId = delegateTask.getProcessInstanceId();
            processOrder = processOrderHelper.findByProcessInstanceId(processInstanceId);
        }else {
            VariableInstance processOrderOid = variableInstances.get("processOrderOid");
            if (processOrderOid == null){
                String processInstanceId = delegateTask.getProcessInstanceId();
                processOrder = processOrderHelper.findByProcessInstanceId(processInstanceId);
            }else {
                String processOrderOidStr = processOrderOid.getValue().toString();
                processOrder = processOrderHelper.findByOid(processOrderOidStr);
            }
        }
        return processOrder;
    }

    private List<InstanceEntity> getChangeInfo(InstanceEntity ecr){

        JWICommonService jwiCommonService = SpringContextUtil.getBean(JWICommonService.class);
        SubFilter subfilter = new SubFilter();
        subfilter.setFromType(ecr.getType());
        subfilter.setFromOid(ecr.getOid());
        subfilter.setType(Impact.TYPE);


        List<InstanceEntity> instanceEntities = jwiCommonService.dynamicQueryByFrom(subfilter, InstanceEntity.class);
        return instanceEntities;
    }
}
