package cn.jwis.product.pdm.customer.service.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR> yefei
 * @Date ： 2024/7/29
 * @Description :
 */
@Data
public class CustomerPartLinkDTO {

    @ExcelIgnore
    private int rowIndex;

    @NotBlank(message = "部件编码不能为空")
    @ExcelProperty(index = 0)
    private String partNumber;

    /**
     * 显示名称
     */
    @NotBlank(message = "CAD编码不能为空")
    @ExcelProperty(index = 1)
    private String cadNumber;

    /**
     * 是否启用
     */
    @NotBlank(message = "CAD类型不能为空")
    @ExcelProperty(index = 2)
    private String cadType;


}
