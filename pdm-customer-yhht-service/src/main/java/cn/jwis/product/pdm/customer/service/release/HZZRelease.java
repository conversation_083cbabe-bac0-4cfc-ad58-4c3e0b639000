package cn.jwis.product.pdm.customer.service.release;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONUtil;
import cn.jwis.framework.base.bean.util.BeanUtil;
import cn.jwis.framework.base.exception.JWIException;
import cn.jwis.framework.base.util.CollectionUtil;
import cn.jwis.framework.database.core.query.dynamic.Condition;
import cn.jwis.platform.plm.account.repo.user.response.UserWithPositionAndOrg;
import cn.jwis.platform.plm.account.user.service.UserHelper;
import cn.jwis.platform.plm.account.user.service.UserService;
import cn.jwis.platform.plm.dataengine.common.JWICommonService;
import cn.jwis.platform.plm.sysconfig.preferences.PreferencesService;
import cn.jwis.platform.plm.workflow.engine.order.entity.ProcessOrder;
import cn.jwis.product.pdm.customer.entity.IntegrationRecord;
import cn.jwis.product.pdm.customer.repo.neo4j.util.DingUtils;
import cn.jwis.product.pdm.customer.service.dto.ChildBOMDTO;
import cn.jwis.product.pdm.customer.service.dto.HZZReleaseDataDTO;
import cn.jwis.product.pdm.customer.service.impl.DingTalkServiceImpl;
import cn.jwis.product.pdm.partbom.part.dto.SimplePartBOMNode;
import cn.jwis.product.pdm.partbom.part.entity.PartIteration;
import cn.jwis.product.pdm.partbom.part.service.PartHelper;
import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Data
@EqualsAndHashCode
@Component
@Transactional
public class HZZRelease extends EntityRelease{


    private static final Logger logger = LogManager.getLogger(HZZRelease.class);
    // 提取常量
    private static final String CONTROLLER = "MyApiController";
    private static final String APP_CODE = "D000119Product";


    @Resource
    JWICommonService jwiCommonService;
    @Resource
    PartHelper partHelper;
    @Resource
    PartEntityRelease partEntityRelease;
    @Resource
    UserHelper userHelper;
    @Resource
    UserService userService;
    @Resource
    PreferencesService preferencesService;
    @Resource
    DingUtils dingUtils;
    @Resource
    DingTalkServiceImpl dingTalkService;

    private static final String API_URL = "https://www.h3yun.com/OpenApi/Invoke";
    private static final String ENGINE_CODE = "ahny4d6ehfq9gfb1ugie25647";
    private static final String ENGINE_SECRET = "velDleM0RBQqP0R5Mps/7bdgiHYei8tEyEFEvxFDMstgMUzDi1kscg==";

    private static final String IMPORT_BOM = "ImportBOM";
    private static final String UPDATE_BOM = "UpdateBOM";


    @Override
    public IntegrationRecord release(ProcessOrder processOrder, String entityOid, String recordOid, int isFirst) {
        PartIteration dbPart = partHelper.findByOid(entityOid);
        if(dbPart == null){
            logger.info("没有查询到物料信息");
            throw new JWIException("没有查询到物料信息");
        }
        if(!dbPart.isLatest()){
            PartIteration dbPartLatest = partHelper.findLatestByNumber(dbPart.getNumber(), dbPart.getViewOid());
            if(dbPartLatest != null)
                dbPart = partHelper.findByOid(dbPartLatest.getOid());
        }
        logger.info("HZZRelease.Release dbPart------->>>>>{}", JSONUtil.toJsonStr(dbPart));
        // 1、检查需要发送的物料中的 所有者部门是否为 载荷毫米波部门
        UserWithPositionAndOrg user = userHelper.findDetailByAccount(dbPart.getUpdateBy());
        List<String> orgList = userService.getOrgTreeNameByAccount(user.getAccount());
        logger.info("当前part的更新人信息：{}", JSONUtil.toJsonStr(user));
        StringBuffer orgTree = new StringBuffer();
        for(int i = orgList.size()-2; i > -1 ; i--){
            orgTree.append(orgList.get(i));
            if(i > 0){
                orgTree.append("-");
            }
        }
        logger.info("当前part的更新人部门信息：{}", JSONUtil.toJsonStr(orgTree));
        String updatorNameAndDepartment = orgTree.toString();
        if (isFirst != 1 && isFirst != 2 && isFirst != 3) {
            if (!updatorNameAndDepartment.contains("载荷毫米波部") ) {
                logger.warn("当前part的更新人部门：{},不是 载荷毫米波部", orgTree);
                throw new JWIException("当前part的更新人部门：" + orgTree + ",不是 载荷毫米波部");
            }
        }
        // 1.1 查询钉钉userId
        String ddUserId = dingUtils.getUserIdByEmail(user.getEmail());


        //2、检查物料是否是 委外加工件和制造件
        String source = dbPart.getSource();
        if (source == null || (!"委外加工件".equals(source) && !"制造件".equals(source))) {
            logger.warn("料品形态: {}不合规，应为 【委外加工件】/【制造件】", source);
            throw new JWIException("料品形态: " + source + "不合规，应为 【委外加工件】/【制造件】");
        }
        // 3、检查是否有BOM
        List<SimplePartBOMNode> bom = partHelper.findSimpleUseTree("",dbPart.getOid(),1);
        List<SimplePartBOMNode> children = bom.get(0).getChildren();
        if(CollectionUtil.isEmpty(children)){
            // 和U9沟通确定：没有子项就不发了，中间版本没有子项也不用发
            logger.warn("当前part-{}下无bom", dbPart.getNumber());
            throw new JWIException("当前part-" + dbPart.getNumber() + "下无bom");
        }

        // 4、判断是初次导入还是更新
        String businessOid = type + "_" + dbPart.getNumber() + "_" + dbPart.getVersion();
        String version = dbPart.getVersion();
//        String ActionName = "A01".equalsIgnoreCase(version) ? "ImportBOM" : "UpdateBOM";
        IntegrationRecord integrationRecord = jwiCommonService.dynamicQueryOne(IntegrationRecord.TYPE,
                Condition.where("businessOid").eq(businessOid).
                        and(Condition.where("isSuccess").eq(Boolean.TRUE))
                        .and(Condition.where("operationType").eq(IMPORT_BOM)), IntegrationRecord.class);

        // 先根据 integrationRecord 判断
        // 初步根据是否已有成功的导入记录判断操作类型
        // isFirst == -1 表示不是从管理端发起 isFirst ==3 表示列表页批量发送
        String actionName = (integrationRecord == null) ? IMPORT_BOM : UPDATE_BOM;

        // 1或 2表示来自 集成看板处发送
        if (isFirst == 1 || isFirst == 2) {
            actionName = (isFirst == 1) ? IMPORT_BOM : UPDATE_BOM;
        }



        HZZReleaseDataDTO hzzReleaseDataDTO = BeanUtil.copyProperties(dbPart,new HZZReleaseDataDTO());
        hzzReleaseDataDTO.setBusinessOid(businessOid);
        hzzReleaseDataDTO.setProcessOrder(processOrder == null ? "" : processOrder.getNumber());
        hzzReleaseDataDTO.setUpdateTime(dbPart.getUpdateDate());
        hzzReleaseDataDTO.setDefaultUnit(partEntityRelease.findUnitsCode(hzzReleaseDataDTO.getDefaultUnit()));
        initPublisher(user,hzzReleaseDataDTO);
        initBOM(children, hzzReleaseDataDTO);

        JSONObject releaseData = JSONObject.parseObject(JSONObject.toJSONString(hzzReleaseDataDTO));
        logger.info("HZZRelease.releaseData------->>>>>>{}", JSONUtil.toJsonStr(releaseData));

        IntegrationRecord record = getIntegrationRecord(recordOid, type, processOrder, releaseData);

        return callHzzAndUpdateRecord(actionName, ddUserId, dbPart, hzzReleaseDataDTO, record);

    }


    /**
     * 构建请求体
     */
    private JSONObject buildRequestBody(String actionName, String ddUserId, String procode, List<?> bomList) {
        JSONObject body = new JSONObject();
        body.put("Controller", CONTROLLER);
        body.put("AppCode", APP_CODE);
        body.put("ActionName", actionName);
        body.put("orname", ddUserId);
        body.put("Procode", procode);
        body.put("BOMList", JSONUtil.toJsonStr(bomList));
        return body;
    }

    public IntegrationRecord callHzzAndUpdateRecord(String initialAction, String ddUserId, PartIteration dbPart, HZZReleaseDataDTO hzzReleaseDataDTO, IntegrationRecord record) {
        String actionName = initialAction;
        JSONObject body = buildRequestBody(actionName, ddUserId, dbPart.getNumber(), hzzReleaseDataDTO.getBOMList());

        logger.info("请求HZZ参数：{}", JSONUtil.toJsonStr(body));
        JSONObject response = sendPostRequest(body);
        String message = "";
        if (response != null) {
            logger.info("HZZ接口返回信息 Response: {}", response.toJSONString());

            JSONObject returnData = response.getJSONObject("ReturnData");
            String result = returnData.getString("result");
             message = returnData.getString("message");

            if (!"Success".equals(result) && !actionName.equals(UPDATE_BOM)) {

                // 第一次失败，尝试用 UpdateBOM 再发一次
                actionName = UPDATE_BOM;
                body = buildRequestBody(actionName, ddUserId, dbPart.getNumber(), hzzReleaseDataDTO.getBOMList());

                logger.info("请求HZZ参数（重试UpdateBOM）：{}", JSONUtil.toJsonStr(body));
                response = sendPostRequest(body);

                if (response != null) {
                    logger.info("HZZ接口返回信息（重试Response）：{}", response.toJSONString());
                    returnData = response.getJSONObject("ReturnData");
                    result = returnData.getString("result");
                    message = returnData.getString("message");
                } else {
                    // 二次请求失败
                    result = "Failed";
                    message = "重试UpdateBOM接口无响应";
                    dingTalkService.createHzzErrorNotice(dbPart.getNumber(), ddUserId, message);
                }
            }

            record.setOperationType(actionName);
            record.setMsg(message);
            record.setConsumer("HZZ");
            record.setIsSuccess("Success".equals(result));
        } else {
            // 首次请求失败
            record.setOperationType(actionName);
            record.setMsg("调用HZZ接口无响应");
            record.setConsumer("HZZ");
            record.setIsSuccess(false);
            dingTalkService.createHzzErrorNotice(dbPart.getNumber(), ddUserId, "调用HZZ接口无响应");

        }

        //  接口返回不成功则触发钉钉通知
        if (!record.getIsSuccess()) {
            dingTalkService.createHzzErrorNotice(dbPart.getNumber(), ddUserId, message);
        }
        return jwiCommonService.update(record);
    }

    private JSONObject sendPostRequest(JSONObject requestBody) {
        try {
            HttpResponse response = HttpRequest.post(API_URL)
                    .header("Content-Type", "application/json")
                    .header("EngineCode", ENGINE_CODE)
                    .header("EngineSecret", ENGINE_SECRET)
                    .body(requestBody.toJSONString())
                    .execute();

            if (response.getStatus() == HttpStatus.OK.value()) {
                return JSONObject.parseObject(response.body());
            } else {
                logger.error("API 调用失败，状态码：{}", response.getStatus());
                throw new JWIException("慧致造接口 调用失败，状态码：" + response.getStatus());
            }
        } catch (Exception e) {
            logger.error("调用 API 失败", e);
            throw new JWIException("调用慧致造接口异常", e);
        }
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        type = EntityReleaseFactory.BusinessType.hzzBOM.name();
        EntityReleaseFactory.register(type,this);
    }
    private void initPublisher(UserWithPositionAndOrg user, HZZReleaseDataDTO partReleaseData) {
        if(user == null){
            partReleaseData.setUpdatorAccount("sys_admin");
            partReleaseData.setUpdatorNameAndDepartment("PDM系统管理员");
            return;
        }
        partReleaseData.setUpdatorAccount(user.getNumber());
        List<String> orgList = userService.getOrgTreeNameByAccount(user.getAccount());
        StringBuffer orgTree = new StringBuffer(user.getName() + ",");
        for(int i = orgList.size()-2; i > -1 ; i--){
            orgTree.append(orgList.get(i));
            if(i > 0){
                orgTree.append("-");
            }
        }
        partReleaseData.setUpdatorNameAndDepartment(orgTree.toString());
    }


    private void initBOM(List<SimplePartBOMNode> children, HZZReleaseDataDTO partReleaseData) {
        List<ChildBOMDTO> childPartDTOS = new ArrayList<>();

        for (SimplePartBOMNode node : children) {
            JSONObject useData = node.getUse() != null ? node.getUse().getExtensionContent() : null;
            String position = useData != null ? useData.getString("position") : null;
            Double quantity = node.getUse() != null ? node.getUse().getQuantity() : null;

            // 位号为空或只包含空格时，直接构造 DTO
            if (StringUtils.isBlank(position)) {
                ChildBOMDTO dto = new ChildBOMDTO();
                dto.setMatProcode(node.getNumber());
                dto.setQty(quantity != null ? quantity : 1.0);
                childPartDTOS.add(dto);
                continue;
            }

            // 标准化分隔符：中文逗号、带空格的逗号 -> 英文逗号
            String normalizedPosition = position.replaceAll("，", ",").replaceAll("\\s*,\\s*", ",");

            String[] positionArray = normalizedPosition.split(",");
            int fieldIndex = 0;
            StringBuilder currentField = new StringBuilder();
            ChildBOMDTO dto = new ChildBOMDTO();

            for (String p : positionArray) {
                if (currentField.length() + p.length() + 2 <= 2000) {
                    if (currentField.length() > 0) {
                        currentField.append(", ");
                    }
                    currentField.append(p);
                } else {
                    setAssemblingNo(dto, fieldIndex, currentField.toString());
                    fieldIndex++;

                    if (fieldIndex == 11) {
                        dto.setMatProcode(node.getNumber());
                        dto.setQty(quantity != null ? quantity : 1.0);
                        childPartDTOS.add(dto);

                        dto = new ChildBOMDTO();
                        fieldIndex = 0;
                    }

                    currentField = new StringBuilder(p);
                }
            }

            // 填充剩余内容
            if (currentField.length() > 0) {
                setAssemblingNo(dto, fieldIndex, currentField.toString());
            }

            dto.setMatProcode(node.getNumber());
            dto.setQty(quantity != null ? quantity : 1.0);
            childPartDTOS.add(dto);
        }

        partReleaseData.setBOMList(childPartDTOS);
    }

    /**
     * 根据 childPartDTO 中的所有 assemblingNoX 计算总数量
     */
    private int countTotalPositions(ChildBOMDTO childPartDTO) {
        int count = 0;
        count += countPositions(childPartDTO.getAssemblingNo());
        count += countPositions(childPartDTO.getAssemblingNo1());
        count += countPositions(childPartDTO.getAssemblingNo2());
        count += countPositions(childPartDTO.getAssemblingNo3());
        count += countPositions(childPartDTO.getAssemblingNo4());
        count += countPositions(childPartDTO.getAssemblingNo5());
        count += countPositions(childPartDTO.getAssemblingNo6());
        count += countPositions(childPartDTO.getAssemblingNo7());
        count += countPositions(childPartDTO.getAssemblingNo8());
        count += countPositions(childPartDTO.getAssemblingNo9());
        count += countPositions(childPartDTO.getAssemblingNo10());
        return count;
    }

    /**
     * 计算单个 assemblingNoX 字段中的位号数量
     */
    private int countPositions(String assemblingNo) {
        return (assemblingNo == null || assemblingNo.isEmpty()) ? 0 : assemblingNo.split(", ").length;
    }

    private void setAssemblingNo(ChildBOMDTO childPartDTO, int index, String value) {
        switch (index) {
            case 0: childPartDTO.setAssemblingNo(value); break;
            case 1: childPartDTO.setAssemblingNo1(value); break;
            case 2: childPartDTO.setAssemblingNo2(value); break;
            case 3: childPartDTO.setAssemblingNo3(value); break;
            case 4: childPartDTO.setAssemblingNo4(value); break;
            case 5: childPartDTO.setAssemblingNo5(value); break;
            case 6: childPartDTO.setAssemblingNo6(value); break;
            case 7: childPartDTO.setAssemblingNo7(value); break;
            case 8: childPartDTO.setAssemblingNo8(value); break;
            case 9: childPartDTO.setAssemblingNo9(value); break;
            case 10: childPartDTO.setAssemblingNo10(value); break;
            default: break;
        }
    }
}
