package cn.jwis.product.pdm.customer.service.impl;

import cn.jwis.framework.base.util.CollectionUtil;
import cn.jwis.framework.database.core.entity.EntityFilter;
import cn.jwis.framework.database.core.query.dynamic.Condition;
import cn.jwis.platform.iam.user.User;
import cn.jwis.platform.plm.dataengine.common.JWICommonService;
import cn.jwis.platform.plm.search.engine.entity.AdvancedSearchCondition;
import cn.jwis.platform.plm.search.engine.entity.SearchHistory;
import cn.jwis.platform.plm.search.engine.service.SearchHelperImpl;
import cn.jwis.product.pdm.customer.entity.assistant.CheckedAssist;
import com.alibaba.fastjson.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2023/11/8 17:32
 * @Email <EMAIL>
 */
@Service
@Primary
@Transactional
public class CustomerSearchHelperImpl extends SearchHelperImpl implements CheckedAssist {

    @Autowired
    JWICommonService jwiCommonService;


    @Override
    public JSONObject globalSearch(SearchHistory dto) {
        //修改搜索條件，增加中文名搜索
        List<AdvancedSearchCondition> conditions =  dto.getConditions();
        if(CollectionUtil.isNotEmpty(conditions)){
            for(AdvancedSearchCondition condition : conditions){
                if("createBy".equals(condition.getName()) || "updateBy".equals(condition.getName())){
                    EntityFilter filter = new EntityFilter();
                    filter.setType(User.TYPE);
                    // 针对user name 目前只支持包含和等于两个搜索条件
                    if("contain".equals(condition.getConnector())) {
                        filter.setFilter(Condition.where("name").contain(condition.getSearchKeyA()));
                    }else{
                        filter.setFilter(Condition.where("name").eq(condition.getSearchKeyA()));
                    }
                    User user = CollectionUtil.getFirst(jwiCommonService.dynamicQuery(filter,User.class));
                    String uerKey = user == null ? "........." : user.getAccount(); // user不存在时，随便写个字符串让搜不到
                    condition.setSearchKeyA(uerKey);
                }
            }
        }

        String searchKey = dto.getSearchKey();
        if("PartIteration".equals(dto.getSearchType()) && !"".equals(searchKey)){
            return super.globalSearch(dto);

//            String displayName = dto.getSearchTypeDisplayName();
//            CustomerSearchHistory cusSearchDto = new CustomerSearchHistory();
//            BeanUtils.copyProperties(dto, cusSearchDto);
//            int index = dto.getIndex(), size = dto.getSize(), searchSize = index * size;
//            AtomicInteger total = new AtomicInteger();
//            UserDTO currUser = SessionHelper.getCurrentUser();
//
//            List<JSONObject> totalList = Arrays.stream(searchKey.split(",")).filter(key -> key.length() > 0).flatMap(applyNoThrow(key -> {
//                SessionHelper.addCurrentUser(currUser);
//                CustomerSearchHistory newCusSeachDto = cusSearchDto.clone();
//                newCusSeachDto.setSearchKey(key);
//                newCusSeachDto.setIndex(1);
//                newCusSeachDto.setSize(searchSize);
//                JSONObject result = super.globalSearch(newCusSeachDto).getJSONObject(displayName);
//                total.addAndGet(result.getInteger("count"));
//                return (Stream<JSONObject>) result.getJSONArray("rows").stream();
//            })).collect(Collectors.toList());
//
//            List<JSONObject> dataList = totalList.stream().collect(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(j -> j.getString("oid")))))
//                    .stream().sorted(Comparator.comparing(j -> -j.getLong("updateDate"))).collect(Collectors.toList());
//            int count = total.addAndGet(-(totalList.size() - dataList.size()));
//
//            List<JSONObject> resultList = dataList.stream().skip(index).limit(size).collect(Collectors.toList());
//            return new JSONObject().fluentPut(displayName, new JSONObject().fluentPut("count", count)
//                    .fluentPut("pageIndex", index).fluentPut("pageSize", size).fluentPut("rows", resultList));
        }else
            return super.globalSearch(dto);
    }

    @Override
    public Object save(SearchHistory dto) {
        dto.setModelDefinition("SearchHistory");
        return super.save(dto);
    }
}
