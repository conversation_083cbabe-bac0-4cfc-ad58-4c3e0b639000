package cn.jwis.product.pdm.customer.service.impl;

import cn.jwis.framework.base.exception.JWIException;
import cn.jwis.framework.base.util.CollectionUtil;
import cn.jwis.framework.base.util.StringUtil;
import cn.jwis.framework.base.web.session.SessionHelper;
import cn.jwis.platform.iam.user.User;
import cn.jwis.platform.plm.account.config.AccountPropertiesConfiguration;
import cn.jwis.platform.plm.account.entity.tenant.Tenant;
import cn.jwis.platform.plm.account.tenant.service.TenantService;
import cn.jwis.platform.plm.account.user.service.UserService;
import cn.jwis.platform.plm.foundation.common.service.CommonAbilityService;
import cn.jwis.product.pdm.customer.entity.CustomerUserTenantAuthInfo;
import cn.jwis.product.pdm.customer.entity.CustomerUserTenantInfoDTO;
import cn.jwis.product.pdm.customer.repo.CustomerPlmUserNeo4jRepo;
import com.alibaba.fastjson.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.MessageFormat;
import java.util.List;

/**
 * <AUTHOR> yefei
 * @Date ： 2024/4/24
 * @Description :
 */
@Transactional
@Service
public class CustomerDefaultAuthHelperImpl {
    @Resource
    UserService userService;
    @Resource
    RedisTemplate redisTemplate;
    @Resource
    CommonAbilityService commonAbilityService;
    @Resource
    TenantService tenantService;

    private String authRedisKey;

    @Resource
    CustomerPlmUserNeo4jRepo customerPlmUserNeo4jRepo;

    public CustomerUserTenantInfoDTO queryUserTenantInfo() {
        String userOid = SessionHelper.getCurrentUser().getOid();
        CustomerUserTenantInfoDTO userTenantInfoDTO = new CustomerUserTenantInfoDTO();
        List<Tenant> tenants = this.tenantService.searchByUserOid(userOid);
        if (CollectionUtil.isEmpty(tenants)) {
            return userTenantInfoDTO;
        } else {
            String tenantOid = this.findUserRecentlyTenant(userOid);
            Tenant defaultTenant = null;
            if (StringUtil.isBlank(tenantOid)) {
                defaultTenant = tenants.get(0);
                this.cacheUserRecentlyTenant(userOid, defaultTenant.getOid());
            } else {
                defaultTenant =
                        tenants.stream().filter((item) -> tenantOid.equals(item.getOid())).findFirst().orElseGet(null);
                if (defaultTenant == null) {
                    defaultTenant = tenants.get(0);
                    this.cacheUserRecentlyTenant(userOid, defaultTenant.getOid());
                }
            }

            CustomerUserTenantAuthInfo userTenantAuthInfo = this.cacheTenantUserAuthInfo(userOid, defaultTenant.getOid());
            userTenantInfoDTO.setTenantList(tenants);
            userTenantInfoDTO.setDefaultTanant(defaultTenant);
            userTenantInfoDTO.setUserTenantAuthInfo(userTenantAuthInfo);
            return userTenantInfoDTO;
        }
    }

    public String findUserRecentlyTenant(String userOid) {
        Object value = this.redisTemplate.opsForHash().get("USER_RECENTLY_LOGIN_TENANT", userOid);
        return this.obj2String(value);
    }

    public String cacheUserRecentlyTenant(String userOid, String tenantOid) {
        this.redisTemplate.opsForHash().put("USER_RECENTLY_LOGIN_TENANT", userOid, tenantOid);
        return tenantOid;
    }

    @Autowired
    private void initConfig(AccountPropertiesConfiguration propertiesConfiguration) {
        this.authRedisKey = propertiesConfiguration.getUserSessionExtend().getRedis().getAuthKey();
    }

    private String obj2String(Object obj) {
        return obj == null ? null : String.valueOf(obj);
    }

    public CustomerUserTenantAuthInfo cacheTenantUserAuthInfo(String userOid, String tenantOid) {
        CustomerUserTenantAuthInfo info = new CustomerUserTenantAuthInfo();
        User user = (User)this.commonAbilityService.findByOid(userOid, "User");
        if (user == null) {
            return info;
        } else {
            boolean isSysAdmin = this.userService.assertUserSystemAdmin(userOid);
            boolean isTenantAdmin = this.userService.assertUserTenantAdmin(userOid, tenantOid);
            info.setSystemAdmin(isSysAdmin);
            info.setTenantAdmin(isTenantAdmin);
            info.setDataAdmin(assertUserDataAdmin(userOid,tenantOid));
            info.setSecurityLevel(user.getSecurityLevel());
            this.redisTemplate.opsForHash().put(MessageFormat.format(this.authRedisKey, userOid), tenantOid, JSONObject.toJSONString(info));
            return info;
        }
    }

    public boolean assertUserDataAdmin(String userOid, String tenantOid) throws JWIException {
        return this.customerPlmUserNeo4jRepo.assertUserDataAdmin(userOid, tenantOid);
    }
}
