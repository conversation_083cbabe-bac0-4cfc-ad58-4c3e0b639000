package cn.jwis.product.pdm.customer.service.impl;

import cn.hutool.json.JSONUtil;
import cn.jwis.framework.base.bean.util.BeanUtil;
import cn.jwis.framework.base.domain.able.info.ModelInfo;
import cn.jwis.framework.base.dto.UserDTO;
import cn.jwis.framework.base.exception.JWIServiceException;
import cn.jwis.framework.base.util.Assert;
import cn.jwis.framework.base.util.CollectionUtil;
import cn.jwis.framework.base.util.MapUtil;
import cn.jwis.framework.base.util.StringUtil;
import cn.jwis.framework.base.web.session.SessionHelper;
import cn.jwis.framework.database.core.query.dynamic.Condition;
import cn.jwis.platform.plm.container.entity.Container;
import cn.jwis.platform.plm.datadistribution.dto.DataDistributionDTO;
import cn.jwis.platform.plm.dataengine.common.JWICommonService;
import cn.jwis.platform.plm.foundation.classification.able.info.ClassificationInfo;
import cn.jwis.platform.plm.foundation.classification.entity.Classification;
import cn.jwis.platform.plm.foundation.classification.responce.ClsPropertyWithRel;
import cn.jwis.platform.plm.foundation.classification.service.ClassificationPropertyHelper;
import cn.jwis.platform.plm.foundation.classification.service.ClassificationService;
import cn.jwis.platform.plm.foundation.common.service.CommonAbilityHelper;
import cn.jwis.platform.plm.foundation.common.service.CommonAbilityService;
import cn.jwis.platform.plm.foundation.location.able.info.LocationInfo;
import cn.jwis.platform.plm.foundation.model.entity.VertexDef;
import cn.jwis.platform.plm.foundation.model.service.ModelHelper;
import cn.jwis.platform.plm.foundation.relationship.Describe;
import cn.jwis.platform.plm.foundation.relationship.PropertyAssign;
import cn.jwis.platform.plm.foundation.relationship.Use;
import cn.jwis.platform.plm.foundation.versionrule.able.LockAble;
import cn.jwis.platform.plm.foundation.versionrule.able.info.LockInfo;
import cn.jwis.platform.plm.permission.helper.PermissionHelper;
import cn.jwis.platform.plm.permission.permission.enums.PermissionKeyEnum;
import cn.jwis.platform.plm.sysconfig.entity.ConfigItem;
import cn.jwis.platform.plm.sysconfig.preferences.PreferencesService;
import cn.jwis.product.pdm.customer.entity.DataDictionary;
import cn.jwis.product.pdm.partbom.cad.PartCadThirdParty;
import cn.jwis.product.pdm.partbom.part.data.constant.SystemConfigConstants;
import cn.jwis.product.pdm.partbom.part.dto.McadToPartDTO;
import cn.jwis.product.pdm.partbom.part.entity.Part;
import cn.jwis.product.pdm.partbom.part.entity.PartIteration;
import cn.jwis.product.pdm.partbom.part.related.MCADIteration;
import cn.jwis.product.pdm.partbom.part.related.responce.MCADBOMNode;
import cn.jwis.product.pdm.partbom.part.response.BOMNode;
import cn.jwis.product.pdm.partbom.part.response.CadPartMapping;
import cn.jwis.product.pdm.partbom.part.response.Mapping;
import cn.jwis.product.pdm.partbom.part.response.RelationPart;
import cn.jwis.product.pdm.partbom.part.service.DefaultMcadPartHelperImpl;
import cn.jwis.product.pdm.partbom.part.service.MCadPartService;
import cn.jwis.product.pdm.partbom.part.service.PartHelper;
import cn.jwis.product.pdm.partbom.part.service.PartService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.beans.BeanInfo;
import java.beans.IntrospectionException;
import java.beans.Introspector;
import java.beans.PropertyDescriptor;
import java.lang.reflect.Method;
import java.text.MessageFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2023/9/27 10:55
 * @Email <EMAIL>
 */
@Slf4j
@Service
@Primary
@Transactional
public class CustomerMcadPartHelperImpl extends DefaultMcadPartHelperImpl {

    @Autowired
    PartService partService;

    @Autowired
    ClassificationService classificationService;

    Set<String> propertyMappingExcludes = null;

    @Value("${part.default.value}")
    String partDefaultValue;

    @Resource
    ClassificationPropertyHelper classificationPropertyHelper;

    @Value("${property.mapping.excludes}")
    private void setPropertyMappingExclude(String excludes){
        String[] split = excludes.split(",");
        propertyMappingExcludes = new HashSet<>(split.length);
        for (String s : split) {
            propertyMappingExcludes.add(s);
        }
    }
    @Autowired
    @Lazy
    PartHelper partHelper;

    @Autowired
    CommonAbilityHelper commonAbilityHelper;

    @Autowired
    CommonAbilityService commonAbilityService;

    @Autowired
    PartCadThirdParty partCadThirdParty;

    @Autowired
    MCadPartService mCadPartService;

    @Autowired
    JWICommonService jwiCommonService;

    @Autowired
    PreferencesService preferencesService;

    @Autowired
    ModelHelper modelHelper;

    @Override
    public PartIteration preparePartCreateData(MCADIteration mcad, CadPartMapping mappingRule, LocationInfo locationInfo,
                                               Map<String, List<String>> oidLinkMap, String partModelDefinitionKey,
                                               Map<String, String> partModelDefinitionMapping, Map<String, VertexDef> sonTypes) {

        PartIteration part =  super.preparePartCreateData(mcad, mappingRule, locationInfo, oidLinkMap, partModelDefinitionKey,
                partModelDefinitionMapping, sonTypes);
        addTempCls(part);
        if(part.getExtensionContent() == null){
            part.setExtensionContent(new JSONObject());
        }

        // 新增时 设置需求人为自己
        putRequirementUser2Ex(part.getExtensionContent(),null);

        JSONObject extensionContent = part.getExtensionContent();
        // 校验 "cn_jwis_gg"，如果不存在或者为空，则赋默认值 "待定"
        if (!extensionContent.containsKey("cn_jwis_gg") || StringUtils.isBlank(extensionContent.getString("cn_jwis_gg"))) {
            extensionContent.put("cn_jwis_gg", "待定");
        }


        // 定义允许的 source 值范围
        Set<String> allowedSources = new HashSet<>(Arrays.asList(
                "采购件", "制造件", "委外加工件", "资产", "费用性料品", "服务", "低值易耗品", "套件"
        ));

        // 获取 part 的 source
        String source = part.getSource();

        // 检查 source 是否存在
        if (source != null && !source.isEmpty()) {
            // 如果 source 不在允许的范围内，设置为 制造件
            if (!allowedSources.contains(source)) {
                part.setSource("制造件");
            }
        }else {
            // 如果 source 为空，设置为 "制造件"
            part.setSource("制造件");
        }

        // 是否通用件默认为否
        part.getExtensionContent().put("cn_jwis_grafting_part","否");
        return part;
    }

    // 检入时向扩展属性内写入需求人信息
    public void putRequirementUser2Ex(JSONObject extensionContent,JSONObject requirementUser) {
        if(extensionContent == null) {
            return;
        }
        if(requirementUser == null) {
            UserDTO currentUser = SessionHelper.getCurrentUser();
            requirementUser = new JSONObject();
            requirementUser.put("account",currentUser.getAccount());
            requirementUser.put("name",currentUser.getName());
        }
        log.info("set default xqr : ",requirementUser);
        extensionContent.put("cn_jwis_xqr", requirementUser);
    }

    // 因为目前所有类型的Part都加了分类必填的校验，所以此处赋予一个临时分类，保证CAD转Part时通过校验，后续在起流程时校验部件必须为正式分类
    private void addTempCls(PartIteration part) {
        Classification classification = classificationService.findByClsCode("CADCHECKIN");
        ClassificationInfo clsInfo = BeanUtil.copyProperties(classification,new ClassificationInfo());
        if (clsInfo != null) {
            partService.setClassification(part, clsInfo);

        }
    }
    public boolean generatePartByMCad(McadToPartDTO dto,Set<String> updateMcadOidSet) {
        log.info("generatePartByMCad dto:" + JSON.toJSONString(dto));
        log.info("updateMcadOidSet : {}" ,updateMcadOidSet);
        MCADBOMNode mcadbomNode = dto.getMcadbomNode();
        JSONObject extensionContent = mcadbomNode.getExtensionContent();
        // 收集需要更新版本的MCAD
        LinkedHashMap<MCADIteration, List<RelationPart>> needUpdatePart = new LinkedHashMap<>();
        // 收集需要使用当前版本的MCAD
        Map<String, List<RelationPart>> needCurrPart = new HashMap<>();
        // 收集需要创建的MCAD，并检查物料的状态
        Map<String,MCADIteration> needCreatePart = new HashMap<>();
        ConfigItem configItem = preferencesService.queryConfigValue(SystemConfigConstants.FILTER_BOM_CHANGE_DATA);
        List<String> staForCannotBind = new ArrayList<>();
        if (configItem != null) {
            staForCannotBind.addAll(
                    Stream.of(configItem.getValue().split(SystemConfigConstants.CONNECTOR_4_CLS_FIELD)).collect(Collectors.toList()));
        }
        Map<String,List<String>> oidLinkMap = new HashMap<>();
        collectPart(mcadbomNode,needCreatePart,needUpdatePart,needCurrPart,staForCannotBind,updateMcadOidSet,oidLinkMap);
        log.info("generatePartByMCad needCreatePart:" + JSON.toJSONString(needCreatePart));
        log.info("generatePartByMCad needUpdatePart:" + JSON.toJSONString(needUpdatePart));
        // 存所有(mcadOid) - [partNumber;;;qqqpartOid] 的map,用于构建树结构
        // 处理需要新建的Part
        List<PartIteration> createList = generateNewPart(needCreatePart,oidLinkMap,dto.getLocationInfo());
        // 处理需要更新的Part
        List<LockAble> updateMPMList = generateUpdatePart(needUpdatePart,oidLinkMap);
        // 处理无须更新的MCAD
        generateCurrPart(needCurrPart,oidLinkMap);
        log.info("generatePartByMCad oidLinkMap:" + JSON.toJSONString(oidLinkMap));
        // 处理BOM
        List<String> needDeleteUse = new ArrayList<>();// 需要删除子项的物料
        List<Use> needCreateUse = new ArrayList<>();// 需要新建的use
        buildPartBom(mcadbomNode,oidLinkMap,needCreateUse,needDeleteUse);
        // 清空旧的 use
        log.info("generatePartByMCad...needDeleteUse:" + JSON.toJSONString(needDeleteUse));
        partService.deleteOutRel(needDeleteUse, Collections.singletonList(Use.TYPE));
        // 创建关系
        log.info("mcadbomNode信息:{}", JSONUtil.toJsonStr(mcadbomNode));
        log.info("需要创建的BOM信息needCreateUse:{}", JSONUtil.toJsonStr(needCreateUse));
        commonAbilityService.createUse(needCreateUse);

        // todo 没有生版本的但是结构变了的物料要不要下发？

        // 建mcad -> part 的描述关系。
        List<String> mcadOidList = new ArrayList<>();
        for(Map.Entry<String,List<String>> entry : oidLinkMap.entrySet()) {
            mcadOidList.add(entry.getKey());
        }
        List<MCADIteration> mcadList = jwiCommonService.findByOid(MCADIteration.TYPE, mcadOidList, MCADIteration.class);
        Map<String, MCADIteration> collect = mcadList.stream().collect(Collectors.toMap(MCADIteration::getOid, Function.identity(), (key1, key2) -> key2));

        Map<String,List<String>> describeMap = new HashMap<>();
        for(Map.Entry<String,List<String>> entry : oidLinkMap.entrySet()){
            String mcadOid = entry.getKey();
            List<String> partOids = new ArrayList<>();
            for (String partData : entry.getValue()) {
                partOids.add(partData.split(";;;qqq")[1]);
            }
            describeMap.put(mcadOid,partOids);
        }
        mCadPartService.createDescribe(describeMap);

        //mpm调整需要，把bom关系，cad关系放在最后。(update,checkIn)
        //数据下发，创建得部件（创建在generateNewPart方法）也需要创建关系走更新，不升版本
        if(CollectionUtil.isNotEmpty(createList)){
            createList.forEach(item -> {
                //数据发放至下游，update代表寻找旧版本部件更新不升版本
                DataDistributionDTO dataDistributionDTO = new DataDistributionDTO(PartIteration.TYPE, item.getOid(), "update",SessionHelper.getAccessToken(),SessionHelper.getCurrentUser(), SessionHelper.getAppId());
                partHelper.dataDistribution(dataDistributionDTO);
            });
        }
        // 数据下发，更新等于创建新版本的部件
        if(CollectionUtil.isNotEmpty(updateMPMList)) {
            log.info("generatePartByMCad updateMPMList:" + JSON.toJSONString(updateMPMList));
            updateMPMList.forEach(item -> {
                //数据发放至下游。
                DataDistributionDTO dataDistributionDTO = new DataDistributionDTO(PartIteration.TYPE, item.getOid(), "checkIn",SessionHelper.getAccessToken(),SessionHelper.getCurrentUser(), SessionHelper.getAppId());
                partHelper.dataDistribution(dataDistributionDTO);
            });
        }
        //数据下发，物料和cad的关系
        for(Map.Entry<String,List<String>> entry : oidLinkMap.entrySet()){
            String mcadOid = entry.getKey();
            for (String partData : entry.getValue()) {
                //数据发放至下游。
                DataDistributionDTO dataDistributionDTO = new DataDistributionDTO(Describe.TYPE, partData.split(";;;qqq")[1], "addDescribeRelation", SessionHelper.getAccessToken(), SessionHelper.getCurrentUser(), SessionHelper.getAppId());
                //mcad的名称和版本，返回体是关系对象。
                dataDistributionDTO.setName(collect.get(mcadOid).getName());
                dataDistributionDTO.setVersion(collect.get(mcadOid).getDisplayVersion());
                Describe describe = new Describe();
                describe.setFromOid(mcadOid);
                describe.setFromType(MCADIteration.TYPE);
                describe.setToType(PartIteration.TYPE);
                describe.setToOid(partData.split(";;;qqq")[1]);
                dataDistributionDTO.setBody(describe);
                partHelper.dataDistribution(dataDistributionDTO);
            }
        }
        return true;
    }

    @Override
    public boolean generatePartByMCad(McadToPartDTO dto) {
        return this.generatePartByMCad(dto,null);
    }

    private void buildPartBom(MCADBOMNode mcadbomNode, Map<String, List<String>> oidLinkMap, List<Use> needCreateUse, List<String> needDeleteUse) {
        if(mcadbomNode == null){
            return;
        }
        // 获取到原部件的父子关系，用于1对多的场景中识别父子。
        List<String> oldBOMNumberList = new ArrayList<>();
        // 因为任何层级的父项都有可能是新挂上来的，所以必须每一层单独去查，如果从顶层一次性查BOM，可能会有断层。
        String topMcadOid = mcadbomNode.getOid();
        List<String> topPartOids = oidLinkMap.get(topMcadOid);
        List<BOMNode> bomNodes = new ArrayList<>(); //此时还未修改结构，所以当前版本和上一版本的结构一致
        if(topPartOids != null) {
            topPartOids.forEach(topPartOid -> bomNodes.addAll(partHelper.fuzzyUseTree(null, topPartOid, 1, null)));
        }
        bomNodes.forEach(node -> getAllBOMNumber(node,oldBOMNumberList));
        // 收集用户自己挂上去的原BOM
        bomNodes.forEach(node -> collectHandmadeUse(node,needCreateUse));
        // 遍历父项，此时每个mcad均有对应的（一个或多个）部件了（不管是已有的还是新增的）
        List<String> fatherPartDatas = oidLinkMap.get(mcadbomNode.getOid());
        if(fatherPartDatas == null) {
            return;
        }
        // 获取到父项的ExcludeFromBOM，包含在其中的子项无需构造BOM
        List<String> needIgnoreSonCads = getIgnoreSonCads(mcadbomNode);
        List<MCADBOMNode> sonMCads = mcadbomNode.getChildren();
        for(String fatherData : fatherPartDatas){
            String[] fatherDataArr = fatherData.split(";;;qqq");
            String fatherNumber = fatherDataArr[0];
            String fatherOid = fatherDataArr[1];
            needDeleteUse.add(fatherOid);
            // 没有子项则无须处理。
            if(CollectionUtil.isEmpty(sonMCads)){
                continue;
            }
            // 遍历子项
            for(MCADBOMNode sonMCad : sonMCads){
                // 父需求忽略此子
                if(needIgnoreSonCads.contains(getIgnoreKey(sonMCad))){
                    continue;
                }
                List<String> sonPartDatas = oidLinkMap.get(sonMCad.getOid());
                if(sonPartDatas == null) {
                    continue;
                }
                String sonPartOid = getTargetSon(sonPartDatas,fatherNumber,oldBOMNumberList);
                Use use = new Use();
                use.setFromType(PartIteration.TYPE);
                use.setFromOid(fatherOid);
                use.setToType(PartIteration.TYPE);
                use.setToOid(sonPartOid);
                use.setQuantity(sonMCad.getQuantity());
                use.setUnit(partDefaultValue);
                use.setGeneratedByCAD(true);
                needCreateUse.add(use);
                // 递归收集
                buildPartBom(sonMCad,oidLinkMap,needCreateUse,needDeleteUse);
            }
        }
    }


    private void collectHandmadeUse(BOMNode fatherNode, List<Use> needCreateUse) {
        if(fatherNode == null){
            return;
        }
        List<BOMNode> children = fatherNode.getChildren();
        if (CollectionUtil.isEmpty(children)) {
            return;
        }
        for (BOMNode node : children) {
            Use oldUse = node.getUse();
            Boolean generatedByCAD = oldUse.getGeneratedByCAD();
            if (generatedByCAD == null || !generatedByCAD) {//不是cad生成的part
                Use use = BeanUtil.copyProperties(oldUse, new Use());
                needCreateUse.add(use);
            }
            collectHandmadeUse(node, needCreateUse);
        }
    }

    private void getAllBOMNumber(BOMNode bomNode, List<String> oldBOMNumberList) {
        if (bomNode == null) {
            return;
        }
        List<BOMNode> children = bomNode.getChildren();
        if (children == null) {
            return;//没有子件跳过。
        }
        for (BOMNode node : children) {
            oldBOMNumberList.add(bomNode.getNumber() + ";;;qqq" + node.getNumber());
            getAllBOMNumber(node,oldBOMNumberList);
        }
    }

    private String getTargetSon(List<String> sonPartDatas, String fatherNumber, List<String> oldBOMNumberList) {
        String sonPartOid = null;
        for(String sonPartData : sonPartDatas){
            String[] sonDataArr = sonPartData.split(";;;qqq");
            String sonPartNumber = sonDataArr[0];
            if(oldBOMNumberList.contains(fatherNumber + ";;;qqq" + sonPartNumber)){
                return sonDataArr[1];
            }
        }
        // 没找到，说明该父子关系此次是新建立
        String[] sonDataArr = sonPartDatas.get(0).split(";;;qqq");
        return sonDataArr[1];
    }

    private String getIgnoreKey(MCADBOMNode sonMCad) {
        String key = sonMCad.getName() + "/" + sonMCad.getModelDefinition();
        if(sonMCad.getAssemblyBy()==null){
            return key;
        }
        JSONObject relationData = sonMCad.getAssemblyBy().getRelationData();
        if(relationData==null || !relationData.containsKey("index")){
            return key;
        }
        String index = relationData.getString("index");
        return key + "/" + index;
    }


    private List<String> getIgnoreSonCads(MCADBOMNode mcadbomNode) {
        JSONObject jsonObject = mcadbomNode.getExtensionContent();
        if(jsonObject == null || !jsonObject.containsKey("ExcludeFromBOM")){
            return new ArrayList<>();
        }
        JSONArray jsonArray = jsonObject.getJSONArray("ExcludeFromBOM");
        return jsonArray.toJavaList(String.class);
    }

    private void generateCurrPart(Map<String, List<RelationPart>> needCurrPart, Map<String, List<String>> oidLinkMap) {
        if (!MapUtil.isEmpty(needCurrPart)) {
            Iterator var3 = needCurrPart.entrySet().iterator();

            while(var3.hasNext()) {
                Map.Entry<String, List<RelationPart>> entry = (Map.Entry)var3.next();
                Iterator var5 = ((List)entry.getValue()).iterator();

                while(var5.hasNext()) {
                    RelationPart relationPart = (RelationPart)var5.next();
                    this.putMap(oidLinkMap, entry.getKey(), relationPart.getNumber() + ";;;qqq" + relationPart.getOid());
                }
            }

        }
    }


    private List<LockAble> generateUpdatePart(LinkedHashMap<MCADIteration, List<RelationPart>> needUpdatePart, Map<String, List<String>> oidLinkMap) {
        List<PartIteration> checkOutPartList = new ArrayList<>();
        try {
            List<LockAble> updatePartList = new ArrayList<>();
            if (MapUtil.isEmpty(needUpdatePart)) {
                return null;
            }
            Map<String, String> nameChangeMap = new HashMap<>();

            Map<String, CadPartMapping> mappingCache = new HashMap<>();
            CadPartMapping mappingRule = null;
            UserDTO user = SessionHelper.getCurrentUser();
            boolean hasAccess = false;
            for (Map.Entry<MCADIteration, List<RelationPart>> entry : needUpdatePart.entrySet()) {
                MCADIteration mcadIteration = entry.getKey();
                List<RelationPart> relationParts = entry.getValue();
                // 获取属性映射规则
                mappingRule = getCadPartMapping(mappingCache, mcadIteration.getModelDefinition());
                for (RelationPart relationPart : relationParts) {
                    if (!hasAccess) { //只查一次
                        hasAccess = PermissionHelper.validate(relationPart, PermissionKeyEnum.EDIT, user);
                        Assert.isTrue(hasAccess, "You have no permission to modify Part(" + relationPart.getName() + ")in this location!");
                    }
                    // 更新 part
                    PartIteration updatePart = preparePartUpdateData(mcadIteration, relationPart, mappingRule, nameChangeMap, oidLinkMap);
                    checkOutPartList.add(updatePart);
                    log.info("partbom更新场景");
                    checkAndSetClassificationInfo(mcadIteration, updatePart);
                    commonAbilityHelper.doUpdate(updatePart);
                    LockInfo lockInfo = new LockInfo();
                    lockInfo.setType(PartIteration.TYPE);
                    lockInfo.setOid(updatePart.getOid());
                    updatePartList.add(partHelper.checkIn(lockInfo));
                    log.info("generateUpdatePart.partNumber:" + updatePart.getNumber());
                }
            }
            // 重命名
            if (MapUtil.isNotEmpty(nameChangeMap)) {
                nameChangeMap.entrySet().forEach(item -> {
                    partService.renameAllIteration(item.getKey(), item.getValue());
                });
            }
            return updatePartList;
        } catch (Exception e) {
            throw e;
        } finally {
            if(CollectionUtils.isNotEmpty(checkOutPartList)) {
                for (PartIteration partIteration : checkOutPartList) {
                    if(StringUtils.isNotBlank(partIteration.getLockOwnerOid())) {
                        this.commonAbilityHelper.removeRedisCheckOutLock(partIteration.getOid());
                    }
                }
            }
        }
    }


    private PartIteration preparePartUpdateData(MCADIteration mcadIteration, PartIteration relationPart, CadPartMapping mappingRule,
                                                Map<String, String> nameChangeMap, Map<String, List<String>> oidLinkMap) {

        String lockOwnerOid = relationPart.getLockOwnerOid();
        if (StringUtil.isNotBlank(lockOwnerOid)) {
            //  part被别人检出则报错
            String userOid = SessionHelper.getCurrentUser().getOid();
            Assert.isTrue(lockOwnerOid.equals(userOid), "Part related by MCAD (" + mcadIteration.getName() + ") has been Checked out!", JWIServiceException.class);
        }

        String oldName = relationPart.getName();
        // 检出 part
        if (StringUtil.isBlank(relationPart.getLockSourceOid())) {
            ModelInfo modelInfo = BeanUtil.copyProperties(relationPart, new ModelInfo());
            relationPart = (PartIteration) partHelper.checkOut(modelInfo);
        }
        // 属性映射
        MCADBOMNode tempMcad = BeanUtil.copyProperties(mcadIteration,new MCADBOMNode());
        mcadPartPropertyMapping(tempMcad, relationPart, mappingRule);
        // 缩略图
        relationPart.setThumbnailOid(mcadIteration.getThumbnailOid());
        // 是否发送重命名
        String partOid = relationPart.getOid();
        String newName = relationPart.getName();
        if (StringUtil.isBlank(newName)) {
            newName = mcadIteration.getName();
            relationPart.setName(newName);
        }
        if (!oldName.equals(relationPart.getName())) {
            nameChangeMap.put(partOid, newName);
        }
        //来源
        String source = relationPart.getSource();
        if (StringUtil.isBlank(source)) {
            relationPart.setSource("自制");
        }
        putMap(oidLinkMap, mcadIteration.getOid(), relationPart.getNumber() + ";;;qqq" + relationPart.getOid());
        return relationPart;

    }



    // mcad -- part 属性映射
    private void mcadPartPropertyMapping(MCADBOMNode mcad, PartIteration part, CadPartMapping mappingRule) {
        List<Mapping> mappings = mappingRule.getMapping();
        if (CollectionUtil.isEmpty(mappings)){
            return;
        }
        // 获取指定类的BeanInfo对象
        String cadAttrName = null;
        String cadAttrType = null;
        String partAttrName = null;
        String partAttrType = null;
        Map<String, PropertyDescriptor> cadPropName2PDs = getClassPropertyDescriptorsMap(MCADIteration.class);
        Map<String, PropertyDescriptor> partPropName2PDs = getClassPropertyDescriptorsMap(PartIteration.class);

        for (Mapping mapping : mappings) {
            cadAttrName = mapping.getCadAttrName();
            cadAttrType = mapping.getCadAttrType();
            partAttrName = mapping.getPartAttrName();
            partAttrType = mapping.getPartAttrType();
            if (StringUtil.isBlank(cadAttrName) || StringUtil.isBlank(partAttrName)){
                continue;
            }
            // 被排除则不 mapping
            if (propertyMappingExcludes.contains(partAttrName)){
                continue;
            }
            // 开始
            // basic和extension
            Object value = null;
            if ("basic".equals(cadAttrType)){
                try {
                    PropertyDescriptor property = cadPropName2PDs.get(cadAttrName);
                    Method readMethod = property.getReadMethod();
                    value = readMethod.invoke(mcad);
                }catch (Exception e){
                    throw new JWIServiceException("属性映射失败：获取属性异常："+e.getMessage());
                }
            }else {
                JSONObject extensionContent = mcad.getExtensionContent();
                if (extensionContent == null){
                    continue;
                }
                value = extensionContent.get(cadAttrName);
            }
            if (value == null){
                continue;
            }
            if("basic".equals(partAttrType)){
                try {
                    PropertyDescriptor property = partPropName2PDs.get(partAttrName);
                    Method writeMethod = property.getWriteMethod();
                    writeMethod.invoke(part, value);
                }catch (Exception e){
                    throw new JWIServiceException("属性映射失败：设置属性异常："+e.getMessage());
                }
            }else {
                JSONObject extensionContent = part.getExtensionContent();
                if (extensionContent == null){
                    extensionContent = new JSONObject();
                    part.setExtensionContent(extensionContent);
                }
                extensionContent.put(partAttrName, value);
            }
        }

    }

    private Map<String, PropertyDescriptor> getClassPropertyDescriptorsMap(Class calzz) {
        BeanInfo beanInfo = null;
        try {
            beanInfo = Introspector.getBeanInfo(calzz, Object.class);
        } catch (IntrospectionException e) {
            throw new JWIServiceException(e);
        }
        PropertyDescriptor[] pds = beanInfo.getPropertyDescriptors();
        Map<String, PropertyDescriptor> propName2PDs = new HashMap<>();
        for (PropertyDescriptor pd : pds) {
            propName2PDs.put(pd.getName(), pd);
        }
        return propName2PDs;
    }

    private void checkAndSetClassificationInfo(MCADIteration mcadIteration,PartIteration partIteration) {
        JSONObject extensionContent = mcadIteration.getExtensionContent();
        String displayName = extensionContent.getString("CCLASS");
        log.info("creo检入 集成参数分类displayName:{}", displayName);
        if(StringUtils.isBlank(displayName)) {
            log.info("creo检入 无集成分类信息");
            return;
        }
        Classification classification = jwiCommonService.dynamicQueryOne(Classification.TYPE, Condition.where(
                "displayName").eq(displayName), Classification.class);
        Assert.notNull(classification,"分类信息不存在");
        ClassificationInfo classificationInfo = new ClassificationInfo();
        classificationInfo.setOid(classification.getOid());
        classificationInfo.setClsCode(classification.getClsCode());
        classificationInfo.setDisplayName(classification.getDisplayName());

        JSONObject properties = new JSONObject();

        List<ClsPropertyWithRel> propertyWithRelList = classificationPropertyHelper.findAllPropByClsOid(classification.getOid());
        List<String> errorMessage = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(propertyWithRelList)) {
            for (ClsPropertyWithRel clsPropertyWithRel : propertyWithRelList) {
                PropertyAssign propertyAssign = clsPropertyWithRel.getRelationship();
                String proCode = clsPropertyWithRel.getCode();
                if(extensionContent.containsKey(proCode.toUpperCase())) {
                    if("datadictionary".equalsIgnoreCase(propertyAssign.getConstraintType())) {
                        Map<String, String> parentCodeDataDictionaryListMap =
                                jwiCommonService.dynamicQuery(DataDictionary.TYPE,
                                        Condition.where(
                                                "parentCode").in(propertyAssign.getConstraintContext().getString("datadictionary")),
                                        DataDictionary.class).stream().collect(Collectors.toMap(DataDictionary::getDisplayName,DataDictionary::getCode,(o1,o2)->o2));
                        String clsValue =
                                parentCodeDataDictionaryListMap.get(extensionContent.get(proCode.toUpperCase()));
                        Assert.notBlank(clsValue,
                                "所填分类属性未找到配置数据,所属分类:" + classification.getDisplayName() + ";对应属性:" + clsPropertyWithRel.getDisplayName() + ";未匹配属性值为:" + extensionContent.get(proCode.toUpperCase()));
                        properties.put(proCode,clsValue);
                    } else {
                        properties.put(proCode,extensionContent.get(proCode.toUpperCase()));
                    }
                } else if(propertyAssign.isRequired()){
                    errorMessage.add("分类:" + classification.getDisplayName() + " 分类属性:" + clsPropertyWithRel.getDisplayName() +" 为必填，未设置值");
                }
            }
        }
        Assert.isEmpty(errorMessage, StringUtils.join(errorMessage, ";"));
        classificationInfo.setProperties(properties);
        log.info("creo检入 分类属性:{}", properties);
//        cn.jwis.product.pdm.cad.mcad.entity.MCADIteration result =  this.mcadService.setClassification(mcadIteration, classificationInfo);
//        log.info("creo检入 设置分类属性后mcad信息:{}", JSONObject.toJSON(result));
        this.partService.setClassification(partIteration,classificationInfo);
    }

    private List<PartIteration> generateNewPart(Map<String, MCADIteration> needCreatePart, Map<String, List<String>> oidLinkMap, LocationInfo locationInfo) {
        if(MapUtil.isEmpty(needCreatePart)){
            return new ArrayList<>();
        }
        // 获取指定Part modeldefinition的MCAD的属性
        ConfigItem keyItem = preferencesService.queryConfigValue("partModelDefinitionKey");
        ConfigItem valueItem = preferencesService.queryConfigValue("partModelDefinitionValue");
        String partModelDefinitionKey = (keyItem==null ? null : keyItem.getValue());
        // 获取cad上PartModelDefinition的对应关系
        Map<String,String> partModelDefinitionMapping = initPartModelDefinitionMapping(valueItem);
        // 获取Part的所有modelDefinition类型
        List<VertexDef> vertexDefList = modelHelper.flapTree(Part.TYPE);
        Map<String,VertexDef> vertexDefMap = vertexDefList.stream().collect(Collectors.toMap(v->v.getCode(),Function.identity()));
        log.info("generateNewPart part sonTypes=" + JSON.toJSONString(vertexDefMap.keySet()));

        List<PartIteration> newPartList = new ArrayList<>();
        Map<String, CadPartMapping> mappingCache = new HashMap<>();
        CadPartMapping mappingRule = null;
        // 逐个处理，转Part
        for(Map.Entry<String,MCADIteration> entry : needCreatePart.entrySet()){
            MCADIteration mcadIteration = entry.getValue();
            Container container = jwiCommonService.findByOid(Container.TYPE, mcadIteration.getContainerOid(),
                    Container.class);
            boolean isSame = mcadIteration.getContainerOid().equals(locationInfo.getContainerOid());
            Assert.isTrue(isSame, "("+ mcadIteration.getName() + ") 已存在于容器(" + container.getName() + ") " +
                    "需要手动创建物料并关联此MCAD");
            // 获取属性映射规则
            mappingRule = getCadPartMapping(mappingCache, mcadIteration.getModelDefinition());
            PartIteration temp = preparePartCreateData(mcadIteration,mappingRule,locationInfo,oidLinkMap,
                    partModelDefinitionKey,partModelDefinitionMapping,vertexDefMap);
            if(temp != null) {
                newPartList.add(temp);
                log.info("partbom创建场景");
                checkAndSetClassificationInfo(mcadIteration,temp);
            }
        }

        //验证权限
        Set<String> modelDefines = newPartList.stream().map(PartIteration::getModelDefinition).collect(Collectors.toSet());
        if(CollectionUtil.isNotEmpty(modelDefines)){
            UserDTO currentUser = SessionHelper.getCurrentUser();
            JSONObject param = new JSONObject();
            param.put("tenantOid", currentUser.getTenantOid());
            param.put("containerOid", locationInfo.getContainerOid());
            param.put("masterType", Part.TYPE);
            //兼容文件夹权限
            param.put("catalogOid", locationInfo.getCatalogOid());
            param.put("catalogType", locationInfo.getCatalogType());

            for (String modelType : modelDefines) {
                param.put("modelDefinition", modelType);
                boolean hasAccess = PermissionHelper.hasCreatePermission(param, PermissionKeyEnum.CREATE, currentUser);
                Assert.isTrue(hasAccess, "User " + currentUser.getAccount() + " has no " + PermissionKeyEnum.CREATE + " permission for Part: " + modelType);
            }
        }
        // 创建的part
        List<PartIteration> list = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(newPartList)){
            list = commonAbilityHelper.doCreate(newPartList);
            list.forEach(item->{
                DataDistributionDTO dataDistributionDTO = new DataDistributionDTO(PartIteration.TYPE,item.getOid(),"create",SessionHelper.getAccessToken(),SessionHelper.getCurrentUser(), SessionHelper.getAppId());
                dataDistributionDTO.setBody(item);
                partHelper.dataDistribution(dataDistributionDTO); // todo 此处不创建结构
            });
        }
        return list;
    }

    private CadPartMapping getCadPartMapping(Map<String, CadPartMapping> mappingCache, String cadModel) {
        CadPartMapping mappingRule;// 获取属性映射规则
        mappingRule = mappingCache.get(cadModel);
        if (mappingRule == null){
            mappingRule = partCadThirdParty.queryAttributeMapping("", cadModel);
            Assert.notNull(mappingRule, "The property mapping rule of " + cadModel + "does not exists", JWIServiceException.class);
            mappingCache.put(cadModel, mappingRule);
        }
        return mappingRule;
    }

    private Map<String, String> initPartModelDefinitionMapping(ConfigItem valueItem) {
        Map<String, String> partModelDefinitionMapping = new HashMap<>();
        if(valueItem==null){
            return partModelDefinitionMapping;
        }
        if(StringUtil.isBlank(valueItem.getValue())){
            return partModelDefinitionMapping;
        }
        String[] arr = valueItem.getValue().split("\\|");
        for(int i = 0; i<arr.length; i++){
            String key = arr[i].split(";;;")[0];
            String value = arr[i].split(";;;")[1];
            partModelDefinitionMapping.put(key,value);
        }
        return partModelDefinitionMapping;
    }


    private void collectPart(MCADBOMNode mcadbomNode, Map<String, MCADIteration> needCreatePart,
                             LinkedHashMap<MCADIteration, List<RelationPart>> needUpdatePart,
                             Map<String, List<RelationPart>> needCurrPart,
                             List<String> staForCannotBind,Set<String> updateMcadOidSet,Map<String,List<String>> oidLinkMap) {
        if(mcadbomNode == null){
            return;
        }

        // 过滤掉 extensionContent 里 "REFERENCE" == 1 的参考件节点 2025-02-07需求
        JSONObject extensionContent = mcadbomNode.getExtensionContent();
        if (extensionContent != null && extensionContent.getInteger("REFERENCE") != null
                && extensionContent.getInteger("REFERENCE") == 1) {
            log.info("过滤掉 MCADBOMNode, OID: {}", mcadbomNode.getOid());
            return; // 直接跳过，不处理
        }


        List<RelationPart> relationParts = mcadbomNode.getRelationParts();
        // 不存在，则需要新建
        if(CollectionUtil.isEmpty(relationParts) || StringUtil.isBlank(relationParts.get(0).getOid())){
            MCADIteration mi = BeanUtil.copyProperties(mcadbomNode,new MCADIteration());
            needCreatePart.put(mcadbomNode.getOid(),mi);
        }else{
            for(RelationPart relationPart : relationParts) {
                // 检查物料的状态
                if(staForCannotBind.stream().anyMatch(b -> b.equalsIgnoreCase(relationPart.getLifecycleStatus()))){
                    Assert.isTrue(false, MessageFormat.format(
                            "{0}编码的物料状态为{1}，不支持构造BOM，请检查！",
                            relationPart.getNumber(), relationPart.getLifecycleStatus()));
                }
                String action = relationPart.getAction();
                if ("0".equals(action) || "2".equals(action)) {
                    // 标识使用当前版本的部件跟MCAD建关系 || 使用页面上搜寻到的部件跟MCAD建关系
                    putMap(needCurrPart,mcadbomNode.getOid(),relationPart);
                } else {
                    // 标识需要更新版本，然后与MCAD建关系
                    MCADIteration mi = BeanUtil.copyProperties(mcadbomNode, new MCADIteration());
                    if(updateMcadOidSet != null && updateMcadOidSet.contains(mcadbomNode.getOid())) {
                        putLinkedHashMap(needUpdatePart,mi,relationPart);
                    } else {
                        // 不进行更新，但要保存 关系
                        putMap(oidLinkMap, mi.getOid(), relationPart.getNumber() + ";;;qqq" + relationPart.getOid());
                        log.info("mcad not need update skip part update name:{},oid:{}",relationPart.getName(),
                                relationPart.getOid());
                    }
                }
            }
        }
        // 递归
        List<MCADBOMNode> children = mcadbomNode.getChildren();
        if(CollectionUtil.isEmpty(children)){
            return;
        }
        for(MCADBOMNode child : children){
            collectPart(child,needCreatePart,needUpdatePart,needCurrPart,staForCannotBind,updateMcadOidSet,oidLinkMap);
        }
    }



    private void putMap(Map map, Object key, Object value){
        Object values = map.get(key);
        if(values == null){
            values = new ArrayList<>();
            map.put(key,values);
        }
        if(!((ArrayList)values).contains(value)) {
            ((ArrayList) values).add(value);
        }
    }

    private void putLinkedHashMap(LinkedHashMap<MCADIteration, List<RelationPart>> needUpdatePart, MCADIteration mi, RelationPart relationPart) {
        List<RelationPart> values = needUpdatePart.remove(mi);
        if(values == null){
            values = new ArrayList<>();
        }
        if(!(values).contains(relationPart)) {
            (values).add(relationPart);
        }

        needUpdatePart.put(mi,values);
    }

}
