package cn.jwis.product.pdm.customer.service.interf;


import cn.jwis.platform.plm.modelexcel.dto.ModelExcelAllImportDTO;
import cn.jwis.product.pdm.customer.service.dto.ExtDocumentCreateDTO;
import cn.jwis.product.pdm.document.dto.DocSignWorkflowDTO;
import cn.jwis.product.pdm.document.dto.DwgSignWorkflowDTO;
import cn.jwis.product.pdm.document.entity.DocumentIteration;
import cn.jwis.product.pdm.document.service.DocumentHelper;
import com.alibaba.fastjson.JSONObject;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

/**
 * @author: 汪江
 * @data: 2023-12-23 11:07
 * @description
 **/
public interface CustomerDocumentHelper extends DocumentHelper {
    void importExcelTemp(@Valid ModelExcelAllImportDTO importDTO, HttpServletRequest request);

    void importUnzipFile(JSONObject jsonObject);

    String checkImportHisData(MultipartFile file);

    DocumentIteration create(ExtDocumentCreateDTO dto);

    boolean ecadDegSignWorkflow(JSONObject dto);


    boolean docSignWorkflowForIds(DocSignWorkflowDTO docSignWorkflowDTO);


    boolean degSignByDingTalk(DwgSignWorkflowDTO dwgSignWorkflowDTO);

}
