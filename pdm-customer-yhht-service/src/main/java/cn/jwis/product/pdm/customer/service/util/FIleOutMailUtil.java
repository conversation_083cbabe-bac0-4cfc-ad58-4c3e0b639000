package cn.jwis.product.pdm.customer.service.util;

import cn.hutool.core.io.FileUtil;
import cn.hutool.extra.mail.MailAccount;
import cn.hutool.extra.mail.MailUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.Arrays;
import java.util.List;

@Component
@Slf4j
public class FIleOutMailUtil {

    @Value("${spring.mail.host}")
    static String host;
    @Value("${spring.mail.username}")
    static String username;
    @Value("${spring.mail.password}")
    static String password;

    private static MailAccount mailAccount;

    /**
     * 使用 Spring 配置初始化邮件账号
     */
    /**
     * 使用 Spring 配置初始化邮件账号
     */
    public FIleOutMailUtil(
            @Value("${spring.mail.host}") String host,
            @Value("${spring.mail.username}") String username,
            @Value("${spring.mail.password}") String password
    ) {
        mailAccount = new MailAccount();
        mailAccount.setHost(host);
        mailAccount.setAuth(true);
        mailAccount.setUser(username); // 登录邮箱的用户名
        mailAccount.setPass(password); // 邮箱授权码
    }

    /**
     * 设置发件人名称（用户名保持不变）
     *
     * @param senderName 发件人名称
     */
    private static void setFrom(String senderName) {
        String from = senderName + "<" + mailAccount.getUser() + ">";  // 动态设置发件人名称 + 固定的邮箱地址
        mailAccount.setFrom(from);
    }

    /**
     * 发送简单文本邮件
     *
     * @param senderName 发件人名称
     * @param to         收件人地址
     * @param subject    邮件主题
     * @param content    邮件正文
     */
    public static void sendSimpleMail(String senderName, String to, String subject, String content) {
        setFrom(senderName);  // 动态设置发件人名称
        MailUtil.send(mailAccount, to, subject, content, false);
    }

    /**
     * 发送带附件的邮件
     *
     * @param senderName 发件人名称
     * @param to         收件人地址
     * @param subject    邮件主题
     * @param content    邮件正文
     * @param filePath   附件路径
     */
    public static void sendMailWithAttachment(String senderName, String to, String subject, String content, File filePath) {
        setFrom(senderName);  // 动态设置发件人名称
        MailUtil.send(mailAccount, to, subject, content, true, filePath);
    }

    /**
     * 发送多人邮件
     *
     * @param senderName 发件人名称
     * @param toList     收件人地址列表
     * @param subject    邮件主题
     * @param content    邮件正文
     * @param filePath   附件路径
     */
    public void sendMailToMultipleRecipients(String senderName, List<String> toList, String subject, String content, List<File> filePath) {
        setFrom(senderName);  // 动态设置发件人名称
        // 打印所有参数信息
        log.info("Sending mail with the following parameters:");
        log.info("Recipients (toList): {}", toList);
        log.info("Subject: {}", subject);
        log.info("Content: {}", content);

        if (filePath != null && !filePath.isEmpty()) {
            log.info("Attachments: ");
            for (File file : filePath) {
                log.info("  - {}", file.getAbsolutePath());
            }
        } else {
            log.info("No attachments.");
        }
        MailUtil.send(mailAccount, toList, subject, content, true, filePath.toArray(new File[0]));
    }

    public static void main(String[] args) {
//        MailAccount mailAccount = new MailAccount();
//        mailAccount.setHost("smtp.qiye.aliyun.com");
//        mailAccount.setAuth(true);
//        mailAccount.setFrom("<EMAIL>"); // 发件人必须是完整的邮箱地址
//        mailAccount.setUser("<EMAIL>"); // 登录邮箱的用户名
//        mailAccount.setPass("yinhe@1o%2?5"); // 邮箱授权码
        File file = FileUtil.file("/Users/<USER>/Documents/测试文件.docx");
//        MailUtil.send(mailAccount, "<EMAIL>", "这是主题", "这是邮件内容", false);
//        MailUtil.send(mailAccount, "<EMAIL>", "PDM外发文件", "这是邮件内容", false,file);


        List<String> list = Arrays.asList("<EMAIL>");
        MailUtil.send(mailAccount, list, "PDM外发文件", "", false,file);


    }
}