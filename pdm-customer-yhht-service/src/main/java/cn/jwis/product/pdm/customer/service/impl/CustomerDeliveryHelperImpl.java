//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package cn.jwis.product.pdm.customer.service.impl;

import cn.jwis.audit.annotation.JWIParam;
import cn.jwis.audit.annotation.JWIServiceAudit;
import cn.jwis.audit.enums.Action;
import cn.jwis.framework.base.bean.util.BeanUtil;
import cn.jwis.framework.base.util.Assert;
import cn.jwis.framework.base.util.ValidationUtil;
import cn.jwis.framework.base.web.session.SessionHelper;
import cn.jwis.framework.database.core.entity.FromToFilter;
import cn.jwis.framework.database.core.query.dynamic.Condition;
import cn.jwis.platform.plm.dataengine.common.JWICommonService;
import cn.jwis.platform.plm.foundation.common.service.CommonAbilityHelper;
import cn.jwis.platform.plm.foundation.common.service.CommonAbilityService;
import cn.jwis.platform.plm.foundation.location.able.info.LocationInfo;
import cn.jwis.platform.plm.foundation.model.service.ModelHelper;
import cn.jwis.platform.plm.foundation.relationship.Contain;
import cn.jwis.product.pdm.customer.entity.CustomerDeliveryTreeNode;
import cn.jwis.product.pdm.customer.repo.CustomerCommonRepo;
import cn.jwis.product.pdm.delivery.dto.AddInstanceToDeliveryDTO;
import cn.jwis.product.pdm.delivery.dto.DeleteInstanceToDeliveryDTO;
import cn.jwis.product.pdm.delivery.dto.DeliveryCreateDTO;
import cn.jwis.product.pdm.delivery.dto.DeliveryUpdateDTO;
import cn.jwis.product.pdm.delivery.entity.Delivery;
import cn.jwis.product.pdm.delivery.entity.DeliveryTreeNode;
import cn.jwis.product.pdm.delivery.helper.DeliveryHelperImpl;
import cn.jwis.product.pdm.delivery.service.DeliveryService;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@Transactional
@Primary
public class CustomerDeliveryHelperImpl extends DeliveryHelperImpl {
    private static final Logger log = LoggerFactory.getLogger(DeliveryHelperImpl.class);
    @Autowired
    CommonAbilityHelper commonAbilityHelper;
    @Autowired
    DeliveryService deliveryService;
    @Autowired
    CommonAbilityService commonAbilityService;
    @Autowired
    ModelHelper modelHelper;

    @Resource
    JWICommonService commonService;

    @Resource
    CustomerCommonRepo customerCommonRepo;

    @Resource
    TriggerAuditServiceImpl triggerAuditService;

    public CustomerDeliveryHelperImpl() {
    }

    @JWIServiceAudit(
            buzOid = "${result.oid}",
            buzType = "${result.type}",
            bizNumber = "${result.number}",
            bizName = "${result.name}",
            action = Action.ADD,
            content = "创建交付清单,名称:${result.name},标识${result.oid}"
    )
    @JWIParam("result")
    @Override
    public Delivery create(DeliveryCreateDTO dto) {
        ValidationUtil.validate(dto);
        Delivery newDelivery = BeanUtil.copyProperties(dto, new Delivery());
        newDelivery.setOid(null);
        //若同父级存在重名的节点，直接返回当前重名节点
        Delivery repeat = checkNameRepeat(dto.getParentOid(), dto.getName(), StringUtils.EMPTY,
                dto.getLocationInfo().getContainerOid());
        if(ObjectUtils.isNotEmpty(repeat)) {
            return repeat;
        }
        LocationInfo locationInfo = dto.getLocationInfo();
        this.deliveryService.setLocation(newDelivery, locationInfo);
        Delivery delivery = this.commonAbilityHelper.doCreate(newDelivery);
        String parentOid = dto.getParentOid();
        if (StringUtils.isNotBlank(parentOid)) {
            this.deliveryService.createContainChild(parentOid, delivery.getOid());
        }

        return delivery;
    }

    @JWIServiceAudit(
            buzOid = "${result.oid}",
            buzType = "${result.type}",
            bizNumber = "${result.number}",
            bizName = "${result.name}",
            action = Action.UPDATE,
            content = "更新交付清单,名称:${result.name}"
    )
    @JWIParam("result")
    @Override
    public Delivery update(DeliveryUpdateDTO dto) {
        ValidationUtil.validate(dto);
        Assert.notNull(dto.getOid(), "oid must be not null.");
        Delivery entity = this.deliveryService.findByOid(dto.getOid());
        if (Objects.isNull(entity)) {
            return null;
        } else {
            BeanUtil.copyProperties(dto, entity);
            //若同父级存在重名的节点，直接返回当前重名节点
            Delivery repeat = checkNameRepeat(queryParentOid(dto.getOid()), dto.getName(), dto.getOid(),entity.getContainerOid());

            JSONObject extension = entity.getExtensionContent();
            Object FZR = null, jhsj = null, wcsj = null;
            Map FZRMap ;
            if(extension != null){
                if((FZRMap = (Map) extension.get("FZR")) != null)
                    FZR = new JSONObject().fluentPut("oid", FZRMap.get("oid")).fluentPut("name", FZRMap.get("name")).toJSONString();
                jhsj = extension.get("jhsj");
                wcsj = extension.get("wcsj");
            }
            customerCommonRepo.updateDeliveryExtension(entity.getOid(), FZR, jhsj, wcsj);

            return ObjectUtils.isNotEmpty(repeat) ? repeat : this.commonAbilityHelper.doUpdate(entity);
        }
    }

    private DeliveryTreeNode checkNameRepeat(String parentOid,String name,String nowOid,String containerOid) {
        List<DeliveryTreeNode> children;
        if(StringUtils.isBlank(parentOid)) {
            children = this.commonService.dynamicQuery(Delivery.TYPE,
                    Condition.where("root").eq(true).and(Condition.where("containerOid").eq(containerOid)),
                    DeliveryTreeNode.class);
        } else {
            children = this.deliveryService.findChildrenByDeliveryOid(parentOid);
        }

        List<DeliveryTreeNode> repeatList =
                children.stream().filter(item->name.equals(item.getName())).filter(item->StringUtils.isBlank(nowOid) || nowOid.equals(item.getOid())).collect(Collectors.toList());
        return repeatList.stream().findAny().orElse(null);
    }

    private String queryParentOid(String deliveryOid) {
        FromToFilter filter = new FromToFilter();
        filter.setFromType(Delivery.TYPE);
        filter.setType(Contain.TYPE);
        filter.setToType(Delivery.TYPE);
        filter.setToFilter(Condition.where("oid").eq(deliveryOid));
        Delivery parent = commonService.dynamicQueryFrom(filter,Delivery.class).stream().findAny().orElse(null);
        return ObjectUtils.isEmpty(parent) ? StringUtils.EMPTY : parent.getOid();
    }

    public List<CustomerDeliveryTreeNode> querySelectDeliveryTree(String containerOid) {
        String userOid = SessionHelper.getCurrentUser().getOid();
        if (StringUtils.isBlank(userOid)) {
            return null;
        }
        List<CustomerDeliveryTreeNode> nodeList = customerCommonRepo.querySelectDeliveryTree(containerOid,userOid);

        List<CustomerDeliveryTreeNode> categoryList =
                nodeList.stream().filter(item->"Category".equalsIgnoreCase(item.getModelDefinition())).collect(Collectors.toList());
        nodeList.remove(categoryList);

        Map<String,List<CustomerDeliveryTreeNode>> parentListMap =
                categoryList.stream().collect(Collectors.groupingBy(CustomerDeliveryTreeNode::getParentOid));
        if(ObjectUtils.isEmpty(parentListMap)) {
            return new ArrayList<>();
        }
        Map<String, CustomerDeliveryTreeNode> parentMap = nodeList.stream()
                .collect(Collectors.toMap(CustomerDeliveryTreeNode::getOid, obj -> obj,
                        (existing, replacement) -> replacement));

        for (Map.Entry<String, List<CustomerDeliveryTreeNode>> entry : parentListMap.entrySet()) {
            if (parentMap.containsKey(entry.getKey())) {
                parentMap.get(entry.getKey()).getChildren().addAll(entry.getValue());
            }
        }

        List<CustomerDeliveryTreeNode> rootList=
                nodeList.stream().filter(item->StringUtils.equals(item.getParentOid(),item.getOid()) && CollectionUtils.isNotEmpty(item.getChildren())).peek(item->item.setRoot(true)).collect(Collectors.toList());
        return rootList;
    }

    @Override
    public Long delete(String oid) {
        Delivery delivery = commonService.findByOid(Delivery.TYPE,oid,Delivery.class);
        Long result = super.delete(oid);
        triggerAuditService.deleteDelivery(delivery);
        return result;
    }

    @Override
    public void batchAddInstance(AddInstanceToDeliveryDTO dto) {
        super.batchAddInstance(dto);
        Delivery delivery = commonService.findByOid(Delivery.TYPE,dto.getDeliveryOid(),Delivery.class);
        dto.getSecObjList().forEach(item -> triggerAuditService.batchAddInstance(item, delivery));
    }

    @Override
    public void batchDeleteInstance(DeleteInstanceToDeliveryDTO dto) {
        super.batchDeleteInstance(dto);
        Delivery delivery = commonService.findByOid(Delivery.TYPE,dto.getDeliveryOid(),Delivery.class);
        dto.getSecObjList().forEach(item -> triggerAuditService.batchDeleteInstance(item, delivery));
    }

    public Object querySelectDeliveryTreeNew(String containerOid) {
        String userOid = SessionHelper.getCurrentUser().getOid();
        if (StringUtils.isBlank(userOid)) {
            return null; // 返回 null 或空集合
        }

        List<CustomerDeliveryTreeNode> nodeList = customerCommonRepo.querySelectDeliveryTreeNew(containerOid, userOid);

        // 通过 oid 去重，只保留每个 oid 的第一个节点
        List<CustomerDeliveryTreeNode> uniqueNodeList = nodeList.stream()
                .collect(Collectors.toMap(
                        CustomerDeliveryTreeNode::getOid,  // 使用 oid 作为键
                        node -> node,                     // 使用节点对象作为值
                        (existing, replacement) -> existing // 遇到重复键时保留已有节点
                ))
                .values()
                .stream()
                .collect(Collectors.toList());       // 收集为新的列表

        // 创建根节点列表
        List<CustomerDeliveryTreeNode> rootList = new ArrayList<>();

        // 构建树形结构
        for (CustomerDeliveryTreeNode node : uniqueNodeList) {
            if (node.getParentOid().equals(node.getOid())) {
                rootList.add(node); // 将根节点添加到根节点列表
            } else {
                // 获取去重后的父节点
                CustomerDeliveryTreeNode parentNode = uniqueNodeList.stream()
                        .filter(n -> n.getOid().equals(node.getParentOid()))
                        .findFirst()
                        .orElse(null);
                if (parentNode != null) {
                    parentNode.getChildren().add(node); // 将当前节点添加到父节点的 children 列表
                }
            }
        }

        // 递归过滤：保留结构中最子级节点是 Category，且父节点是 Structure 的树层级
        rootList = rootList.stream()
                .filter(this::filterStructureWithCategoryLeafOnly)
                .collect(Collectors.toList());

        // 返回处理后的根节点列表，或空列表
        return rootList.isEmpty() ? new ArrayList<>() : rootList;
    }

    /**
     * 递归过滤树节点，仅保留最底层为 Category，父级为 Structure 的节点结构
     */
    private boolean filterStructureWithCategoryLeafOnly(CustomerDeliveryTreeNode node) {
        // 如果节点类型为 Structure
        if ("Structure".equals(node.getModelDefinition())) {
            // 递归过滤子节点
            node.setChildren(node.getChildren().stream()
                    .filter(this::filterStructureWithCategoryLeafOnly)
                    .collect(Collectors.toList()));

            // 保留有子节点且子节点类型为 Category 的 Structure 节点
            return !node.getChildren().isEmpty();
        }

        // 如果节点类型为 Category，且没有子节点，则保留它作为叶子节点
        return "Category".equals(node.getModelDefinition()) && node.getChildren().isEmpty();
    }
}
