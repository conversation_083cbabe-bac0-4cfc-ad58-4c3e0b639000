package cn.jwis.product.pdm.customer.service.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:U9单据
 * @date 2023/9/8 16:06
 * @Email <EMAIL>
 */
@Data
@EqualsAndHashCode
public class U9Transit {
    //物料编码
    private String itemCode;
    //单据类型名称，如：采购订单
    private String DocType;
    //单号
    private String DocNo;
    //创建人邮箱前缀
    private String CreatedBy;
    //组织编码
    private String OrgCode;
    //组织名称
    private String OrgName;

}
