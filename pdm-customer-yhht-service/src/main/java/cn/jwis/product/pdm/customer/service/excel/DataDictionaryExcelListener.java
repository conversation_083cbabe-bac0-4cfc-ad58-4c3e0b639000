package cn.jwis.product.pdm.customer.service.excel;

import cn.jwis.framework.base.bean.util.BeanUtil;
import cn.jwis.framework.base.util.SpringContextUtil;
import cn.jwis.framework.base.util.ValidationUtil;
import cn.jwis.product.pdm.customer.entity.DataDictionary;
import cn.jwis.product.pdm.customer.service.dto.DataDictionaryDTO;
import cn.jwis.product.pdm.customer.service.dto.DataDictionaryExcelDTO;
import cn.jwis.product.pdm.customer.service.interf.DataDictionaryService;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Component
public class DataDictionaryExcelListener implements ReadListener<DataDictionaryExcelDTO> {


    private List<DataDictionaryExcelDTO> excelDTOS = new ArrayList<>();

    @Override
    public void invoke(DataDictionaryExcelDTO dataDictionaryExcelDTO, AnalysisContext analysisContext) {
        ValidationUtil.validate(dataDictionaryExcelDTO);
        excelDTOS.add(dataDictionaryExcelDTO);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        DataDictionaryService dataDictionaryService = SpringContextUtil.getBean(DataDictionaryService.class);
        for (DataDictionaryExcelDTO excelDTO : excelDTOS) {
            DataDictionary dataDictionary = new DataDictionaryDTO();
            BeanUtil.copyProperties(excelDTO, dataDictionary);
            dataDictionaryService.save(dataDictionary, Boolean.TRUE);
        }

    }
}
