package cn.jwis.product.pdm.customer.service.interf;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.List;
import java.util.Map;

public interface ExcelExportService {

    default void cvtDataExportWeb(String fileName, Map<String, List<?>> multiSheetEntityDataMap, HttpServletResponse response){ }

    default InputStream cvtDataExportInputStream(Map<String, List<?>> multiSheetEntityDataMap){
        return null;
    }

    default OutputStream cvtDataExportOutputStream(Map<String, List<?>> multiSheetEntityDataMap){
        return null;
    }

}
