package cn.jwis.product.pdm.customer.service.dto;

import cn.jwis.platform.plm.foundation.attachment.entity.File;
import cn.jwis.platform.plm.foundation.location.able.info.LocationInfo;
import cn.jwis.product.pdm.partbom.part.dto.PartUpdateDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * <AUTHOR> lhm
 * @Date ： Created in 2024/1/17 17:05
 * @Description :
 */
@Data
@EqualsAndHashCode
public class CustomerPartUpdateDTO extends PartUpdateDTO {

    @ApiModelProperty("编码")
    private String number;

    @ApiModelProperty("类型")
    private String type = "PartIteration";
    @NotBlank(
            message = "模型类型不能为空"
    )
    @ApiModelProperty("模型类型")
    private String modelDefinition;

    @ApiModelProperty("位置信息")
    private LocationInfo locationInfo;

    @ApiModelProperty("版本")
    private String version;
    private String iteratedVersion;

    @ApiModelProperty("生命周期")
    private String lifecycleStatus;

    @ApiModelProperty("主文件")
    private List<File> primaryFile;
    private List<File> secondaryFile;

    @ApiModelProperty("数据是否新建标识符")
    private boolean createFlag ;

    @ApiModelProperty("所有者")
    private String owner;

    private String excelVersion;
    private String excelIteratedVersion;
    private String relationNumber;


}
