package cn.jwis.product.pdm.customer.service.util;

import cn.jwis.platform.plm.file.config.MinIoConfig;
import cn.jwis.platform.plm.file.utils.MinioUtil;
import com.alibaba.fastjson.JSONObject;
import io.minio.GetPresignedObjectUrlArgs;
import io.minio.MinioClient;
import io.minio.ObjectStat;
import io.minio.StatObjectArgs;
import io.minio.errors.*;
import io.minio.http.Method;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
@Primary
public class CustomerMinioUtil extends MinioUtil {
    @Autowired
    private MinIoConfig minIoConfig;
    @Value("${minio.default.bucket}")
    private String bucketName;

    @Override
    public boolean bucketExist(String bucketName) throws IOException, InvalidKeyException, InvalidResponseException, InsufficientDataException, NoSuchAlgorithmException, ServerException, InternalException, XmlParserException, InvalidBucketNameException, ErrorResponseException {
        //2025.07.23 bucketExist会触发Minio中 ListBucket 权限验证 ，配置中默认bucket存在
        return this.bucketName.equals(bucketName);
    }


    @Override
    public String getObjectUrl(String bucketName, String objectName) throws IOException, InvalidResponseException, InvalidKeyException, NoSuchAlgorithmException, ServerException, ErrorResponseException, XmlParserException, InvalidBucketNameException, InsufficientDataException, InternalException {
        String url = "";
        MinioClient minioClient = this.getObjectSite(bucketName, objectName);
        if (minioClient != null) {
            url = minioClient.getObjectUrl(bucketName, objectName);
        }

        return url;
    }

    @Override
    public MinioClient getObjectSite(String bucketName, String objectName) throws IOException, InvalidResponseException, InvalidKeyException, NoSuchAlgorithmException, ServerException, ErrorResponseException, XmlParserException, InvalidBucketNameException, InsufficientDataException, InternalException {
        ObjectStat statObject = null;
        log.info("getObjectSite.bucketName=" + bucketName + ",objectName=" + objectName);
        log.info("getObjectSite.minIoConfig.getMinioClient.size=" + JSONObject.toJSONString(this.minIoConfig.getMinioClient().size()));

        for(int i = 0; i < this.minIoConfig.getMinioClient().size(); ++i) {
            MinioClient minioClient = (MinioClient)this.minIoConfig.getMinioClient().get(i);
//            boolean flag = minioClient.bucketExists((BucketExistsArgs)((BucketExistsArgs.Builder)BucketExistsArgs.builder().bucket(bucketName)).build());
            boolean flag = this.bucketName.equals(bucketName);
            if (flag) {
                try {
                    statObject = minioClient.statObject((StatObjectArgs)((StatObjectArgs.Builder)((StatObjectArgs.Builder)StatObjectArgs.builder().bucket(bucketName)).object(objectName)).build());
                } catch (Exception var8) {
                    continue;
                }

                if (statObject != null) {
                    log.info("getObjectSite.minIoConfig.target=" + minioClient.getObjectUrl(bucketName, objectName));
                    return minioClient;
                }
            }
        }

        log.info("getObjectSite.minIoConfig.target is null!");
        return null;
    }

    /**
     * 获取预签名对象URL（带鉴权参数）- 用于上传
     * 注意：此方法使用PUT方法，主要用于文件上传
     * 如需下载/预览，请使用getPresignedDownloadUrl方法
     *
     * @param bucketName 存储桶名称
     * @param objectName 对象名称
     * @return 带鉴权参数的预签名URL
     * @throws InvalidExpiresRangeException 无效过期时间范围异常
     * @throws IOException IO异常
     * @throws InvalidResponseException 无效响应异常
     * @throws InvalidKeyException 无效密钥异常
     * @throws NoSuchAlgorithmException 算法不存在异常
     * @throws ServerException 服务器异常
     * @throws ErrorResponseException 错误响应异常
     * @throws XmlParserException XML解析异常
     * @throws InvalidBucketNameException 无效存储桶名称异常
     * @throws InsufficientDataException 数据不足异常
     * @throws InternalException 内部异常
     */
   /* @Override
    public String getPresignedObjectUrl(String bucketName, String objectName) throws InvalidExpiresRangeException,
            IOException, InvalidResponseException, InvalidKeyException, NoSuchAlgorithmException,
            ServerException, ErrorResponseException, XmlParserException, InvalidBucketNameException,
            InsufficientDataException, InternalException {

        long startTime = System.currentTimeMillis();
        log.info("[CustomerMinioUtil] 开始生成预签名URL, bucketName: {}, objectName: {}", bucketName, objectName);

        try {
            // 参数验证
            if (!StringUtils.hasText(bucketName)) {
                log.error("[CustomerMinioUtil] 存储桶名称为空");
                return "";
            }

            if (!StringUtils.hasText(objectName)) {
                log.error("[CustomerMinioUtil] 对象名称为空");
                return "";
            }

            // 获取MinIO客户端列表
            List<MinioClient> minioClients = this.minIoConfig.getMinioClient();
            if (minioClients == null || minioClients.isEmpty()) {
                log.error("[CustomerMinioUtil] MinIO客户端列表为空");
                return "";
            }

            // 首先尝试找到包含该对象的客户端
            MinioClient targetClient = getObjectSite(bucketName, objectName);
            if (targetClient == null) {
                log.warn("[CustomerMinioUtil] 未找到包含对象的客户端，使用第一个客户端生成预签名URL");
                targetClient = minioClients.get(0);
            }

            // 检查存储桶是否存在
            boolean bucketExists = this.bucketExist(bucketName);
            if (!bucketExists) {
                log.error("[CustomerMinioUtil] 存储桶不存在: {}", bucketName);
                return "";
            }

            // 生成预签名URL（PUT方法，用于上传）
            // 设置过期时间为10分钟
            *//*String presignedUrl = targetClient.getPresignedObjectUrl(
                GetPresignedObjectUrlArgs.builder()
                    .method(Method.PUT)  // 使用PUT方法进行上传
                    .bucket(bucketName)
                    .object(objectName)
                    .build()
            );*//*
            String presignedUrl  = targetClient.presignedGetObject(bucketName, objectName);

            long duration = System.currentTimeMillis() - startTime;
            log.info("[CustomerMinioUtil] 成功生成预签名URL, bucketName: {}, objectName: {}, 耗时: {}ms",
                    bucketName, objectName, duration);
            log.info("[CustomerMinioUtil] 预签名URL: {}", presignedUrl);

            return presignedUrl;

        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("[CustomerMinioUtil] 生成预签名URL失败, bucketName: {}, objectName: {}, 耗时: {}ms, 错误: {}",
                    bucketName, objectName, duration, e.getMessage(), e);
            throw e;
        }
    }*/

    /**
     * 获取下载/预览用的预签名URL（GET方法）
     *
     * @param bucketName 存储桶名称
     * @param objectName 对象名称
     * @param expiryMinutes 过期时间（分钟）
     * @return 用于下载/预览的预签名URL
     */
    public String getPresignedDownloadUrl(String bucketName, String objectName, int expiryMinutes)
            throws InvalidExpiresRangeException, IOException, InvalidResponseException, InvalidKeyException,
            NoSuchAlgorithmException, ServerException, ErrorResponseException, XmlParserException,
            InvalidBucketNameException, InsufficientDataException, InternalException {

        long startTime = System.currentTimeMillis();
        log.info("[CustomerMinioUtil] 开始生成下载预签名URL, bucketName: {}, objectName: {}, expiryMinutes: {}",
                bucketName, objectName, expiryMinutes);

        try {
            // 参数验证
            if (!StringUtils.hasText(bucketName)) {
                log.error("[CustomerMinioUtil] 存储桶名称为空");
                return "";
            }

            if (!StringUtils.hasText(objectName)) {
                log.error("[CustomerMinioUtil] 对象名称为空");
                return "";
            }

            if (expiryMinutes <= 0 || expiryMinutes > 7 * 24 * 60) {
                log.warn("[CustomerMinioUtil] 过期时间无效，使用默认10分钟: {}", expiryMinutes);
                expiryMinutes = 10;
            }

            // 找到包含该对象的客户端
            MinioClient targetClient = getObjectSite(bucketName, objectName);
            if (targetClient == null) {
                log.warn("[CustomerMinioUtil] 未找到包含对象的客户端，使用第一个客户端");
                List<MinioClient> minioClients = this.minIoConfig.getMinioClient();
                if (minioClients == null || minioClients.isEmpty()) {
                    log.error("[CustomerMinioUtil] MinIO客户端列表为空");
                    return "";
                }
                targetClient = minioClients.get(0);
            }

            // 生成下载用的预签名URL（GET方法）
            String presignedUrl = targetClient.getPresignedObjectUrl(
                GetPresignedObjectUrlArgs.builder()
                    .method(Method.GET)  // 使用GET方法进行下载/预览
                    .bucket(bucketName)
                    .object(objectName)
                    .expiry(expiryMinutes, TimeUnit.MINUTES)
                    .build()
            );

            long duration = System.currentTimeMillis() - startTime;
            log.info("[CustomerMinioUtil] 成功生成下载预签名URL, bucketName: {}, objectName: {}, expiryMinutes: {}, 耗时: {}ms",
                    bucketName, objectName, expiryMinutes, duration);

            return presignedUrl;

        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("[CustomerMinioUtil] 生成下载预签名URL失败, bucketName: {}, objectName: {}, expiryMinutes: {}, 耗时: {}ms, 错误: {}",
                    bucketName, objectName, expiryMinutes, duration, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 获取预签名对象URL（自定义过期时间）
     * 注意：此方法现在用于上传，如需下载请使用getPresignedDownloadUrl
     *
     * @param bucketName 存储桶名称
     * @param objectName 对象名称
     * @param expiryMinutes 过期时间（分钟）
     * @return 带鉴权参数的预签名URL
     */
    public String getPresignedObjectUrl(String bucketName, String objectName, int expiryMinutes)
            throws InvalidExpiresRangeException, IOException, InvalidResponseException, InvalidKeyException,
            NoSuchAlgorithmException, ServerException, ErrorResponseException, XmlParserException,
            InvalidBucketNameException, InsufficientDataException, InternalException {

        long startTime = System.currentTimeMillis();
        log.info("[CustomerMinioUtil] 开始生成预签名URL（自定义过期时间）, bucketName: {}, objectName: {}, expiryMinutes: {}",
                bucketName, objectName, expiryMinutes);

        try {
            // 参数验证
            if (!StringUtils.hasText(bucketName)) {
                log.error("[CustomerMinioUtil] 存储桶名称为空");
                return "";
            }

            if (!StringUtils.hasText(objectName)) {
                log.error("[CustomerMinioUtil] 对象名称为空");
                return "";
            }

            if (expiryMinutes <= 0 || expiryMinutes > 7 * 24 * 60) { // 最大7天
                log.warn("[CustomerMinioUtil] 过期时间无效，使用默认10分钟: {}", expiryMinutes);
                expiryMinutes = 10;
            }

            // 获取MinIO客户端
            List<MinioClient> minioClients = this.minIoConfig.getMinioClient();
            if (minioClients == null || minioClients.isEmpty()) {
                log.error("[CustomerMinioUtil] MinIO客户端列表为空");
                return "";
            }

            // 找到包含该对象的客户端
            MinioClient targetClient = getObjectSite(bucketName, objectName);
            if (targetClient == null) {
                log.warn("[CustomerMinioUtil] 未找到包含对象的客户端，使用第一个客户端");
                targetClient = minioClients.get(0);
            }

            // 生成预签名URL
            String presignedUrl = targetClient.getPresignedObjectUrl(
                GetPresignedObjectUrlArgs.builder()
                    .method(Method.GET)
                    .bucket(bucketName)
                    .object(objectName)
                    .expiry(expiryMinutes, TimeUnit.MINUTES)
                    .build()
            );

            long duration = System.currentTimeMillis() - startTime;
            log.info("[CustomerMinioUtil] 成功生成预签名URL（自定义过期时间）, bucketName: {}, objectName: {}, expiryMinutes: {}, 耗时: {}ms",
                    bucketName, objectName, expiryMinutes, duration);

            return presignedUrl;

        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("[CustomerMinioUtil] 生成预签名URL失败（自定义过期时间）, bucketName: {}, objectName: {}, expiryMinutes: {}, 耗时: {}ms, 错误: {}",
                    bucketName, objectName, expiryMinutes, duration, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 获取上传用的预签名URL（PUT方法）
     * 用于客户端直接上传文件到MinIO
     *
     * @param bucketName 存储桶名称
     * @param objectName 对象名称
     * @param expiryMinutes 过期时间（分钟）
     * @return 用于上传的预签名URL
     */
    public String getPresignedUploadUrl(String bucketName, String objectName, int expiryMinutes)
            throws InvalidExpiresRangeException, IOException, InvalidResponseException, InvalidKeyException,
            NoSuchAlgorithmException, ServerException, ErrorResponseException, XmlParserException,
            InvalidBucketNameException, InsufficientDataException, InternalException {

        long startTime = System.currentTimeMillis();
        log.info("[CustomerMinioUtil] 开始生成上传预签名URL, bucketName: {}, objectName: {}, expiryMinutes: {}",
                bucketName, objectName, expiryMinutes);

        try {
            // 参数验证
            if (!StringUtils.hasText(bucketName)) {
                log.error("[CustomerMinioUtil] 存储桶名称为空");
                return "";
            }

            if (!StringUtils.hasText(objectName)) {
                log.error("[CustomerMinioUtil] 对象名称为空");
                return "";
            }

            if (expiryMinutes <= 0 || expiryMinutes > 7 * 24 * 60) {
                log.warn("[CustomerMinioUtil] 过期时间无效，使用默认10分钟: {}", expiryMinutes);
                expiryMinutes = 10;
            }

            // 获取MinIO客户端（使用第一个客户端进行上传）
            List<MinioClient> minioClients = this.minIoConfig.getMinioClient();
            if (minioClients == null || minioClients.isEmpty()) {
                log.error("[CustomerMinioUtil] MinIO客户端列表为空");
                return "";
            }

            MinioClient minioClient = minioClients.get(0);

            // 生成上传用的预签名URL
            String presignedUrl = minioClient.getPresignedObjectUrl(
                GetPresignedObjectUrlArgs.builder()
                    .method(Method.PUT)  // 使用PUT方法进行上传
                    .bucket(bucketName)
                    .object(objectName)
                    .expiry(expiryMinutes, TimeUnit.MINUTES)
                    .build()
            );

            long duration = System.currentTimeMillis() - startTime;
            log.info("[CustomerMinioUtil] 成功生成上传预签名URL, bucketName: {}, objectName: {}, expiryMinutes: {}, 耗时: {}ms",
                    bucketName, objectName, expiryMinutes, duration);

            return presignedUrl;

        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("[CustomerMinioUtil] 生成上传预签名URL失败, bucketName: {}, objectName: {}, expiryMinutes: {}, 耗时: {}ms, 错误: {}",
                    bucketName, objectName, expiryMinutes, duration, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 验证预签名URL是否有效
     * 通过发送HEAD请求来验证URL的有效性
     *
     * @param presignedUrl 预签名URL
     * @return 是否有效
     */
    public boolean validatePresignedUrl(String presignedUrl) {
        if (!StringUtils.hasText(presignedUrl)) {
            log.warn("[CustomerMinioUtil] 预签名URL为空");
            return false;
        }

        try {
            // 这里可以添加URL格式验证逻辑
            // 检查URL是否包含必要的签名参数
            boolean hasSignature = presignedUrl.contains("X-Amz-Signature") || presignedUrl.contains("Signature");
            boolean hasCredential = presignedUrl.contains("X-Amz-Credential") || presignedUrl.contains("Credential");
            boolean hasExpires = presignedUrl.contains("X-Amz-Expires") || presignedUrl.contains("Expires");

            boolean isValid = hasSignature && hasCredential && hasExpires;
            log.debug("[CustomerMinioUtil] 预签名URL验证结果: {}, hasSignature: {}, hasCredential: {}, hasExpires: {}",
                    isValid, hasSignature, hasCredential, hasExpires);

            return isValid;

        } catch (Exception e) {
            log.error("[CustomerMinioUtil] 验证预签名URL时发生异常: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 验证对象是否存在于任何客户端中
     *
     * @param bucketName 存储桶名称
     * @param objectName 对象名称
     * @return 是否存在
     */
    public boolean verifyObjectExists(String bucketName, String objectName) {
        long startTime = System.currentTimeMillis();
        log.info("[CustomerMinioUtil] 开始验证对象是否存在: {}/{}", bucketName, objectName);

        try {
            // 参数验证
            if (!StringUtils.hasText(bucketName) || !StringUtils.hasText(objectName)) {
                log.error("[CustomerMinioUtil] 参数为空: bucketName={}, objectName={}", bucketName, objectName);
                return false;
            }

            // 在所有客户端中查找对象
            MinioClient client = findObjectInAllClients(bucketName, objectName);
            boolean exists = client != null;

            long duration = System.currentTimeMillis() - startTime;
            log.info("[CustomerMinioUtil] 对象存在验证完成: {}/{}, 结果: {}, 耗时: {}ms",
                    bucketName, objectName, exists, duration);

            return exists;

        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("[CustomerMinioUtil] 验证对象存在时发生异常: {}/{}, 耗时: {}ms, 错误: {}",
                    bucketName, objectName, duration, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 在所有客户端中查找对象
     *
     * @param bucketName 存储桶名称
     * @param objectName 对象名称
     * @return 包含对象的客户端，如果未找到则返回null
     */
    public MinioClient findObjectInAllClients(String bucketName, String objectName) {
        List<MinioClient> clients = this.minIoConfig.getMinioClient();
        if (clients == null || clients.isEmpty()) {
            log.error("[CustomerMinioUtil] MinIO客户端列表为空");
            return null;
        }

        log.info("[CustomerMinioUtil] 在 {} 个客户端中查找对象: {}/{}", clients.size(), bucketName, objectName);

        for (int i = 0; i < clients.size(); i++) {
            MinioClient client = clients.get(i);
            try {
                client.statObject(StatObjectArgs.builder()
                    .bucket(bucketName)
                    .object(objectName)
                    .build());
                log.info("[CustomerMinioUtil] 在客户端 {} 中找到对象: {}/{}", i + 1, bucketName, objectName);
                return client;
            } catch (Exception e) {
                log.debug("[CustomerMinioUtil] 客户端 {} 中未找到对象: {}/{}, 错误: {}",
                        i + 1, bucketName, objectName, e.getMessage());
            }
        }

        log.warn("[CustomerMinioUtil] 在所有 {} 个客户端中都未找到对象: {}/{}", clients.size(), bucketName, objectName);
        return null;
    }



    /**
     * 获取对象的详细信息
     *
     * @param bucketName 存储桶名称
     * @param objectName 对象名称
     * @return 对象统计信息，如果不存在则返回null
     */
    public ObjectStat getObjectInfo(String bucketName, String objectName) {
        long startTime = System.currentTimeMillis();
        log.info("[CustomerMinioUtil] 开始获取对象信息: {}/{}", bucketName, objectName);

        try {
            MinioClient client = findObjectInAllClients(bucketName, objectName);
            if (client == null) {
                log.warn("[CustomerMinioUtil] 未找到包含对象的客户端: {}/{}", bucketName, objectName);
                return null;
            }

            ObjectStat stat = client.statObject(StatObjectArgs.builder()
                .bucket(bucketName)
                .object(objectName)
                .build());

            long duration = System.currentTimeMillis() - startTime;
            log.info("[CustomerMinioUtil] 成功获取对象信息: {}/{}, 大小: {} bytes, 耗时: {}ms",
                    bucketName, objectName, stat.length(), duration);

            return stat;

        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("[CustomerMinioUtil] 获取对象信息失败: {}/{}, 耗时: {}ms, 错误: {}",
                    bucketName, objectName, duration, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 列出存储桶中的所有对象（用于调试）
     *
     * @param bucketName 存储桶名称
     * @param prefix 对象名前缀（可选）
     * @param maxKeys 最大返回数量
     */
    public void listObjects(String bucketName, String prefix, int maxKeys) {
        log.info("[CustomerMinioUtil] 开始列出对象: bucket={}, prefix={}, maxKeys={}", bucketName, prefix, maxKeys);

        List<MinioClient> clients = this.minIoConfig.getMinioClient();
        for (int i = 0; i < clients.size(); i++) {
            MinioClient client = clients.get(i);
            try {
                log.info("[CustomerMinioUtil] 客户端 {} 中的对象列表:", i + 1);
                // 这里可以添加具体的列表逻辑
                // 由于MinIO Java客户端的listObjects方法比较复杂，这里只是框架
                log.info("[CustomerMinioUtil] 客户端 {} 对象列表获取完成", i + 1);
            } catch (Exception e) {
                log.error("[CustomerMinioUtil] 客户端 {} 列出对象失败: {}", i + 1, e.getMessage());
            }
        }
    }

}
