package cn.jwis.product.pdm.customer.service.listener;

import cn.jwis.framework.base.annotation.Description;
import cn.jwis.framework.base.bean.util.BeanUtil;
import cn.jwis.framework.base.domain.able.info.ModelInfo;
import cn.jwis.framework.base.dto.UserDTO;
import cn.jwis.framework.base.exception.JWIServiceException;
import cn.jwis.framework.base.response.Result;
import cn.jwis.framework.base.util.Assert;
import cn.jwis.framework.base.util.MapUtil;
import cn.jwis.framework.base.util.SpringContextUtil;
import cn.jwis.platform.iam.user.User;
import cn.jwis.platform.plm.account.user.service.UserHelper;
import cn.jwis.platform.plm.foundation.common.response.InstanceEntity;
import cn.jwis.platform.plm.foundation.location.able.info.LocationInfo;
import cn.jwis.platform.plm.workflow.engine.dto.EcoCreateDTO;
import cn.jwis.platform.plm.workflow.engine.dto.SearchChangeDataDTO;
import cn.jwis.platform.plm.workflow.engine.order.entity.ProcessOrder;
import cn.jwis.platform.plm.workflow.engine.service.interf.ProcessOrderHelper;
import cn.jwis.product.pdm.change.dto.ECACreateDTO;
import cn.jwis.product.pdm.change.entity.ECO;
import cn.jwis.product.pdm.change.entity.ECR;
import cn.jwis.product.pdm.change.response.ECOChangeInfo;
import cn.jwis.product.pdm.customer.remote.change.YHChangeRemote;
import cn.jwis.product.pdm.workflow.change.WorkflowChangeRemote;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.delegate.DelegateTask;
import org.activiti.engine.delegate.TaskListener;
import org.activiti.engine.impl.persistence.entity.VariableInstance;
import org.springframework.util.CollectionUtils;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2023/11/15 15:26
 * @Email <EMAIL>
 */
@Description("创建ECO的监听器")
@Slf4j
public class CreateECOListener implements TaskListener {

    public CreateECOListener() {
    }

    public void notify(DelegateTask delegateTask) {
        log.info("CreateECOListener procId={}", delegateTask.getProcessInstanceId());
        try {
            Map<String, VariableInstance> variableInstances = delegateTask.getVariableInstances();
            String owner = this.fetchOwner(variableInstances);
            ProcessOrderHelper processOrderHelper = (ProcessOrderHelper) SpringContextUtil.getBean("processOrderHelperImpl");
            WorkflowChangeRemote changeRemote = (WorkflowChangeRemote)SpringContextUtil.getBean("workflowChangeRemoteImpl");
            ProcessOrder processOrder = getProcessOrder(delegateTask);
            log.info("ActivitiChangeTaskListener  processOrder==>{}", JSONObject.toJSONString(processOrder));
            List<InstanceEntity> instanceEntityList = processOrderHelper.findBizObject(processOrder.getOid());
            log.info("ActivitiChangeTaskListener  instanceEntityList==>{}", JSONObject.toJSONString(instanceEntityList));
            if (!CollectionUtils.isEmpty(instanceEntityList)) {
                Iterator var11 = instanceEntityList.iterator();

                while(var11.hasNext()) {
                    InstanceEntity instanceEntity = (InstanceEntity)var11.next();
                    if ("ECR".equals(instanceEntity.getType())) {
                        EcoCreateDTO ecoCreateDTO = new EcoCreateDTO();
                        Result<ECR> ecrResult = changeRemote.findECRDetails(instanceEntity.getOid());
                        ECR ecr = (ECR)ecrResult.getResult();
                        if (ecr == null) {
                            log.info("ecr对象为空!oid={}", instanceEntity.getOid());
                        } else {
                            ecoCreateDTO.setName(instanceEntity.getName());
                            ecoCreateDTO.setEcrOid(instanceEntity.getOid());
                            LocationInfo locationInfo = new LocationInfo();
                            locationInfo.setCatalogOid(ecr.getCatalogOid());
                            locationInfo.setCatalogType(ecr.getCatalogType());
                            locationInfo.setContainerOid(ecr.getContainerOid());
                            locationInfo.setContainerType(ecr.getContainerType());
                            locationInfo.setContainerModelDefinition(ecr.getContainerModelDefinition());
                            ecoCreateDTO.setLocationInfo(locationInfo);
                            ecoCreateDTO.setEcrOid(ecr.getOid());
                            log.info("ActivitiChangeTaskListener createEco==>{}", JSON.toJSONString(ecoCreateDTO));
                            Result<ECO> res = changeRemote.createEco(ecoCreateDTO);
                            ECO eco = res.getResult();

                            //基于ECO信息创建ECA
                            this.createECA(eco,owner);
                        }
                    } else {
                        log.info("数据类型==>{}", instanceEntity.getType());
                    }
                }
            }
        } catch (Exception var13) {
            log.error("ActivitiChangeTaskListener error==>", var13);
        }

    }

    private void createECA(ECO eco,String owner){
        YHChangeRemote changeRemote = (YHChangeRemote) SpringContextUtil.getBean("YHChangeRemoteFeignImpl");
        UserHelper userHelper = (UserHelper) SpringContextUtil.getBean("defaultUserHelperImpl");
        ECACreateDTO ecaCreateDTO = new ECACreateDTO();
        ecaCreateDTO.setName(eco.getName());
        User user = new User();
        UserDTO userDTO = userHelper.findByAccount(owner);
        Assert.notNull(userDTO,"获取ECA流程发起人信息异常");
        user.setAccount(owner);
        user.setName(userDTO.getName());
        ecaCreateDTO.setPersonLiable(user);

        Date date = new Date();
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String time = format.format(date);
        ecaCreateDTO.setPlannedCompletionTime(time);

        LocationInfo locationInfo = new LocationInfo();
        locationInfo.setCatalogOid(eco.getCatalogOid());
        locationInfo.setCatalogType(eco.getCatalogType());
        locationInfo.setContainerOid(eco.getContainerOid());
        locationInfo.setContainerType(eco.getContainerType());
        locationInfo.setContainerModelDefinition(eco.getContainerModelDefinition());
        ecaCreateDTO.setLocationInfo(locationInfo);

        ecaCreateDTO.setEcoOid(eco.getOid());
        SearchChangeDataDTO searchChangeDataDTO = new SearchChangeDataDTO();
        searchChangeDataDTO.setOid(eco.getOid());
        List<ECOChangeInfo> changeInfoByEco = changeRemote.findChangeInfoByEco(searchChangeDataDTO);
        List<ModelInfo> modelInfos=new ArrayList<>();
        for (ECOChangeInfo ecoChangeInfo : changeInfoByEco) {
            ModelInfo modelInfo = BeanUtil.copyProperties(ecoChangeInfo, new ModelInfo());
            modelInfos.add(modelInfo);
        }
        ecaCreateDTO.setChangeList(modelInfos);

        changeRemote.createECA(ecaCreateDTO);

    }

    private ProcessOrder getProcessOrder(DelegateTask delegateTask) {
        ProcessOrder processOrder = null;
        Map<String, VariableInstance> variableInstances = delegateTask.getVariableInstances();
        ProcessOrderHelper processOrderHelper = (ProcessOrderHelper) SpringContextUtil.getBean("processOrderHelperImpl");
        if (MapUtil.isEmpty(variableInstances)){
            String processInstanceId = delegateTask.getProcessInstanceId();
            processOrder = processOrderHelper.findByProcessInstanceId(processInstanceId);
        }else {
            VariableInstance processOrderVar = variableInstances.get("processOrderOid");
            if (processOrderVar != null){
                String processInstanceId = delegateTask.getProcessInstanceId();
                processOrder = processOrderHelper.findByProcessInstanceId(processInstanceId);
            }else {
                String processOrderOid = processOrderVar.getValue().toString();
                processOrder = processOrderHelper.findByOid(processOrderOid);
            }
        }
        return processOrder;
    }

    private String fetchOwner(Map<String, VariableInstance> variableInstances) {
        VariableInstance variableInstance = (VariableInstance)variableInstances.get("owner");
        if (variableInstance == null) {
            throw new JWIServiceException("owner can not be null.");
        } else {
            String owner = variableInstance.getTextValue();

            return owner;
        }
    }
}
