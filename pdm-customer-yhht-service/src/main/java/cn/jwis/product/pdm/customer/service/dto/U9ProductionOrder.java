package cn.jwis.product.pdm.customer.service.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2023/9/8 16:45
 * @Email <EMAIL>
 */
@Data
@EqualsAndHashCode
public class U9ProductionOrder {

    //XABMO2112230001
    private String DocNo;
    //BMO
    private String DocTypeCode;
    //正样生产订单
    private String DocTypeName;
    //PT1001-000011
    private String ItemCode;
    //导航接收滤波器(单通道)
    private String ItemName;
    //03009
    private String ProjectCode;
    //物料储备（元器件原材料类）
    private String ProjectName;
    //20.0
    private String ProductQty;
    //XABMO2112230001/PT1001-000011 导航接收滤波器(单通道)/物料储备（元器件原材料类）
    private String DisplayName;

}
