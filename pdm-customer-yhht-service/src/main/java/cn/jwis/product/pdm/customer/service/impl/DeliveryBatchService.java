package cn.jwis.product.pdm.customer.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.jwis.framework.base.dto.UserDTO;
import cn.jwis.framework.base.exception.JWIException;
import cn.jwis.framework.base.response.Result;
import cn.jwis.framework.base.web.session.SessionHelper;
import cn.jwis.framework.database.core.query.dynamic.Condition;
import cn.jwis.platform.iam.user.User;
import cn.jwis.platform.plm.container.entity.Container;
import cn.jwis.platform.plm.dataengine.common.JWICommonService;
import cn.jwis.platform.plm.foundation.common.service.CommonAbilityHelper;
import cn.jwis.platform.plm.foundation.location.able.info.LocationInfo;
import cn.jwis.platform.plm.sysconfig.entity.ConfigItem;
import cn.jwis.platform.plm.sysconfig.preferences.PreferencesService;
import cn.jwis.product.pdm.customer.entity.DeliveryItemDTO;
import cn.jwis.product.pdm.customer.entity.DeliveryPMSDto;
import cn.jwis.product.pdm.customer.repo.neo4j.CustomerContainerRepoImpl;
import cn.jwis.product.pdm.customer.repo.neo4j.CustomerDeliveryRepoImpl;
import cn.jwis.product.pdm.delivery.dto.DeliveryCreateDTO;
import cn.jwis.product.pdm.delivery.entity.Delivery;
import cn.jwis.product.pdm.delivery.helper.DeliveryHelper;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
public class DeliveryBatchService {

    @Resource
    CustomerDeliveryRepoImpl customerDeliveryRepo;
    @Resource
    CustomerContainerRepoImpl customerContainerRepo;
    @Resource
    CommonAbilityHelper commonAbilityHelper;
    @Resource
    JWICommonService jwiCommonService;
    @Resource
    DeliveryHelper deliveryHelper;
    @Value("${tenantOid:6deb5dde-aa39-46fb-962d-a5951f8fab5e}")
    private String tenantOid;
    @Resource
    PreferencesService preferencesService;
    @Value("${prod.env:false}")
    private Boolean prodEnv;



    public void processBatch(DeliveryPMSDto deliveryPMSDto) {
        List<DeliveryItemDTO> deliveryItemDTOs = deliveryPMSDto.getDeliveryItemDTOs();
        if (CollectionUtil.isEmpty(deliveryItemDTOs)) {
            throw new JWIException("交付项列表不能为空，请检查请求参数");
        }
        initUser();
        Boolean isSubProject = deliveryPMSDto.getIsSubProject();
        String containerOid;

        if (isSubProject) {
            //测试环境PMS测试产品
            String defaultContanerOid = "0cde3b47-8b2a-4b97-998f-7f13d5c65a2e";
            if (prodEnv) {
                //产品化文件库
                defaultContanerOid = "145d5c51-62cb-4dde-aedf-1703903fe9ba";
            }
            ConfigItem keyItem = preferencesService.queryConfigValue("CPHWJK-OID");
            containerOid = (keyItem == null || StrUtil.isBlank(keyItem.getValue()))
                    ? defaultContanerOid
                    : keyItem.getValue();
            log.info("产品化文件库oid:{}", containerOid);
        } else {
            String projectCode = deliveryPMSDto.getProjectCode();
            String projectId = deliveryPMSDto.getProjectId();
            if (StrUtil.isBlank(projectCode)) {
                throw new JWIException("projectCode不能为空");
            }
            // 根据PMS中-projectCode查询对应PDM中 产品容器
            Container container = customerContainerRepo.findContainerByProjectCode(projectCode);
            if (container == null) {
                throw new JWIException("未找到项目编号对应的产品容器: " + projectCode);
            }

            containerOid = container.getOid();
            Container contain = (Container) commonAbilityHelper.findDetailEntity(containerOid, Container.TYPE);
            log.info("当前产品容器详情：{}", JSONUtil.toJsonStr(contain));
            // 更新projectNumber
            if (StrUtil.isBlank(projectId)) {
                throw new JWIException("projectId不能为空");
            }
            JSONObject extensionJson = contain.getExtensionContent();
            if (extensionJson == null) {
                extensionJson = new JSONObject();
                contain.setExtensionContent(extensionJson);
            }
            extensionJson.put("cn_pms_project_id", projectId);
            commonAbilityHelper.doUpdate(contain);
        }

        for (DeliveryItemDTO rootItem : deliveryPMSDto.getDeliveryItemDTOs()) {
            processDeliveryItem(rootItem, null, containerOid);
        }
    }


    private void processDeliveryItem(DeliveryItemDTO item, String parentOid, String containerOid) {
        try {
            if ("删除".equals(item.getStatus())) {
                if (StrUtil.isBlank(item.getId())) {
                    throw new JWIException("删除操作必须提供节点ID");
                }
                Delivery delivery = this.findByExtensionPmsId(item.getId());
                if (delivery == null) {
                    throw new JWIException("未找到对应交付清单节点: " + item.getId());
                }
                deliveryHelper.delete(delivery.getOid());
                return;
            }

            //  递归传入的 parentOid 优先
            String systemParentOid = null;
            if (StrUtil.isNotBlank(parentOid)) {
                systemParentOid = parentOid;
            } else if (StrUtil.isNotBlank(item.getParentId())) {
                Delivery parentDelivery = this.findByExtensionPmsId(item.getParentId());
                if (parentDelivery == null) {
                    throw new JWIException("未找到父节点: " + item.getParentId());
                }
                systemParentOid = parentDelivery.getOid();
            }


            if ("新增".equals(item.getStatus())) {
                // 检查pms_id是否已经存在
                Delivery existDelivery = this.findByExtensionPmsId(item.getId());
                if (existDelivery != null) {
                    throw new JWIException("新增操作失败: 已存在pms_id为 " + item.getId() + " 的交付清单节点: " + existDelivery.getName());
                }


                DeliveryCreateDTO dto = new DeliveryCreateDTO();
                dto.setName(item.getName());
                dto.setModelDefinition(item.getType());
                dto.setRoot(systemParentOid == null);
                dto.setParentOid(systemParentOid);

                LocationInfo locationInfo = new LocationInfo();
                locationInfo.setCatalogOid(containerOid);
                locationInfo.setContainerOid(containerOid);
                locationInfo.setCatalogType("Container");
                locationInfo.setContainerType("Container");
                dto.setLocationInfo(locationInfo);

                JSONObject extension = new JSONObject();
                extension.put("pms_id", item.getId());
                extension.put("pms_parent_id", item.getParentId());
                extension.put("pms_document_type", item.getDocumentType());
                if ("Category".equals(item.getType())) {
                    JSONObject fzrJson = new JSONObject();
                    User userByAccount = findUserByAccount(item.getResponsible());
                    fzrJson.fluentPut("oid", userByAccount.getOid());
                    fzrJson.fluentPut("name", userByAccount.getName());
                    extension.fluentPut("FZR", fzrJson);
                    if (item.getPlanFinishDate() != null) {
                        extension.fluentPut("jhsj", item.getPlanFinishDate());
                    }
                }
                dto.setExtensionContent(extension);
                Delivery delivery = deliveryHelper.create(dto);
                // 递归用创建出来的OID作为子节点父ID
                systemParentOid = delivery.getOid();
            }

            /*if ("修改".equals(item.getStatus())) {
                if (StrUtil.isBlank(item.getId())) {
                    throw new JWIException("修改操作必须提供节点ID");
                }
                Delivery delivery = this.findByExtensionPmsId(item.getId());
                if (delivery == null) {
                    throw new JWIException("未找到对应交付清单节点: " + item.getId());
                }
                // 检查parentId是否变化
                String oldParentPmsId = delivery.getExtensionContent() != null
                        ? delivery.getExtensionContent().getString("pms_parent_id")
                        : null;
                if (!Objects.equals(oldParentPmsId, item.getParentId())) {
                    // 父节点发生变化
                    Delivery oldParent = findByExtensionPmsId(oldParentPmsId);
                    log.info("oldParentPmsId:{}", oldParent.getOid());
                    Delivery newParent = findByExtensionPmsId(item.getParentId());
                    log.info("newParentPmsId:{}", newParent.getOid());
                    if (newParent == null) {
                        throw new JWIException("父节点发生变化，但未找到新父节点: " + item.getParentId());
                    }
                    log.info("父节点发生变化，原父节点：{},现在父节点:{}", oldParentPmsId, item.getParentId());
                    customerDeliveryRepo.move(oldParent.getOid(), delivery.getOid(), newParent.getOid());
                }

                JSONObject extension = delivery.getExtensionContent() != null
                        ? new JSONObject(new HashMap<>(delivery.getExtensionContent()))
                        : new JSONObject();

                extension.put("pms_id", item.getId());
                extension.put("pms_parent_id", item.getParentId());
                extension.put("pms_document_type", item.getDocumentType());
                // 如果是Category类型，需要更新扩展字段
                if ("Category".equals(item.getType())) {
                    JSONObject fzrJson = new JSONObject();
                    if (StrUtil.isNotBlank(item.getResponsible())) {
                        User userByAccount = findUserByAccount(item.getResponsible());
                        fzrJson.fluentPut("oid", userByAccount.getOid());
                        fzrJson.fluentPut("name", userByAccount.getName());
                    }
                    extension.fluentPut("FZR", fzrJson);
                    if (StrUtil.isNotBlank(item.getPlanFinishDate())) {
                        extension.fluentPut("jhsj", item.getPlanFinishDate());
                    }
                }
                delivery.setExtensionContent(extension);
                delivery.setName(item.getName());
                delivery.setModelDefinition(item.getType());
                delivery.setRoot(systemParentOid == null);
                // 调用更新方法
                Delivery updatedDelivery = commonAbilityHelper.doUpdate(delivery);
                if (updatedDelivery == null) {
                    throw new JWIException("修改失败：未找到ID对应的交付清单节点");
                }
                systemParentOid = delivery.getOid();
            }*/
            if ("修改".equals(item.getStatus())) {
                if (StrUtil.isBlank(item.getId())) {
                    throw new JWIException("修改操作必须提供节点ID");
                }

                Delivery delivery = this.findByExtensionPmsId(item.getId());
                if (delivery == null) {
                    throw new JWIException("未找到对应交付清单节点: " + item.getId());
                }

                // 读取旧父节点 PMS_ID
                String oldParentPmsId = null;
                if (delivery.getExtensionContent() != null) {
                    oldParentPmsId = delivery.getExtensionContent().getString("pms_parent_id");
                }

                // 如果新父节点和旧父节点都不为空，并且不同，才处理移动
                if (StrUtil.isNotBlank(oldParentPmsId) && StrUtil.isNotBlank(item.getParentId())
                        && !Objects.equals(oldParentPmsId, item.getParentId())) {

                    Delivery oldParent = findByExtensionPmsId(oldParentPmsId);
                    if (oldParent == null) {
                        throw new JWIException("父节点发生变化，但未找到原父节点: " + oldParentPmsId);
                    }
                    Delivery newParent = findByExtensionPmsId(item.getParentId());
                    if (newParent == null) {
                        throw new JWIException("父节点发生变化，但未找到新父节点: " + item.getParentId());
                    }

                    log.info("父节点发生变化，原父节点：{}, 新父节点：{}", oldParentPmsId, item.getParentId());
                    customerDeliveryRepo.move(oldParent.getOid(), delivery.getOid(), newParent.getOid());
                }

                // 构造Extension
                JSONObject extension = delivery.getExtensionContent() != null
                        ? new JSONObject(new HashMap<>(delivery.getExtensionContent()))
                        : new JSONObject();

                extension.put("pms_id", item.getId());
                extension.put("pms_parent_id", item.getParentId());
                extension.put("pms_document_type", item.getDocumentType());

                // 如果是Category类型，需要更新扩展字段
                if ("Category".equals(item.getType())) {
                    JSONObject fzrJson = new JSONObject();
                    if (StrUtil.isNotBlank(item.getResponsible())) {
                        User userByAccount = findUserByAccount(item.getResponsible());
                        fzrJson.fluentPut("oid", userByAccount.getOid());
                        fzrJson.fluentPut("name", userByAccount.getName());
                    }
                    extension.fluentPut("FZR", fzrJson);
                    if (StrUtil.isNotBlank(item.getPlanFinishDate())) {
                        extension.fluentPut("jhsj", item.getPlanFinishDate());
                    }
                }

                delivery.setExtensionContent(extension);
                delivery.setName(item.getName());
                delivery.setModelDefinition(item.getType());
                delivery.setRoot(systemParentOid == null);

                // 调用更新
                Delivery updatedDelivery = commonAbilityHelper.doUpdate(delivery);
                if (updatedDelivery == null) {
                    throw new JWIException("修改失败：未找到ID对应的交付清单节点");
                }

                systemParentOid = delivery.getOid();
            }

            if (CollUtil.isNotEmpty(item.getChildren())) {
                for (DeliveryItemDTO child : item.getChildren()) {
                    processDeliveryItem(child, systemParentOid, containerOid);
                }
            }
        } catch (Exception e) {
            log.error("当前同步问题：", e);
            String nodeInfo = StrUtil.format("[{}:{}:{}]", item.getName(), item.getType(),item.getId());
            throw new RuntimeException("节点 " + nodeInfo + " 处理失败: " + e.getMessage(), e);
        }
    }

    private Delivery findByExtensionPmsId(String pmsId) {
        //根据pmsId，来查询pdm中对应的Delivery节点
        return customerDeliveryRepo.findByExtensionPmsId(pmsId);
    }


    private User findUserByAccount(String account) {
        // 先在本地查
        User user = jwiCommonService.dynamicQueryOne(User.TYPE, Condition.where("account").eq(account), User.class);
        // 如果没有，直接报错
        if (user == null) {
            throw new JWIException("未找到用户，账号: " + account);
        }
        return user;
    }


    private void initUser() {
        UserDTO userDto = new UserDTO();
        userDto.setTenantOid(tenantOid);
        userDto.setOid("sys_admin");
        userDto.setAccount("sys_admin");
        userDto.setSystemAdmin(Boolean.TRUE);
        userDto.setIpAddress("127.0.0.1");
        SessionHelper.addCurrentUser(userDto);
    }

    /**
     * PMS批量创建交付清单
     * @param deliveryPMSDto 入参DTO
     * @return
     */
    public Result batchCreateDelivery(DeliveryPMSDto deliveryPMSDto) {
        log.info("当前PMS批量创建交付清单入参: {}", JSONUtil.toJsonStr(deliveryPMSDto));
        try {
            this.processBatch(deliveryPMSDto);
            return Result.success("操作成功");
        } catch (JWIException e) {
            log.error("操作失败: {}", e.getMessage(), e);
            throw new JWIException("批量处理交付清单失败: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("批量处理交付清单系统异常: {}", e.getMessage(), e);
            throw new JWIException("批量处理交付清单失败: " + e.getMessage(), e);
        }
    }
}