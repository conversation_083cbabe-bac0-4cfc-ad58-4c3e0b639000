package cn.jwis.product.pdm.customer.service.impl;

import cn.jwis.framework.base.dto.UserDTO;
import cn.jwis.framework.base.util.CollectionUtil;
import cn.jwis.framework.base.util.StringUtil;
import cn.jwis.framework.base.web.session.SessionHelper;
import cn.jwis.framework.database.core.entity.EntityFilter;
import cn.jwis.framework.database.core.query.dynamic.Condition;
import cn.jwis.platform.plm.account.user.service.UserHelper;
import cn.jwis.platform.plm.dataengine.common.JWICommonService;
import cn.jwis.platform.plm.foundation.common.service.CommonAbilityHelper;
import cn.jwis.platform.plm.workflow.engine.dto.TaskRequest;
import cn.jwis.platform.plm.workflow.engine.order.entity.ProcessOrder;
import cn.jwis.platform.plm.workflow.engine.service.impl.TaskHelperImpl;
import cn.jwis.platform.plm.workflow.engine.service.interf.HistoryHelper;
import cn.jwis.platform.plm.workflow.engine.service.interf.ProcessOrderHelper;
import cn.jwis.platform.plm.workflow.engine.service.interf.TaskHelper;
import cn.jwis.platform.plm.workflow.engine.util.ConditionExpressionUtil;
import cn.jwis.product.pdm.customer.entity.DingTaskRecord;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.activiti.bpmn.model.SequenceFlow;
import org.activiti.engine.RuntimeService;
import org.activiti.engine.TaskService;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2023/9/7 20:44
 * @Email <EMAIL>
 */
@Service
@Primary
@Transactional
@Slf4j
public class CustomerTaskHelperImpl extends TaskHelperImpl {

    @Autowired
    UserHelper userHelper;

    @Autowired
    JWICommonService jwiCommonService;

    @Autowired
    DingTalkCall dingTalkCall;

    @Autowired
    CommonAbilityHelper commonAbilityHelper;

    @Autowired
    private HistoryHelper historyHelper;

    @Resource
    RuntimeService runtimeService;

    @Resource
    TaskService taskService;

    @Autowired
    ProcessOrderHelper processOrderHelper;

    @Autowired
    TaskHelper taskHelper;

    //不用签名的流程
    @Value("#{'${no.doc.sign.flowName:文件外发流程,变更}'.split(',')}")
    List<String> noDocSignFlowName;

    @Override
    public void finishTask(TaskRequest taskRequest) {
        String taskId = taskRequest.getTaskId();
        EntityFilter filter = new EntityFilter();
        filter.setType(DingTaskRecord.TYPE);
        filter.setFilter(Condition.where("pdmTaskOid").eq(taskId));
        DingTaskRecord record = CollectionUtil.getFirst(
                jwiCommonService.dynamicQuery(filter,DingTaskRecord.class));

        Map<String, Object> variables = runtimeService.getVariables(taskRequest.getProcessInstanceId());

        if(variables.get("cn_jwis_countersigner") == null){
            Map<String, Object> map = new HashMap<>();
            map.put("cn_jwis_countersigner", "");
            taskRequest.setVariables(map);
        }
        if(record !=null && StringUtil.isBlank(record.getResult())) {
            log.info("CustomerTaskHelperImpl.finishTask-->finish dingTalk task.");
            UserDTO account = userHelper.findByAccount(record.getDirector());
            String phone = account.getPhone();
            if(StringUtil.isNotBlank(phone) && phone.startsWith("86")){
                phone = phone.replaceFirst("86","");
            }
            if(StringUtil.isBlank(record.getResult())) {
                record.setResult("PDM complete!");
            }
            commonAbilityHelper.doUpdate(record);
            dingTalkCall.finishTask(record, phone);
        }
        //则初始化processor变量，用于pdf签名
//        if(record != null && !"文件外发流程".equals(record.getPdmProcessName()) && !record.getPdmProcessName().contains("文件外发流程"))
//            createDocSignData(taskRequest,record);

        if(record != null && !noDocSignFlowName.stream().anyMatch(t -> record.getPdmProcessName().contains(t)))
            createDocSignData(taskRequest,record);

        log.info("finishTask ====> bef super finish processId:{}",taskRequest.getProcessInstanceId());
        super.finishTask(taskRequest);
        log.info("finishTask ====> aft super finish processId:{}",taskRequest.getProcessInstanceId());
        // 1. 无任务记录说明该任务未发送到钉钉，忽略
        // 2. 有结论则认为是已经完成了该任务，忽略
        // 3. 无结论则认为是在PDM完成的任务，此时需要修改DingTaskRecord，并同步完成钉钉的任务
    }

    public void createDocSignData(TaskRequest taskRequest,DingTaskRecord record) {
//        String taskId = taskRequest.getTaskId();
//        TaskDetailResponseDTO taskDetail = historyHelper.findTaskDetail(taskId);
//        String processDefinitionId = taskDetail.getProcessDefinitionId();
//        List<String> pdfDocSignProcessDefinitionKey = Arrays.asList("cn.jwis.partworkflow","cn_jwis_partworkflow");
//        if(!pdfDocSignProcessDefinitionKey.contains(processDefinitionId.split(":")[0])){
//            return;
//        }
        log.info("createDocSignData ====> processId:{}",taskRequest.getProcessInstanceId());
        try {
            ProcessOrder processOrder = processOrderHelper.findByProcessInstanceId(record.getPdmProcessInstanceId());
            if(processOrder == null){
                processOrder = processOrderHelper.findByOid(record.getPdmProcessOrderId());
            }
            String taskName = record.getPdmTaskName();
            // 开始收集签名所需的数据 from liuhaomiao
            SequenceFlow selectRouting = taskRequest.getSelectRouting();
            String taskOwner;
            Map<String, Object> selectRoutingExpression = new HashMap<>();
            if (selectRouting != null) {
                taskOwner = selectRouting.getConditionExpression();
                if (StringUtils.isNotEmpty(taskOwner)) {
                    selectRoutingExpression = ConditionExpressionUtil.parseTaskSelectRoutingExpression(taskOwner);
                    // 设置任务处理人变量
                    Map<String, Object> variables = runtimeService.getVariables(record.getPdmProcessInstanceId());
                    String processor = (String) variables.get("processor");
                    String val = (String) selectRoutingExpression.values().iterator().next();
                    if ("1".equals(val)) {
                        JSONObject node;
                        if (ObjectUtils.isEmpty(processor)) {
                            node = new JSONObject();
                        } else {
                            node = JSONObject.parseObject(processor);
                        }
                        // 补齐提交者信息
                        fillCreatorData(node, variables, processOrder);
                        UserDTO user = SessionHelper.getCurrentUser();
                        if ("会签".equals(taskName)) {
                            fillCounterSigner(node, user);
                        } else {
                            JSONObject jsonObject = new JSONObject();
                            jsonObject.put("assignee", user.getAccount());
                            jsonObject.put("name", user.getName());
                            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy/M/d");
                            String currentTime = dateFormat.format(new Date());
                            jsonObject.put("date", currentTime);
                            node.put(taskName, jsonObject);
                        }
                        runtimeService.setVariable(taskRequest.getProcessInstanceId(), "processor", node.toString());
                    }
                }
            }
        }catch (Exception e){
            log.error("createDocSignData error:" ,e);
        }
    }

    private void fillCounterSigner(JSONObject node, UserDTO user) {
        if(node.containsKey("会签")){
            JSONObject counterSignJson = node.getJSONObject("会签");
            String assignee = counterSignJson.getString("assignee");
            String name = counterSignJson.getString("name");
            List<String> accountList = Arrays.asList(assignee.split(","));
            if(!accountList.contains(user.getAccount())){
                assignee = assignee + "," + user.getAccount();
                name = name + "," + user.getName();
                counterSignJson.put("assignee",assignee);
                counterSignJson.put("name",name);
                node.put("会签",counterSignJson);
            }
        }else{
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("assignee", user.getAccount());
            jsonObject.put("name", user.getName());
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy/M/d");
            String currentTime = dateFormat.format(new Date());
            jsonObject.put("date", currentTime);
            node.put("会签", jsonObject);
        }
    }

    private void fillCreatorData(JSONObject node, Map<String, Object> variables, ProcessOrder processOrder) {
        if(node.containsKey("提交审核")){
            return;
        }
        String ownerAccount = variables.get("owner").toString();
        UserDTO userDTO = userHelper.findByAccount(ownerAccount);
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy/M/d");
        String date = dateFormat.format(new Date(processOrder.getCreateDate()));
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("assignee",ownerAccount);
        jsonObject.put("name",userDTO.getName());
        jsonObject.put("date",date);
        node.put("提交审核",jsonObject);
    }
}
