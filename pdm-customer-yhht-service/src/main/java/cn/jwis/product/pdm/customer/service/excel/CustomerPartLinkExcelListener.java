package cn.jwis.product.pdm.customer.service.excel;

import cn.jwis.framework.base.domain.able.ModelAble;
import cn.jwis.framework.base.domain.entity.BaseRelationship;
import cn.jwis.framework.base.util.Assert;
import cn.jwis.framework.base.util.SpringContextUtil;
import cn.jwis.framework.database.core.query.dynamic.Condition;
import cn.jwis.platform.plm.dataengine.common.JWICommonService;
import cn.jwis.platform.plm.foundation.relationship.Describe;
import cn.jwis.platform.plm.foundation.relationship.Reference;
import cn.jwis.platform.plm.foundation.versionrule.able.MasterAble;
import cn.jwis.product.pdm.cad.ecad.entity.ECAD;
import cn.jwis.product.pdm.cad.ecad.entity.ECADIteration;
import cn.jwis.product.pdm.cad.mcad.entity.MCADIteration;
import cn.jwis.product.pdm.customer.repo.CustomerCommonRepo;
import cn.jwis.product.pdm.customer.service.dto.CustomerPartLinkDTO;
import cn.jwis.product.pdm.partbom.part.entity.Part;
import cn.jwis.product.pdm.partbom.part.entity.PartIteration;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.read.metadata.holder.ReadRowHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR> yefei
 * @Date ： 2024/7/29
 * @Description :
 */

@Slf4j
public class CustomerPartLinkExcelListener implements ReadListener<CustomerPartLinkDTO> {

    class ImportData{

        private  Map<String,PartIteration> partNumberOidMap;

        private Map<String, ModelAble> mcadNumberOidMap;

        private Map<String, MasterAble> ecadNumberOidMap;

        public Map<String, ModelAble> getMcadNumberOidMap() {
            return mcadNumberOidMap;
        }

        public void setMcadNumberOidMap(Map<String, ModelAble> mcadNumberOidMap) {
            this.mcadNumberOidMap = mcadNumberOidMap;
        }

        public Map<String, MasterAble> getEcadNumberOidMap() {
            return ecadNumberOidMap;
        }

        public void setEcadNumberOidMap(Map<String, MasterAble> ecadNumberOidMap) {
            this.ecadNumberOidMap = ecadNumberOidMap;
        }

        public Map<String, PartIteration> getPartNumberOidMap() {
            return partNumberOidMap;
        }

        public void setPartNumberOidMap(Map<String, PartIteration> partNumberOidMap) {
            this.partNumberOidMap = partNumberOidMap;
        }
    }

    private List<CustomerPartLinkDTO> excelDTOS = new ArrayList<>();

    private JWICommonService jwiCommonService = SpringContextUtil.getBean(JWICommonService.class);

    @Override
    public void invoke(CustomerPartLinkDTO customerPartLinkDTO, AnalysisContext analysisContext) {
        // 三条属性都为空的直接过滤
        if(StringUtils.isNotBlank(customerPartLinkDTO.getPartNumber()) || StringUtils.isNotBlank(customerPartLinkDTO.getCadNumber()) || StringUtils.isNotBlank(customerPartLinkDTO.getCadType())) {
            ReadRowHolder readRowHolder = analysisContext.readRowHolder();
            Integer rowIndex = readRowHolder.getRowIndex();
            customerPartLinkDTO.setRowIndex(rowIndex+1);
            excelDTOS.add(customerPartLinkDTO);
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        ImportData importData = new ImportData();
        checkAndReturnError(importData);


    }

    private void checkAndReturnError(ImportData importData) {
        Map<Integer,List<String>> errorMap = checkData(importData);
        List<String> allMessage = new ArrayList<>();
        List<String> lastError = errorMap.remove(0);
        for (Map.Entry<Integer, List<String>> error : errorMap.entrySet()) {
            allMessage.add("第" + error.getKey() + "行数据," + StringUtils.join(error.getValue(),":"));
        }
        if(lastError != null) {
            allMessage.addAll(lastError);
        }
        Assert.isEmpty(allMessage, StringUtils.join(allMessage, "\n"));

        startToBind(importData);
    }


    private Map<Integer,List<String>> checkData(ImportData importData) {
        Map<Integer,List<String>> errorMap = new HashMap<>();
        Map<String,Map<String, Set<String>>> partTypeCadNumberMap = new HashMap<>();

        for (CustomerPartLinkDTO excelDTO : excelDTOS) {
            if(StringUtils.isBlank(excelDTO.getPartNumber())) {
                errorMap.computeIfAbsent(excelDTO.getRowIndex(),k->new ArrayList<>()).add("部件编码不能为空");
            }
            if(StringUtils.isBlank(excelDTO.getCadNumber())) {
                errorMap.computeIfAbsent(excelDTO.getRowIndex(),k->new ArrayList<>()).add("CAD编码不能为空");
            }
            if(StringUtils.isBlank(excelDTO.getCadType())) {
                errorMap.computeIfAbsent(excelDTO.getRowIndex(),k->new ArrayList<>()).add("CAD类型不能为空");
            }
            if(errorMap.containsKey(excelDTO.getRowIndex())) {
                continue;
            }
            partTypeCadNumberMap.computeIfAbsent(excelDTO.getPartNumber(), k -> new HashMap<>()).computeIfAbsent(excelDTO.getCadType(), t -> new HashSet<>()).add(excelDTO.getCadNumber());
        }
        // 校验部件编码有效性
        Map<String, PartIteration> partNumberOidMap = jwiCommonService.dynamicQuery(PartIteration.TYPE, Condition.where(
                "number").in(partTypeCadNumberMap.keySet()).and(Condition.where("latest").eq(true)),
                PartIteration.class).stream().collect(Collectors.toMap(PartIteration::getNumber,v->v
                ,(o1,o2)->o2));

        checkInvalidNumberAddMessage(errorMap,partTypeCadNumberMap.keySet(),partNumberOidMap.keySet(),"以下部件编码无效");
        importData.setPartNumberOidMap(partNumberOidMap);
        // 校验CAD编码有效性
        Map<String, Set<String>> typeNumberSet =
                partTypeCadNumberMap.values().stream().flatMap(map -> map.entrySet().stream()).collect(Collectors.toMap(
                        Map.Entry::getKey, Map.Entry::getValue,
                            (set1, set2) -> {
                            set1.addAll(set2); // 合并相同键的 Set
                            return set1;
                        }
                ));
        Map<String, ModelAble> mcadNumberOidMap = jwiCommonService.dynamicQuery(MCADIteration.TYPE, Condition.where(
                "number").in(typeNumberSet.get("MCAD")).and(Condition.where("latest").eq(true)),MCADIteration.class).stream().collect(Collectors.toMap(MCADIteration::getNumber,v->v,(o1,o2)->o2));

        Map<String,MasterAble> ecadNumberOidMap = jwiCommonService.dynamicQuery(ECADIteration.TYPE, Condition.where(
                "number").in(typeNumberSet.get("ECAD")).and(Condition.where("latest").eq(true)),ECADIteration.class).stream().collect(Collectors.toMap(ECADIteration::getNumber,v->v,(o1,o2)->o2));

        checkInvalidNumberAddMessage(errorMap,typeNumberSet.get("MCAD"),mcadNumberOidMap.keySet(),"以下MCAD编码无效");
        checkInvalidNumberAddMessage(errorMap,typeNumberSet.get("ECAD"),ecadNumberOidMap.keySet(),"以下ECAD编码无效");

        importData.setMcadNumberOidMap(mcadNumberOidMap);
        importData.setEcadNumberOidMap(ecadNumberOidMap);
        return errorMap;
    }

    private void checkInvalidNumberAddMessage(Map<Integer,List<String>> errorMap, Set<String> paramSet, Set<String> resultSet, String message) {
        if(paramSet == null) {
            return;
        }
        if(paramSet.size() > resultSet.size()) {
            Set<String> hashSet = new HashSet<>(paramSet);
            hashSet.removeAll(resultSet);
            errorMap.computeIfAbsent(0,k->new ArrayList<>()).add(message + StringUtils.join(hashSet,","));
        }
    }

    private void startToBind(ImportData importData) {
        CustomerCommonRepo customerCommonRepo = SpringContextUtil.getBean(CustomerCommonRepo.class);
        Map<String,Set<String>> partEcadSetMap = new HashMap<>();

        Map<String,List<String>> partEcadMasterOidMap = customerCommonRepo.findRelationBothSideByTo(ECAD.TYPE,
                Reference.TYPE,
                Part.TYPE,
                importData.getPartNumberOidMap().values().stream().map(PartIteration::getMasterOid).collect(Collectors.toSet()));

        for (Map.Entry<String, List<String>> entry : partEcadMasterOidMap.entrySet()) {
            partEcadSetMap.computeIfAbsent(entry.getKey(),k->new HashSet<>()).addAll(entry.getValue());
        }

        Map<String,Set<String>> partMcadSetMap = new HashMap<>();
        List<Describe> mcadRelationList = customerCommonRepo.findRelationByTo(MCADIteration.TYPE, Describe.TYPE,
                PartIteration.TYPE, importData.getPartNumberOidMap().values().stream().map(PartIteration::getOid).collect(Collectors.toSet()));
        for (Describe describe : mcadRelationList) {
            partMcadSetMap.computeIfAbsent(describe.getToOid(),k->new HashSet<>()).add(describe.getFromOid());
        }

        List<BaseRelationship> createRelationList = new ArrayList<>();
        List<BaseRelationship> deleteRelationList = new ArrayList<>();

        for (CustomerPartLinkDTO excelDTO : excelDTOS) {
            PartIteration partIteration = importData.getPartNumberOidMap().get(excelDTO.getPartNumber());
            if("ecad".equalsIgnoreCase(excelDTO.getCadType())) {
                MasterAble masterAble = importData.getEcadNumberOidMap().get(excelDTO.getCadNumber());
                if (partEcadSetMap.containsKey(partIteration.getMasterOid()) && partEcadSetMap.get(partIteration.getMasterOid()).contains(masterAble.getMasterOid())) {
                    partEcadSetMap.get(partIteration.getMasterOid()).remove(masterAble.getMasterOid());
                } else {
                    createRelationList.add(new Reference(ECAD.TYPE, masterAble.getMasterOid(), Part.TYPE, partIteration.getMasterOid()));
                }
            } else if ("mcad".equalsIgnoreCase(excelDTO.getCadType())){
                ModelAble modelAble = importData.getMcadNumberOidMap().get(excelDTO.getCadNumber());
                if (partMcadSetMap.containsKey(partIteration.getOid()) && partMcadSetMap.get(partIteration.getOid()).contains(modelAble.getOid())) {
                    partMcadSetMap.get(partIteration.getOid()).remove(modelAble.getOid());
                } else {
                    createRelationList.add(new Describe(MCADIteration.TYPE, modelAble.getOid(), PartIteration.TYPE,
                            partIteration.getOid()));
                }
            }
        }
        jwiCommonService.createRelation(createRelationList);

        deleteRelationList.addAll(partEcadSetMap.entrySet().stream().flatMap(entry -> entry.getValue().stream().map(value -> new Reference(ECAD.TYPE, value,Part.TYPE,entry.getKey()))).collect(Collectors.toList()));
        //存在可能的正向历史数据，清理时，需要再进行正向关系组装尝试进行删除
        deleteRelationList.addAll(partEcadSetMap.entrySet().stream().flatMap(entry -> entry.getValue().stream().map(value -> new Reference(Part.TYPE, entry.getKey(), ECAD.TYPE, value))).collect(Collectors.toList()));
        deleteRelationList.addAll(partMcadSetMap.entrySet().stream().flatMap(entry -> entry.getValue().stream().map(value -> new Describe(MCADIteration.TYPE, value,PartIteration.TYPE,entry.getKey()))).collect(Collectors.toList()));

        jwiCommonService.deleteRelation(deleteRelationList);
    }

}
