package cn.jwis.product.pdm.customer.service.release;

import cn.jwis.framework.base.bean.util.BeanUtil;
import cn.jwis.platform.plm.datadistribution.service.DataDistributionService;
import cn.jwis.platform.plm.dataengine.common.JWICommonService;
import cn.jwis.platform.plm.file.service.FileService;
import cn.jwis.platform.plm.workflow.engine.order.entity.ProcessOrder;
import cn.jwis.product.pdm.cad.ecad.service.ECADService;
import cn.jwis.product.pdm.cad.mcad.service.MCADHelper;
import cn.jwis.product.pdm.cad.mcad.service.PartMcadService;
import cn.jwis.product.pdm.customer.entity.IntegrationRecord;
import cn.jwis.product.pdm.customer.service.dto.ItemReleaseDataDTO;
import cn.jwis.product.pdm.customer.service.dto.TCOReleaseDataDTO;
import cn.jwis.product.pdm.customer.service.impl.CustomerCommonServiceImpl;
import cn.jwis.product.pdm.partbom.part.entity.PartIteration;
import cn.jwis.product.pdm.partbom.part.service.PartHelper;
import com.alibaba.fastjson.JSONObject;
import jodd.util.StringUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 技术变更单发布能力提供者
 * @date 2023/8/15 11:13
 * @Email <EMAIL>
 */
@Data
@EqualsAndHashCode
@Component
@Transactional
public class TCOEntityRelease extends EntityRelease{

    private final List<String> targetLifecycleStatus = Arrays.asList("Released","Inwork","Design","Draft","UnderReview");

    @Autowired
    DataDistributionService dataDistributionService;

    @Autowired
    PartHelper partHelper;

    @Autowired
    MCADHelper mcadHelper;

    @Autowired
    ECADService ecadService;

    @Autowired
    JWICommonService jwiCommonService;

    @Autowired
    CustomerCommonServiceImpl customerCommonServiceImpl;

    @Autowired
    PartMcadService partMcadService;

    @Autowired
    FileService fileService;

    @Autowired
    PartEntityRelease partEntityRelease;

    /**
     * 真正的Part发布实现，在此处推送发放的部件到任意外部接口，当前只发MQ。
     * @return
     */
    @Override
    @Async
    public IntegrationRecord release(ProcessOrder processOrder,String entityOid, String recordOid, int isFirst) {
        TCOReleaseDataDTO tcoReleaseDataDTO = new TCOReleaseDataDTO();
        PartIteration dbPart = partHelper.findByOid(entityOid);
        if(dbPart == null) return null;
        tcoReleaseDataDTO.setBusinessOid(type + "_" + dbPart.getNumber() + "_" + dbPart.getVersion());
        ItemReleaseDataDTO partReleaseData = BeanUtil.copyProperties(dbPart,new ItemReleaseDataDTO());
        tcoReleaseDataDTO.setPart(partReleaseData);
        tcoReleaseDataDTO.setType(type); // 用于找到对应的routingKey
        // 初始化技术变更单的属性
        initTCOAttr(tcoReleaseDataDTO,processOrder == null ? new JSONObject() : processOrder.getExtensionContent());
        JSONObject releaseData = JSONObject.parseObject(JSONObject.toJSONString(tcoReleaseDataDTO));
        // 获取集成记录
        IntegrationRecord record = getIntegrationRecord(recordOid,type,processOrder,releaseData);
        // 发MQ
        String result = releaseMQ(record,dataDistributionService);
        record.setMqSuccess(StringUtil.isBlank(result) ? true : false);
        // U9处理结果，预设为 waiting
        record.setConsumer("hzz");
        record.setIsSuccess(false);
        record.setMsg("waiting");
        return jwiCommonService.update(record);
    }

    private void initTCOAttr(TCOReleaseDataDTO tcoReleaseDataDTO,JSONObject extensionContent) {
        //执行负责人
        tcoReleaseDataDTO.setExecuteOwner(extensionContent.getString("executeOwner"));
        // 质检负责人
        tcoReleaseDataDTO.setQualityOwner(extensionContent.getString("qualityOwner"));
        // 生产工单U9
        tcoReleaseDataDTO.setProductionOrder(extensionContent.getString("productionOrder"));
        // 库存数量U9
        tcoReleaseDataDTO.setInventoryQuantity(extensionContent.getString("inventoryQuantity"));
        // 变更前
        tcoReleaseDataDTO.setChangeBefore(extensionContent.getString("changeBefore"));
        // 变更后
        tcoReleaseDataDTO.setChangeAfter(extensionContent.getString("changeAfter"));
        // 更改原因
        tcoReleaseDataDTO.setChangeReason(extensionContent.getString("changeReason"));
        // 库存（在制品）处理意见
        tcoReleaseDataDTO.setInventoryHandlingOpinions(extensionContent.getString("inventoryHandlingOpinions"));
        // 变更数量
        tcoReleaseDataDTO.setChangeQuantity(extensionContent.getString("changeQuantity"));
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        type = EntityReleaseFactory.BusinessType.techChange.name();
        EntityReleaseFactory.register(type,this);
    }
}
