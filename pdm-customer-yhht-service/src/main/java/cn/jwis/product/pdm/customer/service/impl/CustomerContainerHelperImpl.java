package cn.jwis.product.pdm.customer.service.impl;

import cn.jwis.audit.annotation.JWIParam;
import cn.jwis.audit.annotation.JWIServiceAudit;
import cn.jwis.audit.enums.Action;
import cn.jwis.framework.base.bean.util.BeanUtil;
import cn.jwis.framework.base.domain.able.RelationAble;
import cn.jwis.framework.base.exception.JWIException;
import cn.jwis.framework.base.exception.JWIServiceException;
import cn.jwis.framework.base.feign.RemoteAnalysisHelper;
import cn.jwis.framework.base.response.Result;
import cn.jwis.framework.base.util.*;
import cn.jwis.framework.base.web.session.SessionHelper;
import cn.jwis.platform.iam.user.User;
import cn.jwis.platform.plm.account.auth.service.AuthHelper;
import cn.jwis.platform.plm.account.entity.team.TeamTemplateRole;
import cn.jwis.platform.plm.container.account.AccountRemote;
import cn.jwis.platform.plm.container.account.FileRemote;
import cn.jwis.platform.plm.container.dto.ContainerTemplateCreateDTO;
import cn.jwis.platform.plm.container.dto.FolderCreateDTO;
import cn.jwis.platform.plm.container.dto.container.ContainerCreateDTO;
import cn.jwis.platform.plm.container.entity.*;
import cn.jwis.platform.plm.container.entity.response.*;
import cn.jwis.platform.plm.container.file.FileRemoteFeign;
import cn.jwis.platform.plm.container.service.*;
import cn.jwis.platform.plm.datadistribution.annotation.DataDistribution;
import cn.jwis.platform.plm.dataengine.common.JWICommonService;
import cn.jwis.platform.plm.foundation.attachment.entity.File;
import cn.jwis.platform.plm.foundation.classification.able.info.ClassificationInfo;
import cn.jwis.platform.plm.foundation.classification.responce.ClassificationTreeNode;
import cn.jwis.platform.plm.foundation.classification.service.ClassificationService;
import cn.jwis.platform.plm.foundation.common.service.CommonAbilityHelper;
import cn.jwis.platform.plm.foundation.common.service.CommonAbilityService;
import cn.jwis.platform.plm.foundation.location.able.info.LocationInfo;
import cn.jwis.platform.plm.permission.administrativedomain.AdministrativeDomainService;
import cn.jwis.platform.plm.permission.administrativedomain.entity.AdministrativeDomain;
import cn.jwis.platform.plm.permission.administrativedomain.entity.Inherit;
import cn.jwis.platform.plm.permission.administrativedomain.entity.Point;
import cn.jwis.platform.plm.permission.util.PermissionCacheUtil;
import cn.jwis.product.pdm.customer.service.dto.ComtainerTemplateExportDTO;
import cn.jwis.product.pdm.customer.service.dto.FolderForExportDTO;
import cn.jwis.product.pdm.customer.service.dto.SaveAsTemplateDTO;
import cn.jwis.product.pdm.customer.service.dto.TeamRoleForExportDTO;
import cn.jwis.product.pdm.customer.service.interf.CustomerContainerHelper;
import cn.jwis.product.pdm.customer.service.interf.PDMFolderServiceI;
import com.alibaba.fastjson.JSONObject;
import feign.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import javax.annotation.Resource;
import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.text.MessageFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: 汪江
 * @data: 2023-12-29 11:19
 * @description:
 **/
@Service
@Slf4j
public class CustomerContainerHelperImpl implements CustomerContainerHelper {
    @Resource
    private ContainerService containerService;
    @Resource
    private AuthHelper authHelper;
    @Resource
    private ContainerTemplateHelper containerTemplateHelper;
    @Resource
    private TeamService teamService;
    @Resource
    private FileRemote fileRemote;
    @Resource
    private PDMFolderServiceI pdmFolderServiceI;
    @Resource(name = "customerTeamHelperImpl")
    private TeamHelper teamHelper;
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private CommonAbilityHelper commonAbilityHelper;
    @Resource
    private FolderHelper folderHelper;
    @Resource
    private AdministrativeDomainService administrativeDomainService;
    @Resource
    private CommonAbilityService commonAbilityService;
    @Resource
    private ClassificationService classificationService;
    @Resource
    private AccountRemote accountRemote;
    @Resource
    private TeamRoleService teamRoleService;
    @Autowired
    private FileRemoteFeign fileRemoteFeign;
    @Autowired
    private JWICommonService jwiCommonService;
    @Autowired
    private PermissionCacheUtil permissionCacheUtil;

    @Override
    public ContainerTemplate saveAsTemplate(SaveAsTemplateDTO dto, String category) throws JWIException {
        ValidationUtil.validate(dto);
        String templateName = dto.getTemplateName();
        String containerOid = dto.getContainerOid();
        ContainerDetail container = this.findDetail(containerOid);
        Assert.notNull(container, "The node is not exist");
        ContainerTemplateCreateDTO createDTO = new ContainerTemplateCreateDTO();
        createDTO.setName(templateName);
        createDTO.setCategory(category);
        createDTO.setDescription(dto.getDescription());
        ContainerTemplate containerTemplate = this.containerTemplateHelper.create(createDTO);

        ComtainerTemplateExportDTO templateJSONClazz = new ComtainerTemplateExportDTO();
        TeamForExport teamForExport = this.teamService.findDetailForExport(container.getTeam().getOid());
        templateJSONClazz.setTeamRoles(teamForExport.getTeamRoles());
        templateJSONClazz.setFolderTree(getFolderForExportDTO(dto).get(0).getChildren());
//        ComtainerTemplateExport templateJSONClazz = new ComtainerTemplateExport();
//        TeamForExport teamForExport = this.teamService.findDetailForExport(container.getTeam().getOid());
//        FolderForExport rootFolder = this.folderService.findFolderTreeForExport(containerOid);
//        templateJSONClazz.setTeamRoles(teamForExport.getTeamRoles());
//        templateJSONClazz.setFolderTree(rootFolder.getChildren());
        log.info("模板信息为：" + JSONObject.toJSON(templateJSONClazz));
        String suffix = ".json";
        JSONObject fileUploadResult = this.fileUpload(templateName, suffix, (JSONObject) JSONObject.toJSON(templateJSONClazz));
        JSONObject fileJSON = fileUploadResult.getJSONObject(templateName + suffix);
        File file = new File();
        file.setOid(fileJSON.getString("oid"));
        file.setName(templateName);
        containerTemplate.setFile(file);
        this.containerService.updateEntity(containerTemplate);
        return containerTemplate;
    }

    /**
     * 获取文件夹及其子文件夹的导出DTO列表
     *
     * @param dto SaveAsTemplateDTO对象
     * @return 包含文件夹及其子文件夹导出DTO的列表
     */
    public List<FolderForExportDTO> getFolderForExportDTO(SaveAsTemplateDTO dto) {
        List<FolderForExportDTO> folderForExportDTOS = new ArrayList<>();

        // 获取根文件夹节点列表
        List<FolderTreeNode> folderTreeNodes = pdmFolderServiceI.searchFoldersWithPermisson(null, dto.getContainerOid(), null);

        // 遍历根文件夹节点列表
        for (FolderTreeNode folderTreeNode : folderTreeNodes) {
            // 递归处理根文件夹及其子文件夹，构建导出DTO
            FolderForExportDTO folderForExportDTO = setChildrenFolderForExportDTO(folderTreeNode, dto);

            // 将构建好的导出DTO添加到列表
            folderForExportDTOS.add(folderForExportDTO);
        }

        return folderForExportDTOS;
    }

    /**
     * 递归处理文件夹及其子文件夹，构建导出DTO
     *
     * @param folderTreeNode 文件夹节点
     * @param dto            SaveAsTemplateDTO对象
     * @return 构建好的导出DTO
     */
    private FolderForExportDTO setChildrenFolderForExportDTO(FolderTreeNode folderTreeNode, SaveAsTemplateDTO dto) {
        // 创建文件夹导出DTO对象
        FolderForExportDTO folderForExportDTO = new FolderForExportDTO();
        folderForExportDTO.setName(folderTreeNode.getName());
        folderForExportDTO.setDescription(folderTreeNode.getDescription());

        // 获取文件夹的团队角色及用户信息
        Team teamRole = pdmFolderServiceI.getTeamRole(folderTreeNode.getOid(), null);
        List<TeamRoleWithUser> teamRoleWithUsers = this.teamHelper.fuzzyContent(teamRole.getOid(), null);

        // 将团队角色及用户信息转换为导出DTO格式
        List<TeamRoleForExportDTO> teamRoleForExports = convertTeamRoles(teamRoleWithUsers, dto.getIsAddPer());
        folderForExportDTO.setTeamRoles(teamRoleForExports);

        // 如果当前文件夹有子文件夹，递归处理子文件夹
        if (folderTreeNode.getChildren() != null) {
            List<FolderForExportDTO> childDTOs = new ArrayList<>();
            for (FolderTreeNode child : folderTreeNode.getChildren()) {
                FolderForExportDTO childFolderDTO = setChildrenFolderForExportDTO(child, dto);
                childDTOs.add(childFolderDTO);
            }
            folderForExportDTO.setChildren(childDTOs);
        }

        return folderForExportDTO;
    }

    /**
     * 转换团队角色及用户信息为导出DTO格式
     *
     * @param teamRoleWithUsers 团队角色及用户信息列表
     * @param addPer            是否添加权限信息
     * @return 转换后的导出DTO格式列表
     */
    private List<TeamRoleForExportDTO> convertTeamRoles(List<TeamRoleWithUser> teamRoleWithUsers, boolean addPer) {
        List<TeamRoleForExportDTO> teamRoleForExports = new ArrayList<>();

        // 遍历团队角色及用户信息列表
        for (TeamRoleWithUser teamRoleWithUser : teamRoleWithUsers) {
            // 创建团队角色导出DTO对象
            TeamRoleForExportDTO teamRoleForExport = new TeamRoleForExportDTO();
            teamRoleForExport.setTeamRoleOid(teamRoleWithUser.getOid());
            teamRoleForExport.setName(teamRoleWithUser.getName());
//            // 如果需要添加权限信息，转换用户信息为导出DTO格式
//            if (addPer) {
//                List<UserForExport> userForExports = new ArrayList<>();
//                log.info("人员信息" + teamRoleWithUser.getUsers().toString());
//                for (UserDTO userDTO : teamRoleWithUser.getUsers()) {
//                    UserForExport userForExport = new UserForExport();
//                    userForExport.setAccount(userDTO.getAccount());
//                    userForExports.add(userForExport);
//                }
//                teamRoleForExport.setUsers(userForExports);
//            }
            // 添加团队角色导出DTO到列表
            teamRoleForExports.add(teamRoleForExport);
        }

        return teamRoleForExports;
    }

    public ContainerDetail findDetail(String oid) {
        this.authHelper.fillUserAuthInfo(SessionHelper.getCurrentUser());
        return this.containerService.findDetail(oid);
    }

    public JSONObject fileUpload(String fileName, String suffix, JSONObject data) {
        try {
            java.io.File tempFile = java.io.File.createTempFile("containerServiceTemp" + OidGenerator.newOid() + "__", ".json");

            JSONObject var31;
            try {
                System.out.println(tempFile.getAbsoluteFile());
                DiskFileItemFactory factory = new DiskFileItemFactory();
                FileItem fileItem = factory.createItem(fileName, "text/plain", true, fileName + suffix);
                OutputStream fileOutputStream = fileItem.getOutputStream();
                Throwable var8 = null;

                try {
                    fileOutputStream.write(data.toJSONString().getBytes());
                } catch (Throwable var26) {
                    var8 = var26;
                    throw var26;
                } finally {
                    if (fileOutputStream != null) {
                        if (var8 != null) {
                            try {
                                fileOutputStream.close();
                            } catch (Throwable var25) {
                                var8.addSuppressed(var25);
                            }
                        } else {
                            fileOutputStream.close();
                        }
                    }

                }

                MultipartFile multiPartFile = new CommonsMultipartFile(fileItem);
                var31 = this.fileRemote.upload(multiPartFile);
            } finally {
                tempFile.deleteOnExit();
            }
            return var31;
        } catch (Exception e) {

        }
        return null;
    }

    @JWIServiceAudit(
            buzOid = "${result.oid}",
            bizNumber = "${result.number}",
            bizName = "${result.name}",
            buzType = "${result.modelDefinition}",
            action = Action.ADD,
            content = "创建产品容器 名称为：${result.name}"
    )
    @JWIParam("result")
    @DataDistribution(
            dataOpt = "create"
    )
    @Override
    public Container createContainer(ContainerCreateDTO dto) throws JWIException {
        ValidationUtil.validate(dto);
        RLock nameLock = this.redissonClient.getLock(MessageFormat.format("Container:Create:Name:{0}", dto.getName()));
        nameLock.tryLock();
        List<Container> byName = this.containerService.findContainerByName(dto.getName(), dto.getModelDefinition());
        Assert.isEmpty(byName, "存在相同名称容器:" + dto.getName());

        Container var25;
        try {
            String tenantOid = SessionHelper.getCurrentUser().getTenantOid();
            Container newContainer = (Container) BeanUtil.copyProperties(dto, new Container());
            newContainer.setOid(OidGenerator.newOid());
            newContainer.setCreateBy(SessionHelper.getCurrentUser().getName());
            newContainer.setCreateDate(System.currentTimeMillis());
            newContainer.setModelDefinition(dto.getModelDefinition());
            Container container = (Container) this.commonAbilityHelper.doCreate(newContainer);
            String containerType = "Container";
            String containerOid = container.getOid();
            String catalogOid = dto.getProductCatalogOid();
            if (StringUtil.isNotEmpty(catalogOid)) {
                this.containerService.createProductCatalogLink(containerOid, catalogOid);
            } else {
                this.containerService.createTenantLink(containerOid, tenantOid);
            }

            String administrativeDomainOid = this.initDomain(container, catalogOid);
            String territory = dto.getTerritory();
            Folder rootFolder = null;
            if ("electron".equals(territory)) {
                rootFolder = this.createFolderNodeByclassify(container);
            } else {
                rootFolder = this.folderHelper.createFolderRootNode(container);
            }

            Team team = this.initTeamForContainer(newContainer, container);
            boolean teamTemplateExists = StringUtils.isNotBlank(dto.getTeamTemplateOid());
            if (teamTemplateExists) {
                this.handleTeamTemplate(dto, team);
            }

            String containerTemplateOid = dto.getContainerTemplateOid();
            if (StringUtil.isNotBlank(containerTemplateOid)) {
                ContainerTemplate template = this.containerTemplateHelper.findByOid(containerTemplateOid);
                Assert.notNull(template, "container template does not exists");
                File file = template.getFile();
                if (file != null) {
                    ComtainerTemplateExportDTO templateContext = downloadByOid(file.getOid());
                    if (!teamTemplateExists) {
                        List<TeamRoleForExport> teamRoles = templateContext.getTeamRoles();
                        if(CollectionUtil.isNotEmpty(teamRoles)){
                            for (TeamRoleForExport teamRole : teamRoles) {
                                teamRole.setUsers(null);
                            }
                            this.teamHelper.buildTeam(team, teamRoles);
                        }
                    }

                    LocationInfo locationInfo = new LocationInfo();
                    locationInfo.setCatalogType("Folder");
                    locationInfo.setCatalogOid(rootFolder.getOid());
                    locationInfo.setContainerType(containerType);
                    locationInfo.setContainerOid(containerOid);
                    locationInfo.setContainerModelDefinition(dto.getModelDefinition());
                    this.buildFolderTree(locationInfo, templateContext.getFolderTree());
                }
            }

            String account = SessionHelper.getCurrentUser().getAccount();
            List<String> pmUserAccounts = dto.getProductManagerAccounts();
            if (CollectionUtil.isEmpty((Collection) pmUserAccounts)) {
                pmUserAccounts = new ArrayList();
                ((List) pmUserAccounts).add(account);
            } else if (!((List) pmUserAccounts).contains(account)) {
                ((List) pmUserAccounts).add(account);
            }

            this.bindSiteRoleUser("ProductManager", (List) pmUserAccounts, team);
            this.bindSiteRoleUser("member", dto.getMemberAccounts(), team);
            container.setTeam(team);
            this.refreshUserCache(administrativeDomainOid, team);
            var25 = container;
        } finally {
            if (nameLock.isHeldByCurrentThread()) {
                nameLock.unlock();
            }

        }
        return var25;
    }

    private String initDomain(Container container, String catalogOid) {
        AdministrativeDomain administrativeDomain = new AdministrativeDomain();
        String administrativeDomainOid = OidGenerator.newOid();
        administrativeDomain.setOid(administrativeDomainOid);
        administrativeDomain.setName(container.getName());
        administrativeDomain.setDescription(container.getDescription());
        administrativeDomain.setContainerModelType("Container");
        administrativeDomain.setContainerOid(container.getOid());
        this.administrativeDomainService.create(administrativeDomain);
        List<RelationAble> rels = new ArrayList();
        Point relatedTo = new Point("Container", container.getOid(), "AdministrativeDomain", administrativeDomainOid);
        rels.add(relatedTo);
        AdministrativeDomain oneByFrom;
        if (StringUtils.isNotEmpty(catalogOid)) {
            oneByFrom = this.administrativeDomainService.findOneByFrom("ProductCatalog", catalogOid, "POINT");
        } else {
            String tenantOid = SessionHelper.getCurrentUser().getTenantOid();
            oneByFrom = this.administrativeDomainService.findOneByFrom("Tenant", tenantOid, "POINT");
        }

        if (oneByFrom != null) {
            rels.add(new Inherit("AdministrativeDomain", oneByFrom.getOid(), "AdministrativeDomain", administrativeDomainOid));
        }

        this.commonAbilityService.createInterRelation(rels);
        return administrativeDomainOid;
    }

    private Folder createFolderNodeByclassify(Container container) {
        List<ClassificationTreeNode> classificationTreeNodes = this.classificationService.searchTree(container.getClassify(), "");
        ClassificationTreeNode classificationTreeNode = (ClassificationTreeNode) classificationTreeNodes.get(0);
        List<ClassificationTreeNode> children = classificationTreeNode.getChildren();
        container.setName(classificationTreeNode.getDisplayName());
        FolderLinkClassify folderLinkClassify = (FolderLinkClassify) BeanUtil.copyProperties(classificationTreeNode, new FolderLinkClassify());
        folderLinkClassify.setModelIcon(classificationTreeNode.getIconUri());
        Folder rootFolder = this.folderHelper.createFolderRootNode(container, folderLinkClassify);
        LocationInfo locationInfo = new LocationInfo();
        locationInfo.setCatalogType("Folder");
        locationInfo.setCatalogOid(rootFolder.getOid());
        locationInfo.setContainerType(container.getType());
        locationInfo.setContainerOid(container.getOid());
        locationInfo.setContainerModelDefinition(container.getModelDefinition());
        List<FolderForExport> folderList = new ArrayList();
        this.getFolderTree(children, folderList);
        this.folderHelper.buildFolderTree(locationInfo, folderList);
        return rootFolder;
    }

    private void getFolderTree(List<ClassificationTreeNode> treeNodes, List<FolderForExport> folderList) {
        if (!CollectionUtil.isEmpty(treeNodes)) {
            Iterator var3 = treeNodes.iterator();
            while (var3.hasNext()) {
                ClassificationTreeNode treeNode = (ClassificationTreeNode) var3.next();
                FolderForExport folder = new FolderForExport();
                folder.setName(treeNode.getDisplayName());
                folder.setDescription(treeNode.getDescription());
                ClassificationInfo classificationInfo = (ClassificationInfo) BeanUtil.copyProperties(treeNode, new ClassificationInfo());
                folder.setClassificationInfo(classificationInfo);
                folder.setModelIcon(treeNode.getIconUri());
                folderList.add(folder);
                List<FolderForExport> folderChildList = new ArrayList();
                folder.setChildren(folderChildList);
                List<ClassificationTreeNode> treeChildNodes = treeNode.getChildren();
                this.getFolderTree(treeChildNodes, folderChildList);
            }

        }
    }

    private Team initTeamForContainer(Container newContainer, Container container) {
        Team team = new Team();
        team.setOid(OidGenerator.newOid());
        team.setName(String.format("%s_Team", container.getName()));
        team.setDescription(String.format("Initialize team description for %s", container.getName()));
        team.setCreateBy(SessionHelper.getCurrentUser().getName());
        team.setCreateDate(System.currentTimeMillis());
        return this.containerService.createTeamWithLink(newContainer.getOid(), team);
    }

    private void handleTeamTemplate(ContainerCreateDTO dto, Team team) {
        Result<List<TeamTemplateRole>> roleResult = this.accountRemote.searchTeamTemplateRoleByOid(dto.getTeamTemplateOid());
        List<TeamTemplateRole> templateRoles = (List) RemoteAnalysisHelper.getResult(roleResult);
        if (!CollectionUtil.isEmpty(templateRoles)) {
            String teamOid = team.getOid();
            BindRole bindRole = new BindRole();
            bindRole.setTeamOid(teamOid);
            Map<String, TeamRole> oid2TeamRole = new HashMap();
            List<TeamRole> shouleBeBind = (List) templateRoles.stream().map((item) -> {
                TeamRole teamRole = (TeamRole) BeanUtil.copyProperties(item, new TeamRole());
                oid2TeamRole.put(item.getOid(), teamRole);
                return teamRole;
            }).collect(Collectors.toList());
            bindRole.setTeamRoleList(shouleBeBind);
            this.teamService.bindRole(bindRole);
//            Iterator var9 = templateRoles.iterator();
//
//            while (var9.hasNext()) {
//                TeamTemplateRole templateRole = (TeamTemplateRole) var9.next();
//                String roleOid = templateRole.getOid();
//                List<User> users = (List) RemoteAnalysisHelper.getResult(this.accountRemote.searchTeamTemplateUserByOid(roleOid));
//                if (!CollectionUtil.isEmpty(users)) {
//                    List<String> userOids = CollectionUtil.mapToList(users, BaseEntity::getOid);
//                    this.containerService.createTeamUserWithLinkForTeam(((TeamRole) oid2TeamRole.get(roleOid)).getOid(), userOids);
//                }
//            }

        }
    }

    private void bindSiteRoleUser(String siteRoleName, List<String> userAccounts, Team team) {
        String teamOid = team.getOid();
        TeamRole teamRole = this.teamRoleService.findTeamRole(teamOid, siteRoleName);
        if (teamRole == null) {
            teamRole = this.teamRoleService.bindSiteRole(teamOid, siteRoleName);
        }

        if (teamRole == null) {
            throw new JWIServiceException("Role named " + siteRoleName + " does not exists");
        } else if (!CollectionUtil.isEmpty(userAccounts)) {
            String teamRoleOid = teamRole.getOid();
            this.teamRoleService.bindUserByAccount(teamRoleOid, userAccounts);
        }
    }

    private void refreshUserCache(String domainOid, Team team) {
        List<TeamRoleWithUser> teamRoleWithUserList = this.teamHelper.fuzzyContent(team.getOid(), (String) null);
        Iterator var4 = teamRoleWithUserList.iterator();

        while (var4.hasNext()) {
            TeamRoleWithUser teamRoleWithUser = (TeamRoleWithUser) var4.next();
            if (!CollectionUtil.isEmpty(teamRoleWithUser.getUsers())) {
                List<User> users = new ArrayList();
                teamRoleWithUser.getUsers().forEach((userDTO) -> {
                    users.add(BeanUtil.copyProperties(userDTO, new User()));
                });
                teamRoleWithUser.setUsers((List) null);
                this.teamHelper.addUserRoleCache(domainOid, teamRoleWithUser, users);
            }
        }

    }

    public ComtainerTemplateExportDTO downloadByOid(String fileOid) {
        try {
            Response response = this.fileRemoteFeign.downloadByOid(fileOid);
            StringBuffer dataStr = new StringBuffer();

            try {
                InputStream inputStream = response.body().asInputStream();
                Throwable var5 = null;

                ComtainerTemplateExportDTO var9;
                try {
                    BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(inputStream));
                    Throwable var7 = null;

                    try {
                        String line;
                        while ((line = bufferedReader.readLine()) != null) {
                            dataStr.append(line);
                        }

                        var9 = (ComtainerTemplateExportDTO) JSONObject.parseObject(dataStr.toString(), ComtainerTemplateExportDTO.class);
                    } catch (Throwable var36) {
                        var7 = var36;
                        throw var36;
                    } finally {
                        if (bufferedReader != null) {
                            if (var7 != null) {
                                try {
                                    bufferedReader.close();
                                } catch (Throwable var35) {
                                    var7.addSuppressed(var35);
                                }
                            } else {
                                bufferedReader.close();
                            }
                        }

                    }
                } catch (Throwable var38) {
                    var5 = var38;
                    throw var38;
                } finally {
                    if (inputStream != null) {
                        if (var5 != null) {
                            try {
                                inputStream.close();
                            } catch (Throwable var34) {
                                var5.addSuppressed(var34);
                            }
                        } else {
                            inputStream.close();
                        }
                    }

                }

                return var9;
            } catch (Exception var40) {
                throw new JWIServiceException("file download error");
            }
        } catch (Throwable var41) {
            throw var41;
        }
    }

    public void buildFolderTree(LocationInfo locationInfo, List<FolderForExportDTO> folderTree) {
        if (!CollectionUtil.isEmpty(folderTree)) {
            FolderCreateDTO dto = new FolderCreateDTO();
            List<FolderForExportDTO> children = null;
            LocationInfo subInfo = (LocationInfo) BeanUtil.copyProperties(locationInfo, new LocationInfo());
            Iterator var6 = folderTree.iterator();

            while (var6.hasNext()) {
                FolderForExportDTO folderForExport = (FolderForExportDTO) var6.next();
                BeanUtil.copyProperties(folderForExport, dto);
                dto.setLocationInfo(locationInfo);
                Folder folder = this.folderHelper.create(dto);
                // 给文件添加角色人员信息
                List<TeamRoleForExportDTO> teamRoles = folderForExport.getTeamRoles();
                Team team = pdmFolderServiceI.getTeamRole(folder.getOid(), null);
                BindRole bindRole = new BindRole();
                bindRole.setTeamOid(team.getOid());
                bindRole.setContainerOid(locationInfo.getContainerOid());
                List<TeamRole> teamRoleList = new ArrayList<>();
                if (teamRoles != null) {
                    for (TeamRoleForExportDTO teamRoleForExportDTO : teamRoles) {
                        TeamRole byOid = jwiCommonService.findByOid(TeamRole.TYPE, teamRoleForExportDTO.getTeamRoleOid(), TeamRole.class);
                        teamRoleList.add(byOid);
//                        List<UserForExport> users = teamRoleForExportDTO.getUsers();
//                        if (users == null) {
//                            continue;
//                        }
//                        // 给角色添加人员信息
//                        log.info("角色人员信息" + teamRoleForExportDTO.toString());
//                        BindUser bindUser = new BindUser();
//                        bindUser.setTeamRoleOid(teamRoleForExportDTO.getTeamRoleOid());
//                        bindUser.setContainerOid(locationInfo.getContainerOid());
//                        List<User> teamUserList = new ArrayList<>();
//                        for (UserForExport userForExport : users) {
//                            User byKey = jwiCommonService.findByKey(User.TYPE, "account", userForExport.getAccount(), User.class);
//                            teamUserList.add(byKey);
//                        }
//                        bindUser.setTeamUserList(teamUserList);
//                        this.permissionCacheUtil.clearCatch((List) bindUser.getTeamUserList().stream().map(BaseEntity::getOid).collect(Collectors.toList()));
//                        try {
//                            ObjectMapper objectMapper = new ObjectMapper();
//                            log.info("要添加的人员信息为" + objectMapper.writeValueAsString(bindUser));
//                        } catch (JsonProcessingException e) {
//                            e.printStackTrace();
//                        }
//                        Long aLong = this.teamHelper.bindUser(bindUser);
//                        log.info("添加人员的结果为：" + aLong);
                    }
                }
                bindRole.setTeamRoleList(teamRoleList);
                this.teamHelper.bindRole(bindRole);

                children = folderForExport.getChildren();
                subInfo.setCatalogOid(folder.getOid());
                this.buildFolderTree(subInfo, children);
            }
        }
    }
}
