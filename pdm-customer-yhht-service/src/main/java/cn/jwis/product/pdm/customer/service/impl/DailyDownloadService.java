package cn.jwis.product.pdm.customer.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.jwis.framework.base.util.StringUtil;
import cn.jwis.platform.plm.sysconfig.entity.ConfigItem;
import cn.jwis.platform.plm.sysconfig.preferences.PreferencesService;
import cn.jwis.product.pdm.customer.repo.DailyDownloadRepo;
import cn.jwis.product.pdm.customer.service.util.FIleOutMailUtil;
import cn.jwis.product.pdm.customer.service.util.RedisService;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Transactional
@Slf4j
public class DailyDownloadService {
    @Resource
    RedisService redisService;

    @Resource
    private DailyDownloadRepo dailyDownloadRepo;

    @Autowired
    private PreferencesService preferencesService;

    @Autowired
     FIleOutMailUtil fIleOutMailUtil;

    // 定义文件分隔符和临时目录路径
    public final static String fileSeparator = java.io.File.separator;
    public final static String tmpPath = System.getProperty("os.name").toLowerCase().contains("win") ?
            ("C:" + fileSeparator + "tmp" + fileSeparator) :
            (fileSeparator + "tmp" + fileSeparator);
    /**
     * 扫描 Redis 获取下载数据
     */
    public List<Map<String, Object>> scanRedisKeys() {
        int maxDownloadsPerDay = 1;
        int downloadLimit = this.getDownLoadNumber(maxDownloadsPerDay); // 从 Neo4j 获取每日最大下载量
        log.info("动态配置的每日最大下载量为: {}", downloadLimit);

        // 扫描 Redis 中的键
        List<String> keys = redisService.scan("DownloadLimit:*");
        List<Map<String, Object>> userData = new ArrayList<>();

        for (String key : keys) {
            Object value = redisService.get(key); // 从 Redis 获取值
            if (value == null) {
                log.warn("Redis key [{}] 的值为 null，跳过", key);
                continue;
            }

            int downloads;
            try {
                downloads = Integer.parseInt(value.toString()); // 确保值可以转换为整数
            } catch (NumberFormatException e) {
                log.error("Redis key [{}] 的值 [{}] 无法解析为整数，跳过", key, value, e);
                continue;
            }

            // 匹配 downloadLimit 的数据
            if (downloads == downloadLimit) {
                String oid = key.split(":")[1].split("_")[0]; // 提取 oid
                Map<String, Object> data = new HashMap<>();
                data.put("key", key);
                data.put("oid", oid);
                data.put("downloads", downloads);
                userData.add(data);
            }
        }

        return userData;
    }

    /**
     * 合并 Redis 数据和 Neo4j 用户信息
     */
    public List<Map<String, Object>> mergeData(List<Map<String, Object>> redisData) {
        List<String> oids = new ArrayList<>();
        for (Map<String, Object> item : redisData) {
            oids.add((String) item.get("oid"));
        }

        List<Map<String, Object>> userInfo = dailyDownloadRepo.fetchUserAndDepartmentData(oids);
        List<Map<String, Object>> finalData = new ArrayList<>();

        for (Map<String, Object> redisItem : redisData) {
            for (Map<String, Object> user : userInfo) {
                if (redisItem.get("oid").equals(user.get("oid"))) {
                    Map<String, Object> combined = new HashMap<>();
                    combined.putAll(redisItem);
                    combined.putAll(user);
                    combined.put("date", DateUtil.today());
                    finalData.add(combined);
                }
            }
        }
        return finalData;
    }

    /**
     * 导出数据到 Excel
     */
    public String exportToExcel(List<Map<String, Object>> data, String fileNamePrefix) {
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("用户下载数据");

        // 设置标题
        Row header = sheet.createRow(0);
        String[] headers = {"姓名", "部门", "下载数量", "日期"};
        for (int i = 0; i < headers.length; i++) {
            Cell cell = header.createCell(i);
            cell.setCellValue(headers[i]);
        }

        // 填充数据
        int rowIndex = 1;
        for (Map<String, Object> row : data) {
            Row sheetRow = sheet.createRow(rowIndex++);
            sheetRow.createCell(0).setCellValue((String) row.get("name"));
            sheetRow.createCell(1).setCellValue((String) row.get("department_path"));
            sheetRow.createCell(2).setCellValue((int) row.get("downloads"));
            sheetRow.createCell(3).setCellValue((String) row.get("date"));
        }

        // 自动调整列宽
        for (int i = 0; i < headers.length; i++) {
            sheet.autoSizeColumn(i);
        }

        // 保存文件到指定路径
        String filePath = tmpPath + fileNamePrefix + fileSeparator +
                DateUtil.format(new Date(), "yyyyMMddHHmmss") + fileSeparator +
                "文件每日下载限制用户清单" + DateUtil.today() + ".xlsx";
        try {
            // 确保目录存在
            File file = new File(filePath);
            File parentDir = file.getParentFile();
            if (!parentDir.exists()) {
                parentDir.mkdirs(); // 创建父级目录
            }

            FileOutputStream fileOut = new FileOutputStream(filePath);
            workbook.write(fileOut);
            fileOut.close();
            workbook.close();
            log.info("Excel 文件已保存为: " + filePath);
        } catch (IOException e) {
            log.error("Excel 文件保存失败", e);
        }
        return filePath;
    }

    /**
     * 如果无数据，导出空的 Excel 文件
     */
    public String exportEmptyExcel(String fileNamePrefix) {
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("用户下载数据");

        Row header = sheet.createRow(0);
        header.createCell(0).setCellValue("提示");
        Row row = sheet.createRow(1);
        row.createCell(0).setCellValue("没有符合的数据");

        // 保存文件到指定路径
        String filePath = tmpPath + fileNamePrefix + fileSeparator +
                DateUtil.format(new Date(), "yyyyMMddHHmmss") + fileSeparator +
                "文件每日下载限制用户清单" + DateUtil.today() + ".xlsx";
        try {
            // 确保目录存在
            File file = new File(filePath);
            File parentDir = file.getParentFile();
            if (!parentDir.exists()) {
                parentDir.mkdirs(); // 创建父级目录
            }

            FileOutputStream fileOut = new FileOutputStream(filePath);
            workbook.write(fileOut);
            fileOut.close();
            workbook.close();
            log.info("空 Excel 文件已保存为: " + filePath);
        } catch (IOException e) {
            log.error("空 Excel 文件保存失败", e);
        }
        return filePath;
    }

    /**
     * 主任务入口
     */
    public void runTask() {
        log.info("开始扫描 Redis");
        List<Map<String, Object>> redisData = scanRedisKeys();
        String filePrefixPath = "DailyDownloadService";
        // 调用抽取的方法来加载邮件列表
        List<String> emailList = loadEmailList();

        // 如果 emailList 为空，记录日志并采取适当措施
        if (emailList.isEmpty()) {
            log.error("邮件列表为空，无法发送邮件。任务结束");
            return;
        }
        if (redisData.isEmpty()) {
            log.info("没有符合条件的 Redis 数据，任务结束，不发送邮件");
            return;
        }

        log.info("开始查询 Neo4j 用户信息");
        List<Map<String, Object>> finalData = mergeData(redisData);

        if (finalData.isEmpty()) {
            log.info("没有匹配的用户数据，任务结束，不发送邮件");
            return;
        }

        String excelPath = exportToExcel(finalData, filePrefixPath);
        sendEmail(Collections.singletonList(excelPath), "用户每日下载清单", emailList);
    }

    /**
     * 发送邮件
     */
    private void sendEmail(List<String> filePaths, String subject,List<String> emailList ) {
        List<File> files = new ArrayList<>();
        for (String path : filePaths) {
            File file = new File(path);
            if (file.exists()) {
                files.add(file);
            } else {
                log.warn("附件文件不存在: {}", path);
            }
        }

        if (files.isEmpty()) {
            log.warn("没有有效的附件，邮件将不发送");
            return;
        }

        String emailContent = "这是每日用户下载数据的清单，请查收。";

        try {
            fIleOutMailUtil.sendMailToMultipleRecipients("文件下载日报",emailList, subject, emailContent, files);
            log.info("邮件发送成功");
        } catch (Exception e) {
            log.error("邮件发送失败", e);
        }
    }

    private int getDownLoadNumber(int maxDownloadsPerDay) {
        ConfigItem item = preferencesService.queryConfigValue("download_limit");
        if (item != null && StringUtil.isNotBlank(item.getValue())) {
            try {
                // 检查 item.getValue() 是否为数字
                if (item.getValue().matches("\\d+")) {
                    maxDownloadsPerDay = Integer.parseInt(item.getValue());
                } else {
                    log.warn("配置项值不是有效数字：{}", item.getValue());
                }
            } catch (NumberFormatException e) {
                log.error("解析每日下载限制配置时发生错误，使用默认值: {}", maxDownloadsPerDay, e);
            }
        } else {
            log.info("配置项为空或未定义，使用默认每日下载次数: {}", maxDownloadsPerDay);
        }
        return maxDownloadsPerDay;
    }

    // 抽取出来的方法，加载并返回邮件列表
    public List<String> loadEmailList() {
        List<String> emailList = new ArrayList<>();

        // 获取配置的邮件列表
        ConfigItem item = preferencesService.queryConfigValue("download_email_list");
        if (item != null && StringUtil.isNotBlank(item.getValue())) {
            String value = item.getValue().trim();
            if (StringUtil.isNotBlank(value)) {
                String[] split = value.split("\\|");
                List<String> roleList = Arrays.stream(split)
                        .map(String::trim)
                        .filter(s -> !s.isEmpty()) // 去除空字符串
                        .collect(Collectors.toList());
                if (!roleList.isEmpty()) {
                    emailList = roleList;
                    log.info("成功加载邮件列表: {}", emailList);
                } else {
                    log.warn("邮件列表配置存在空角色名，邮件列表为空");
                }
            } else {
                log.warn("邮件列表配置值为空");
            }
        } else {
            log.warn("未找到邮件列表配置项或配置值为空");
        }

        return emailList;
    }
}