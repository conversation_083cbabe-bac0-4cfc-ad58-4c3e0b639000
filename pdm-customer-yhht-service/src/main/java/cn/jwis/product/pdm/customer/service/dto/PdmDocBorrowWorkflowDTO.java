package cn.jwis.product.pdm.customer.service.dto;

import cn.jwis.platform.plm.foundation.location.able.info.LocationInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR> lhm
 * @Date ： Created in 2023/9/19 10:37
 * @Description :
 */
@Data
@EqualsAndHashCode
public class PdmDocBorrowWorkflowDTO {

    @NotBlank(message = "Document的oid不能为空")
    @ApiModelProperty("document的oid")
    private String oid;

    @ApiModelProperty("类型")
    private String type = "DocumentIteration";

    @NotBlank(message = "Document的名称不能为空")
    @ApiModelProperty("文档对象的名称")
    private String name;

    @NotEmpty
    @Valid
    private String catalogOid;
    @NotEmpty
    @Valid
    private String catalogType;
    private String containerOid;
    private String containerType;


    @ApiModelProperty("文档对象源位置")
    private LocationInfo source;
}
