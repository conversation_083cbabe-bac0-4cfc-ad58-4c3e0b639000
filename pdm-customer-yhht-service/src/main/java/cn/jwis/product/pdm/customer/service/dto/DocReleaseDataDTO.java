package cn.jwis.product.pdm.customer.service.dto;

import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2023/8/15 17:28
 * @Email <EMAIL>
 */
@Data
@EqualsAndHashCode
public class DocReleaseDataDTO {
    @ApiModelProperty("businessOid")
    private String businessOid;
    @ApiModelProperty("oid")
    private @NotBlank String oid;
    @ApiModelProperty("type")
    private String type;
    @ApiModelProperty("modelDefinition")
    private String modelDefinition;
    @ApiModelProperty("文档名称")
    private String name;
    @ApiModelProperty("文档编码")
    private String number;
    @ApiModelProperty("文档描述")
    private String description;
    @ApiModelProperty("大版本")
    private String version;
    @ApiModelProperty("状态")
    private String lifecycleStatus;
    @ApiModelProperty("更新人账号")
    private String updatorAccount;
    @ApiModelProperty("更新人名称+部门")
    private String updatorNameAndDepartment;
    @ApiModelProperty("更新时间")
    private long updateTime;
    @ApiModelProperty("文档分类名路径：文档-任务书-单机研制任务书")
    private String clsPath;
    @ApiModelProperty("产品型号")
    private String productName;
    @ApiModelProperty("扩展属性")
    private JSONObject extensionContent;
    @ApiModelProperty("分类属性")
    private JSONObject clsProperty;
    @ApiModelProperty("主要文件")
    private List<IntegrationFile> primaryFile = new ArrayList<>();
    @ApiModelProperty("次要文件")
    private List<IntegrationFile> secondaryFile = new ArrayList<>();
    @ApiModelProperty("关联描述物料和参考物料")
    private List<BaseEntityDTO> relationPart = new ArrayList<>();
}
