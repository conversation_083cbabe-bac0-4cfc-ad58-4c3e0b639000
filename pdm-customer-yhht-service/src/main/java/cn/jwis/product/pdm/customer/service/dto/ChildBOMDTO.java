package cn.jwis.product.pdm.customer.service.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode
public class ChildBOMDTO {
    /**
     * 安装位置号
     */
    private String assemblingNo;
    /**
     * 安装位置号1
     */
    private String assemblingNo1;
    /**
     * 安装位置号10
     */
    private String assemblingNo10;
    /**
     * 安装位置号2
     */
    private String assemblingNo2;
    /**
     * 安装位置号3
     */
    private String assemblingNo3;
    /**
     * 安装位置号4
     */
    private String assemblingNo4;
    /**
     * 安装位置号5
     */
    private String assemblingNo5;
    /**
     * 安装位置号6
     */
    private String assemblingNo6;
    /**
     * 安装位置号7
     */
    private String assemblingNo7;
    /**
     * 安装位置号8
     */
    private String assemblingNo8;
    /**
     * 安装位置号9
     */
    private String assemblingNo9;
    /**
     * 材料编码
     */
    private String MatProcode;
    /**
     * 标准用量
     */
    private double Qty;


}