package cn.jwis.product.pdm.customer.service.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode
public class BOMChangeEntry {

    private String submitter; // 提交者
    private String departmentName; // 所属部门
    private String modificationTime; // 修改时间 (可以考虑使用 Date 或 LocalDateTime 类型)
    private String changeCategory; // 更改类别
    private String changeReason; // 更改原因
    private String changePlan; // 更改方案
    private String changeImpactAnalysis; // 更改影响分析
    private String affectedProjects; // 更改受影响的项目
    private String changedBomMaterialNumber; // 更改BOM料号
    private String changedBomConvertedCode; // 更改BOM转换后编码
    private String changedBomTtjdCode; // 更改BOM天通精电编码
    private String changedBomProductName; // 更改BOM产品名称
    private String changedBomProductSpecification; // 更改BOM产品规格
    private String changedBomManufacturer; // 更改BOM生产厂家
    private String changedBomPackageForm; // 更改BOM封装形式
    private String changedBomQualityGrade; // 更改BOM质量等级
    private String changedBomUnit; // 更改BOM单位
    private String changedBomVersion; // 更改BOM版本

    private String changeDetailOid; // 变更明细.oid
    private String changeDetailSequenceNumber; // 变更明细.序号
    private String changeDetailChangeType; // 变更明细.变更方式
    private String changeDetailMaterialCode; // 变更明细.物料编码
    private String changeDetailConvertedU9Code; // 变更明细.转化后U9编码
    private String changeDetailTtjdCode; // 变更明细.天通精电编码
    private String changeDetailProductName; // 变更明细.产品名称
    private String changeDetailProductSpecification; // 变更明细.产品规格
    private String changeDetailUnit; // 变更明细.单位
    private String changeDetailPreviousQuantity; // 变更明细.变更前数量
    private String changeDetailQuantity; // 变更明细.数量
    private String changeDetailInstallationPosition; // 变更明细.安装位号0
    private String changeDetailInstallationPosition1; // 变更明细.安装位号1
    private String changeDetailInstallationPosition2; // 变更明细.安装位号2
    private String changeDetailInstallationPosition3; // 变更明细.安装位号3
    private String changeDetailInstallationPosition4; // 变更明细.安装位号4
    private String changeDetailInstallationPosition5; // 变更明细.安装位号5
    private String changeDetailInstallationPosition6; // 变更明细.安装位号6
    private String changeDetailInstallationPosition7; // 变更明细.安装位号7
    private String changeDetailInstallationPosition8; // 变更明细.安装位号8
    private String changeDetailInstallationPosition9; // 变更明细.安装位号9
    private String changeDetailDescription; // 变更明细.描述
    private String changeDetailManufacturer; // 变更明细.生产厂家
    private String changeDetailPackageForm; // 变更明细.封装形式
    private String changeDetailQualityGrade; // 变更明细.质量等级
    private String changeDetailMslLevel; // 变更明细.MSL（湿敏等级）
}