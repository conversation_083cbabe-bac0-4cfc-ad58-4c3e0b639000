package cn.jwis.product.pdm.customer.service.dto;

import cn.jwis.platform.plm.file.entity.FileMetadata;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2023/8/16 9:35
 * @Email <EMAIL>
 */
@Data
@EqualsAndHashCode
public class IntegrationFile {

    public IntegrationFile(FileMetadata fileMetadata){
        this.name = fileMetadata.getFileOriginalName();
        this.url = fileMetadata.getFilePath();
        this.size =fileMetadata.getFileSize();
        this.type = fileMetadata.getFileSuffix();
    }

    @ApiModelProperty("文件名称")
    private String name;
    @ApiModelProperty("minio文件下载链接")
    private String url;
    @ApiModelProperty("文件businessType")
    private String type;
    @ApiModelProperty("文件大小")
    private long size;
}
