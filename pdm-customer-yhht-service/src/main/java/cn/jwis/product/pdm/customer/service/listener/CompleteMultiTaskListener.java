package cn.jwis.product.pdm.customer.service.listener;


import cn.jwis.framework.base.annotation.Description;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.TaskService;
import org.activiti.engine.delegate.DelegateTask;
import org.activiti.engine.delegate.TaskListener;
import org.activiti.engine.impl.persistence.entity.VariableInstance;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Description("多实例任务完成事件监听器")
@Component
@Slf4j
public class CompleteMultiTaskListener implements TaskListener {

    private static TaskService taskService;
    @Autowired
    public void setTaskService(TaskService taskService){
        this.taskService=taskService;
    }


    @Override
    public void notify(DelegateTask delegateTask) {
        VariableInstance variableInstance = delegateTask.getVariableInstance("agree");
        if("1".equals(variableInstance.getTextValue())){   // 通过时，设置为0，标识没有驳回
            delegateTask.setVariable("nrOfRejectedInstances",0);
        }else{  // 驳回时，设置为1，标识需要一票否决
            delegateTask.setVariable("nrOfRejectedInstances",1);
        }


    }
}