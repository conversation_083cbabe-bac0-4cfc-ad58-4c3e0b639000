package cn.jwis.product.pdm.customer.service.signtemplate;

import cn.jwis.framework.base.exception.JWIException;
import cn.jwis.platform.plm.signtemplate.entity.BeSignDocWithFileData;
import cn.jwis.platform.plm.signtemplate.service.delegate.AppendDelegate;
import cn.jwis.product.pdm.customer.service.util.Doc2PdfUtil;
import com.aspose.words.Document;
import com.aspose.words.DocumentBuilder;
import com.aspose.words.ImportFormatMode;
import com.aspose.words.SaveFormat;
import com.aspose.words.Section;
import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.config.Configure;
import com.deepoove.poi.plugin.table.LoopRowTableRenderPolicy;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.poi.openxml4j.util.ZipSecureFile;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;


/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2023/10/11 9:30
 * @Email <EMAIL>
 */
@Service
@Primary
@Slf4j
public class CustomerSignAppendDelegate extends AppendDelegate {

    @SneakyThrows
    @Override
    public Map<String, BeSignDocWithFileData> startToSign(List<BeSignDocWithFileData> beSignFileDataList, Object replaceData, String replaceKey) {
        Map<String, BeSignDocWithFileData> result = new HashMap<>();
        Doc2PdfUtil.registerWord_v_22_5();
        ZipSecureFile.setMinInflateRatio(-1.0d);
        List<File> tempFileList = new ArrayList<>();
        for (BeSignDocWithFileData beSignFileData : beSignFileDataList) {
            try (ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(beSignFileData.getFileBytes())) {
                String name = String.format(Locale.ENGLISH, "signDoc_%s_%s", beSignFileData.getOid(),
                        new Date().getTime());
                File tempFile = FileUtils.getFile(FileUtils.getTempDirectory(), name + beSignFileData.getFileSuffix());
                Document finalDoc;
                // 字节流为空 则创建新的空文档
                if(ObjectUtils.isEmpty(beSignFileData.getFileBytes())) {
                    finalDoc = new Document();
                } else {
                    finalDoc = new Document(byteArrayInputStream);
                }
                // 创建替换内容后的签名模板 并输出到到includeOutputStream
                Document signDoc = new Document();
                DocumentBuilder builder = new DocumentBuilder(signDoc);
                // 插入文本
                builder.write(String.format(Locale.ENGLISH, "{{+%s}}", replaceKey));
                ByteArrayOutputStream signDocOutputStream = new ByteArrayOutputStream();
                signDoc.save(signDocOutputStream, SaveFormat.DOCX);
                ByteArrayOutputStream includeOutputStream = new ByteArrayOutputStream();

                //单独处理会签表格
                LoopRowTableRenderPolicy policy = new LoopRowTableRenderPolicy();
                Configure config = Configure.builder()
                        .bind("cs",policy).build();

                XWPFTemplate.compile(new ByteArrayInputStream(signDocOutputStream.toByteArray()),config).render(replaceData)
                        .writeAndClose(includeOutputStream);
                // 加载有内容的签名模板，并在流程主文件开始部分进行段落填充
                Document insertDoc = new Document(new ByteArrayInputStream(includeOutputStream.toByteArray()));
                for (Section section : insertDoc.getSections()) {
                    finalDoc.prependChild(finalDoc.importNode(section, true, ImportFormatMode.KEEP_DIFFERENT_STYLES));
                }
                finalDoc.save(tempFile.getCanonicalPath(), SaveFormat.DOCX);
                //返回生成后的临时文件目录
                beSignFileData.setFileBytes(null);
                beSignFileData.setFilePath(tempFile.getCanonicalPath());
                result.put(beSignFileData.getOid(), beSignFileData);
                tempFileList.add(tempFile);
            } catch (IOException e) {
                log.error("singDoc fail oid", e);
                tempFileList.forEach(file -> file.deleteOnExit());
                throw new JWIException(String.format("signDoc fail oid:%s,message:%s", beSignFileData.getOid(),
                        e.getMessage()));
            }
        }
        return result;
    }
}
