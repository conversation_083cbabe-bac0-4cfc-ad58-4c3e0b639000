package cn.jwis.product.pdm.customer.service.interf;

import cn.jwis.product.pdm.customer.entity.CustomerFolderTreeNode;
import cn.jwis.product.pdm.customer.service.dto.IDSDocCreateDTO;
import cn.jwis.product.pdm.document.entity.DocumentIteration;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface DocumentMilkyWayHelper {


    List<CustomerFolderTreeNode> searchFolders(String account);

    DocumentIteration createData(MultipartFile[] file,String account, String number) throws Exception;

    String getIDSNumber(IDSDocCreateDTO dto,String account,String deliveryOid);

    Object createDocumentForIDS(MultipartFile[] file, String account, String number, String location, String source, String auditlist, String fileEncode, String deliveryOid);
}
