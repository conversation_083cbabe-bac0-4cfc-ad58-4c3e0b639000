package cn.jwis.product.pdm.customer.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.jwis.framework.base.bean.util.BeanUtil;
import cn.jwis.framework.base.domain.able.RelationAble;
import cn.jwis.framework.base.domain.able.info.ModelInfo;
import cn.jwis.framework.base.dto.UserDTO;
import cn.jwis.framework.base.exception.JWIException;
import cn.jwis.framework.base.util.Assert;
import cn.jwis.framework.base.util.CollectionUtil;
import cn.jwis.framework.base.util.SpringContextUtil;
import cn.jwis.framework.base.util.StringUtil;
import cn.jwis.framework.base.web.session.SessionHelper;
import cn.jwis.platform.plm.account.user.service.UserHelper;
import cn.jwis.platform.plm.dataengine.common.JWICommonService;
import cn.jwis.platform.plm.foundation.classification.able.info.ClassificationInfo;
import cn.jwis.platform.plm.foundation.common.param.RelatedBatchOperate;
import cn.jwis.platform.plm.foundation.common.service.CommonAbilityHelper;
import cn.jwis.platform.plm.foundation.common.service.InstanceHelper;
import cn.jwis.platform.plm.foundation.lifecycle.entity.LifecycleStatus;
import cn.jwis.platform.plm.foundation.location.able.info.LocationInfo;
import cn.jwis.platform.plm.foundation.model.entity.VertexDef;
import cn.jwis.platform.plm.foundation.model.service.ModelHelper;
import cn.jwis.platform.plm.foundation.relationship.enums.RelationConstraint;
import cn.jwis.platform.plm.foundation.versionrule.able.LockAble;
import cn.jwis.platform.plm.foundation.versionrule.able.info.LockInfo;
import cn.jwis.platform.plm.foundation.versionrule.service.VersionRuleHelper;
import cn.jwis.platform.plm.modelexcel.constants.ModelSystemPropKey;
import cn.jwis.platform.plm.modelexcel.dto.ModelExcelAllCreateDTO;
import cn.jwis.platform.plm.modelexcel.service.ModelDataSaveService;
import cn.jwis.product.pdm.customer.entity.Forming;
import cn.jwis.product.pdm.customer.remote.SysconfigRemote;
import cn.jwis.product.pdm.customer.service.dto.CustomerPartUpdateDTO;
import cn.jwis.product.pdm.customer.service.interf.PartScreenAutoHelper;
import cn.jwis.product.pdm.partbom.part.dto.BatchCopyPartDTO;
import cn.jwis.product.pdm.partbom.part.dto.BatchCopyPartDocumentBatchItemDTO;
import cn.jwis.product.pdm.partbom.part.entity.Part;
import cn.jwis.product.pdm.partbom.part.entity.PartIteration;
import cn.jwis.product.pdm.partbom.part.service.PartHelper;
import cn.jwis.product.pdm.partbom.part.service.PartService;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: 汪江
 * @data: 2023-12-21 14:31
 * @description:
 **/
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class CustomerPartDataSaveServiceImpl implements ModelDataSaveService {
    @Resource
    private CommonAbilityHelper commonAbilityHelper;

    @Autowired
    JWICommonService jwiCommonService;

    @Resource
    private ModelHelper modelHelper;

    @Resource
    PartService partService;

    @Autowired
    PartHelper partHelper;

    @Autowired
    SysconfigRemote sysconfigRemote;

    @Resource
    VersionRuleHelper versionRuleService;

    @Autowired
    PartScreenAutoHelper partScreenAutoHelper;

    @Autowired
    private InstanceHelper instanceHelper;

    //分类下拉值 从系统配置获取
//    cn_jwis_zldj:"bjz",//质量等级
//    cn_jwis_fzxs:"PackageType",//封装形式
//    cn_jwis_sccj:"manufacturer",//生产厂家
//    cn_jwis_smdj:"msl",//湿敏等级
//    cn_jwis_bzh:"standardNumber",//标准号

    @Value("#{${cls.sysconfig.map:{cn_jwis_zldj:'bjz',cn_jwis_fzxs:'PackageType',cn_jwis_sccj:'manufacturer',cn_jwis_smdj:'msl',cn_jwis_bzh:'standardNumber'}}}")
    private Map<String, String> clsSysconfigMap;

    @Override
    public void batchSave(List<ModelExcelAllCreateDTO> res) {
        List<CustomerPartUpdateDTO> batchPart = new ArrayList<>(res.size());

        Set<String> modelDefines = modelHelper.flapTree("Part").stream().map(VertexDef::getCode).collect(Collectors.toSet());

        for (ModelExcelAllCreateDTO re : res) {
            CustomerPartUpdateDTO customerPartUpdateDTO = new CustomerPartUpdateDTO();

            BeanUtil.copyProperties(re,customerPartUpdateDTO);

            //系统属性
            Map<String, String> partExcelDTO = re.getAllPropData();

            if (re.getLifecycleStatus() != null) {
                LifecycleStatus status = re.getLifecycleStatus();
                customerPartUpdateDTO.setLifecycleStatus(status.getCode());
            }

            String owner = partExcelDTO.get("owner");
            if(!StringUtils.isEmpty(owner))
                customerPartUpdateDTO.setOwner(owner);

            customerPartUpdateDTO.setDefaultUnit(partExcelDTO.get(ModelSystemPropKey.defaultUnit));
            customerPartUpdateDTO.setSource(partExcelDTO.get(ModelSystemPropKey.source));
            customerPartUpdateDTO.setCurrentStage(partExcelDTO.get(ModelSystemPropKey.currentStage));
            customerPartUpdateDTO.setPreferenceLevel(partExcelDTO.get(ModelSystemPropKey.preferenceLevel));

            //基本属性
            customerPartUpdateDTO.setNumber(partExcelDTO.get(ModelSystemPropKey.number));
            customerPartUpdateDTO.setName(partExcelDTO.get(ModelSystemPropKey.name));
            if (!modelDefines.contains(re.getModelDefinition())) {
                throw new JWIException("部件不支持导入类型：" + partExcelDTO.get(ModelSystemPropKey.modelDefinition));
            }

            customerPartUpdateDTO.setDescription(partExcelDTO.get(ModelSystemPropKey.description));

            customerPartUpdateDTO.setExcelVersion(partExcelDTO.get("excelVersion"));
            customerPartUpdateDTO.setExcelIteratedVersion(partExcelDTO.get("excelIteratedVersion"));
            String relationNumber = partExcelDTO.get("relationNumber");
            customerPartUpdateDTO.setRelationNumber(StringUtils.isNotBlank(relationNumber) ? relationNumber : "");

            batchPart.add(customerPartUpdateDTO);
        }
        this.checkImportData(batchPart);
        List<CustomerPartUpdateDTO> createExcelParts = batchPart.stream().filter(t -> t.isCreateFlag()).collect(Collectors.toList());
        //只校验新创建的数据
        this.checkExtensionContent(createExcelParts);
        this.save(batchPart);

        log.info("批量多模型保存：" + res.size());
    }

    private void checkImportData(List<CustomerPartUpdateDTO> batchPart) {
        Map<String,List<String>> numberNameMap = new HashMap<>();
        for (CustomerPartUpdateDTO dto : batchPart) {
            if(StringUtils.isNotBlank(dto.getNumber())) {
                numberNameMap.computeIfAbsent(dto.getNumber(),k->new ArrayList<>()).add(dto.getName());
            }
        }
        List<String> errorList = numberNameMap.entrySet().stream().filter(item->item.getValue().size()>1).map(entry ->
                "编码重复:" + entry.getKey() + " 部件名称:" + StringUtils.join(entry.getValue(),",")).collect(Collectors.toList());
        Assert.isEmpty(errorList,StringUtils.join(errorList,";"));
    }


    private void checkExtensionContent(List<CustomerPartUpdateDTO> batchPart) {
        List<String> errorList = new ArrayList<>();

        // 定义需要检查的属性及其对应的中文名称
        Map<String, String> propertyToChineseName = new HashMap<>();
        propertyToChineseName.put("cn_jwis_zldj", "质量等级");
        propertyToChineseName.put("cn_jwis_smdj", "湿敏等级");
        propertyToChineseName.put("cn_jwis_fzxs", "封装形式");
        propertyToChineseName.put("cn_jwis_sxzldj", "是否需要筛选质量等级");
        propertyToChineseName.put("cn_jwis_sccj", "生产厂家");

        for (CustomerPartUpdateDTO dto : batchPart) {
            log.info("checkExtensionContent当前导入的物料信息:{}", JSONUtil.toJsonStr(dto));
            ClassificationInfo classificationInfo = dto.getClassificationInfo();
            // 检查 displayName 是否为空或没有值
            if (classificationInfo == null || StringUtils.isBlank(classificationInfo.getDisplayName())) {
                errorList.add("部件 " + dto.getName() + " 的分类信息中的 料品分类 为空或没有值");
                continue;
            }

            String displayName = classificationInfo.getDisplayName();
            // 判断是否需要检查属性
            if (!displayName.toLowerCase().startsWith("rm") || displayName.toLowerCase().contains("rm0325")) {
                continue; // 如果 displayName 不以 "rm" 开头或者不包含 "rm0325"，则跳过校验
            }

            JSONObject properties = classificationInfo.getProperties();
            if (properties == null) {
                errorList.add("部件 " + dto.getName() + " 的扩展内容为空");
                continue;
            }

            // 优化 cn_jwis_gg 的取值逻辑
            String partName = properties.containsKey("cn_jwis_gg") &&
                    StringUtils.isNotBlank(properties.getString("cn_jwis_gg"))
                    ? properties.getString("cn_jwis_gg")
                    : dto.getNumber();

            // 收集当前部件的所有错误
            List<String> partErrors = new ArrayList<>();
            for (Map.Entry<String, String> entry : propertyToChineseName.entrySet()) {
                String property = entry.getKey();
                String chineseName = entry.getValue();
                if (!properties.containsKey(property) || StringUtils.isBlank(properties.getString(property))) {
                    partErrors.add(chineseName);
                }
            }

            // 如果当前部件有错误，汇总并添加到错误列表
            if (!partErrors.isEmpty()) {
                errorList.add("部件 " + partName + " 的扩展内容缺少属性或属性值为空: " + StringUtils.join(partErrors, ", "));
            }
        }

        if (!errorList.isEmpty()) {
            throw new JWIException("扩展内容校验失败: " + StringUtils.join(errorList, "; "));
        }
    }

    private void save(List<CustomerPartUpdateDTO> batchPart) {

        List<String> codes = batchPart.stream().map(CustomerPartUpdateDTO::getNumber)
                .filter(StringUtil::isNotBlank).collect(Collectors.toList());

        List<PartIteration> dbPart = commonAbilityHelper.findDetailEntityByNumber(codes, PartIteration.TYPE)
                .stream().map(item -> (PartIteration) item).collect(Collectors.toList());

        Map<String, PartIteration> numberDbPartMap = dbPart.stream().collect(Collectors.toMap(PartIteration::getNumber, part -> part, (o1, o2) -> o2));

        //更新数据
        List<CustomerPartUpdateDTO> updateExcelParts = batchPart.stream().filter(t -> !t.isCreateFlag()).collect(Collectors.toList());

        String treadName = Thread.currentThread().getName();
        log.info("Part save {} ",treadName);
        if(CollectionUtil.isNotEmpty(updateExcelParts)){
            Map<String, CustomerPartUpdateDTO> numberExcelPartMap=new HashMap<>();
            List<ModelInfo> checkOutData = updateExcelParts.stream()
                    .map(item -> {
                        ModelInfo modelInfo = new ModelInfo();
                        String number = item.getNumber();
                        numberExcelPartMap.put(number,item);
                        PartIteration partIteration = numberDbPartMap.get(number);
                        modelInfo.setOid(partIteration.getOid());
                        modelInfo.setType(partIteration.getType());
                        modelInfo.setModelDefinition(partIteration.getModelDefinition());
                        return modelInfo;
                    })
                    .collect(Collectors.toList());

            List<LockAble> lockAbles = partHelper.checkOut(checkOutData);

            List<PartIteration> updateParts=new ArrayList<>();
            for (LockAble lockAble : lockAbles) {
                PartIteration byCheckOut = (PartIteration) lockAble;
                String checkOutOid = byCheckOut.getOid();
                String checkOutType = byCheckOut.getType();
                String checkOutModelDefinition = byCheckOut.getModelDefinition();
                String number = byCheckOut.getNumber();
                String checkOutVersion = byCheckOut.getVersion();
                CustomerPartUpdateDTO customerPartUpdateDTO = numberExcelPartMap.get(number);
                BeanUtil.copyProperties(customerPartUpdateDTO,byCheckOut);

                //1、查询需要关联的历史物料，根据relationNumber查询 2、查到后处理 3、关联历史物料
//                setRelationNumber(customerPartUpdateDTO, byCheckOut.getMasterOid());
//                log.info("原物料信息part:{}", JSONUtil.toJsonStr(byCheckOut));
//                addRelationship(customerPartUpdateDTO.getRelationNumber(),byCheckOut.getMasterOid(),byCheckOut.getModelDefinition(),"FORMING",RelationConstraint.MM,Part.TYPE);

                PartIteration partIteration = numberDbPartMap.get(number);
                if (StringUtil.isNotBlank(customerPartUpdateDTO.getExcelVersion())) {
                    byCheckOut.setVersion(customerPartUpdateDTO.getExcelVersion());

                    if (customerPartUpdateDTO.getExcelVersion().equalsIgnoreCase(partIteration.getVersion())) {
                        byCheckOut.setIteratedVersion(partIteration.getIteratedVersion()); // 大版本一样，小版本填了也不用，自动加一就行
                    } else {
                        if (StringUtil.isNotBlank(customerPartUpdateDTO.getExcelIteratedVersion())) {
                            int iteratedVersion = Integer.parseInt(customerPartUpdateDTO.getExcelIteratedVersion()) - 1;
                            byCheckOut.setIteratedVersion(iteratedVersion);
                        }else{
                            byCheckOut.setIteratedVersion(0); // 因为更新的时候小版本会自动+1，所以这里减个1
                        }
                    }

                }else{
                    if ("已发布".equalsIgnoreCase(partIteration.getLifecycleStatus()) || "released".equalsIgnoreCase(partIteration.getLifecycleStatus())){
                        log.info("Part save {} Released befNext version:{}",treadName, checkOutVersion);
                        ModelInfo modelInfo = new ModelInfo();
                        modelInfo.setOid(checkOutOid);
                        modelInfo.setType(checkOutType);
                        modelInfo.setModelDefinition(checkOutModelDefinition);
                        byCheckOut.setVersion(versionRuleService.getNextVersion(modelInfo).stream().findAny().get());  //
                        // 不写版本且为已发布则升级大版本，小版本置为1
                        byCheckOut.setIteratedVersion(0);
                        log.info("Part save {} Released atfNext version:{} iterationVersion 0",treadName,
                                byCheckOut.getVersion());
                    }else{
                        byCheckOut.setVersion(partIteration.getVersion()); // 不写版本不是已发布，大版本不变，小版本+1
                        byCheckOut.setIteratedVersion(partIteration.getIteratedVersion());
                    }
                }

                byCheckOut.setOid(checkOutOid);
                ClassificationInfo clsInfo = customerPartUpdateDTO.getClassificationInfo();
                if (clsInfo != null) {
                    partService.setClassification(byCheckOut, clsInfo);
                } else {
                    byCheckOut.setClsOid("");
                }
                updateParts.add(byCheckOut);
            }
            try {
                commonAbilityHelper.doUpdate(updateParts);
                //检入
                List<LockInfo> needCheckIn = updateParts.stream().map(item -> {
                    LockInfo lockInfo = new LockInfo();
                    lockInfo.setOid(item.getOid());
                    lockInfo.setType(item.getType());
                    return lockInfo;
                }).collect(Collectors.toList());

                List<LockAble> checkInResult = partHelper.checkIn(needCheckIn);
                log.info("检入结果result: {}", JSONUtil.toJsonStr(checkInResult));
                log.info("检入后开始关联信息 START");
                for (LockAble lockAble : lockAbles) {
                    PartIteration byCheckOut = (PartIteration) lockAble;
                    String number = byCheckOut.getNumber();
                    CustomerPartUpdateDTO customerPartUpdateDTO = numberExcelPartMap.get(number);
                    log.info("原物料信息part:{}", JSONUtil.toJsonStr(byCheckOut));
                    addRelationship(customerPartUpdateDTO.getRelationNumber(), byCheckOut.getOid(), byCheckOut.getModelDefinition(), "FORMING", RelationConstraint.MM, Part.TYPE);
                }
                log.info("检入后开始关联信息 END");

            }catch (JWIException e) {
                checkOutData.forEach(item -> this.commonAbilityHelper.removeRedisCheckOutLock(item.getOid()));
                throw e;
            }
        }
        //创建数据
        List<CustomerPartUpdateDTO> createExcelParts = batchPart.stream().filter(t -> t.isCreateFlag()).collect(Collectors.toList());
        if(CollectionUtil.isNotEmpty(createExcelParts)){
            List<PartIteration> createParts=new ArrayList<>();
            for (CustomerPartUpdateDTO createExcelPart : createExcelParts) {
                PartIteration partIteration = BeanUtil.copyProperties(createExcelPart, new PartIteration());
                partService.setLocation(partIteration, createExcelPart.getLocationInfo());

                UserDTO currentUser = SessionHelper.getCurrentUser();

                String createBy = partIteration.getCreateBy();
                if (StrUtil.isEmpty(createBy)) {
                    createBy = currentUser.getAccount();
                }
                UserHelper userHelper = (UserHelper) SpringContextUtil.getBean("defaultUserHelperImpl");
                UserDTO byAccount = userHelper.findByAccount(createBy);
                JSONObject extensionContent = partIteration.getExtensionContent();
                if (extensionContent == null) {
                    extensionContent = new JSONObject();
                    partIteration.setExtensionContent(extensionContent);
                }
                JSONObject requirementUser = new JSONObject();
                requirementUser.put("account", createBy);
                requirementUser.put("name", byAccount.getName());
                extensionContent.put("cn_jwis_xqr", requirementUser);

                ClassificationInfo clsInfo = createExcelPart.getClassificationInfo();
                if (clsInfo != null) {
                    partService.setClassification(partIteration, clsInfo);

                }
                //设置视图，如果viewInfo为空，默认取Design视图

                partService.setView(partIteration);

                if (StringUtil.isNotBlank(createExcelPart.getExcelVersion())) {
                    partIteration.setVersion(createExcelPart.getExcelVersion());
                }

                if (StringUtil.isNotBlank(createExcelPart.getExcelIteratedVersion())) {
                    partIteration.setIteratedVersion(Integer.parseInt(createExcelPart.getExcelIteratedVersion()));
                }

                createParts.add(partIteration);

                //支持筛选级和成型的同时创建
                PartIteration result = this.commonAbilityHelper.doCreate(partIteration, Boolean.TRUE);

                JSONObject clsJson = result.getClsProperty();
                if (null != clsJson) {
                    String sxzldj = clsJson.getString("cn_jwis_sxzldj");
                    createPartScreen(createExcelPart.getLocationInfo(), result, sxzldj);
                    if (sxzldj != null && "否".equals(sxzldj.trim())) {
                        //将已存在的part进行 相关对象-筛选级（成型） 关联
//                        setRelationNumber(createExcelPart, result.getMasterOid());
//                        addRelationship(createExcelPart.getRelationNumber(),result,"FORMING",RelationConstraint.MM,Part.TYPE);
                        addRelationship(createExcelPart.getRelationNumber(), result.getOid(), result.getModelDefinition(), "FORMING", RelationConstraint.MM, Part.TYPE);
                    }
                }

            }


            log.info("doCreate createParts==>" + JSONUtil.toJsonStr(createParts));
//            commonAbilityHelper.doCreate(createParts);
            //处理状态
            List<String> status = createExcelParts.stream().map(CustomerPartUpdateDTO::getLifecycleStatus).collect(Collectors.toList());
            for (int i = 0; i < createParts.size(); i++) {
                String sta = status.get(i);
                if (StringUtil.isNotBlank(sta)) {
                    PartIteration part = createParts.get(i);
                    ModelInfo modelInfo = new ModelInfo();
                    modelInfo.setOid(part.getOid());
                    modelInfo.setType(PartIteration.TYPE);
                    partHelper.setStatus(modelInfo, sta);
                }

                CustomerPartUpdateDTO createExcelPart = createExcelParts.get(i);
                PartIteration part = createParts.get(i);

                if (StringUtil.isNotBlank(createExcelPart.getExcelVersion())) {
                    part.setVersion(createExcelPart.getExcelVersion());
                    if (StringUtil.isNotBlank(createExcelPart.getExcelIteratedVersion())) {
                        part.setIteratedVersion(Integer.parseInt(createExcelPart.getExcelIteratedVersion()));
                        part.setDisplayVersion(part.getVersion() + "." + part.getIteratedVersion());
                    }else{
                        part.setDisplayVersion(part.getVersion() + ".1" );
                    }
                    log.info("update part==>" + JSONUtil.toJsonStr(part));
                    this.jwiCommonService.update(part); // 既然创建的时候创不进去，那就更新吧
                }

            }

        }
    }

    private void setRelationNumber(CustomerPartUpdateDTO customerPartUpdateDTO, String fromMasterOid) {
        if (StrUtil.isNotBlank(customerPartUpdateDTO.getRelationNumber())) {
            List<String> relationNumber = new ArrayList<>();
            relationNumber.add(customerPartUpdateDTO.getRelationNumber());
            PartIteration relationPart = commonAbilityHelper.findDetailEntityByNumber(relationNumber, PartIteration.TYPE)
                    .stream()
                    .map(item -> (PartIteration) item)
                    .findFirst()
                    .orElse(null);

            if (relationPart!= null) {
                log.info("需要关联的relationPart信息:{}", JSONUtil.toJsonStr(relationPart));
                List<RelationAble> rel = new ArrayList<>();
                Forming form = new Forming();
                form.setFromOid(fromMasterOid);
                form.setToOid(relationPart.getMasterOid());
                form.setType(Forming.TYPE);
                form.setFromType(Part.TYPE);
                form.setToType(Part.TYPE);
                rel.add(form);
                jwiCommonService.createRelation(rel);
            }
        }
    }

    /**
     * 增加 筛选级/成型的关系
     * @param slaveNumber 非筛选级物料编码
     * @param oid  筛选级物料Oid
     * @param modelDefinition  筛选级物料modelDefinition
     * @param relationshipName 关系名称
     * @param relationConstraint 约束
     * @param slaveObjectType 非筛选级物料类型
     */
    private void addRelationship(String slaveNumber, String oid,String modelDefinition, String relationshipName,
                                 RelationConstraint relationConstraint, String slaveObjectType) {

        if (slaveNumber == null) {
            log.warn("参数不完整，无法建立关联关系");
            return;
        }
        RelatedBatchOperate operate = new RelatedBatchOperate();
        ArrayList<String> slaveNumbers = new ArrayList<>();
        slaveNumbers.add(slaveNumber);
        PartIteration relationPart = commonAbilityHelper.findDetailEntityByNumber(slaveNumbers, PartIteration.TYPE)
                .stream()
                .map(item -> (PartIteration) item)
                .findFirst()
                .orElse(null);
        if (relationPart!= null) {
            log.info("需要关联的relationPart信息:{}", JSONUtil.toJsonStr(relationPart));
            ArrayList<String> slaveOids = new ArrayList<>();
            slaveOids.add(relationPart.getOid());
//            operate.setMainObjectType(part.getModelDefinition());
            operate.setMainObjectType(modelDefinition);
//            operate.setMainObjectOid(part.getOid());
            operate.setMainObjectOid(oid);
            operate.setRelationshipName(relationshipName);
            operate.setRelationConstraint(relationConstraint);
            operate.setForward(Boolean.FALSE);
            operate.setSlaveObjectType(slaveObjectType);
            operate.setSlaveObjectOids(slaveOids);
            operate.setMainObjectClassName(PartIteration.TYPE);
            operate.setSlaveObjectClassName(PartIteration.TYPE);
            instanceHelper.batchAddRelated(operate);
        }
    }


    void createPartScreen(LocationInfo dto, PartIteration part, String cn_jwis_sxzldj){
        if(cn_jwis_sxzldj != null && !"否".equals(cn_jwis_sxzldj)){
            BatchCopyPartDTO batchCopyPartDTO = new BatchCopyPartDTO();
            batchCopyPartDTO.setCurrentStage("");
            batchCopyPartDTO.setLinkRelationship(Collections.emptyList());
            batchCopyPartDTO.setLocationInfo(dto);

            BatchCopyPartDocumentBatchItemDTO batchCopyPartDocumentBatchItemDTO = new BatchCopyPartDocumentBatchItemDTO();
            batchCopyPartDocumentBatchItemDTO.setSourceOid(part.getOid());

            batchCopyPartDTO.setSourceOids(Arrays.asList(part.getOid()));
            batchCopyPartDTO.setUnLinkRelationship(Collections.emptyList());


            if("筛选级".equals(cn_jwis_sxzldj.trim())){
                batchCopyPartDTO.setType("screen");
                batchCopyPartDocumentBatchItemDTO.setNewName(part.getName() + "（筛选级）");
                batchCopyPartDTO.setNames(Arrays.asList(batchCopyPartDocumentBatchItemDTO));
                partScreenAutoHelper.partBatchCopy(batchCopyPartDTO, Boolean.TRUE);
            }else if("成型".equals(cn_jwis_sxzldj.trim())){
                batchCopyPartDTO.setType("forming");
                batchCopyPartDocumentBatchItemDTO.setNewName(part.getName() + "（未成型）");
                batchCopyPartDTO.setNames(Arrays.asList(batchCopyPartDocumentBatchItemDTO));
                partScreenAutoHelper.partBatchCopy(batchCopyPartDTO, Boolean.TRUE);
            }

        }
    }

}
