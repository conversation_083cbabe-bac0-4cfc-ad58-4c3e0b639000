package cn.jwis.product.pdm.customer.service.interf;

import cn.jwis.framework.base.response.PageResult;
import cn.jwis.platform.plm.container.entity.Folder;
import cn.jwis.platform.plm.foundation.common.dto.InstanceBasicDTO;
import cn.jwis.product.pdm.change.entity.Issue;
import cn.jwis.product.pdm.customer.entity.DeliveryReport;
import cn.jwis.product.pdm.customer.entity.PermApplyEntity;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2023/8/16 10:42
 * @Email <EMAIL>
 */
public interface CommonService {

    // 单组织系统中获取tenantOid
    String getAloneTenantOid();

    // 获取文件夹路径集合
    List<Folder> getFolderPath(String type, String oid) throws Exception;

    List<Issue> findIssueByEntity(List<InstanceBasicDTO> instances,boolean isClosed) throws Exception;

    List<DeliveryReport> getDeliveryData(String containerOid);

    void exportDeliveryData(String containerOid, HttpServletResponse response);

    PageResult<PermApplyEntity> queryPerm(PermApplyEntity permApplyEntity);
}
