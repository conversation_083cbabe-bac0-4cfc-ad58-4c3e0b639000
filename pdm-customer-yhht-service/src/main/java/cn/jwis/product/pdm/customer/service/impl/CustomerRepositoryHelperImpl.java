package cn.jwis.product.pdm.customer.service.impl;

import cn.jwis.platform.plm.workflow.engine.dto.VariableCandidateDTO;
import cn.jwis.platform.plm.workflow.engine.service.impl.RepositoryHelperImpl;
import org.activiti.engine.RepositoryService;
import org.activiti.engine.repository.ProcessDefinition;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2023/9/13 16:31
 * @Email <EMAIL>
 */
@Service
@Primary
@Transactional
public class CustomerRepositoryHelperImpl extends RepositoryHelperImpl {

    @Resource
    private RepositoryService repositoryService;

    @Override
    public List<VariableCandidateDTO> processVariablesForStart(String deploymentId) {
        List<VariableCandidateDTO> list = super.processVariablesForStart(deploymentId);

        ProcessDefinition processDefinition = (ProcessDefinition)this.repositoryService.createProcessDefinitionQuery().deploymentId(deploymentId).singleResult();

//        if(list != null && list.size() > 1){
//            VariableCandidateDTO it0 = list.get(0);
//            VariableCandidateDTO it = list.get(1);
////            if(("1382501".equals(deploymentId) || "62999".equals(deploymentId)) && ("文件外发审核".equals(it.getDisplayName()) || "审核".equals(it.getDisplayName())) && ("cn_jwis_outgoing_countersigner".equals(it.getName()) || "cn_jwis_shz".equals(it.getName()))){
//            if(("1382501".equals(deploymentId) || "62999".equals(deploymentId)) || ("文件外发审核".equals(it.getDisplayName()) || "审核".equals(it.getDisplayName())) && ("cn_jwis_outgoing_countersigner".equals(it.getName()) || "cn_jwis_shz".equals(it.getName()))
//              && "owner".equals(it0.getName())){
        if(processDefinition != null && "文件外发流程".equals(processDefinition.getName())){
            VariableCandidateDTO dto = new VariableCandidateDTO();
            dto.setCountersignFlag(true);
            dto.setName("cn_jwis_outgoing_fileReciever");
            dto.setDisplayName("文件接收人");
            list.add(dto);
            VariableCandidateDTO ccDto = new VariableCandidateDTO();
            ccDto.setCountersignFlag(true);
            ccDto.setName("cn_jwis_notifier");
            ccDto.setDisplayName("后置抄送");
            list.add(ccDto);
            return list;

        }else if(processDefinition != null && ("技术文档发布流程".equals(processDefinition.getName()) || "文档变更流程".equals(processDefinition.getName()))){
            VariableCandidateDTO ccDto = new VariableCandidateDTO();
            ccDto.setCountersignFlag(true);
            ccDto.setName("cn_jwis_qzcsr");
            ccDto.setDisplayName("前置抄送");
            list.add(1, ccDto);
            VariableCandidateDTO dto = new VariableCandidateDTO();
            dto.setCountersignFlag(true);
            dto.setName("cn_jwis_notifier");
            dto.setDisplayName("后置抄送");
            list.add(dto);
            return list;
        } else if (processDefinition != null && "物料BOM及图纸发布流程".equals(processDefinition.getName())) {
            VariableCandidateDTO dto = new VariableCandidateDTO();
            dto.setCountersignFlag(true);
            dto.setName("cn_jwis_outgoing_fileReciever");
            dto.setDisplayName("文件接收人");
            //0228 需求暂时取消 文件接收人d
//            list.add(dto);
            return list;
        }
//            }
//        }

//        VariableCandidateDTO dto = new VariableCandidateDTO();
//        dto.setCountersignFlag(true);
//        dto.setName("cn_jwis_notifier");
//        dto.setDisplayName("通知人");
//        list.add(dto);
        return list;
    }
}
