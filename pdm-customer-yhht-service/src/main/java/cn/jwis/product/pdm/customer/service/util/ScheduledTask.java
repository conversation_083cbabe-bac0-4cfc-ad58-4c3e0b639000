package cn.jwis.product.pdm.customer.service.util;

import cn.hutool.cron.CronUtil;
import cn.hutool.cron.task.Task;
import cn.jwis.framework.base.util.StringUtil;
import cn.jwis.platform.plm.sysconfig.entity.ConfigItem;
import cn.jwis.platform.plm.sysconfig.preferences.PreferencesService;
import cn.jwis.product.pdm.customer.service.impl.DailyDownloadService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;


@Component
@Slf4j
public class ScheduledTask implements CommandLineRunner {

    @Resource
    private DailyDownloadService dailyDownloadService;

    @Autowired
    private PreferencesService preferencesService;

    private String defaultCron = "0 0 23 * * ?"; // 默认的 cron 表达式
    private String cronTaskId; // 记录任务 ID，便于停止任务
    private boolean isCronStarted = false; // 记录 CronUtil 是否启动

    @Override
    public void run(String... args) {
        // 加载动态配置
        updateCronTask();
        startConfigRefreshTask(); // 定时检查配置更新
    }

    /**
     * 更新定时任务，根据配置项动态调整任务调度
     */
    private void updateCronTask() {
        // 查询任务开关
        ConfigItem enableItem = preferencesService.queryConfigValue("task_enable");
        boolean isTaskEnabled = enableItem != null && "true".equalsIgnoreCase(enableItem.getValue());
        if (!isTaskEnabled) {
            log.info("定时任务已被禁用 (task_enable=false)");
            if (cronTaskId != null) {
                CronUtil.remove(cronTaskId); // 停止任务
                cronTaskId = null;
            }
            return;
        }

        // 查询任务的 cron 表达式
        ConfigItem cronItem = preferencesService.queryConfigValue("task_cron_expression");
        String cronExpression = (cronItem != null && StringUtil.isNotBlank(cronItem.getValue()))
                ? cronItem.getValue()
                : defaultCron;

        log.info("加载定时任务: 开关={}, cron表达式={}", isTaskEnabled, cronExpression);

        // 如果任务已存在，先移除旧任务
        if (cronTaskId != null) {
            CronUtil.remove(cronTaskId);
        }

        // 创建新的定时任务
        cronTaskId = CronUtil.schedule(cronExpression, new Task() {
            @Override
            public void execute() {
                dailyDownloadService.runTask();
            }
        });

        startCronUtilIfNeeded(); // 检查并启动 CronUtil
    }

    /**
     * 定时检查配置是否有变化
     */
    private void startConfigRefreshTask() {
        String configRefreshCron = "0 0/5 * * * ?"; // 每 5 分钟检查一次配置
        CronUtil.schedule(configRefreshCron, new Task() {
            @Override
            public void execute() {
                updateCronTask();
            }
        });

        startCronUtilIfNeeded(); // 确保定时器启动
    }

    /**
     * 启动 CronUtil，如果尚未启动
     */
    private void startCronUtilIfNeeded() {
        if (!isCronStarted) {
            CronUtil.start();
            isCronStarted = true;
            log.info("CronUtil 成功启动");
        } else {
            log.info("CronUtil 已经启动，无需重复启动");
        }
    }
}