package cn.jwis.product.pdm.customer.service.impl;

import cn.jwis.platform.plm.foundation.lifecycle.able.LifecycleAble;
import cn.jwis.product.pdm.change.service.IssueServiceImpl;
import cn.jwis.product.pdm.document.dto.LifecycleStatusUpdateDTO;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR> yefei
 * @Date ： 2024/3/29
 * @Description :
 */

@Component
@Primary
public class CustomerIssueServiceImpl extends IssueServiceImpl {

    @Resource
    TriggerAuditServiceImpl triggerAuditService;

    @Override
    public LifecycleAble updateStatus(LifecycleStatusUpdateDTO dto) {
        LifecycleAble result = super.updateStatus(dto);
        triggerAuditService.setIssueLife(dto);
        return result;
    }
}
