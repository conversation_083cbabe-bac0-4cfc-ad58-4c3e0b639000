package cn.jwis.product.pdm.customer.service.listener;

import cn.jwis.framework.base.annotation.Description;
import cn.jwis.framework.base.dto.UserDTO;
import cn.jwis.framework.base.exception.JWIServiceException;
import cn.jwis.framework.base.util.Assert;
import cn.jwis.framework.base.util.CollectionUtil;
import cn.jwis.framework.base.util.MapUtil;
import cn.jwis.framework.base.util.SpringContextUtil;
import cn.jwis.framework.base.web.session.SessionHelper;
import cn.jwis.platform.plm.foundation.common.response.InstanceEntity;
import cn.jwis.platform.plm.workflow.engine.order.entity.ProcessOrder;
import cn.jwis.platform.plm.workflow.engine.service.interf.ProcessOrderHelper;
import cn.jwis.platform.plm.workflow.engine.util.ActivitiNodeEventEnum;
import cn.jwis.platform.plm.workflow.engine.workflow.service.interf.ProcessOrderService;
import cn.jwis.product.pdm.customer.service.interf.DingTalkService;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.delegate.DelegateTask;
import org.activiti.engine.delegate.TaskListener;

import java.util.Arrays;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2023/9/5 21:10
 * @Email <EMAIL>
 */
@Description("人工节点发布钉钉任务监听器")
@Slf4j
public class JWIDingTalkTaskListener  implements TaskListener {

    //public static final ExecutorService DING_TASK_THREAD_POOL = Executors.newFixedThreadPool(10);

    @Override
    public void notify(DelegateTask delegateTask) {
        final UserDTO currentUser = SessionHelper.getCurrentUser();
        final String accessToken = SessionHelper.getAccessToken();
        final String appId = SessionHelper.getAppId();
        final JSONObject finalDelegateTask = JSONObject.parseObject(JSONObject.toJSONString(delegateTask));
        SessionHelper.addCurrentUser(currentUser);
        SessionHelper.setAccessToken(accessToken);
        SessionHelper.setAppId(appId);
        doNotify(0, finalDelegateTask);
//        DING_TASK_THREAD_POOL.execute(new Runnable() {
//            public void run() {
//
//            }
//        });
    }

    private void doNotify(int times, JSONObject delegateTask) {
        // 尝试 5 次
        if (times == 5) {
            log.info("JWIDingTalkTaskListener failed");
            throw new JWIServiceException("JWIDingTalkTaskListener failed: System busy");
        }
        ProcessOrder processOrder = getProcessOrder(delegateTask);
        if (processOrder == null){
            try {
                Thread.sleep(500);
                times++;
                doNotify(times, delegateTask);
                return ;
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
        // 获取 需要授权的对象
        List<InstanceEntity> instanceList = getInstanceList(processOrder);
        if (CollectionUtil.isEmpty(instanceList)){
            return ;
        }
        DingTalkService dingTalkService = (DingTalkService) SpringContextUtil.getBean("dingTalkServiceImpl");
        // 开始根据事件类型，创建/删除权限
        String eventName = delegateTask.getString("eventName");
        String taskId = delegateTask.getString("id");
        String assignee = delegateTask.getString("assignee");
        if (ActivitiNodeEventEnum.create.name().equals(eventName)) {
            // 同步在钉钉创建任务
            log.info("JWIDingTalkTaskListener doNotify, eventName: {},  taskId: {}, owner:{},createBy:{}",
                    eventName, taskId,assignee,processOrder.getCreateBy());
//            dingTalkService.createTask(delegateTask,processOrder,instanceList);
        } else if (ActivitiNodeEventEnum.complete.name().equals(eventName)) {
            // 同步完成钉钉的任务
//            dingTalkService.completeTask(delegateTask,processOrder,instanceList);

            //同步完成钉钉的 审批
//            dingTalkService.completeApproval(delegateTask,processOrder,instanceList);

        } else if (ActivitiNodeEventEnum.assignment.name().equals(eventName)){
            // 重新指定钉钉的任务
            dingTalkService.assignment(delegateTask,processOrder,instanceList);
        }
    }

    private ProcessOrder getProcessOrder(JSONObject delegateTask) {
        ProcessOrder processOrder = null;
        JSONObject variableInstances = delegateTask.getJSONObject("variableInstances");
        ProcessOrderHelper processOrderHelper = (ProcessOrderHelper) SpringContextUtil.getBean("processOrderHelperImpl");
        if (MapUtil.isEmpty(variableInstances)){
            String processInstanceId = delegateTask.getString("processInstanceId");
            processOrder = processOrderHelper.findByProcessInstanceId(processInstanceId);
        }else {
            JSONObject processOrderVar = variableInstances.getJSONObject("processOrderOid");
            if (MapUtil.isEmpty(processOrderVar)){
                String processInstanceId = delegateTask.getString("processInstanceId");
                processOrder = processOrderHelper.findByProcessInstanceId(processInstanceId);
            }else {
                String processOrderOid = processOrderVar.getString("value");
                processOrder = processOrderHelper.findByOid(processOrderOid);
            }
        }
        return processOrder;
    }

    /**
     * @Description  获取评审对象
     * @Auther hsy
     */
    private List<InstanceEntity> getInstanceList(ProcessOrder processOrder) {
        ProcessOrderHelper processOrderHelper = SpringContextUtil.getBean(ProcessOrderHelper.class);
        return processOrderHelper.findBizObject(processOrder.getOid());
    }

}
