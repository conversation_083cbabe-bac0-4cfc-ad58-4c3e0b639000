package cn.jwis.product.pdm.customer.service.interf;

import cn.jwis.framework.base.exception.JWIException;
import cn.jwis.platform.plm.container.dto.container.ContainerCreateDTO;
import cn.jwis.platform.plm.container.entity.Container;
import cn.jwis.platform.plm.container.entity.ContainerTemplate;
import cn.jwis.product.pdm.customer.service.dto.SaveAsTemplateDTO;

/**
 * @author: 汪江
 * @data: 2023-12-29 11:19
 * @description:
 **/
public interface CustomerContainerHelper {
    ContainerTemplate saveAsTemplate(SaveAsTemplateDTO dto, String category) throws JWIException;

    Container createContainer(ContainerCreateDTO dto) throws JWIException;
}
