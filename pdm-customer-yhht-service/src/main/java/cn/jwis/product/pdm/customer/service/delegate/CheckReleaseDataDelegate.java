package cn.jwis.product.pdm.customer.service.delegate;


import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.JavaDelegate;


/**
 * <AUTHOR>
 * @version 1.0
 * @description: 工作流执行代理：等待下游处理MQ数据完成
 * @date 2023/8/15 10:44
 * @Email <EMAIL>
 */
@Slf4j
public class CheckReleaseDataDelegate implements JavaDelegate {

    @Override
    public void execute(DelegateExecution execution) {
        try {
            log.info("CheckReleaseDataDelegate.start:" + execution.getProcessInstanceId());
            Thread.sleep(3000);
            log.info("CheckReleaseDataDelegate.end:" + execution.getProcessInstanceId());
        } catch (Exception e) {
            log.error("CheckReleaseDataDelegate.sleep.error");
        }
    }
}
