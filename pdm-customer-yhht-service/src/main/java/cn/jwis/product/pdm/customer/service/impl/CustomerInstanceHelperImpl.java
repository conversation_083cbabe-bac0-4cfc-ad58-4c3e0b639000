package cn.jwis.product.pdm.customer.service.impl;

import cn.jwis.framework.base.domain.entity.BaseEntity;
import cn.jwis.platform.plm.dataengine.common.JWICommonService;
import cn.jwis.platform.plm.foundation.common.service.InstanceHelperImpl;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR> yefei
 * @Date ： 2024/3/28
 * @Description :
 */
@Component
@Primary
public class CustomerInstanceHelperImpl extends InstanceHelperImpl {

    @Resource
    TriggerAuditServiceImpl triggerAuditService;

    @Resource
    JWICommonService commonService;

    @Override
    public boolean setOwner(String oid, String type, String ownerAccount) {
        boolean result = super.setOwner(oid, type, ownerAccount);
        BaseEntity baseEntity = (BaseEntity) commonService.findEntity(type,oid);
        triggerAuditService.setOwnerAudit(baseEntity, ownerAccount);
        return result;
    }
}
