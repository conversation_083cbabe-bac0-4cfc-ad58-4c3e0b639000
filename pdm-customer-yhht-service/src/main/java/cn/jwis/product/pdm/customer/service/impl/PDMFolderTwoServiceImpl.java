package cn.jwis.product.pdm.customer.service.impl;

import cn.jwis.framework.base.bean.util.BeanUtil;
import cn.jwis.framework.base.domain.able.ModelAble;
import cn.jwis.framework.base.domain.able.RelationAble;
import cn.jwis.framework.base.domain.entity.BaseEntity;
import cn.jwis.framework.base.exception.JWIException;
import cn.jwis.framework.base.response.PageResult;
import cn.jwis.framework.base.util.*;
import cn.jwis.framework.database.core.entity.SubFilter;
import cn.jwis.framework.database.core.query.dynamic.Condition;
import cn.jwis.platform.plm.container.entity.Container;
import cn.jwis.platform.plm.container.entity.Folder;
import cn.jwis.platform.plm.container.entity.FolderTreeNode;
import cn.jwis.platform.plm.container.entity.Team;
import cn.jwis.platform.plm.container.service.FolderService;
import cn.jwis.platform.plm.foundation.classification.able.info.ClassificationInfo;
import cn.jwis.platform.plm.foundation.common.response.InstanceEntity;
import cn.jwis.platform.plm.foundation.common.service.CommonAbilityHelper;
import cn.jwis.platform.plm.foundation.common.service.CommonAbilityService;
import cn.jwis.platform.plm.foundation.relationship.Assign;
import cn.jwis.platform.plm.foundation.relationship.Contain;
import cn.jwis.platform.plm.permission.administrativedomain.AdministrativeDomainService;
import cn.jwis.platform.plm.permission.administrativedomain.entity.AdministrativeDomain;
import cn.jwis.platform.plm.permission.administrativedomain.entity.Inherit;
import cn.jwis.platform.plm.permission.administrativedomain.entity.Point;
import cn.jwis.product.pdm.container.entity.response.PDMInstanceEntityWithCatalog;
import cn.jwis.product.pdm.customer.entity.FuzzySubPageWithModelDTO;
import cn.jwis.product.pdm.customer.repo.PdmFolderTwoRepo;
import cn.jwis.product.pdm.customer.service.dto.FolderUpdateDTO;
import cn.jwis.product.pdm.customer.service.dto.PdmFolderCreateDTO;
import cn.jwis.product.pdm.customer.service.interf.PDMFolderServiceI;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;

@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class PDMFolderTwoServiceImpl implements PDMFolderServiceI {

    @Resource
    private CommonAbilityHelper commonAbilityHelper;

    @Resource
    private PdmFolderTwoRepo pdmFolderTwoRepo;

    @Resource
    private FolderService folderService;

    @Resource
    private AdministrativeDomainService administrativeDomainService;

    @Resource
    private CommonAbilityService commonAbilityService;

    @Override
    public ModelAble update(FolderUpdateDTO dto) {
        Folder folder =(Folder)commonAbilityHelper.findDetailEntity(dto.getOid(), Folder.TYPE);
        folder.setName(dto.getName());
        folder.setExtensionContent(dto.getExtensionContent());
        this.validCode(folder.getName(), dto.getExtensionContent(), dto.getOid(), folder.getCatalogOid());
        return commonAbilityHelper.doUpdate(folder);
    }

    @Override
    public ModelAble create(PdmFolderCreateDTO dto) {
        ValidationUtil.validate(dto);
        validCode(dto.getName(), dto.getExtensionContent(), null, dto.getLocationInfo().getCatalogOid());
        Folder folder = BeanUtil.copyProperties(dto, new Folder());
        //将库信息重新存入对象中
        folderService.setLocation(folder, dto.getLocationInfo());
        // 文件夹增加分类信息
        ClassificationInfo clsInfo = dto.getClassificationInfo();
        if (clsInfo != null) {
            this.folderService.setClassification(folder, clsInfo);
        }
        // 重名校验
        String catalogType = folder.getCatalogType();
        String catalogOid = folder.getCatalogOid();
        Assert.notBlank(catalogOid, "catalogOid can not be null");
        SubFilter subFilter = new SubFilter(catalogType, catalogOid, Contain.TYPE, Folder.TYPE);
        subFilter.setFilter(Condition.where("name").eq(dto.getName()));
        List<Folder> folders = folderService.dynamicQueryByFrom(subFilter);
        Assert.isEmpty(folders, "文件夹名称相同层级已存在:" + dto.getName());

        // 获取当前文件夹的最大值
        ModelAble entity = folderService.findDetailEntity(catalogOid, catalogType);
        String type = entity.getType();
        int orderBy = 0;
        if (Folder.TYPE.equals(type)) {
            Folder lastFolder = (Folder) entity;
            orderBy = lastFolder.getFolderMax();
            orderBy++;
            // 更新文件夹的文件夹最大值
            lastFolder.setFolderMax(orderBy);
            commonAbilityHelper.doUpdate(lastFolder);
        }
        // 文件夹排序字段更新
        folder.setOrderBy(orderBy);
        folder.setExtensionContent(dto.getExtensionContent());
        // 创建
        commonAbilityHelper.doCreate(folder);

        String folderOid = folder.getOid();
        // 8. 创建配置管理域
        AdministrativeDomain administrativeDomain = new AdministrativeDomain();
        String administrativeDomainOid = OidGenerator.newOid();
        administrativeDomain.setOid(administrativeDomainOid);
        administrativeDomain.setName(folder.getName());
        administrativeDomain.setDescription(folder.getDescription());
        administrativeDomain.setContainerModelType(Folder.TYPE);
        administrativeDomain.setContainerOid(folderOid);
        administrativeDomainService.create(administrativeDomain);
        List<RelationAble> rels = new ArrayList<>();
        Point relatedTo = new Point(Folder.TYPE, folderOid, AdministrativeDomain.TYPE, administrativeDomainOid);
        rels.add(relatedTo);

        // 父节点管理域关联 自身管理域
        AdministrativeDomain oneByFrom = administrativeDomainService.findOneByFrom(catalogType, catalogOid, Point.TYPE);
        if (oneByFrom!=null){
            rels.add(new Inherit(AdministrativeDomain.TYPE, oneByFrom.getOid(), AdministrativeDomain.TYPE, administrativeDomainOid));
        }
        commonAbilityService.createInterRelation(rels);
        return folder;
    }

    private void validCode(String folderName,JSONObject extension, String oid, String catalogOid){
        //验证扩展属性code同级不能重复
        String code = getCode(extension, "code");
        if(StringUtil.isNotBlank(code)){
            List<Folder> folders = pdmFolderTwoRepo.findByParentOid(catalogOid);
            folders.forEach((val) -> {
                String nowCode = getCode(val.getExtensionContent(), "code");
                if((StringUtil.isBlank(oid) || !Objects.equals(val.getOid(), oid)) && StringUtil.isNotBlank(nowCode) && Objects.equals(code, nowCode)){
                    throw new JWIException("同级存在相同分系统/单机号：" + code + " 文件夹名称:" + folderName + " 与已存在文件夹分系统重复名称:" +val.getName());
                }
            });
        }
    }

    private String getCode(JSONObject jsonObject, String param){
        return jsonObject != null && jsonObject.get(param) != null ? jsonObject.get(param).toString() : null;
    }

    @Override
    public List<FolderTreeNode> searchFoldersWithPermisson(String containerModel, String containerOid, String searchKey) throws JWIException {
        return pdmFolderTwoRepo.searchFoldersWithPermisson(Container.TYPE, containerModel, containerOid, searchKey);
    }

    /**
     * 获取团队角色
     * @param folderOid
     * @return
     */
    @Override
    public Team getTeamRole(String folderOid, String searchKey) {
        Team team = pdmFolderTwoRepo.getTeam(folderOid);
        if(team == null){
            //创建默认问团队
            team = new Team();
            team.setOid(OidGenerator.newOid());
            team.setName("文件夹默认team");
            team = commonAbilityHelper.doCreate(team);
            Assign assign = new Assign();
            assign.setFromOid(folderOid);
            assign.setFromType(Folder.TYPE);
            assign.setToOid(team.getOid());
            assign.setToType(Team.TYPE);
            commonAbilityService.createOutRelation(Collections.singletonList(assign));
        }
        return team;
    }
    @Override
    public Team getTeam(String folderOid) {
        return pdmFolderTwoRepo.getTeam(folderOid);
    }

    @Override
    public Team getInstanceTeam(InstanceEntity instance) {
        Team team = pdmFolderTwoRepo.getInstanceTeam(instance);
        if(team == null){
            //创建默认问团队
            team = new Team();
            team.setOid(OidGenerator.newOid());
            team.setName("实例默认team");
            team.setCreateBy(instance.getCreateBy());
            team.setCreateDate(System.currentTimeMillis());
            team = commonAbilityHelper.doCreate(team);
            Assign assign = new Assign();
            assign.setFromOid(instance.getOid());
            assign.setFromType(instance.getType());
            assign.setToOid(team.getOid());
            assign.setToType(Team.TYPE);
            commonAbilityService.createOutRelation(Collections.singletonList(assign));
        }
        return team;
    }

    @Override
    public Object fuzzySubPageWithModel(FuzzySubPageWithModelDTO dto) {
        ValidationUtil.validate(dto);
        String folderOid = dto.getFromOid();
        String searchKey = dto.getSearchKey();
        int index = dto.getIndex();
        int size = dto.getSize();
        List<Folder> folders = this.folderService.flapFolderTree(folderOid);
        if (CollectionUtil.isEmpty(folders)) {
            return PageResult.init(0, index, size, new LinkedList());
        } else {
            Map<String, String> folderOid2Name = CollectionUtil.splitToMap(folders, BaseEntity::getOid, Folder::getName);
            PageResult<PDMInstanceEntityWithCatalog> result = this.pdmFolderTwoRepo.fuzzyFolderContentPageWithModel(folderOid2Name.keySet(), searchKey, index, size, dto);
            List<PDMInstanceEntityWithCatalog> rows = result.getRows();
            if (CollectionUtil.isNotEmpty(rows)) {
                rows.stream().forEach((item) -> {
                    item.setCatalogName((String)folderOid2Name.get(item.getCatalogOid()));
                });
            }

            return result;
        }
    }
}
