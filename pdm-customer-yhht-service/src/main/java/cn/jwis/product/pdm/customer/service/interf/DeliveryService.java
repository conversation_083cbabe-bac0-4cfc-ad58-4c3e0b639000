package cn.jwis.product.pdm.customer.service.interf;

import cn.jwis.product.pdm.delivery.entity.Delivery;
import cn.jwis.product.pdm.delivery.entity.DeliveryTreeNode;
import java.util.List;

public interface DeliveryService extends cn.jwis.product.pdm.delivery.service.DeliveryService {
    Long delete(String oid, String level);

    Long delete(List<String> oids);

    List<DeliveryTreeNode> findByProductCatalogOid(String productCatalogOid);

    List<DeliveryTreeNode> findStructureTree(String catalogType, String catalogOid, Integer level, String searchKey);

    List<DeliveryTreeNode> findNodesByCatalogOid(String catalogType, String catalogOid, Integer level);

    List<DeliveryTreeNode> findChildrenByDeliveryOid(String oid);

    void createContainChild(String parentOid, String oid);

    Delivery findByOid(String oid);

    Delivery findByClsOidAndName(String catalogOid, String oid, String clsOid, String name, String modelDefinition);

    Delivery findByClsOid(String catalogOid, String clsOid);

    List<Delivery> findLeafNodes(String containerOid, String searchKey);

    List<DeliveryTreeNode> findAllChildrenObjByDeliveryOid(String oid, String level);

    Delivery findByParentOidAndName(String name,String parentOid,Boolean isRoot);
}
