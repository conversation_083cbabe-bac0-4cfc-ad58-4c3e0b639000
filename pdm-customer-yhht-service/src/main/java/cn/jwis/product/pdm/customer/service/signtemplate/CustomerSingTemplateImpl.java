package cn.jwis.product.pdm.customer.service.signtemplate;

import cn.hutool.json.JSONUtil;
import cn.jwis.framework.base.dto.UserDTO;
import cn.jwis.framework.base.exception.JWIException;
import cn.jwis.framework.base.util.StringUtil;
import cn.jwis.framework.base.web.session.SessionHelper;
import cn.jwis.platform.plm.file.entity.FileMetadata;
import cn.jwis.platform.plm.foundation.attachment.entity.File;
import cn.jwis.platform.plm.signtemplate.SignTemplateFileRemote;
import cn.jwis.platform.plm.signtemplate.entity.GenerateSignDocumentDTO;
import cn.jwis.platform.plm.signtemplate.entity.SignPersonDTO;
import cn.jwis.platform.plm.signtemplate.service.SingTemplateImpl;
import cn.jwis.product.pdm.customer.entity.SignInfo;
import cn.jwis.product.pdm.customer.service.dto.PdfSignatureTemplate;
import cn.jwis.product.pdm.customer.service.interf.ContentParserService;
import com.alibaba.fastjson.JSONObject;
import com.itextpdf.awt.geom.Rectangle2D;
import com.itextpdf.text.BaseColor;
import com.itextpdf.text.Rectangle;
import com.itextpdf.text.pdf.BaseFont;
import com.itextpdf.text.pdf.PdfContentByte;
import com.itextpdf.text.pdf.PdfReader;
import com.itextpdf.text.pdf.PdfStamper;
import com.itextpdf.text.pdf.parser.ImageRenderInfo;
import com.itextpdf.text.pdf.parser.PdfReaderContentParser;
import com.itextpdf.text.pdf.parser.RenderListener;
import com.itextpdf.text.pdf.parser.TextRenderInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import javax.annotation.Resource;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2023/11/9 11:26
 * @Email <EMAIL>
 */
@Service
@Primary
@Transactional
@Slf4j
public class CustomerSingTemplateImpl extends SingTemplateImpl {

    @Value("${sign-template.out.file.key}")
    private String DOC_FILE_KEY;

    @Autowired
    private SignTemplateFileRemote fileRemote;

    @Resource
    private ContentParserService contentParserService;

    @Override
    public Map<String, FileMetadata> addWaterMark(byte[] srcPdfPath, SignPersonDTO dto,
                                                  PdfReaderContentParser pdfReaderContentParser) throws Exception {
        String fileName = "电子签名.pdf";
        JSONObject processNode = dto.getProcessNode();
        JSONObject processTemplateData = dto.getProcessTemplateData();
        if(processTemplateData != null && processTemplateData.getString("tenantOid") != null){
            UserDTO userDTO = new UserDTO();
            userDTO.setTenantOid(processTemplateData.getString("tenantOid"));
            userDTO.setAccount(processTemplateData.getString("userAccount"));
            SessionHelper.addCurrentUser(userDTO);
        }
        PdfReader reader =null;
        PdfStamper stamper = null;
        //fileName = String.format(Locale.ENGLISH, "signDoc_%s",fileName);
        DiskFileItemFactory factory = new DiskFileItemFactory();
        FileItem fileItem = factory.createItem(DOC_FILE_KEY, "text/plain", true, fileName);
        float fontSize= (float) 10.0;
        try {
            try {
                Set<String> nodeNames = processNode.keySet();
                reader = new PdfReader(srcPdfPath);
                stamper = new PdfStamper(reader, fileItem.getOutputStream());
                BaseFont font =  BaseFont.createFont("STSong-Light","UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);

                StringBuilder stringBuilder = new StringBuilder();
                StringBuilder stringBuilder1 = new StringBuilder();
                //signTemplateCode
                for (String nodeName : nodeNames) {
                    String pt = processTemplateData.getString(nodeName);
                    if(StringUtil.isBlank(pt)){
                        pt =nodeName;
                    }
                    JSONObject nodeInfo = processNode.getJSONObject(nodeName);
                    String signNameStr = nodeInfo.getString("name");
//                    CheckUtil.checkEmpty(signNameStr, "流程节点处理人为空请检查 name 参数 ");
                    String[] signNameList = signNameStr.split(",");
                    String signTime = nodeInfo.getString("date");
//                    CheckUtil.checkEmpty(signTime, "流程节点处理时间为空请检查 date 参数 ");
                    int total = reader.getNumberOfPages() + 1;

                    log.info(" 当前图纸width：" + reader.getPageSize(1).getWidth() + " 当前图纸height：" + reader.getPageSize(1).getHeight());
                    Boolean calcu = Boolean.TRUE;
                    SignInfo singInfo = contentParserService.contentParser(reader);
                    pdfReaderContentParser = singInfo.getPdfReaderContentParser();
                    if(pdfReaderContentParser == null)
                        throw new JWIException("未找到当前图纸模板尺寸 当前图纸width：" + reader.getPageSize(1).getWidth() + " 当前图纸height：" + reader.getPageSize(1).getHeight());

//                    for (int i = 1; i < total; i++) {
                    log.info("当前pdf总共：{}页", total);
                    for (int i = 1; i <= 1; i++) {
                        log.info("当前pdf第：{}页", i);
                        PdfContentByte content = stamper.getOverContent(i);
                        content.setColorFill(new BaseColor(60,60,60)); //水印颜色
                        content.setFontAndSize(font, fontSize); //水印字体样式和大小
                        String placeholderText=pt;
                        Boolean finalCalcu = calcu;
                        pdfReaderContentParser.processContent(1,
                                new RenderListener() {
                                    int index = 0;
                                    @Override
                                    public void beginTextBlock() {}
                                    @Override
                                    public void renderText(TextRenderInfo textRenderInfo) {
                                        String text = textRenderInfo.getText(); // 整页内容
                                        stringBuilder.append("==>" + text);
                                        if (null != text ) {
                                            if(text.equals(placeholderText)){
                                                fillSignText(getSignName(signNameList,index),textRenderInfo,content, finalCalcu, stringBuilder1, singInfo);
                                                index++;
                                            }
                                            if(text.equals(placeholderText+"日期")){
                                                fillSignText(signTime,textRenderInfo,content, finalCalcu, stringBuilder1, singInfo);
                                            }
                                        }
                                    }
                                    @Override
                                    public void endTextBlock() {}
                                    @Override
                                    public void renderImage(ImageRenderInfo imageRenderInfo) {}
                                });

                        stringBuilder.append("\n");

                    }
                }
                log.info("filed==>\n" + stringBuilder.toString());
                log.info("textRender info==>\n" + stringBuilder1.toString());
            } catch (Exception e){
                log.error(e.getMessage(), e);
            }finally {
                if(stamper!=null){
                    stamper.close();
                }
                if(reader!=null){
                    reader.close();
                }
            }
            log.info(String.format(Locale.ENGLISH, "PDF水印添加完成！ %s",fileName));
            MultipartFile multiPartFile = new CommonsMultipartFile(fileItem);
            MultipartFile[] uploadFiles = new MultipartFile[]{multiPartFile};
            Map<String, FileMetadata> updateResult = fileRemote.upload(uploadFiles);
            return updateResult;
        } catch (Exception e){
            log.error(e.getMessage());
        }finally {
            fileItem.delete();
        }
        return null;
    }

    public String getSignName(String[] signNames,int index){
        if(index < signNames.length){
            return signNames[index];
        }else{
            return "   ";
        }

    }

    public void fillSignText(String signName, TextRenderInfo textRenderInfo, PdfContentByte content, Boolean calcu, StringBuilder stringBuilder1, SignInfo signInfo) {

        log.info("fillSignText.signInfo==>{}", JSONUtil.toJsonStr(signInfo));

        Rectangle2D.Float boundingRectange = textRenderInfo.getBaseline().getBoundingRectange();
        Rectangle2D.Float rectAscen = textRenderInfo.getAscentLine().getBoundingRectange();
        // 检查 textRender 内容并调整 boundingRectange.x
        String textRender = textRenderInfo.getText();
        //PDF签名模板
        String pdfTemplateName = signInfo.getPdfTemplateName();
        if (PdfSignatureTemplate.A4.getKey().equals(pdfTemplateName)) {
            if ("审核者".equals(textRender) ||"校对者".equals(textRender) ||"标准化".equals(textRender) || "批准者".equals(textRender) || textRender.contains("日期")) {
                boundingRectange.x += 20.0F;
            }
            if ("会签者".equals(textRender)) {
                boundingRectange.x += 10.0F;
                boundingRectange.y += 3.0F;
            }
        }

        float leftX = (float)boundingRectange.getMinX();
        float leftY = (float)boundingRectange.getMinY();// - 1.0F;
        float rightX = (float)rectAscen.getMaxX();
        float rightY = (float)rectAscen.getMaxY() ;//+ 1.0F;
        float H = rightY - leftY;
        Rectangle rectangle2 = null;
        float x = signInfo.getRevert() ? boundingRectange.y : boundingRectange.x, y = signInfo.getRevert() ? signInfo.getPdfReader().getPageSize(1).getWidth() - boundingRectange.x : boundingRectange.y;
        if(textRenderInfo.getText().contains("日期")){
            rectangle2 = new Rectangle(boundingRectange.x+20.0F, boundingRectange.y - 2.0F, boundingRectange.x + 70.0F, boundingRectange.y + H);
            if(calcu)
                rectangle2 = contentParserService.rectangle(signName, boundingRectange, textRenderInfo, calcu, signInfo);
        }else{
            rectangle2 = new Rectangle(boundingRectange.x+10.0F, boundingRectange.y - 2.0F, boundingRectange.x + 50.0F, boundingRectange.y + H);
            if(calcu)
                rectangle2 = contentParserService.rectangle(signName, boundingRectange, textRenderInfo, calcu, signInfo);
        }

        rectangle2.setBackgroundColor(BaseColor.WHITE);
//        content.rectangle(rectangle2);
        content.beginText();
        content.showTextAligned(0, signName, x, y, 0.0F);
        stringBuilder1.append("textRender==>" + textRenderInfo.getText() + " signName==>" + signName + " boundingRectange.x==>" + boundingRectange.x + " boundingRectange.y==>" + boundingRectange.y + "\n");
        content.endText();
    }

    @Override
    public Map<String, FileMetadata> toGenerateSignDocument(GenerateSignDocumentDTO dto) {

        List<File> operateList = dto.getOperateDocList();
        operateList.stream().forEach(it -> {
            if(it.getName().endsWith(".doc"))
                it.setName(it.getName().replace(".doc", ".docx"));
        });

        return super.toGenerateSignDocument(dto);
    }
}
