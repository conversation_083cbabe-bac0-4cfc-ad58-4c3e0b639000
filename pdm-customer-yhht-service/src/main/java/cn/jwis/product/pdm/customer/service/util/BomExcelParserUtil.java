package cn.jwis.product.pdm.customer.service.util;

import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import cn.jwis.framework.base.exception.JWIException;

import java.io.InputStream;
import java.util.*;

public class BomExcelParserUtil {

    /*public static void main(String[] args) {
        List<BOMItem> bomItems = parseBomFromExcel("/Users/<USER>/Documents/BOM批量导入模板0407.xlsx");

        // Map<ParentPartNo, List<BOMItem>>
        Map<String, List<BOMItem>> groupedByParent = bomItems.stream()
                .filter(item -> item.getParentPartNo() != null) // 忽略 ROOT 层
                .collect(Collectors.groupingBy(BOMItem::getParentPartNo));


        for (Map.Entry<String, List<BOMItem>> entry : groupedByParent.entrySet()) {
            String parentPartNo = entry.getKey();
            List<BOMItem> children = entry.getValue();

            System.out.println("父项料号：" + parentPartNo);
            for (BOMItem child : children) {
                System.out.println("  子项料号：" + child.getPartNo() + "，用量：" + child.getQty());
            }
        }

//        bomItems.forEach(System.out::println);


    }*/

    public static List<BOMItem> parseBomFromExcel(InputStream inputStream) {
        ExcelReader reader = ExcelUtil.getReader(inputStream);

        // 读取双行标题
        List<List<Object>> headerRows = reader.read(0, 2);
        List<String> headers = mergeHeaders(headerRows.get(0), headerRows.get(1));

        // 收集字段索引
        List<String> bomKeys = new ArrayList<>();
        Map<String, Integer> headerIndexMap = new HashMap<>();
        Integer positionIndex = null;
        Integer lineNumberIndex = null; // 新增子件项次列索引

        for (int i = 0; i < headers.size(); i++) {
            String h = headers.get(i);
            if (h.startsWith("BOM层级（必填项）_")) {
                bomKeys.add(h);
            } else if (h.startsWith("料号（必填项）_")) {
                headerIndexMap.put("partNo", i);
            } else if (h.startsWith("用量（必填项）_")) {
                headerIndexMap.put("qty", i);
            } else if (h.startsWith("位号")) {
                positionIndex = i;
            } else if (h.startsWith("子件项次")) {
                lineNumberIndex = i;
            }
        }

        if (!headerIndexMap.keySet().containsAll(Arrays.asList("partNo", "qty")) || bomKeys.isEmpty()) {
            throw new JWIException("Excel缺少必要字段：BOM层级、料号（必填项）、用量（必填项）");
        }

        List<List<Object>> dataRows = reader.read(2);
        List<BOMItem> bomList = new ArrayList<>();
        Map<Integer, String> latestPartNoByLevel = new HashMap<>();

        // 错误信息容器
        StringBuilder errorBuilder = new StringBuilder();
        Map<String, Set<String>> parentToPositions = new HashMap<>();
        Map<String, Set<String>> parentToLineNumbers = new HashMap<>();
        Set<String> mismatchPartNos = new HashSet<>();
        Set<String> duplicatePositions = new LinkedHashSet<>();
        Set<String> duplicateLineNumbers = new LinkedHashSet<>();

        int rowNum = 3; // 从第3行开始
        for (List<Object> row : dataRows) {
            int level = -1;
            for (int i = 0; i < bomKeys.size(); i++) {
                int colIdx = headers.indexOf(bomKeys.get(i));
                if (colIdx < row.size() && row.get(colIdx) != null && !row.get(colIdx).toString().trim().isEmpty()) {
                    level = i;
                    break;
                }
            }

            String partNo = getCellValue(row, headerIndexMap.get("partNo"));
            String qtyStr = getCellValue(row, headerIndexMap.get("qty"));
            String positionStr = positionIndex != null ? getCellValue(row, positionIndex) : "";
            String lineNumberStr = lineNumberIndex != null ? getCellValue(row, lineNumberIndex) : "";

            // 基础校验
            if (level == -1) {
                errorBuilder.append("第 ").append(rowNum).append(" 行缺少 BOM层级（必填项）\n");
            }
            if (partNo.isEmpty()) {
                errorBuilder.append("第 ").append(rowNum).append(" 行缺少 料号（必填项）\n");
            }
            if (qtyStr.isEmpty()) {
                errorBuilder.append("第 ").append(rowNum).append(" 行缺少 用量（必填项）\n");
            }

            String parentPartNo = (level == 0) ? null : latestPartNoByLevel.get(level - 1);
            latestPartNoByLevel.put(level, partNo);

            // 处理位号与用量一致性
            if (!qtyStr.isEmpty() && !positionStr.isEmpty()) {
                try {
                    int quantity = Integer.parseInt(qtyStr.trim());
                    int refCount = positionStr.split("[,，]").length;
                    if (refCount != quantity) {
                        if (!partNo.isEmpty()) {
                            mismatchPartNos.add(partNo);
                        } else {
                            mismatchPartNos.add("第 " + rowNum + " 行");
                        }
                    }
                } catch (NumberFormatException e) {
                    if (!partNo.isEmpty()) {
                        mismatchPartNos.add(partNo + "（用量非数字）");
                    } else {
                        mismatchPartNos.add("第 " + rowNum + " 行（用量非数字）");
                    }
                }
            }

            // 位号重复校验（按 parentPartNo）
            if (!positionStr.isEmpty()) {
                String[] positions = positionStr.split("[,，]");
                Set<String> parentPositions = parentToPositions.computeIfAbsent(parentPartNo == null ? "ROOT" : parentPartNo, k -> new HashSet<>());
                for (String pos : positions) {
                    pos = pos.trim();
                    if (pos.isEmpty()) continue;
                    if (!parentPositions.add(pos)) {
                        duplicatePositions.add("父项【" + (parentPartNo == null ? "ROOT" : parentPartNo) + "】下位号重复：" + pos);
                    }
                }
            }

            // 子件项次重复校验（按 parentPartNo）
            if (!lineNumberStr.isEmpty()) {
                Set<String> parentLineNums = parentToLineNumbers.computeIfAbsent(parentPartNo == null ? "ROOT" : parentPartNo, k -> new HashSet<>());
                if (!parentLineNums.add(lineNumberStr.trim())) {
                    //0513 去除子项重复校验
//                    duplicateLineNumbers.add("父项【" + (parentPartNo == null ? "ROOT" : parentPartNo) + "】下子件项次重复：" + lineNumberStr);
                }
            }

            // 构建 BOMItem
            bomList.add(new BOMItem(partNo, qtyStr, parentPartNo, level, positionStr, lineNumberStr));
            rowNum++;
        }

        // 构造错误信息
        if (!mismatchPartNos.isEmpty()) {
            errorBuilder.append("位号与数量不匹配：")
                    .append(String.join("，", mismatchPartNos)).append("\n");
        }
        if (!duplicatePositions.isEmpty()) {
            errorBuilder.append(String.join("\n", duplicatePositions)).append("\n");
        }
        if (!duplicateLineNumbers.isEmpty()) {
            errorBuilder.append(String.join("\n", duplicateLineNumbers)).append("\n");
        }

        if (errorBuilder.length() > 0) {
            reader.close();
            throw new JWIException("BOM数据校验失败：\n" + errorBuilder.toString());
        }

        reader.close();
        return bomList;
    }



    private static String getCellValue(List<Object> row, int index) {
        if (index >= row.size()) return "";
        Object val = row.get(index);
        return val == null ? "" : val.toString().trim();
    }

    private static List<String> mergeHeaders(List<Object> row1, List<Object> row2) {
        List<String> merged = new ArrayList<>();
        for (int i = 0; i < row2.size(); i++) {
            String top = i < row1.size() ? String.valueOf(row1.get(i)).trim() : "";
            String bottom = String.valueOf(row2.get(i)).trim();
            String title = top.isEmpty() ? bottom : top + "_" + bottom;
            merged.add(title);
        }
        return merged;
    }

    // 内部数据结构类
    public static class BOMItem {
        private final String partNo;
        private final String qty;
        private final String parentPartNo;
        private final int level;
        private final String position;
        private final String lineNumber; // 新增子件项次字段

        public BOMItem(String partNo, String qty, String parentPartNo, int level, String position, String lineNumber) {
            this.partNo = partNo;
            this.qty = qty;
            this.parentPartNo = parentPartNo;
            this.level = level;
            this.position = position;
            this.lineNumber = lineNumber;
        }

        public String getPartNo() { return partNo; }
        public String getQty() { return qty; }
        public String getParentPartNo() { return parentPartNo; }
        public int getLevel() { return level; }
        public String getPosition() { return position; }
        public String getLineNumber() { return lineNumber; }

        @Override
        public String toString() {
            return "Level: " + level + ", PartNo: " + partNo +
                    ", Position: " + position + ", LineNumber: " + lineNumber +
                    ", Qty: " + qty + ", Parent: " + (parentPartNo != null ? parentPartNo : "ROOT");
        }
    }
}