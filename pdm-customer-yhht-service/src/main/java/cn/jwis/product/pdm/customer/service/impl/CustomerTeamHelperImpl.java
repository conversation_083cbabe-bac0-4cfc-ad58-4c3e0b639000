package cn.jwis.product.pdm.customer.service.impl;

import cn.hutool.json.JSONUtil;
import cn.jwis.audit.annotation.JWIParam;
import cn.jwis.audit.annotation.JWIServiceAudit;
import cn.jwis.audit.enums.Action;
import cn.jwis.core.base.database.redis.utils.MultiTenantRedisUtil;
import cn.jwis.framework.base.bean.util.BeanUtil;
import cn.jwis.framework.base.dto.UserDTO;
import cn.jwis.framework.base.exception.JWIException;
import cn.jwis.framework.base.util.Assert;
import cn.jwis.framework.base.util.CollectionUtil;
import cn.jwis.framework.base.util.OidGenerator;
import cn.jwis.framework.base.util.SpringContextUtil;
import cn.jwis.framework.base.util.ValidationUtil;
import cn.jwis.framework.base.web.session.SessionHelper;
import cn.jwis.framework.database.core.query.dynamic.Condition;
import cn.jwis.platform.iam.user.User;
import cn.jwis.platform.plm.account.entity.role.Role;
import cn.jwis.platform.plm.container.TeamRepo;
import cn.jwis.platform.plm.container.dto.team.UnBindUserBatchDTO;
import cn.jwis.platform.plm.container.entity.*;
import cn.jwis.platform.plm.container.entity.response.TeamRoleWithUser;
import cn.jwis.platform.plm.container.service.Impl.TeamHelperImpl;
import cn.jwis.platform.plm.container.service.TeamHelper;
import cn.jwis.platform.plm.container.service.TeamRoleService;
import cn.jwis.platform.plm.container.service.TeamService;
import cn.jwis.platform.plm.container.service.impl.FolderServiceImpl;
import cn.jwis.platform.plm.container.util.Constaints;
import cn.jwis.platform.plm.dataengine.common.JWICommonService;
import cn.jwis.platform.plm.foundation.common.service.CommonAbilityHelper;
import cn.jwis.platform.plm.foundation.common.service.CommonAbilityService;
import cn.jwis.platform.plm.foundation.site.Site;
import cn.jwis.platform.plm.permission.administrativedomain.AdministrativeDomainService;
import cn.jwis.platform.plm.permission.administrativedomain.dto.AdministrativeContainerDTO;
import cn.jwis.platform.plm.permission.administrativedomain.entity.AdministrativeDomain;
import cn.jwis.platform.plm.permission.util.PermissionCacheUtil;
import cn.jwis.platform.plm.permissions.util.RedisUtil;
import cn.jwis.product.pdm.customer.dos.UserRoleInfo;
import cn.jwis.product.pdm.customer.repo.CustomerCommonRepo;
import cn.jwis.product.pdm.customer.repo.PdmTeamRepo;
import cn.jwis.product.pdm.customer.service.interf.CustomerTeamHelper;
import cn.jwis.product.pdm.customer.service.interf.PDMFolderServiceI;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Primary;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2023/10/31 15:39
 * @Email <EMAIL>
 */
@Service
@Primary
@Transactional
@Slf4j
public class CustomerTeamHelperImpl extends TeamHelperImpl  implements CustomerTeamHelper {

    private static final String MEMBER = "member";

    private static final String PM = "ProductManager";

    @Autowired
    CommonAbilityHelper commonAbilityHelper;

    @Autowired
    PdmTeamRepo pdmTeamRepo;

    @Autowired
    private TeamRoleService teamRoleService;

    @Autowired
    private AdministrativeDomainService domainService;

    @Resource
    private CommonAbilityService commonAbilityService;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private TeamService teamService;
    @Resource
    CustomerCommonRepo customerCommonRepo;

    @Resource
    JWICommonService jwiCommonService;

    @Resource
    TeamRepo teamRepo;

    @Autowired
    private PermissionCacheUtil permissionCacheUtil;

    @Resource
    private PDMFolderServiceI pdmFolderServiceI;

    @Resource
    private TeamHelper teamHelper;

    @Autowired
    private CustomerFolderServiceImpl folderService;


    @Override
    public Long bindUser(BindUser bindUser) throws JWIException {
        long result =  this.customerBindUser(bindUser,true);
        // 如果是更新文件夹团队的角色，则获取到上级的（产品、资源）容器团队，把人加到成员角色中，以解决文件夹团队的人看不到私有容器的问题。
        Folder folder = (Folder)commonAbilityHelper.findDetailEntity(bindUser.getContainerOid(), Folder.TYPE);
        if(folder != null){
            CustomerTeamHelperImpl customerTeamHelper = SpringContextUtil.getBean(CustomerTeamHelperImpl.class);
            customerTeamHelper.bindContainerTeam(folder,bindUser);
        }
        return result;
    }

    public Long customerBindUser(BindUser bindUser,boolean filterMember) {
        ValidationUtil.validate(bindUser);
        List<User> teamUserList = bindUser.getTeamUserList();
        if (CollectionUtils.isEmpty(teamUserList)) {
            return 0L;
        }
        String teamRoleOid = bindUser.getTeamRoleOid();
        TeamRole byOid = teamRoleService.findByOid(teamRoleOid);
        Assert.notNull(byOid, "Team role does not exists");
        Assert.isFalse(filterMember && MEMBER.equals(byOid.getName()), "Can not change user of member role");
        List<User> teamUserFromList = teamRoleService.findTeamUserByOid(teamRoleOid);
        List<String> teamUserOidList = teamUserFromList.stream().map(User::getOid).collect(Collectors.toList());
        teamUserList =
                bindUser.getTeamUserList().stream().filter(user -> !teamUserOidList.contains(user.getOid())).collect(Collectors.toList());
        List<String> userOids = teamUserList.stream().map(User::getOid).collect(Collectors.toList());

        Long count = 0L;
        if(CollectionUtils.isNotEmpty(userOids)) {
            count = teamRoleService.bindUser(teamRoleOid, userOids);
        }
        if(CollectionUtils.isNotEmpty(teamUserList)) {
            AdministrativeDomain domain = domainService.searchByContainerOid(bindUser.getContainerOid());
            addUserRoleCache(domain.getOid(), byOid, teamUserList);
        }
        log.info("addUserRoleCache count ==>{}", count);
        return count;
    }

    @Async
    @Override
    public void bindContainerTeam(Folder folder, BindUser bindUser) {
        // 取到容器的member角色
        TeamRole memberRole = queryMember(folder.getContainerOid());
        if(memberRole == null) {
            return;
        }
        bindUser.setContainerOid(folder.getContainerOid());
        bindUser.setTeamRoleOid(memberRole.getOid());
        // 取到容器下所有文件夹团队的人
//        List<User> teamUserList = pdmTeamRepo.findFolderTeamAllUser(folder.getContainerOid());
//        bindUser.setTeamUserList(teamUserList);
        customerBindUser(bindUser,false);
    }

    private TeamRole queryMember(String containerOid) {
        return pdmTeamRepo.findContainerMemberRole(containerOid,"member");
    }

    @Override
    public void batchUnBindUser(UnBindUserBatchDTO dto) {
        super.batchUnBindUser(dto);
        batchUnBindAllFolder(dto);
        // 如果是更新文件夹团队的角色，则获取到上级的（产品、资源）容器团队，把人加到成员角色中，以解决文件夹团队的人看不到私有容器的问题。
        Folder folder = (Folder)commonAbilityHelper.findDetailEntity(dto.getContainerOid(), Folder.TYPE);
        if(folder != null){
            bindContainerTeam(folder,new BindUser());
        }
    }


    @JWIServiceAudit(buzOid = "${result.oid}", bizName = "${result.number}",buzType = "${dto.modelDefinition}",action = Action.ADD,content = "团队批量解除绑定用户")
    @JWIParam("result")
    @Override
    public void unBindUser(UnBindUser unBindUser) throws JWIException {
        ValidationUtil.validate(unBindUser);
        List<String> teamUserOids = unBindUser.getTeamUserOidList();
        if (org.apache.commons.collections.CollectionUtils.isEmpty(teamUserOids)) {
            return;
        }
        String teamRoleOid = unBindUser.getTeamRoleOid();
        TeamRole byOid = teamRoleService.findByOid(teamRoleOid);
        Assert.notNull(byOid, "Team role does not exists");
        //去除团队成员的删除防护
//        Assert.isFalse(MEMBER.equals(byOid.getName()), "Can not change user of member role");
        TeamRole teamRole = teamRoleService.findTeamRole(teamRoleOid);
        Assert.notNull(teamRole, "role does not exists ");
        if (PM.equals(teamRole.getName())){
            // pm至少得有一名user
            Assert.isTrue(teamRoleService.existsUserAbsent(teamRoleOid, teamUserOids),
                    "please remain one Product manager at least");
        }
        teamRoleService.unBindUser(unBindUser);
        removeRedisData(unBindUser, teamUserOids, byOid);
    }


    private void removeRedisData(UnBindUser unBindUser, List<String> teamUserOids, TeamRole byOid) {
        String key;
        String roleDataStr;
        List<TeamRole> teamRoles;
        List<String> teamRoleOids;
        AdministrativeDomain domain = domainService.searchByContainerOid(unBindUser.getContainerOid());
        List<AdministrativeContainerDTO> administrativeContainerDTOList;
        MultiTenantRedisUtil.changeOtherToDefaultDB();
        User user;
        TeamRole redisRole = null;
        for (String userOid : teamUserOids) {
            user = (User) commonAbilityService.findByOid(userOid, User.TYPE);
            key = Constaints.PERMISSION + ":" + user.getAccount();
            roleDataStr = (String) redisUtil.get(key);
            if (StringUtils.isNotEmpty(roleDataStr)) {
                administrativeContainerDTOList = JSONObject.parseArray(roleDataStr, AdministrativeContainerDTO.class);
                Map<String, List<TeamRole>> adRoleMap = administrativeContainerDTOList.stream().collect(Collectors.toMap(AdministrativeContainerDTO::getDomainOid, AdministrativeContainerDTO::getRoles));
                teamRoles = adRoleMap.get(domain.getOid());
                if(teamRoles == null){//匹配不上的情况，添加用户的地方漏了添加缓存(应当补充好)，那么这里特殊处理放行，不处理缓存了
                    continue;
                }else{
                    redisRole = getTeamRole(byOid, teamRoles, redisRole);
                    if(redisRole != null) {
                        teamRoles.remove(redisRole);
                        redisUtil.set(key, JSONObject.toJSONString(administrativeContainerDTOList, SerializerFeature.DisableCircularReferenceDetect), -1);
                    }
                }
            }
        }
    }

    private TeamRole getTeamRole(TeamRole byOid, List<TeamRole> teamRoles, TeamRole redisRole) {
        for (TeamRole teamRole : teamRoles) {
            if(StringUtils.isNotEmpty(teamRole.getOid()) && teamRole.getOid().equals(byOid.getOid())) {
                redisRole = teamRole;
                break;
            }
        }
        return redisRole;
    }

    /**
     *
     * @param target    目标团队
     * @param teamRoles 原文件夹团队下的角色-用户
     * @param folderOid 目标文件夹oid
     * @param isCopy
     * @return
     */
    @Override
    public boolean buildTeamByCopyFolder(Team target, List<TeamRoleWithUser> teamRoles, String folderOid, boolean isCopy) {
        if (CollectionUtil.isEmpty(teamRoles)){
            return true;
        }
        String oid = target.getOid();
        BindRole bindRole = new BindRole();
        bindRole.setTeamOid(oid);
        Set<String> roleNames = CollectionUtil.mapToSet(teamRoles, TeamRoleWithUser::getName);
        List<TeamRole> roleByName = findRoleByName(roleNames);
        bindRole.setTeamRoleList(roleByName);
        // 绑定角色
        teamService.bindRole(bindRole);//带进去的是原文件夹的团队角色人员，出来的是新的团队角色人员。取决于bindRole.setTeamOid(oid);传的值
        // 是否绑定用户
        if (isCopy) {
            Map<String, String> roleName2Oid = CollectionUtil.splitToMap(roleByName, TeamRole::getName, TeamRole::getOid);
            //查询团队角色
            List<TeamRole> targetTeamRoles = teamRoleService.searchTeamRole(oid);
            String roleOid = null;
            List<UserDTO> users = null;
            for (TeamRoleWithUser teamRole : teamRoles) {
                users = teamRole.getUsers();
                if (CollectionUtil.isEmpty(users)){
                    continue;
                }
                roleOid = roleName2Oid.get(teamRole.getName());
                teamRoleService.bindUserByAccount(roleOid, CollectionUtil.mapToList(users, UserDTO::getAccount));
                //添加管流域-团队角色-用户的权限缓存
                if (CollectionUtils.isNotEmpty(users)) {
                    TeamRole targetTeamRole = targetTeamRoles.stream().filter(item -> item.getName().equals(teamRole.getName())).findFirst().orElse(new TeamRole());;
                    AdministrativeDomain domain = domainService.searchByContainerOid(folderOid);//新创建的文件夹对应的管理域
                    List<User> listUser = BeanUtil.cloneList(users, User.class);
                    addUserRoleCache(domain.getOid(), targetTeamRole, listUser);
                    //同步复制listUser 到 产品/资源容器的 团队成员中
                    Folder parent = folderService.findByOids(Collections.singletonList(folderOid)).stream().findFirst().orElse(new Folder());
                    BindUser bindUser = new BindUser();
                    bindUser.setTeamUserList(listUser);
                    bindContainerTeam(parent,bindUser);
                }
            }
        }
        return true;
    }

    private List<TeamRole> findRoleByName(Collection<String> roleNames){
        // 查询 系统默认角色
        List<TeamRole> teamRoles = teamRoleService.findAccountRoleWithCondition(Condition.where("containerModelType").eq(Site.TYPE)
                .and(Condition.where("name").in(roleNames)).and(Condition.where("disable").eq(0)));
        // 过滤出非系统默认角色
        Set<String> systeamRoleName = CollectionUtil.mapToSet(teamRoles, TeamRole::getName);
        List<String> otherRoleNames = roleNames.stream().filter(item -> !systeamRoleName.contains(item)).collect(Collectors.toList());
        // 查询非系统默认
        List<TeamRole> otherTeamRoles = teamRoleService.findAccountRoleWithCondition(
                Condition.where("containerOid").eq(SessionHelper.getCurrentUser().getTenantOid())
                        .and(Condition.where("disable").eq(0)).and(Condition.where("name").in(otherRoleNames))
        );
        teamRoles.addAll(otherTeamRoles);
        return teamRoles;
    }


    public boolean changeRole(String account) {
        User user = jwiCommonService.dynamicQueryOne(User.TYPE,Condition.where("account").eq(account),User.class);
        Assert.notNull(user,"未找到用户信息");
        List<UserRoleInfo> userRoleInfoList = customerCommonRepo.queryUserRoleInfo(account);
        Map<String,List<UserRoleInfo>> folderOidRoleMap =
                userRoleInfoList.stream().collect(Collectors.groupingBy(UserRoleInfo::getCatalogOid));
        //需要创建访客的 文件夹oid 用户oid
        Map<String,String> needBindVisitorFolderUserMap = new HashMap<>();
        //需要创建访客的 文件夹oid,teamOid
        Map<String,String> needBindVisitorFolderTeamMap = new HashMap<>();
        //需要创建访客的 文件夹oid,teamOidList
        Map<String,List<String>> toDeleteCatalogRoleListLink = new HashMap<>();
        for (Map.Entry<String, List<UserRoleInfo>> entry : folderOidRoleMap.entrySet()) {
            UserRoleInfo roleInfo =
                    entry.getValue().stream().filter(item->"cn_jwis_visitor".equalsIgnoreCase(item.getRoleName())).findAny().orElse(null);
            // 需要进行访客绑定的人与文件夹
            if(roleInfo == null) {
                UserRoleInfo any = entry.getValue().stream().findAny().get();
                needBindVisitorFolderUserMap.put(any.getCatalogOid(),any.getUserOid());
                needBindVisitorFolderTeamMap.put(any.getCatalogOid(),any.getTeamOid());
            } else {
                // 不需要进行访客绑定的人与文件夹
                entry.getValue().remove(roleInfo);
            }
            for (UserRoleInfo userRoleInfo : entry.getValue()) {
                toDeleteCatalogRoleListLink.computeIfAbsent(userRoleInfo.getCatalogOid(),k->new ArrayList<>()).add(userRoleInfo.getRoleOid());
            }
        }

        if (!needBindVisitorFolderTeamMap.isEmpty()) {
            // 清理导航缓存
            this.permissionCacheUtil.clearCatch(Lists.newArrayList(user.getOid()));
            // 查询已存在访客目录
            Map<String, UserRoleInfo> hasVisitorRoleCatalogMap =
                    customerCommonRepo.queryVisitorRoleInFolder(needBindVisitorFolderTeamMap.keySet()).stream().collect(Collectors.toMap(UserRoleInfo::getCatalogOid, v -> v, (o1, o2) -> o2));
            //需要创建访客角色的 文件夹 team oidMAP
            Map<String,String> needCreate =
                    needBindVisitorFolderTeamMap.entrySet().stream().filter(item-> !hasVisitorRoleCatalogMap.containsKey(item.getKey())).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
            // 创建访客角色并绑定用户
            if(!needCreate.isEmpty()) {
                Role visitorRole = jwiCommonService.dynamicQueryOne(Role.TYPE, Condition.where("name").eq(
                        "cn_jwis_visitor"), Role.class);
                Assert.notNull(visitorRole, "系统内未找到访客角色");

                Map<String, TeamRole> folderTeamRoleOidMap = bingVisitorRole(needCreate, visitorRole);
                for (Map.Entry<String, TeamRole> folderTeamRoleEntry : folderTeamRoleOidMap.entrySet()) {

                    bindUser2TeamRole(folderTeamRoleEntry.getValue(), folderTeamRoleEntry.getKey(),
                            Lists.newArrayList(user.getOid()), account);
                }
            }
            // 已存在访客直接添加
            for (UserRoleInfo userRoleInfo : hasVisitorRoleCatalogMap.values()) {
                TeamRole teamRole = new TeamRole();
                teamRole.setOid(userRoleInfo.getRoleOid());
                teamRole.setName(userRoleInfo.getRoleName());
                teamRole.setSourceOid(userRoleInfo.getRoleSourceId());
                bindUser2TeamRole(teamRole, userRoleInfo.getCatalogOid(),
                        Lists.newArrayList(user.getOid()), account);
            }

        }
        //解除原有绑定
        if(!toDeleteCatalogRoleListLink.isEmpty()) {
            unBindUser2TeamRole(toDeleteCatalogRoleListLink,user.getOid());
        }
        return false;
    }

    private void unBindUser2TeamRole(Map<String,List<String>> toDeleteFolderRoleListLink,
                                     String userOid) {
        for (Map.Entry<String, List<String>> entry : toDeleteFolderRoleListLink.entrySet()) {
            for (String roleOid : entry.getValue()) {
                UnBindUser unBindUser = new UnBindUser();
                unBindUser.setContainerOid(entry.getKey());
                unBindUser.setTeamRoleOid(roleOid);
                unBindUser.setTeamUserOidList(Collections.singletonList(userOid));
                this.teamRoleService.unBindUser(unBindUser);
                TeamRole teamRole = new TeamRole();
                teamRole.setOid(roleOid);
                this.removeRedisData(unBindUser, Collections.singletonList(userOid), teamRole);
            }
        }
    }

    private long bindUser2TeamRole(TeamRole teamRole, String folderOid, List<String> userOidList, String account) {
        User user = new User();
        user.setAccount(account);
        long count = 0;
        if (CollectionUtils.isNotEmpty(userOidList)) {
            count = teamRoleService.bindUser(teamRole.getOid(), userOidList);
        }
        AdministrativeDomain domain = domainService.searchByContainerOid(folderOid);
        addUserRoleCache(domain.getOid(), teamRole, Lists.newArrayList(user));
        return count;
    }

    // 返回文件夹oid, teamRoleOid
    private Map<String,TeamRole> bingVisitorRole(Map<String,String> needBindVisitorFolderTeam,Role visitorRole) {
        Map<String,TeamRole> result = new HashMap<>();

        for (Map.Entry<String, String> entry : needBindVisitorFolderTeam.entrySet()) {
            BindRole bindRole = new BindRole();
            bindRole.setContainerOid(entry.getKey());
            bindRole.setTeamOid(entry.getValue());

            String teamRoleOid = OidGenerator.newOid();
            TeamRole teamRole = BeanUtil.copyProperties(visitorRole,new TeamRole());
            teamRole.setSourceOid(visitorRole.getOid());
            teamRole.setOid(teamRoleOid);
            teamRole.setModelDefinition(TeamRole.TYPE);
            teamRole.setName(visitorRole.getName());
            bindRole.setTeamRoleList(Lists.newArrayList(teamRole));
            teamRepo.bindRole(bindRole);

            result.put(entry.getKey(),teamRole);
        }
        return result;
    }
    private void batchUnBindAllFolder(UnBindUserBatchDTO dto) {
        //去掉容器下 所有文件夹下的团队中的 1、找到产品容器下的所有文件夹 2、找到每个文件夹下的所有roles 3、每个roles下寻找是否有当前user 4、如果有当前user，添加到UnBindUserBatchDTO对象中，删除
        //1、获取当前容器id
        String containerOid = dto.getContainerOid();
        String userOid = dto.getUserOid();
        //2、获取当前容器下的所有文件夹
        // 获取根文件夹节点列表
        List<FolderTreeNode> folderTreeNodes = pdmFolderServiceI.searchFoldersWithPermisson(null, containerOid, null);
        for (FolderTreeNode folderTreeNode : folderTreeNodes) {
            processFolderTreeNode(folderTreeNode, userOid, containerOid);
        }
    }

    private void processFolderTreeNode(FolderTreeNode folderTreeNode, String userOid, String containerOid) {
        UnBindUserBatchDTO unBindUserBatchDTO = new UnBindUserBatchDTO();
        ArrayList<String> roles = new ArrayList<>();
        unBindUserBatchDTO.setUserOid(userOid);
        unBindUserBatchDTO.setContainerOid(containerOid);
        unBindUserBatchDTO.setRoles(roles);

        log.info("Processing folderTreeNode--->>{}", JSONUtil.toJsonStr(folderTreeNode));

        // 获取文件夹的团队角色及用户信息
        Team teamRole = pdmFolderServiceI.getTeamRole(folderTreeNode.getOid(), null);
        log.info("teamRole--->>{}", JSONUtil.toJsonStr(teamRole));

        List<TeamRoleWithUser> teamRoleWithUsers = this.teamHelper.fuzzyContent(teamRole.getOid(), null);
        log.info("teamRoleWithUsers--->>{}", JSONUtil.toJsonStr(teamRoleWithUsers));

        for (TeamRoleWithUser teamRoleWithUser : teamRoleWithUsers) {
            List<UserDTO> users = teamRoleWithUser.getUsers();
            for (UserDTO user : users) {
                if (user.getOid().equals(userOid)) {
                    roles.add(teamRoleWithUser.getOid());
                    break;
                }
            }
        }

        log.info("unBindUserBatchDTO--->>{}", JSONUtil.toJsonStr(unBindUserBatchDTO));
        if (!unBindUserBatchDTO.getRoles().isEmpty()) {
            super.batchUnBindUser(unBindUserBatchDTO);
        }

        // 递归处理 children
        List<FolderTreeNode> children = folderTreeNode.getChildren();
        if (children != null && !children.isEmpty()) {
            for (FolderTreeNode child : children) {
                processFolderTreeNode(child, userOid, containerOid);
            }
        }
    }

}
