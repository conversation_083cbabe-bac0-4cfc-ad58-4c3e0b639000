package cn.jwis.product.pdm.customer.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.jwis.framework.base.bean.util.BeanUtil;
import cn.jwis.framework.base.domain.able.info.ModelInfo;
import cn.jwis.framework.base.domain.entity.BaseEntity;
import cn.jwis.framework.base.dto.UserDTO;
import cn.jwis.framework.base.exception.JWIException;
import cn.jwis.framework.base.util.Assert;
import cn.jwis.framework.base.util.CollectionUtil;
import cn.jwis.framework.base.util.StringUtil;
import cn.jwis.framework.base.util.ValidationUtil;
import cn.jwis.framework.base.web.session.SessionHelper;
import cn.jwis.framework.database.core.entity.SubFilter;
import cn.jwis.framework.database.core.query.dynamic.Condition;
import cn.jwis.platform.iam.user.User;
import cn.jwis.platform.plm.account.auth.service.AuthHelper;
import cn.jwis.platform.plm.account.entity.team.response.TeamTemplateRoleWithUser;
import cn.jwis.platform.plm.account.tenant.service.TenantService;
import cn.jwis.platform.plm.account.user.service.UserHelper;
import cn.jwis.platform.plm.container.account.FileRemote;
import cn.jwis.platform.plm.container.entity.Container;
import cn.jwis.platform.plm.container.entity.Folder;
import cn.jwis.platform.plm.container.service.ContainerHelper;
import cn.jwis.platform.plm.container.service.FolderService;
import cn.jwis.platform.plm.dataengine.common.JWICommonService;
import cn.jwis.platform.plm.file.entity.FileMetadata;
import cn.jwis.platform.plm.file.service.FileHelper;
import cn.jwis.platform.plm.foundation.attachment.entity.File;
import cn.jwis.platform.plm.foundation.classification.able.info.ClassificationInfo;
import cn.jwis.platform.plm.foundation.classification.entity.Classification;
import cn.jwis.platform.plm.foundation.classification.service.ClassificationHelper;
import cn.jwis.platform.plm.foundation.common.service.CommonAbilityHelper;
import cn.jwis.platform.plm.foundation.common.service.CommonAbilityService;
import cn.jwis.platform.plm.foundation.location.able.info.LocationInfo;
import cn.jwis.platform.plm.foundation.relationship.Contain;
import cn.jwis.platform.plm.foundation.relationship.Reference;
import cn.jwis.platform.plm.foundation.versionrule.able.info.LockInfo;
import cn.jwis.product.pdm.customer.entity.CustomerFolderTreeNode;
import cn.jwis.product.pdm.customer.entity.CustomerInstanceEntity;
import cn.jwis.product.pdm.customer.remote.IamRemote;
import cn.jwis.product.pdm.customer.repo.CustomerCommonRepo;
import cn.jwis.product.pdm.customer.service.dto.IDSDocCreateDTO;
import cn.jwis.product.pdm.customer.service.interf.CommonService;
import cn.jwis.product.pdm.customer.service.interf.DingTalkService;
import cn.jwis.product.pdm.customer.service.interf.DocumentMilkyWayHelper;
import cn.jwis.product.pdm.customer.service.util.HttpClientUtil;
import cn.jwis.product.pdm.delivery.dto.DeliveryCreateDTO;
import cn.jwis.product.pdm.delivery.entity.Delivery;
import cn.jwis.product.pdm.delivery.helper.DeliveryHelper;
import cn.jwis.product.pdm.document.entity.Document;
import cn.jwis.product.pdm.document.entity.DocumentIteration;
import cn.jwis.product.pdm.document.service.DocumentHelper;
import cn.jwis.product.pdm.document.service.DocumentService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.dingtalkoauth2_1_0.models.GetAccessTokenResponse;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.text.MessageFormat;
import java.util.*;
import java.util.stream.Collectors;

import static cn.jwis.product.pdm.document.service.DocumentHelper.REDISSON_DOCUMENT_NAME_KEY;

@Service
@Transactional
public class DocumentMilkyWayHelperImpl implements DocumentMilkyWayHelper {

    public static final Logger logger = LoggerFactory.getLogger(DocumentMilkyWayHelperImpl.class);

    @Value("${dingTalk.appKeyNew:${dingTalk.appKey}}")
    private String dingTalkAppKeyNew;
    @Value("${dingTalk.appSecretNew:${dingTalk.appSecret}}")
    private String dingTalkAppSecretNew;

    @Resource
    private FileRemote fileRemote;

    @Autowired
    private UserHelper userHelper;

    @Autowired
    private FolderService folderService;

    @Autowired
    private AuthHelper authHelper;

    @Autowired
    TenantService tenantService;

    @Autowired
    private DocumentHelper docAppService;

    @Autowired
    private FileHelper fileHelper;

    @Autowired
    private CommonService commonService;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private DocumentService documentService;

    @Autowired
    private CommonAbilityHelper commonAbilityHelper;

    @Autowired
    private CommonAbilityService commonAbilityService;

    @Autowired
    private DeliveryHelper deliveryHelper;

    @Autowired
    JWICommonService jwiCommonService;

    @Autowired
    CustomerCommonAbilityHelperImpl customerCommonAbilityHelper;

    @Autowired
    private ClassificationHelper classificationHelper;

    @Autowired
    private CustomerCommonRepo customerCommonRepo;

    @Autowired
    ContainerHelper containerHelper;

    @Autowired
    private DingTalkService dingTalkService;

    @Resource
    IamRemote iamRemote;

    @Autowired
    CustomerProcessOrderHelper processOrderHelper;

    @Autowired
    private DingTalkCallListen dingTalkCallListen;
    @Resource
    CustomerDeliveryHelperImpl customerDeliveryHelper;





    @Override
    public DocumentIteration createDocumentForIDS(MultipartFile[] files, String account, String name, String location, String source, String auditlist, String fileEncode, String deliveryOid)   {

        logger.info("createDocumentForIDS入参: files={}, account={}, name={}, location={}, source={}, auditlist={},fileEncode={},deliveryOid={}",
                Arrays.toString(files), account, name, location, source, auditlist, fileEncode, deliveryOid);
        //初始化账号信息
        initContext(account);

        List<String> userDingIdList = new ArrayList<>();
        // 空值校验
        if (auditlist == null || auditlist.trim().isEmpty()) {
            logger.info("ids传递的会签人员为空");
        }else {
            String[] split = auditlist.split(",");
            //处理会签人信息
             userDingIdList = getUserDingDingId(split);
        }
        logger.info("会签人userDingIdList 信息{}", JSONUtil.toJsonStr(userDingIdList));
        //pdm里创建数据记录
        String number = "";
        if (null != fileEncode && StrUtil.isNotEmpty(fileEncode)) {
            number = fileEncode;
        }else {
            //没有传递 fileEncode，走创建逻辑
            String containerName = location;
            String folderName = "IDS集合";
            Container container = containerHelper.findProductByName(containerName);
            logger.info("Container 信息{}", JSONUtil.toJsonStr(container));
            Assert.notNull(container, "没有查询到" + containerName + "容器信息");

            String containerOid = container.getOid();
            Folder folder = jwiCommonService.dynamicQueryOne(Folder.TYPE, Condition.where("name").eq(folderName).and(Condition.where("containerOid").eq(containerOid)), Folder.class);
            Assert.notNull(folder, "当前产品容器:【" + location + "】未创建【" + folderName + "】文件夹");
            logger.info("folder 信息{}", JSONUtil.toJsonStr(folder));
            String folderOid = folder.getOid();

            //创建数据记录
            IDSDocCreateDTO idsDocCreateDTO = new IDSDocCreateDTO();
            idsDocCreateDTO.setSource(source);
            idsDocCreateDTO.setSecrecy("内部");
            idsDocCreateDTO.setName(name);
            LocationInfo locationInfo = new LocationInfo();
            locationInfo.setCatalogType("Folder");
            locationInfo.setContainerType("Container");
            locationInfo.setCatalogOid(folderOid);
            locationInfo.setContainerOid(containerOid);
            idsDocCreateDTO.setLocationInfo(locationInfo);
            number = getIDSNumber(idsDocCreateDTO, account, deliveryOid);
        }

        DocumentIteration doc =docAppService.findByNumber(number);
        Assert.notNull(doc, number + "文档不存在");

        if(files == null || files.length == 0){
            return doc;
//            throw new JWIException("文件不存在");
        }
        //处理没有绑定交付清单的ids历史数据
        if (null != fileEncode && StrUtil.isNotEmpty(fileEncode)) {
            JSONObject extensionContent = doc.getExtensionContent();
            if (extensionContent != null && !extensionContent.containsKey("deliveryOid")) {
                logger.info("IDS历史数据" + doc.getNumber() + "没有绑定交付清单，开始关联交付清单");
                //此时需要关联交付清单
                if (null == deliveryOid || StrUtil.isEmpty(deliveryOid)) {
                    String containerOid = doc.getContainerOid();
                    String rootNodeName = "其他交付";
                    String categoryNodeName = "其他文档";
                    Delivery deliveryRoot = createDeliveryRoot(containerOid,rootNodeName);
                    logger.info("Delivery root:{}", JSONUtil.toJsonStr(deliveryRoot));
                    deliveryOid = createCategoryNode(containerOid, categoryNodeName, deliveryRoot.getOid());
                }
                extensionContent.put("deliveryOid", deliveryOid);
                doc.setExtensionContent(extensionContent);
                commonAbilityHelper.doUpdate(doc);
                if (StringUtils.isNotBlank(getDeliveryOidFromJson(doc.getExtensionContent()))) {
                    bindToDelivery(doc.getMasterOid(), getDeliveryOidFromJson(doc.getExtensionContent()));
                }
            }
        }

        long threadId = Thread.currentThread().getId();
        logger.info("threadId:{}, start to upLoadFile",threadId);
        logger.info("files==>" + JSONUtil.toJsonStr(Arrays.stream(files).map(it -> "name==>" + it.getName() + " originalFileName==>" + it.getOriginalFilename()).collect(Collectors.joining(", "))) + " account==>" + account + " number==>" + number);

        String priFileName = files[0].getOriginalFilename(), secondFileName = files.length > 1 ? files[1].getOriginalFilename() : "";



        String lifecycleStatus = doc.getLifecycleStatus();
        if ("UnderReview".equals(lifecycleStatus)) {
            throw new JWIException("当前文档" + number + "正在审阅中");
        }


        LocationInfo locationInfo = new LocationInfo();
        locationInfo.setCatalogType("Folder");
        locationInfo.setContainerType("Container");
        locationInfo.setCatalogOid(doc.getCatalogOid());
        locationInfo.setContainerOid(doc.getContainerOid());

        // 上传文件
        List<File> primaryFile = new ArrayList<>();
        List<File> secondaryFile = new ArrayList<>();
        try {
            // 遍历 MultipartFile 数组
            for (MultipartFile multipartFile : files) {
                String originalFilename = multipartFile.getOriginalFilename();
                JSONObject primary = fileRemote.upload(multipartFile);
                // 获取文件信息
                JSONObject fileInfo = primary.getJSONObject(originalFilename);
                if (fileInfo != null) {
                    File fileNew = new File();
                    fileNew.setOid(fileInfo.getString("oid"));
                    fileNew.setName(fileInfo.getString("fileOriginalName"));
                    // 分类存储文件到 primaryFile 或 secondaryFile
                    if (priFileName.equals(originalFilename)) {
                        primaryFile.add(fileNew);
                    } else if (secondFileName.equals(originalFilename)) {
                        secondaryFile.add(fileNew);
                    }
                }
            }

        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            throw new RuntimeException("文件上传失败");
        }

        logger.info("threadId:{}, after upLoadFile primaryFile:{} ; secondaryFile{}",threadId,primaryFile,secondaryFile);

        if(CollectionUtil.isEmpty(doc.getPrimaryFile())){
            // 首次上传文件，升小版本
            // 检出
            ModelInfo modelInfo = new ModelInfo();
            modelInfo.setOid(doc.getOid());
            modelInfo.setType(doc.getType());
            doc = (DocumentIteration)docAppService.checkOut(modelInfo);
            // 更新
            doc.setPrimaryFile(primaryFile);
            doc.setSecondaryFile(secondaryFile);
            commonAbilityHelper.doUpdate(doc);
            logger.info("threadId:{}, first upload primaryFile doc:{}",threadId,JSONObject.toJSONString(doc));
            // 检入
            doc = (DocumentIteration) docAppService.checkIn(BeanUtil.copyProperties(doc, new LockInfo()));
        }else{
            // 非首次上传文件，升大版本
            // 修订
            if ("Released".equals(lifecycleStatus)) {
                ModelInfo modelInfo = new ModelInfo();
                modelInfo.setOid(doc.getOid());
                modelInfo.setType(doc.getType());
                doc = (DocumentIteration)docAppService.revise(modelInfo);
            }
            // 更新
            doc.setPrimaryFile(primaryFile);
            doc.setSecondaryFile(secondaryFile);
            logger.info("threadId:{},revise doc:{}",threadId,JSONObject.toJSONString(doc));
            commonAbilityHelper.doUpdate(doc);
        }


        //发起技术文档发布流程
        JSONArray bizObjects = new JSONArray();
        bizObjects.add(JSON.parseObject(JSON.toJSONString(doc)));
        JSONObject extensionContent = new JSONObject();
        JSONObject docInfo = new JSONObject();
        extensionContent.put("docInfo", docInfo);

        //构造审批团队
//        JSONArray teamContent = getTeamContent(doc);
        JSONArray teamContent = getTeamContentForIds(doc);

       /* JSONObject delegateTask = new JSONObject();
        delegateTask.put("locationInfo", locationInfo);
        delegateTask.put("bizObjects", bizObjects);
        delegateTask.put("name", "技术文档发布流程");
        delegateTask.put("outgoingType", "电子版本");
        delegateTask.put("modelDefinition", "ProcessOrder");
        delegateTask.put("outgoingNumber", 1);
        delegateTask.put("extensionContent", extensionContent);
        delegateTask.put("teamContent", teamContent);

        try {
            dingTalkService.createSendTaskForIds(delegateTask,userDingIdList);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            throw new JWIException("流程发起失败\n" + e.getMessage(), e);
        }*/
        // 直接 将主文件生成pdf
        try {
            dingTalkCallListen.releaseDocForIds(doc,teamContent,userDingIdList);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            logger.error("ids直接归档失败", e);
        }
        return doc;
    }



    private  JSONArray getTeamContent(DocumentIteration doc) {
        JSONArray teamContent = new JSONArray();
        try {
            CustomerInstanceEntity customerInstanceEntity = new CustomerInstanceEntity();
            BeanUtils.copyProperties(doc, customerInstanceEntity);
            List<CustomerInstanceEntity> instanceEntityList = new ArrayList<>();
            instanceEntityList.add(customerInstanceEntity);
            List<TeamTemplateRoleWithUser> teamTemplateRoleWithUsers = processOrderHelper.autoUser(instanceEntityList);

            boolean isContainPZ = teamTemplateRoleWithUsers.stream()
                    .anyMatch(role -> "cn_jwis_ratifier".equals(role.getName()));
            if (!isContainPZ) {
                throw new JWIException("当前目录没有设置批准人");
            }
            // 初始化 teamContent

            JSONObject firstRole = createRole("owner", "所有者");
            JSONObject secondRole =  createRole("cn_jwis_qzcsr", "前置抄送");
            JSONObject thirdRole = createRole("cn_jwis_bzh", "标准化");
            JSONObject seventhRole = createRole("cn_jwis_ratifier", "批准");
            JSONObject fourthRole = createRole("cn_jwis_proofreader", "校对");
            JSONObject fifthRole = createRole("cn_jwis_shz", "审核");
            JSONObject sixthRole = createRole("cn_jwis_countersigner", "会签");
            JSONObject eighthRole = createRole("cn_jwis_notifier", "后置抄送");
            teamContent.add(firstRole);
            teamContent.add(secondRole);
            teamContent.add(thirdRole);
            teamContent.add(fourthRole);
            teamContent.add(fifthRole);
            teamContent.add(sixthRole);
            teamContent.add(seventhRole);
            teamContent.add(eighthRole);

            for (TeamTemplateRoleWithUser role : teamTemplateRoleWithUsers) {
                switch (role.getName()) {
                    //批准
                    case "cn_jwis_ratifier":
                        updateRoleUsers(seventhRole, role.getUsers());
                        break;
                    //标准化
                    case "cn_jwis_bzh":
                        updateRoleUsers(thirdRole, role.getUsers());
                        break;
                    //校对
                    case "cn_jwis_proofreader":
                        updateRoleUsers(fourthRole, role.getUsers());
                        break;
                    //审核
                    case "cn_jwis_shz":
                        updateRoleUsers(fifthRole, role.getUsers());
                        break;
                }
            }

            logger.info("seventhRole---->>>>{}", JSONUtil.toJsonStr(seventhRole));


        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            throw new JWIException(e.getMessage());
        }

        logger.info("teamContent---->>>>{}", JSONUtil.toJsonStr(teamContent));
        return teamContent;
    }

    private  JSONArray getTeamContentForIds(DocumentIteration doc) {

        JSONArray teamContent = new JSONArray();
        try {
            String tokenNew = getTokenNew();
            CustomerInstanceEntity customerInstanceEntity = new CustomerInstanceEntity();
            BeanUtils.copyProperties(doc, customerInstanceEntity);
            List<CustomerInstanceEntity> instanceEntityList = new ArrayList<>();
            instanceEntityList.add(customerInstanceEntity);
            List<TeamTemplateRoleWithUser> teamTemplateRoleWithUsers = processOrderHelper.autoUser(instanceEntityList);

            logger.info("当前目录角色:{}", JSON.toJSONString(teamTemplateRoleWithUsers));

            boolean isContainPZ = teamTemplateRoleWithUsers.stream()
                    .anyMatch(role -> "cn_jwis_ratifier".equals(role.getName()));
            if (!isContainPZ) {
                throw new JWIException("当前目录没有设置批准人");
            }
            // 初始化 teamContent

            JSONObject firstRole = createRole("owner", "所有者");
            JSONObject secondRole =  createRole("cn_jwis_qzcsr", "前置抄送");
            JSONObject thirdRole = createRole("cn_jwis_bzh", "标准化");
            JSONObject seventhRole = createRole("cn_jwis_ratifier", "批准");
            JSONObject fourthRole = createRole("cn_jwis_proofreader", "校对");
            JSONObject fifthRole = createRole("cn_jwis_shz", "审核");
            JSONObject sixthRole = createRole("cn_jwis_countersigner", "会签");
            JSONObject eighthRole = createRole("cn_jwis_notifier", "后置抄送");
            teamContent.add(firstRole);
            teamContent.add(secondRole);
            teamContent.add(thirdRole);
            teamContent.add(fourthRole);
            teamContent.add(fifthRole);
            teamContent.add(sixthRole);
            teamContent.add(seventhRole);
            teamContent.add(eighthRole);

            for (TeamTemplateRoleWithUser role : teamTemplateRoleWithUsers) {
                switch (role.getName()) {
                    //批准
                    case "cn_jwis_ratifier":
                        updateRoleUsersForIds(seventhRole, role.getUsers(),tokenNew);
                        break;
                    //标准化
                    case "cn_jwis_bzh":
                        updateRoleUsersForIds(thirdRole, role.getUsers(), tokenNew);
                        break;
                    //校对
                    case "cn_jwis_proofreader":
                        updateRoleUsersForIds(fourthRole, role.getUsers(), tokenNew);
                        break;
                    //审核
                    case "cn_jwis_shz":
                        updateRoleUsersForIds(fifthRole, role.getUsers(), tokenNew);
                        break;
                }
            }

            logger.info("seventhRole---->>>>{}", JSONUtil.toJsonStr(seventhRole));


        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            throw new JWIException(e.getMessage());
        }

        logger.info("teamContent---->>>>{}", JSONUtil.toJsonStr(teamContent));
        return teamContent;
    }

    // 构造角色的 JSONObject
    private JSONObject createRole(String roleName, String name) {
        JSONObject role = new JSONObject();
        JSONArray userAccounts = new JSONArray();
        JSONArray users = new JSONArray();

        if ("前置抄送".equals(name)) {
            String account = SessionHelper.getCurrentUser().getAccount();
            userAccounts.fluentAdd(account);
            users.fluentAdd(account);
        }

        role.put("userAccounts", userAccounts);
        role.put("users", users);
        role.put("roleName", roleName);
        role.put("name", name);
        return role;
    }

    // 更新角色的用户信息
    private void updateRoleUsers(JSONObject role, List<UserDTO> users) {
        JSONArray userAccountsArray = new JSONArray();
        JSONArray usersArray = new JSONArray();
        if (users != null) {
            for (UserDTO user : users) {
                userAccountsArray.add(user.getAccount());
                usersArray.add(user.getAccount());
            }
        }
        role.put("userAccounts", userAccountsArray);
        role.put("users", usersArray);
    }

    // 更新角色的用户信息
    private void updateRoleUsersForIds(JSONObject role, List<UserDTO> users, String tokenNew) {
        JSONArray userAccountsArray = new JSONArray();
        JSONArray usersArray = new JSONArray();
        if (users != null) {
            for (UserDTO user : users) {
                String account = user.getAccount();
                userAccountsArray.add(user.getAccount());
                //更新为钉钉 id
                String phone = user.getPhone();
                Assert.notBlank(phone, "用户未配置电话号码，无法完成归档 账户名称:" + user.getName() + " 账户:" + user.getAccount());
                if(StringUtil.isNotBlank(phone) && phone.startsWith("86"))
                    phone = phone.replaceFirst("86", "");
                String userDingId = getUserIdByPhone(phone, tokenNew);
                Assert.notBlank(userDingId, "未获取到钉钉用户信息，无法完成归档 账户名称:" + user.getName() + " 账户:" + user.getAccount() + " 电话:" + user.getPhone());
                usersArray.add(userDingId);
            }
        }
        role.put("userAccounts", userAccountsArray);
        role.put("users", usersArray);
    }

    @Override
    public List<CustomerFolderTreeNode> searchFolders(String account) {
        initContext(account);
//        List<FolderTreeNode> folderTreeNodeList = this.folderService.searchFoldersWithPermisson("Container", "ProductContainer", "", "");
//        return getCustomerFolderTreeNode(folderTreeNodeList);
        return customerCommonRepo.searchFoldersWithPermisson("Container", "ProductContainer", "", "");
    }

    @Override
    public DocumentIteration createData(MultipartFile[] files, String account, String number) throws Exception {
        long threadId = Thread.currentThread().getId();
        logger.info("threadId:{}, start toc upLoadFile",threadId);
        logger.info("files==>" + JSONUtil.toJsonStr(Arrays.stream(files).map(it -> "name==>" + it.getName() + " originalFileName==>" + it.getOriginalFilename()).collect(Collectors.joining(", "))) + " account==>" + account + " number==>" + number);

        if(files == null || files.length == 0){
            throw new JWIException("文件不存在");
        }

        String priFileName = files[0].getOriginalFilename(), secondFileName = files.length > 1 ? files[1].getOriginalFilename() : "";

        DocumentIteration doc =docAppService.findByNumber(number);
        Assert.notNull(doc,"编码不存在");
        initContext(account);
        // 上传文件
        List<File> primaryFile = new ArrayList<>();
        List<File> secondaryFile = new ArrayList<>();
        //
        Map<String, FileMetadata> upload = this.fileHelper.upload(files);
        for (Map.Entry<String, FileMetadata> fileMetadataEntry : upload.entrySet()) {
            File fileNew = new File();
            String key = fileMetadataEntry.getKey();
            FileMetadata value = fileMetadataEntry.getValue();
            fileNew.setOid(value.getOid());
            fileNew.setName(value.getFileOriginalName());
            if (priFileName.equals(key)) {
                primaryFile.add(fileNew);
            } else if(secondFileName.equals(key)) {
                secondaryFile.add(fileNew);
            }
        }
        logger.info("threadId:{}, after upLoadFile primaryFile:{} ; secondaryFile{}",threadId,primaryFile,secondaryFile);
        if(CollectionUtil.isEmpty(doc.getPrimaryFile())){
            // 首次上传文件，升小版本
            // 检出
            ModelInfo modelInfo = new ModelInfo();
            modelInfo.setOid(doc.getOid());
            modelInfo.setType(doc.getType());
            doc = (DocumentIteration)docAppService.checkOut(modelInfo);
            // 更新
            doc.setPrimaryFile(primaryFile);
            doc.setSecondaryFile(secondaryFile);
            commonAbilityHelper.doUpdate(doc);
            logger.info("threadId:{}, first upload primaryFile doc:",threadId,JSONObject.toJSONString(doc));
            // 检入
            doc = (DocumentIteration) docAppService.checkIn(BeanUtil.copyProperties(doc, new LockInfo()));
        }else{
            // 非首次上传文件，升大版本
            // 修订
            ModelInfo modelInfo = new ModelInfo();
            modelInfo.setOid(doc.getOid());
            modelInfo.setType(doc.getType());
            doc = (DocumentIteration)docAppService.revise(modelInfo);
            // 检出
//            modelInfo.setOid(doc.getOid());
//            modelInfo.setType(doc.getType());
//            doc = (DocumentIteration)docAppService.checkOut(modelInfo);
            // 更新
            doc.setPrimaryFile(primaryFile);
            doc.setSecondaryFile(secondaryFile);
            logger.info("threadId:{},revise doc:",threadId,JSONObject.toJSONString(doc));
            commonAbilityHelper.doUpdate(doc);
            // 检入
//            doc = (DocumentIteration) docAppService.checkIn(BeanUtil.copyProperties(doc, new LockInfo()));
        }
        return doc;
    }

    public String getIDSNumber(IDSDocCreateDTO dto, String account, String deliveryOid) {

        if (null == deliveryOid || StrUtil.isEmpty(deliveryOid)) {
            //关联交付清单
            LocationInfo locationInfo = dto.getLocationInfo();
            Assert.notNull(locationInfo, "没有查询到" + dto.getName() + "容器位置信息");
            String containerOid = locationInfo.getContainerOid();
            String targetOid = null;
            String rootNodeName = "其他交付";
            String categoryNodeName = "其他文档";

            Delivery deliveryRoot = createDeliveryRoot(containerOid,rootNodeName);
            logger.info("Delivery root:{}", JSONUtil.toJsonStr(deliveryRoot));
            targetOid = createCategoryNode(containerOid, categoryNodeName, deliveryRoot.getOid());

            if (targetOid != null) {
                deliveryOid = targetOid;
                logger.info("containerOid信息:{},deliveryOid信息: {}", containerOid, deliveryOid);
            } else {
                Assert.notNull(targetOid, "当前产品交付清单中未创建 其他交付-其他文档");
            }
        }
        dto.getLocationInfo().setCatalogType("Folder");
        logger.info("IDSDocCreateDTO==>" + JSONUtil.toJsonStr(dto) + "account==>" + account);

        initContext(account);
        ValidationUtil.validate(dto);
        // 确定 所在目录
        String catalogOid = dto.getLocationInfo().getCatalogOid();
        String catalogType = dto.getLocationInfo().getCatalogType();
        // 校验目录下重名，获取目录下的名称锁
        String name = dto.getName();
        DocumentIteration doc = new DocumentIteration();
        doc.setModelDefinition("JWIGeneralDocument");
        RLock nameLock = redissonClient.getLock(MessageFormat.format(REDISSON_DOCUMENT_NAME_KEY, catalogOid, name));
        try {
            nameLock.tryLock();
            Assert.isTrue(nameLock.isHeldByCurrentThread(), "同名文档创建中:" + name);
            List<DocumentIteration> byName = findByNameAndCatalog(catalogType, catalogOid, name);
            Assert.isEmpty(byName, "目录下存在相同名称文档:" + name);
            doc.setName(name);
            doc.setDescription(dto.getMsg());
            JSONObject extensionContent = new JSONObject();
            extensionContent.put("cn_jwis_secrecy",dto.getSecrecy());
            extensionContent.put("source",dto.getSource());
            extensionContent.put("deliveryOid", deliveryOid);
            doc.setExtensionContent(extensionContent);
            documentService.setLocation(doc, dto.getLocationInfo());
            // 设置分类
            Classification classification = classificationHelper.findByCode("IDS");
            ClassificationInfo classificationInfo = BeanUtil.copyProperties(classification,new ClassificationInfo());
            documentService.setClassification(doc, classificationInfo);
            doc = commonAbilityHelper.doCreate(doc);
            //如果文档关联了分类则去找分类关联的交付清单类型节点。并将他与之挂关系CONTAIN
          /*  if (classificationInfo != null) {
                Delivery result = deliveryHelper.findByClsOid(doc.getContainerOid(), classificationInfo.getOid());
                if (result != null) {
                    String deliveryOid = result.getOid();
                    String deliveryType = result.getType();
                    //挂关系Reference
                    List<RelationAble> rels = new ArrayList<>();
                    Reference reference = new Reference();
                    reference.setFromOid(deliveryOid);
                    reference.setFromType(deliveryType);
                    reference.setToOid(doc.getMasterOid());
                    reference.setToType(doc.getMasterType());
                    rels.add(reference);
                    commonAbilityService.createOutRelation(rels);
                }
            }*/

            if (StringUtils.isNotBlank(getDeliveryOidFromJson(doc.getExtensionContent()))) {
                bindToDelivery(doc.getMasterOid(), getDeliveryOidFromJson(doc.getExtensionContent()));
            }
        } finally {
            if (nameLock.isHeldByCurrentThread()) {
                nameLock.unlock();
            }
        }
        return doc.getNumber();
    }

    private String createCategoryNode(String containerOid, String otherWD, String oid) {
        DeliveryCreateDTO deliveryCreateDTO = new DeliveryCreateDTO();
        LocationInfo locationInfo1 = new LocationInfo();
        locationInfo1.setCatalogType(Container.TYPE);
        locationInfo1.setCatalogOid(containerOid);
        locationInfo1.setContainerModelDefinition("ProductContainer");
        locationInfo1.setContainerType(Container.TYPE);
        locationInfo1.setContainerOid(containerOid);
        deliveryCreateDTO.setLocationInfo(locationInfo1);

        JSONObject extensionContent = new JSONObject();
        extensionContent.put("FZR", " ");
        extensionContent.put("cn_yh_sfhs", "否");
        deliveryCreateDTO.setExtensionContent(extensionContent);

        deliveryCreateDTO.setModelDefinition("Category");
        deliveryCreateDTO.setName(otherWD);
        deliveryCreateDTO.setRoot(Boolean.FALSE);
        deliveryCreateDTO.setParentOid(oid);
        Delivery delivery = deliveryHelper.create(deliveryCreateDTO);
        logger.info("Delivery Category:{}", JSONUtil.toJsonStr(delivery));
        return delivery.getOid();
    }

    private Delivery createDeliveryRoot(String containerOid, String name) {
        DeliveryCreateDTO deliveryCreateDTO = new DeliveryCreateDTO();
        LocationInfo locationInfo1 = new LocationInfo();
        locationInfo1.setCatalogType(Container.TYPE);
        locationInfo1.setCatalogOid(containerOid);
        locationInfo1.setContainerModelDefinition("ProductContainer");
        locationInfo1.setContainerType(Container.TYPE);
        locationInfo1.setContainerOid(containerOid);
        deliveryCreateDTO.setLocationInfo(locationInfo1);
        deliveryCreateDTO.setModelDefinition("Structure");
        deliveryCreateDTO.setName(name);
        deliveryCreateDTO.setRoot(Boolean.TRUE);
        Delivery delivery = deliveryHelper.create(deliveryCreateDTO);
        return delivery;
    }

    private void bindToDelivery(String masterOid, String deliveryOid) {
        commonAbilityService.createOutRelation(createRelation(masterOid,deliveryOid));
    }
    private List<Reference> createRelation(String masterOid, String deliveryOid) {
        BaseEntity result = jwiCommonService.findByOid(Delivery.TYPE, deliveryOid,Delivery.class);
        if (ObjectUtils.isNotEmpty(result)) {
            Reference reference = new Reference();
            reference.setFromOid(result.getOid());
            reference.setFromType(result.getType());
            reference.setToOid(masterOid);
            reference.setToType(Document.TYPE);
            return Lists.newArrayList(reference);
        } else {
            return Lists.newArrayList();
        }
    }

    private String getDeliveryOidFromJson(JSONObject jsonObject) {
        if(jsonObject == null) {
            return StringUtils.EMPTY;
        } else {
            return jsonObject.getString("deliveryOid");
        }
    }

    private void initContext(String account){
        UserDTO byAccount = this.userHelper.findByAccount(account);
        if (byAccount == null) {
            throw new JWIException("该用户账号" + account + "不存在");
        }
        String tenantOid = commonService.getAloneTenantOid();
        byAccount.setTenantOid(tenantOid);
        byAccount.setOid(byAccount.getOid());
        byAccount.setAccount(account);
        this.authHelper.fillUserAuthInfo(byAccount);
        String accessToken = SessionHelper.getAccessToken();
        SessionHelper.setAccessToken(accessToken);
        SessionHelper.addCurrentUser(byAccount);
    }

    public List<DocumentIteration> findByNameAndCatalog(String catalogType, String catalogOid, String name) {
        SubFilter subFilter = new SubFilter();
        subFilter.setFromType(catalogType);
        subFilter.setFromOid(catalogOid);
        subFilter.setType(Contain.TYPE);
        subFilter.setToType(Document.TYPE);
        subFilter.setFilter(Condition.where("name").eq(name));
        return documentService.findMIByFrom(subFilter);
    }

    private List<String> getUserDingDingId(String[] split) {
        List<String> dingUserList = new ArrayList<>();
        String accessTokenDing = null;
        try {
            accessTokenDing = getTokenNew();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            throw new RuntimeException(e);
        }
        for (String userAccount : split) {
            User teamUser = jwiCommonService.dynamicQueryOne(User.TYPE, Condition.where("account").eq(userAccount), User.class);

            if (teamUser == null) {
                List<User> userByAccount = iamRemote.findUserByAccount(Lists.newArrayList(userAccount));
                if (userByAccount != null && !userByAccount.isEmpty()) {
                    teamUser = userByAccount.stream().findAny().orElse(null);
                } else {
                    logger.info("未获取到PDM用户信息，账户名称: " + userAccount);
                    continue;
                }
            }

            String phone = teamUser.getPhone();
            Assert.notBlank(phone, "用户未配置电话号码，无法启动流程 账户名称:" + teamUser.getName() + " 账户:" + teamUser.getAccount());

            if (StringUtil.isNotBlank(phone) && phone.startsWith("86"))
                phone = phone.replaceFirst("86", "");

            String userDingId = getUserIdByPhone(phone, accessTokenDing);
            //获取不到钉钉
            if (null != userDingId && StrUtil.isNotEmpty(userDingId)) {
                dingUserList.add(userDingId);
            } else {
//                Assert.notBlank(userDingId, "未获取到钉钉用户信息，无法启动流程 账户名称:" + teamUser.getName() + " 账户:" + teamUser.getAccount() + " 电话:" + teamUser.getPhone());
                logger.info("未获取到钉钉用户信息， 账户名称:" + teamUser.getName() + " 账户:" + teamUser.getAccount() + " 电话:" + teamUser.getPhone());
            }
        }
        return dingUserList;
    }

    public String getTokenNew() throws Exception {
        logger.info("getTokenNew dingTalkAppKeyNew:{} , dingTalkAppSecretNew:{}", dingTalkAppKeyNew, dingTalkAppSecretNew);
        com.aliyun.teaopenapi.models.Config config = new com.aliyun.teaopenapi.models.Config();
        config.protocol = "https";
        config.regionId = "central";
        com.aliyun.dingtalkoauth2_1_0.Client client = new com.aliyun.dingtalkoauth2_1_0.Client(config);
        com.aliyun.dingtalkoauth2_1_0.models.GetAccessTokenRequest getAccessTokenRequest = new com.aliyun.dingtalkoauth2_1_0.models.GetAccessTokenRequest()
                .setAppKey(dingTalkAppKeyNew).setAppSecret(dingTalkAppSecretNew);
        GetAccessTokenResponse response = client.getAccessToken(getAccessTokenRequest);
        return response.getBody().getAccessToken();
    }

    public String getUserIdByPhone(String phoneNumber, String token) {
        String responseStr = null;
        try {
            JSONObject body = new JSONObject();
            body.put("mobile", phoneNumber);
            body.put("support_exclusive_account_search", "true");
            String url = "https://oapi.dingtalk.com/topapi/v2/user/getbymobile?access_token=" + token;
            responseStr = HttpClientUtil.postJson(url, null, new HashMap<>(), body.toJSONString(), false);
            JSONObject response = JSONObject.parseObject(responseStr);
            String userid;
            List<String> exclusiveAccountList =
                    response.getJSONObject("result").getJSONArray("exclusive_account_userid_list").toJavaList(String.class);
            if(CollectionUtils.isNotEmpty(exclusiveAccountList)) {
                userid = exclusiveAccountList.stream().findAny().get();
            } else {
                userid = response.getJSONObject("result").getString("userid");
            }
            logger.info("DingTalkCall.getUserByPhone:" + phoneNumber + "->" + userid);
            return userid;
        } catch (Exception e) {
            logger.error("DingTalkCall.phoneNumber= " + phoneNumber + " ,response:" + responseStr);
        }
        return "";
    }
}
