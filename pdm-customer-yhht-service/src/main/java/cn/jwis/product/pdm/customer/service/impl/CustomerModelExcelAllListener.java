package cn.jwis.product.pdm.customer.service.impl;

import cn.jwis.platform.plm.modelexcel.dto.ModelExcelAllCreateDTO;
import cn.jwis.platform.plm.modelexcel.dto.ModelExcelAllPropDTO;
import cn.jwis.platform.plm.modelexcel.listener.ModelExcelAllListener;
import cn.jwis.platform.plm.modelexcel.service.ModelDataSaveService;
import cn.jwis.platform.plm.modelexcel.service.ModelExcelActService;
import com.alibaba.excel.context.AnalysisContext;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR> yefei
 * @Date ： 2024/3/11
 * @Description :
 */

@Component
public class CustomerModelExcelAllListener extends ModelExcelAllListener {

    private static ModelExcelActService modelExcelActService;

    private File dir;

    private List<ModelExcelAllPropDTO> datalist = new ArrayList<>();

    private Map<String, File> fileMap;

    private String bucket;

    private ModelDataSaveService service;

    private String type;

    boolean isHis;

    public CustomerModelExcelAllListener() {
        super();
    }

    public CustomerModelExcelAllListener(Map<String, File> fileMap, File dir, ModelDataSaveService service,
                                         String bucket) {
        super(fileMap,dir,service,bucket);
        this.fileMap = fileMap;
        this.dir = dir;
        this.service = service;
        this.bucket = bucket;
        this.isHis = false;
    }

    public CustomerModelExcelAllListener(Map<String, File> fileMap, File dir, ModelDataSaveService service,
                                         String bucket,boolean isHis) {
        super(fileMap,dir,service,bucket);
        this.fileMap = fileMap;
        this.dir = dir;
        this.service = service;
        this.bucket = bucket;
        this.isHis = isHis;
    }

    public void invoke(ModelExcelAllPropDTO baseDTO, AnalysisContext analysisContext) {
        analysisContext.readWorkbookHolder().setIgnoreEmptyRow(false);
        this.datalist.add(baseDTO);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        List<ModelExcelAllCreateDTO> res = modelExcelActService.getCreateListAllData(this.datalist, this.fileMap,
                this.bucket, this.isHis);
        res = res.stream().filter(Objects::nonNull).peek(item -> {
            if (CollectionUtils.isEmpty(item.getSecondaryFile()) && CollectionUtils.isNotEmpty(item.getPrimaryFile())) {
                item.setSecondaryFile(item.getPrimaryFile());
            }
        }).collect(Collectors.toList());
        this.service.batchSave(res);
    }

    @Resource
    public void setModelExcelActService(ModelExcelActService modelExcelActService) {
        CustomerModelExcelAllListener.modelExcelActService = modelExcelActService;
    }
}
