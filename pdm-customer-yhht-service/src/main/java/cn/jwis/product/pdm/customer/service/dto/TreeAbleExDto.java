package cn.jwis.product.pdm.customer.service.dto;

import cn.jwis.platform.iam.structure.TreeAbleEx;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.Collection;

/**
 * <AUTHOR> yefei
 * @Date ： 2024/7/22
 * @Description :
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class TreeAbleExDto implements TreeAbleEx{

    private String account;
    private String email;
    private String name;
    private String number;
    private String oid;
    private String phone;
    private Collection<String> positions;
    private String type;
    private String displayName;

    @Override
    public String getParentNodeId() {
        return null;
    }

    public String getCode() {
        return null;
    }

    public String getUniqueId() {
        return null;
    }
    public String getCurrentNodeId() {
        return null;
    }
}
