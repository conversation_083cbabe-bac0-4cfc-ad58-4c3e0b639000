package cn.jwis.product.pdm.customer.service.impl;

import cn.jwis.platform.plm.foundation.common.service.CommonAbilityHelper;
import cn.jwis.platform.plm.permission.filter.service.factory.instance.InstanceFilter;
import cn.jwis.platform.plm.permission.permission.dto.FilterParameterDTO;
import cn.jwis.platform.plm.permission.permission.entity.BaseCatalogue;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;


@Component
@Slf4j
@Primary
public class CustomerBomInstanceFilter extends InstanceFilter {

    @Autowired
    private CommonAbilityHelper commonAbilityHelper;

    @Override
    public Boolean doFilter(BaseCatalogue baseCatalogue, FilterParameterDTO dto) {
        Boolean flag = super.doFilter(baseCatalogue, dto);
        if (dto.getState().equalsIgnoreCase("Released") || dto.getState().equalsIgnoreCase("UnderReview")) {
            flag = false;
        }
        return flag;
    }
}
