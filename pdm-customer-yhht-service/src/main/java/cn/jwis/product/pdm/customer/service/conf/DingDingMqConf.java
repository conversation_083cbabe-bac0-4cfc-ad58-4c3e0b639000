package cn.jwis.product.pdm.customer.service.conf;

import org.springframework.amqp.core.AcknowledgeMode;
import org.springframework.amqp.rabbit.config.SimpleRabbitListenerContainerFactory;
import org.springframework.amqp.rabbit.connection.CachingConnectionFactory;
import org.springframework.amqp.rabbit.connection.Connection;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class DingDingMqConf {

    @Value("${spring.rabbitmq.host:************}")
    private String ddMqHost;
    @Value("${spring.rabbitmq.port:5672}")
    private int ddMqPort;
    @Value("${spring.rabbitmq.username:admin}")
    private String ddMqUsername;
    @Value("${spring.rabbitmq.password:admin}")
    private String ddMqPassword;
    @Value("${spring.rabbitmq.virtual-host:digital-pdm-test}")
    private String ddMqVirtualHost;

    @Bean(value = "rabbitListenerContainerFactory")
    public SimpleRabbitListenerContainerFactory rabbitListenerContainerFactory (@Qualifier("dingdingConnectionFactory") ConnectionFactory dingdingConnectionFactory) {
        SimpleRabbitListenerContainerFactory factory = new SimpleRabbitListenerContainerFactory();
        factory.setConnectionFactory(dingdingConnectionFactory);
        factory.setAcknowledgeMode(AcknowledgeMode.MANUAL);
        return factory;
    }

    @Bean(value = "dingdingConnectionFactory")
    public ConnectionFactory dingdingConnectionFactory() {
        CachingConnectionFactory connectionFactory = new CachingConnectionFactory();
        connectionFactory.setHost(ddMqHost);
        connectionFactory.setPort(ddMqPort);
        connectionFactory.setUsername(ddMqUsername);
        connectionFactory.setPassword(ddMqPassword);
        connectionFactory.setVirtualHost(ddMqVirtualHost);
        Connection c = connectionFactory.createConnection();
        return connectionFactory;
    }
}
