package cn.jwis.product.pdm.customer.service.impl;

import cn.hutool.json.JSONUtil;
import cn.jwis.framework.base.domain.entity.BaseEntity;
import cn.jwis.framework.base.util.Assert;
import cn.jwis.framework.base.util.CollectionUtil;
import cn.jwis.framework.base.util.StringUtil;
import cn.jwis.platform.plm.container.entity.FolderTreeNode;
import cn.jwis.platform.plm.container.service.FolderService;
import cn.jwis.platform.plm.file.entity.FileMetadata;
import cn.jwis.platform.plm.foundation.classification.responce.ClsPropertyWithRel;
import cn.jwis.platform.plm.foundation.classification.service.ClassificationPropertyHelper;
import cn.jwis.platform.plm.foundation.common.dto.RelatedFuzzyDTO;
import cn.jwis.platform.plm.foundation.common.response.InstanceEntity;
import cn.jwis.platform.plm.foundation.common.service.CommonAbilityService;
import cn.jwis.platform.plm.foundation.common.service.InstanceHelper;
import cn.jwis.platform.plm.foundation.model.response.ModelPropertyWithRel;
import cn.jwis.platform.plm.foundation.model.service.ModelPropertyHelper;
import cn.jwis.platform.plm.foundation.relationship.enums.RelationConstraint;
import cn.jwis.platform.plm.sysconfig.collectionrule.service.CollectionRuleHelper;
import cn.jwis.platform.plm.sysconfig.entity.CollectionRule;
import cn.jwis.product.pdm.cad.accessdb.smbfile.SmbFileUtils;
import cn.jwis.product.pdm.cad.dto.altiumdesigner.ClsMappingDTO;
import cn.jwis.product.pdm.cad.dto.altiumdesigner.ComponentSystemProp;
import cn.jwis.product.pdm.cad.ecad.entity.ECADIteration;
import cn.jwis.product.pdm.cad.ecad.service.impl.AltiumDesignerHelperImpl;
import cn.jwis.product.pdm.cad.enums.EdaIntegrationTypeEnum;
import cn.jwis.product.pdm.customer.entity.assistant.SafeWrapAssist;
import cn.jwis.product.pdm.customer.service.interf.CustomerEdaIntegrationHelper;
import cn.jwis.product.pdm.partbom.part.entity.PartIteration;
import com.alibaba.fastjson.JSONObject;
import com.healthmarketscience.jackcess.Database;
import com.healthmarketscience.jackcess.Table;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.File;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Primary
@Transactional
public class CustomerAltiumDesignerHelperImpl extends AltiumDesignerHelperImpl implements SafeWrapAssist, CustomerEdaIntegrationHelper {


    private static final Logger log = LoggerFactory.getLogger(CustomerAltiumDesignerHelperImpl.class);
    @Resource
    private CommonAbilityService commonAbilityService;
    @Resource
    private ModelPropertyHelper modelPropertyHelper;
    @Resource
    private ClassificationPropertyHelper classificationPropertyHelper;
    @Resource
    private FolderService folderService;
    @Autowired
    private InstanceHelper instanceHelper;
    @Autowired
    private CollectionRuleHelper collectionRuleHelper;


    private Map<String, String> RM03_ruleMap = new HashMap<String, String>() {{
        put("图符维护", "MM");
    }};

    @Override
//    public FileMetadata syncLibraryForMentor(String containerOid) {
    public FileMetadata syncLibraryForMentor(String containerOid,List<String> selectList) {
        log.info("syncLibraryForMentor入参：{}", JSONUtil.toJsonStr(containerOid));

        List<CollectionRule> materialRuleList = collectionRuleHelper.findByAppliedType("Part_Related_Object", "JWIRawMaterial");
        List<CollectionRule> RM03_ruleList = filterCollect(materialRuleList, it -> RM03_ruleMap.get(it.getRelationDisplayName()) != null);
        List<PartIteration> parts = null;
        if (null != selectList && selectList.size() > 0) {
            log.info("当前同步的元器件selectList:{}", JSONUtil.toJsonStr(selectList));
            parts  = (List)this.commonAbilityService.findDetailEntity(selectList, "PartIteration");
        }else {
            parts = (List) this.commonAbilityService.findDetailEntityByContainerOid(containerOid, "PartIteration");
        }

        parts = parts.stream().map((item) -> {
            return (PartIteration) item;
        }).filter((item) -> {
            return Objects.equals(item.getModelDefinition(), "JWIRawMaterial");
        }).filter((item) -> {
            return "Released".equals(item.getLifecycleStatus());
        }).filter((item) -> {

            CollectionRule rule = RM03_ruleList.get(0);
            RelatedFuzzyDTO relatedFuzzyDTO = new RelatedFuzzyDTO();
            relatedFuzzyDTO.setRelationConstraint("MM".equals(rule.getRelationConstraint()) ? RelationConstraint.MM : "II".equals(rule.getRelationConstraint()) ? RelationConstraint.II : RelationConstraint.MI);
            BeanUtils.copyProperties(rule, relatedFuzzyDTO);
            relatedFuzzyDTO.setMainObjectOid(item.getOid());
            List<InstanceEntity> relateObjList = instanceHelper.fuzzyRelated(relatedFuzzyDTO);
            return null != relateObjList && relateObjList.size() > 0;
        }).collect(Collectors.toList());

        Assert.notEmpty(parts, "该容器中没有可用的元器件！");
        log.info("当前容器过滤后有效结果数量: " + parts.size());
        Map<String, List<PartIteration>> mapPart = (Map) parts.stream().filter((item) -> {
            return StringUtil.isNotBlank(item.getModelDefinition());
        }).collect(Collectors.groupingBy(BaseEntity::getModelDefinition));

        Map<String, List<ECADIteration>> partOidEcadMap = this.syncSchematicPcb(parts);

        List<FolderTreeNode> folders = this.folderService.searchFolders("Container", containerOid, (String) null);
        List<FolderTreeNode> secFolder = (List) folders.stream().filter((item) -> {
            return CollectionUtil.isNotEmpty(item.getChildren());
        }).flatMap((item) -> {
            return item.getChildren().stream();
        }).collect(Collectors.toList());
        List<ClsMappingDTO> clsMapping = this.getClsGroup(secFolder);

        File file = this.getDbFile(EdaIntegrationTypeEnum.AltiummDesigner);
        Database dbDatabase = this.getDbDatabase(file);
        mapPart.forEach((modelDefine, part) -> {
            List<ModelPropertyWithRel> extensionProps = (List) this.modelPropertyHelper.findAllByModel(modelDefine).stream().filter((item) -> {
                return !item.isSystemDefault();
            }).collect(Collectors.toList());
            Iterator var7 = clsMapping.iterator();

            label31:
            while (var7.hasNext()) {
                ClsMappingDTO mappingDTO = (ClsMappingDTO) var7.next();
                List<ClsPropertyWithRel> clsProps = mappingDTO.getProps();
                String clsName = mappingDTO.getClsName();
                Table clsTable = null;
                Iterator var12 = part.iterator();

                while (true) {
                    PartIteration v;
                    do {
                        if (!var12.hasNext()) {
                            continue label31;
                        }

                        v = (PartIteration) var12.next();
                    } while (!Objects.equals(mappingDTO.getClsOid(), v.getClsOid()) && !mappingDTO.getChildOids().contains(v.getClsOid()));

                    if (clsTable == null) {
                        clsTable = this.createTable(clsName, dbDatabase, modelDefine, ComponentSystemProp.class, extensionProps, clsProps);
                    }

                    ComponentSystemProp sysData = new ComponentSystemProp();
                    sysData.setPartNumber(v.getNumber());
                    sysData.setPartName(v.getName());
                    sysData.setPartType(v.getClsDisplayName());
                    this.findAndFillEcadInfo2Part(partOidEcadMap, v);
                    this.addColumnData(clsTable, ComponentSystemProp.class.getDeclaredFields(), sysData, v, extensionProps, clsProps);
                }
            }

        });

        try {
            Thread.sleep(1000L);
        } catch (InterruptedException var11) {
            InterruptedException e = var11;
            throw new RuntimeException(e);
        }

        SmbFileUtils.uploadDbFile(file);
        file.delete();
        return null;
    }


    private List<ClsMappingDTO> getClsGroup(List<FolderTreeNode> folders) {
        List<ClsMappingDTO> mappingDTOS = new ArrayList();
        Iterator var3 = folders.iterator();

        while (var3.hasNext()) {
            FolderTreeNode folder = (FolderTreeNode) var3.next();
            ClsMappingDTO mappingDTO = new ClsMappingDTO();
            mappingDTO.setClsOid(folder.getClsOid());
            mappingDTO.setClsName(folder.getClsDisplayName());
            mappingDTO.setChildOids(new ArrayList());
            mappingDTO.setProps(new ArrayList());
            this.getClsChildrenOid(folder.getChildren(), mappingDTO);
            List<String> allClsOids = new ArrayList(mappingDTO.getChildOids());
            allClsOids.add(mappingDTO.getClsOid());
            Map<String, List<ClsPropertyWithRel>> props = this.classificationPropertyHelper.fuzzyByClsOidsRel(allClsOids);
            Iterator var8 = allClsOids.iterator();

            while (var8.hasNext()) {
                String allClsOid = (String) var8.next();
                if (props.containsKey(allClsOid)) {
                    mappingDTO.getProps().addAll((Collection) props.get(allClsOid));
                }
            }

            List<ClsPropertyWithRel> distProps = (List) mappingDTO.getProps().stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> {
                return new TreeSet(Comparator.comparing(BaseEntity::getOid));
            }), ArrayList::new));
            mappingDTO.setProps(distProps);
            mappingDTOS.add(mappingDTO);
        }

        return mappingDTOS;
    }

    private void getClsChildrenOid(List<FolderTreeNode> children, ClsMappingDTO mapping) {
        if (CollectionUtil.isNotEmpty(children)) {
            Iterator var3 = children.iterator();

            while (var3.hasNext()) {
                FolderTreeNode child = (FolderTreeNode) var3.next();
                mapping.getChildOids().add(child.getClsOid());
                if (CollectionUtils.isNotEmpty(child.getChildren())) {
                    this.getClsChildrenOid(child.getChildren(), mapping);
                }
            }
        }

    }

    private void findAndFillEcadInfo2Part(Map<String, List<ECADIteration>> partOidEcadMap, PartIteration partIteration) {
        List<ECADIteration> ecadIterationList = (List) partOidEcadMap.get(partIteration.getMasterOid());
        if (CollectionUtils.isNotEmpty(ecadIterationList)) {
            JSONObject extension = partIteration.getExtensionContent();
            if (extension == null) {
                extension = new JSONObject();
                partIteration.setExtensionContent(extension);
            }

            Iterator var5 = ecadIterationList.iterator();

            while (var5.hasNext()) {
                ECADIteration ecadIteration = (ECADIteration) var5.next();
                if ("Symbol".equalsIgnoreCase(ecadIteration.getModelDefinition())) {
                    extension.put("LibraryRef", ecadIteration.getName());
                    extension.put("LibraryPath", ecadIteration.getExtensionContent() == null ? "" : this.changePath(ecadIteration.getExtensionContent().getString("sharedFileDir")));
                } else if ("Encapsulation".equalsIgnoreCase(ecadIteration.getModelDefinition())) {
                    extension.put("FootprintRef", ecadIteration.getName());
                    extension.put("FootprintPath", ecadIteration.getExtensionContent() == null ? "" : this.changePath(ecadIteration.getExtensionContent().getString("sharedFileDir")));
                }
            }
        }

    }

    private String changePath(String path) {
        return "\\\\" + path.replace("/", "\\");
    }

}