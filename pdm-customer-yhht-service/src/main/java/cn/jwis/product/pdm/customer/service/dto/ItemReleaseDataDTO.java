package cn.jwis.product.pdm.customer.service.dto;

import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2023/8/15 17:06
 * @Email <EMAIL>
 */
@Data
@EqualsAndHashCode
public class ItemReleaseDataDTO {
    @ApiModelProperty("businessOid")
    private @NotBlank String businessOid;
    @ApiModelProperty("oid")
    private @NotBlank String oid;
    @ApiModelProperty("type")
    private @NotBlank String type;
    @ApiModelProperty("modelDefinition")
    private @NotBlank String modelDefinition;
    @ApiModelProperty("名称")
    private @NotBlank String name;
    @ApiModelProperty("编码")
    private String number;
    @ApiModelProperty("默认单位")
    private @NotNull String defaultUnit;
    @ApiModelProperty("来源")
    private String source;
    @ApiModelProperty("备注：单号")
    private String processOrder;
    @ApiModelProperty("发布时间")
    private long updateTime;
    @ApiModelProperty("描述")
    private String description;
    @ApiModelProperty("大版本")
    private String version;
    @ApiModelProperty("状态,create1,update2,disable3")
    private int lifecycleStatus;
    @ApiModelProperty("是否下发慧智造：1=下发，0=不下发")
    private int sendHZZ;
    @ApiModelProperty("是否慧智造生效：1=生效，0=不生效")
    private int activeHZZ;
    @ApiModelProperty("申请人")
    private String updatorAccount;
    @ApiModelProperty("申请人名称及部门")
    private String updatorNameAndDepartment;
    @ApiModelProperty("扩展属性")
    private JSONObject extensionContent;
    @ApiModelProperty("分类属性")
    private JSONObject clsProperty;
    @ApiModelProperty("参考文档")
    private List<BaseEntityDTO> referenceDoc;
    @ApiModelProperty("描述文档")
    private List<BaseEntityDTO> describeDoc;
    @ApiModelProperty("替代件")
    private List<BaseEntityDTO> substitute;
    @ApiModelProperty("关联的非筛选件")
    private List<BaseEntityDTO> screen;
    @ApiModelProperty("关联的未成型件")
    private List<BaseEntityDTO> forming;
    @ApiModelProperty("全局替代件")
    private List<GlobalSubstituteRelation> globalRel;
}
