package cn.jwis.product.pdm.customer.service.util;

import cn.hutool.core.util.IdUtil;
import com.aspose.words.*;
import com.aspose.words.Comment;
import com.aspose.words.Document;
import com.aspose.words.NodeCollection;
import com.aspose.words.NodeType;
import com.aspose.words.PdfSaveOptions;
import com.aspose.words.SaveFormat;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.io.FileOutputStream;
import java.lang.reflect.Constructor;
import java.lang.reflect.Field;
import java.util.Date;

public class Doc2PdfUtil {

    private static final Logger logger = LoggerFactory.getLogger(Doc2PdfUtil.class);

    public static void main(String[] args) {
    }

    public static String doc2pdf(String wordPath) {
        logger.info("doc2pdf start wordPath---->>>>: {}", wordPath);
        String pdfPath = null;
        try (FileOutputStream os = new FileOutputStream(pdfPath = wordPath.substring(0, wordPath.lastIndexOf(".")) + ".pdf")){
            registerWord_v_22_5();
            Document doc = new Document(wordPath);

            // 强制更新文档的布局
            doc.updatePageLayout();

            //转换前清理word文档内批注信息，防止转换后样式错误
            NodeCollection<Comment> comments = doc.getChildNodes(NodeType.COMMENT, true);
            comments.clear();

            // 设置PDF保存选项
            PdfSaveOptions saveOptions = new PdfSaveOptions();
            saveOptions.setUpdateFields(false);
            // 启用大纲书签
            saveOptions.getOutlineOptions().setDefaultBookmarksOutlineLevel(0); // 1 表示第一层书签
            saveOptions.getOutlineOptions().setExpandedOutlineLevels(1); // 展开到一级书签
            saveOptions.getOutlineOptions().setHeadingsOutlineLevels(3); // 设置标题层级，通常设置为3级

            // 保存文档为PDF并包含书签
            doc.save(os, saveOptions);
//            doc.save(os, SaveFormat.PDF);
        } catch (Exception e) {
            logger.error("Word 转 Pdf 失败..." + e.getMessage(), e);
        }
        logger.info("doc2pdf end pdfPath--->>>: {}", pdfPath);

        return pdfPath;
    }

    public static void registerWord_v_22_5() throws Exception {
        Class<?> zzjXClass = Class.forName("com.aspose.words.zzjX");
        Constructor<?> constructor = zzjXClass.getDeclaredConstructors()[0];
        constructor.setAccessible(true);
        Object zzjXInstance = constructor.newInstance();

        // zzZ7O
        Field zzZ7O = zzjXClass.getDeclaredField("zzZ7O");
        zzZ7O.setAccessible(true);
        zzZ7O.set(zzjXInstance, new Date(Long.MAX_VALUE));

        // zzBf
        Field zzZfB = zzjXClass.getDeclaredField("zzZfB");
        zzZfB.setAccessible(true);
        Class<?> zzYP3Class = Class.forName("com.aspose.words.zzYP3");
        Field zzBfField = zzYP3Class.getDeclaredField("zzBf");
        zzBfField.setAccessible(true);
        zzZfB.set(zzjXInstance, zzBfField.get(null));

        // zzZjA
        Field zzZjA = zzjXClass.getDeclaredField("zzZjA");
        zzZjA.setAccessible(true);
        zzZjA.set(null, zzjXInstance);

        Class<?> zzCnClass = Class.forName("com.aspose.words.zzCn");
        Field zzZyx = zzCnClass.getDeclaredField("zzZyx");
        zzZyx.setAccessible(true);
        zzZyx.set(null, 128);
        Field zzZ8w = zzCnClass.getDeclaredField("zzZ8w");
        zzZ8w.setAccessible(true);
        zzZ8w.set(null, false);
    }

}