package cn.jwis.product.pdm.customer.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import cn.jwis.framework.base.dto.UserDTO;
import cn.jwis.framework.base.web.session.SessionHelper;
import cn.jwis.framework.database.core.query.dynamic.Condition;
import cn.jwis.platform.plm.container.entity.Container;
import cn.jwis.platform.plm.container.entity.FolderTreeNode;
import cn.jwis.platform.plm.container.entity.Team;
import cn.jwis.platform.plm.container.entity.response.TeamRoleWithUser;
import cn.jwis.platform.plm.container.service.ContainerHelper;
import cn.jwis.platform.plm.container.service.TeamHelper;
import cn.jwis.platform.plm.dataengine.common.JWICommonService;
import cn.jwis.platform.plm.foundation.common.response.InstanceEntity;
import cn.jwis.platform.plm.foundation.common.service.InstanceService;
import cn.jwis.product.pdm.customer.repo.PdmFolderTwoRepo;
import cn.jwis.product.pdm.customer.service.interf.PDMFolderServiceI;
import com.alibaba.fastjson.JSON;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

@Component
@Slf4j
public class DingTalkConnectorService {
    
    /**
     * 请求参数类
     */
    public static class SearchRequest {
        private String containerName;
        private String foldPath;

        public String getContainerName() {
            return containerName;
        }

        public void setContainerName(String containerName) {
            this.containerName = containerName;
        }
        
        public String getFoldPath() {
            return foldPath;
        }
        
        public void setFoldPath(String foldPath) {
            this.foldPath = foldPath;
        }
    }
    
    /**
     * 角色用户信息结果类
     */
    @Data
    public static class RoleUserResult {
        // 调度角色钉钉用户ID
        private String dispatchDingUserIds;
        // 调度角色用户真实姓名
        private String dispatchRealNames = "";
        // 产品经理角色钉钉用户ID
        private String managerDingUserIds;
        // 产品经理角色用户真实姓名
        private String managerRealNames = "";
    }
    
    // 角色名称常量
    private static final String DISPATCH_ROLE = "cn_jwis_dispatch";
    private static final String MANAGER_ROLE = "ProductManager";
    
    @Autowired
    private JWICommonService jwiCommonService;

    @Resource
    private PDMFolderServiceI pdmFolderServiceI;

    @Autowired
    private ContainerHelper containerHelper;
    
    @Autowired
    private TeamHelper teamHelper;
    
    @Autowired
    private DingTalkServiceImpl dingTalkService;
    
    @Autowired
    private InstanceService instanceService;
    
    @Autowired
    private PDMFolderServiceI pdmFolderService;
    
    @Resource
    private PdmFolderTwoRepo pdmFolderTwoRepo;

    @Value("${tenantOid:6deb5dde-aa39-46fb-962d-a5951f8fab5e}")
    private String tenantOid;
    
    /**
     * 根据容器名称和角色名称获取对应的钉钉用户ID
     * @param request 包含容器名称和角色名称的请求对象
     * @return 查询结果，包含结果数据和消息
     */
    public Map<String, Object> getTeamRoleDingUsers(SearchRequest request) {
        return getTeamRoleDingUsers(request.getContainerName(), DISPATCH_ROLE);
    }

    /**
     * 根据容器名称和角色名称获取对应的钉钉用户ID
     * @param containerName 容器名称
     * @param roleName 角色名称，如果为空则默认使用"cn_jwis_dispatch"
     * @return 查询结果，包含结果数据和消息
     */
    public Map<String, Object> getTeamRoleDingUsers(String containerName, String roleName) {
        // 初始化用户信息
        initUser();
        
        Map<String, Object> response = new HashMap<>();
        Map<String, List<String>> result = null;
        String message = null;
        
        try {
            if (StringUtils.isBlank(containerName)) {
                result = Collections.singletonMap(roleName != null ? roleName : "unknown", Collections.emptyList());
                message = "容器名称不能为空";
                response.put("result", result);
                response.put("message", message);
                return response;
            }
            
            if (StringUtils.isBlank(roleName)) {
                roleName = DISPATCH_ROLE; // 默认角色名
            }
            
            // 1. 根据产品名称查询对应的Container产品信息（最新版本）
            List<Container> containerDetails = jwiCommonService.dynamicQuery(
                    Container.TYPE,
                    Condition.where("name").eq(containerName)
                            .and(Condition.where("markForDelete").eq(false)),
                    Container.class);
            
            if (containerDetails == null || containerDetails.isEmpty()) {
                log.error("未找到产品容器: {}", containerName);
                result = Collections.singletonMap(roleName, Collections.emptyList());
                message = "未找到产品容器: " + containerName;
                response.put("result", result);
                response.put("message", message);
                return response;
            }
            
            // 多个取一个最新的
            String containerOid = containerDetails.get(0).getOid();
            log.info("根据产品名称[{}]查询到产品容器ID: {}", containerName, containerOid);
            
            // 尝试第一条路径：通过文件夹树查找
            result = findDispatchUsersByFolderPath(containerOid, roleName);
            
            if (isValidResult(result, roleName)) {
                response.put("result", result);
                return response;
            } else {
                message = "通过文件夹树未找到角色[" + roleName + "]的用户";
                log.info(message);
            }
            
            // 第一条路径失败，尝试第二条路径：直接通过容器的Team查找
            log.info("通过文件夹树查找失败或未找到用户，尝试通过容器Team直接查找");
            result = findDispatchUsersByInstanceTeam(containerOid, roleName);
            
            if (isValidResult(result, roleName)) {
                response.put("result", result);
                return response;
            } else {
                message = message + "，通过容器Team也未找到角色[" + roleName + "]的用户";
                log.info(message);
            }
            
            // 两条路径都失败，返回空结果
            Map<String, List<String>> emptyResult = Collections.singletonMap(roleName, Collections.emptyList());
            response.put("result", emptyResult);
            response.put("message", message);
            return response;
            
        } catch (Exception e) {
            log.error("查找钉钉角色用户失败", e);
            // 即使发生异常，也返回成功状态和空数组
            Map<String, List<String>> emptyResult = Collections.singletonMap(
                StringUtils.isNotBlank(roleName) ? roleName : DISPATCH_ROLE, 
                Collections.emptyList()
            );
            response.put("result", emptyResult);
            response.put("message", "查找钉钉角色用户失败: " + e.getMessage());
            return response;
        }
    }
    
    /**
     * 判断结果是否有效（不为null且包含非空的用户列表）
     * @param result 结果映射
     * @param roleName 角色名称
     * @return 是否有效
     */
    private boolean isValidResult(Map<String, List<String>> result, String roleName) {
        return result != null && 
               result.containsKey(roleName) && 
               !result.get(roleName).isEmpty();
    }
    
    /**
     * 第一条路径：通过文件夹树查找调度角色用户
     * @param containerOid 容器OID
     * @param roleName 角色名称
     * @return 调度角色用户ID映射，如果失败则返回包含空列表的映射
     */
    private Map<String, List<String>> findDispatchUsersByFolderPath(String containerOid, String roleName) {
        try {
            // 2. 使用pdmFolderServiceI查询文件夹树
            SessionHelper.getCurrentUser().setSystemAdmin(true);
            List<FolderTreeNode> folderTreeNodes = pdmFolderServiceI.searchFoldersWithPermisson(
                    "ProductContainer",
                    containerOid, 
                    "");
            
            if (CollUtil.isEmpty(folderTreeNodes)) {
                log.error("未找到容器[{}]下的文件夹", containerOid);
                return Collections.singletonMap(roleName, Collections.emptyList());
            }
            
            // 3. 获取最顶层的FolderId
            String folderOid = folderTreeNodes.get(0).getOid();
            log.info("找到顶层文件夹ID: {}", folderOid);
            
            // 4. 根据FolderId获取Team
            Team team = pdmFolderServiceI.getTeamRole(folderOid, "");
            if (team == null) {
                log.error("未找到文件夹[{}]对应的Team", folderOid);
                return Collections.singletonMap(roleName, Collections.emptyList());
            }
            log.info("获取到文件夹[{}]对应的Team: {}", folderOid, team.getOid());
            
            return findDispatchUsersByTeam(team.getOid(), roleName);
            
        } catch (Exception e) {
            log.error("通过文件夹树查找角色[{}]用户失败", roleName, e);
            return Collections.singletonMap(roleName, Collections.emptyList());
        }
    }
    
    /**
     * 第二条路径：通过容器的Team直接查找调度角色用户
     * @param containerOid 容器OID
     * @param roleName 角色名称
     * @return 调度角色用户ID映射，如果失败则返回包含空列表的映射
     */
    private Map<String, List<String>> findDispatchUsersByInstanceTeam(String containerOid, String roleName) {
        try {
            // 1. 使用instanceService.findInstance获取容器信息
            InstanceEntity instance = instanceService.findInstance(containerOid, Container.TYPE);
            log.info("第二条路径产品instance信息:{}", JSONUtil.toJsonStr(instance));
            // 2. 获取容器的Team
            Team team = pdmFolderTwoRepo.getInstanceTeam(instance);
            log.info("第二条路径team: {}", JSONUtil.toJsonStr(team));
            if (team == null) {
                log.error("容器[{}]没有关联的Team", containerOid);
                return Collections.singletonMap(roleName, Collections.emptyList());
            }
            log.info("获取到容器[{}]关联的Team: {}", containerOid, team.getOid());
            
            return findDispatchUsersByTeam(team.getOid(), roleName);
            
        } catch (Exception e) {
            log.error("通过容器Team查找角色[{}]用户失败", roleName, e);
            return Collections.singletonMap(roleName, Collections.emptyList());
        }
    }
    
    /**
     * 根据TeamOid查找调度角色用户
     * @param teamOid 团队OID
     * @param roleName 角色名称
     * @return 调度角色用户ID映射，如果失败则返回包含空列表的映射
     */
    private Map<String, List<String>> findDispatchUsersByTeam(String teamOid, String roleName) {
        try {
            // 1. 获取TeamRole列表
            List<TeamRoleWithUser> teamRoleWithUsers = teamHelper.fuzzyContent(teamOid, "");
            if (CollUtil.isEmpty(teamRoleWithUsers)) {
                log.error("未找到团队[{}]下的角色", teamOid);
                return Collections.singletonMap(roleName, Collections.emptyList());
            }
            log.info("获取到团队[{}]下的角色数量: {}", teamOid, teamRoleWithUsers.size());
            
            // 2. 查找名称为指定roleName的TeamRole
            TeamRoleWithUser dispatchTeamRole = teamRoleWithUsers.stream()
                    .filter(role -> roleName.equals(role.getName()))
                    .findFirst()
                    .orElse(null);
            
            if (dispatchTeamRole == null) {
                log.error("未找到名称为[{}]的TeamRole", roleName);
                return Collections.singletonMap(roleName, Collections.emptyList());
            }
            log.info("找到名称为[{}]的TeamRole: {}", roleName, dispatchTeamRole.getOid());
            
            // 3. 根据TeamRole的Oid获取对应的TeamUser
            List<com.alibaba.fastjson.JSONObject> users = teamHelper.searchTeamUser(dispatchTeamRole.getOid(), "");
            
            if (CollUtil.isEmpty(users)) {
                log.info("角色[{}]下没有关联用户", dispatchTeamRole.getOid());
                return Collections.singletonMap(roleName, new ArrayList<>());
            }
            
            // 4. 获取用户对应的钉钉userId
            List<String> dingTalkUserIds = new ArrayList<>();
            
            // 获取钉钉访问令牌
            String accessToken = dingTalkService.getTokenNew();
            
            for (com.alibaba.fastjson.JSONObject userJson : users) {
                // 从用户信息中获取手机号
                String phone = userJson.getString("phone");
                if (StringUtils.isNotBlank(phone)) {
                    // 如果手机号以8开头，去掉前缀
                    if (phone.startsWith("86")) {
                        phone = phone.substring(2);
                    }
                    
                    // 使用手机号获取钉钉userId
                    String dingTalkUserId = dingTalkService.getUserIdByPhone(phone, accessToken);
                    if (StringUtils.isNotBlank(dingTalkUserId)) {
                        dingTalkUserIds.add(dingTalkUserId);
                        log.info("用户[{}]的手机号[{}]对应的钉钉userId: {}", 
                                userJson.getString("name"), phone, dingTalkUserId);
                    } else {
                        log.warn("未找到用户[{}]手机号[{}]对应的钉钉userId", 
                                userJson.getString("name"), phone);
                    }
                } else {
                    log.warn("用户[{}]没有设置手机号", userJson.getString("name"));
                }
            }
            
            log.info("获取到角色[{}]下的钉钉用户ID数量: {}", roleName, dingTalkUserIds.size());
            
            // 返回结果
            Map<String, List<String>> result = new HashMap<>();
            result.put(roleName, dingTalkUserIds);
            return result;
            
        } catch (Exception e) {
            log.error("通过Team查找角色[{}]用户失败", roleName, e);
            return Collections.singletonMap(roleName, Collections.emptyList());
        }
    }
    
    /**
     * 初始化系统管理员用户
     */
    private void initUser() {
        UserDTO currUser = SessionHelper.getCurrentUser();
        if(currUser == null || (currUser != null && currUser.getTenantOid() == null)){
            UserDTO userDto = new UserDTO();
            userDto.setTenantOid(tenantOid);
            userDto.setOid("sys_admin");
            userDto.setAccount("sys_admin");
            userDto.setSystemAdmin(Boolean.TRUE);
            userDto.setIpAddress("127.0.0.1");
            SessionHelper.addCurrentUser(userDto);
        }
    }

    /**
     * 根据容器名称查询调度角色和产品经理角色的用户信息 Now
     * @param request 包含容器名称的请求对象
     * @return 查询结果，包含结果数据和消息
     */
    public Map<String, Object> getTeamRoleUsers(SearchRequest request) {
        return getTeamRoleUsers(request.getContainerName(), request.getFoldPath());
    }
    
    /**
     * 根据容器名称查询调度角色和产品经理角色的用户信息
     * @param containerName 容器名称
     * @param foldPath 文件夹路径，仅当containerName为"产品化文件库"时有效
     * @return 查询结果，包含结果数据和消息
     */
    public Map<String, Object> getTeamRoleUsers(String containerName, String foldPath) {
        // 初始化用户信息
        initUser();
        
        Map<String, Object> response = new HashMap<>();
        RoleUserResult roleUserResult = new RoleUserResult();
        // 初始化钉钉用户ID字段为空数组的JSON字符串
        String emptyArrayJson = JSON.toJSONString(Collections.emptyList());
        roleUserResult.setDispatchDingUserIds(emptyArrayJson);
        roleUserResult.setManagerDingUserIds(emptyArrayJson);
        // 真实姓名字段默认为空字符串，在构造函数中已初始化
        
        String message = null;
        
        try {
            if (StringUtils.isBlank(containerName)) {
                message = "容器名称不能为空";
                response.put("result", roleUserResult);
                response.put("message", message);
                return response;
            }
            
            // 1. 根据产品名称查询对应的Container产品信息（最新版本）
            List<Container> containerDetails = jwiCommonService.dynamicQuery(
                    Container.TYPE,
                    Condition.where("name").eq(containerName)
                            .and(Condition.where("markForDelete").eq(false)),
                    Container.class);
            
            if (containerDetails == null || containerDetails.isEmpty()) {
                log.error("未找到产品容器: {}", containerName);
                message = "未找到产品容器: " + containerName;
                response.put("result", roleUserResult);
                response.put("message", message);
                return response;
            }
            
            // 多个取一个最新的
            String containerOid = containerDetails.get(0).getOid();
            log.info("根据产品名称[{}]查询到产品容器ID: {}", containerName, containerOid);
            
            // 2. 查询调度角色用户信息
            UserRoleInfo dispatchRoleInfo = findRoleUserInfo(containerOid, DISPATCH_ROLE, containerName, foldPath);
            if (dispatchRoleInfo != null && !dispatchRoleInfo.getDingUserIds().isEmpty()) {
                roleUserResult.setDispatchDingUserIds(JSON.toJSONString(dispatchRoleInfo.getDingUserIds()));
                roleUserResult.setDispatchRealNames(String.join(",", dispatchRoleInfo.getRealNames()));
                log.info("获取到调度角色用户信息: {}", JSON.toJSONString(dispatchRoleInfo));
            } else {
                log.warn("未找到调度角色用户信息");
            }
            
            // 3. 查询产品经理角色用户信息
            UserRoleInfo managerRoleInfo = findRoleUserInfo(containerOid, MANAGER_ROLE, containerName, foldPath);
            if (managerRoleInfo != null && !managerRoleInfo.getDingUserIds().isEmpty()) {
                roleUserResult.setManagerDingUserIds(JSON.toJSONString(managerRoleInfo.getDingUserIds()));
                roleUserResult.setManagerRealNames(String.join(",", managerRoleInfo.getRealNames()));
                log.info("获取到产品经理角色用户信息: {}", JSON.toJSONString(managerRoleInfo));
            } else {
                log.warn("未找到产品经理角色用户信息");
            }
            
            response.put("result", roleUserResult);
            return response;
            
        } catch (Exception e) {
            log.error("查找角色用户失败", e);
            // 即使发生异常，也返回成功状态和空结果
            response.put("result", roleUserResult);
            response.put("message", "查找角色用户失败: " + e.getMessage());
            return response;
        }
    }
    
    /**
     * 用户角色信息类，包含钉钉用户ID和真实姓名
     */
    @Data
    private static class UserRoleInfo {
        private List<String> dingUserIds = new ArrayList<>();
        private List<String> realNames = new ArrayList<>();
    }
    
    /**
     * 查找指定角色的用户信息
     * @param containerOid 容器OID
     * @param roleName 角色名称
     * @param containerName 容器名称
     * @param foldPath 文件夹路径，仅当containerName为"产品化文件库"时有效
     * @return 用户角色信息，包含钉钉用户ID和真实姓名
     */
    private UserRoleInfo findRoleUserInfo(String containerOid, String roleName, String containerName, String foldPath) {
        try {
            // 尝试第一条路径：通过文件夹树查找
            UserRoleInfo roleInfo = findRoleUsersByFolderPath(containerOid, roleName, containerName, foldPath);
            
            if (roleInfo != null && !roleInfo.getDingUserIds().isEmpty()) {
                return roleInfo;
            }
            
            // 第一条路径失败，尝试第二条路径：直接通过容器的Team查找
            log.info("通过文件夹树查找失败或未找到用户，尝试通过容器Team直接查找");
            UserRoleInfo teamRoleInfo = findRoleUsersByInstanceTeam(containerOid, roleName);
            
            if (teamRoleInfo != null) {
                return teamRoleInfo;
            }
            
            // 两条路径都失败，返回空对象
            return new UserRoleInfo();
            
        } catch (Exception e) {
            log.error("查找角色[{}]用户信息失败", roleName, e);
            return new UserRoleInfo();
        }
    }
    
    /**
     * 通过文件夹树查找角色用户信息
     * @param containerOid 容器OID
     * @param roleName 角色名称
     * @param containerName 容器名称
     * @param foldPath 文件夹路径，仅当containerName为"产品化文件库"时有效
     * @return 用户角色信息
     */
    private UserRoleInfo findRoleUsersByFolderPath(String containerOid, String roleName, String containerName, String foldPath) {
        try {
            // 使用pdmFolderServiceI查询文件夹树
            SessionHelper.getCurrentUser().setSystemAdmin(true);
            List<FolderTreeNode> folderTreeNodes = pdmFolderServiceI.searchFoldersWithPermisson(
                    "ProductContainer",
                    containerOid, 
                    "");
            
            if (CollUtil.isEmpty(folderTreeNodes)) {
                log.error("未找到容器[{}]下的文件夹", containerOid);
                return new UserRoleInfo();
            }
            
            // 获取FolderId
            String folderOid = folderTreeNodes.get(0).getOid();
            
            // 如果是产品化文件库，并且foldPath不为空，则根据foldPath查找特定的文件夹
            if ("产品化文件库".equals(containerName) && StringUtils.isNotBlank(foldPath)) {
                log.info("容器为产品化文件库，使用foldPath[{}]查找特定文件夹", foldPath);
                
                // 处理foldPath，先用逗号切割，取第一个
                String[] paths = foldPath.split(",");
                String targetPath = paths[0];
                log.info("处理后的目标路径: {}", targetPath);
                
                // 特殊处理路径
                FolderTreeNode targetFolder = processSpecialPath(folderTreeNodes, targetPath);
                
                if (targetFolder != null) {
                    folderOid = targetFolder.getOid();
                    log.info("找到目标文件夹, ID: {}", folderOid);
                } else {
                    log.error("未找到目标文件夹路径: {}", targetPath);
                    return new UserRoleInfo();
                }
            } else {
                log.info("使用顶层文件夹ID: {}", folderOid);
            }
            
            // 根据FolderId获取Team
            Team team = pdmFolderServiceI.getTeamRole(folderOid, "");
            if (team == null) {
                log.error("未找到文件夹[{}]对应的Team", folderOid);
                return new UserRoleInfo();
            }
            log.info("获取到文件夹[{}]对应的Team: {}", folderOid, team.getOid());
            
            UserRoleInfo roleInfo = findRoleUsersByTeam(team.getOid(), roleName);
            return roleInfo != null ? roleInfo : new UserRoleInfo();
            
        } catch (Exception e) {
            log.error("通过文件夹树查找角色[{}]用户信息失败", roleName, e);
            return new UserRoleInfo();
        }
    }
    
    /**
     * 处理特殊路径，只关注前三层，使用contains判断第三层
     * @param folderTreeNodes 文件夹树节点列表
     * @param targetPath 目标路径，如"产品化文件库-产品化文件库Z正样-结构类产品-平台"
     * @return 目标文件夹，如果未找到则返回null
     */
    private FolderTreeNode processSpecialPath(List<FolderTreeNode> folderTreeNodes, String targetPath) {
        // 按层级切割
        String[] segments = targetPath.split("-");
        if (segments.length < 3) {
            log.error("路径格式不正确，层级不足: {}", targetPath);
            return null;
        }
        
        // 提取根节点和父节点
        String rootName = segments[0];
        String parentName = segments[1];
        String thirdLevelName = segments[2]; // 只取第三层的名称
        
        log.info("解析路径: 根节点[{}], 父节点[{}], 第三层[{}]", rootName, parentName, thirdLevelName);
        
        // 从根节点开始查找
        FolderTreeNode rootNode = null;
        for (FolderTreeNode node : folderTreeNodes) {
            if (rootName.equals(node.getName())) {
                rootNode = node;
                break;
            }
        }
        
        if (rootNode == null) {
            log.error("未找到根节点: {}", rootName);
            return null;
        }
        
        // 查找父节点
        FolderTreeNode parentNode = null;
        if (rootNode.getChildren() != null) {
            for (FolderTreeNode node : rootNode.getChildren()) {
                if (parentName.equals(node.getName())) {
                    parentNode = node;
                    break;
                }
            }
        }
        
        if (parentNode == null) {
            log.error("未找到父节点: {}", parentName);
            return null;
        }
        
        // 查找第三层节点，使用contains而不是equals
        FolderTreeNode thirdLevelNode = null;
        if (parentNode.getChildren() != null) {
            for (FolderTreeNode node : parentNode.getChildren()) {
                if (node.getName() != null && node.getName().contains(thirdLevelName)) {
                    thirdLevelNode = node;
                    log.info("使用contains找到第三层节点: [{}]，匹配关键词: [{}]", node.getName(), thirdLevelName);
                    break;
                }
            }
        }
        
        if (thirdLevelNode == null) {
            log.error("未找到包含[{}]的第三层节点", thirdLevelName);
            return null;
        }
        
        log.info("成功找到第三层文件夹: [{}]", thirdLevelNode.getName());
        return thirdLevelNode;
    }
    
    /**
     * 通过容器的Team直接查找角色用户信息
     * @param containerOid 容器OID
     * @param roleName 角色名称
     * @return 用户角色信息
     */
    private UserRoleInfo findRoleUsersByInstanceTeam(String containerOid, String roleName) {
        try {
            // 使用instanceService.findInstance获取容器信息
            InstanceEntity instance = instanceService.findInstance(containerOid, Container.TYPE);
            log.info("第二条路径产品instance信息:{}", JSONUtil.toJsonStr(instance));
            // 获取容器的Team
            Team team = pdmFolderTwoRepo.getInstanceTeam(instance);
            log.info("第二条路径team: {}", JSONUtil.toJsonStr(team));
            if (team == null) {
                log.error("容器[{}]没有关联的Team", containerOid);
                return new UserRoleInfo();
            }
            log.info("获取到容器[{}]关联的Team: {}", containerOid, team.getOid());
            
            UserRoleInfo roleInfo = findRoleUsersByTeam(team.getOid(), roleName);
            return roleInfo != null ? roleInfo : new UserRoleInfo();
            
        } catch (Exception e) {
            log.error("通过容器Team查找角色[{}]用户信息失败", roleName, e);
            return new UserRoleInfo();
        }
    }
    
    /**
     * 根据TeamOid查找角色用户信息
     * @param teamOid 团队OID
     * @param roleName 角色名称
     * @return 用户角色信息
     */
    private UserRoleInfo findRoleUsersByTeam(String teamOid, String roleName) {
        try {
            // 获取TeamRole列表
            List<TeamRoleWithUser> teamRoleWithUsers = teamHelper.fuzzyContent(teamOid, "");
            if (CollUtil.isEmpty(teamRoleWithUsers)) {
                log.error("未找到团队[{}]下的角色", teamOid);
                return new UserRoleInfo();
            }
            log.info("获取到团队[{}]下的角色数量: {}", teamOid, teamRoleWithUsers.size());
            
            // 查找名称为指定roleName的TeamRole
            TeamRoleWithUser targetTeamRole = teamRoleWithUsers.stream()
                    .filter(role -> roleName.equals(role.getName()))
                    .findFirst()
                    .orElse(null);
            
            if (targetTeamRole == null) {
                log.error("未找到名称为[{}]的TeamRole", roleName);
                return new UserRoleInfo();
            }
            log.info("找到名称为[{}]的TeamRole: {}", roleName, targetTeamRole.getOid());
            
            // 根据TeamRole的Oid获取对应的TeamUser
            List<com.alibaba.fastjson.JSONObject> users = teamHelper.searchTeamUser(targetTeamRole.getOid(), "");
            
            if (CollUtil.isEmpty(users)) {
                log.info("角色[{}]下没有关联用户", targetTeamRole.getOid());
                return new UserRoleInfo();
            }
            
            // 获取用户对应的钉钉userId和真实姓名
            UserRoleInfo roleInfo = new UserRoleInfo();
            
            // 获取钉钉访问令牌
            String accessToken = dingTalkService.getTokenNew();
            
            for (com.alibaba.fastjson.JSONObject userJson : users) {
                // 获取用户真实姓名
                String realName = userJson.getString("name");
                if (StringUtils.isNotBlank(realName)) {
                    roleInfo.getRealNames().add(realName);
                }
                
                // 从用户信息中获取手机号
                String phone = userJson.getString("phone");
                if (StringUtils.isNotBlank(phone)) {
                    // 如果手机号以86开头，去掉前缀
                    if (phone.startsWith("86")) {
                        phone = phone.substring(2);
                    }
                    
                    // 使用手机号获取钉钉userId
                    String dingTalkUserId = dingTalkService.getUserIdByPhone(phone, accessToken);
                    if (StringUtils.isNotBlank(dingTalkUserId)) {
                        roleInfo.getDingUserIds().add(dingTalkUserId);
                        log.info("用户[{}]的手机号[{}]对应的钉钉userId: {}", realName, phone, dingTalkUserId);
                    } else {
                        log.warn("未找到用户[{}]手机号[{}]对应的钉钉userId", realName, phone);
                    }
                } else {
                    log.warn("用户[{}]没有设置手机号", realName);
                }
            }
            
            log.info("获取到角色[{}]下的钉钉用户ID数量: {}, 真实姓名数量: {}", 
                    roleName, roleInfo.getDingUserIds().size(), roleInfo.getRealNames().size());
            
            return roleInfo;
            
        } catch (Exception e) {
            log.error("通过Team查找角色[{}]用户信息失败", roleName, e);
            return new UserRoleInfo();
        }
    }
}
