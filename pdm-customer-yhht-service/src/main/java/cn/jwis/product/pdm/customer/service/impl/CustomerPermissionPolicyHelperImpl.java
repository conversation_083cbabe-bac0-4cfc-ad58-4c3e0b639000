package cn.jwis.product.pdm.customer.service.impl;

import cn.jwis.platform.plm.container.entity.TeamRole;
import cn.jwis.platform.plm.permission.service.DefaultPermissionPolicyHelperImpl;
import cn.jwis.product.pdm.customer.repo.CustomerCommonRepo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2023/10/31 14:12
 * @Email <EMAIL>
 */
@Service
@Primary
@Transactional
public class CustomerPermissionPolicyHelperImpl extends DefaultPermissionPolicyHelperImpl {

    @Autowired
    CustomerCommonRepo customerCommonRepo;

    @Override
    public Map<String, List<TeamRole>> queryContainerRole(String tenantOid, String userOid) {
        return customerCommonRepo.queryContainerRole(tenantOid, userOid);
    }
}
