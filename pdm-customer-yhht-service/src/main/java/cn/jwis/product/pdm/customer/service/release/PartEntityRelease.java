package cn.jwis.product.pdm.customer.service.release;

import cn.hutool.json.JSONUtil;
import cn.jwis.framework.base.bean.util.BeanUtil;
import cn.jwis.framework.base.domain.able.ModelAble;
import cn.jwis.framework.base.domain.able.info.ModelInfo;
import cn.jwis.framework.base.exception.JWIException;
import cn.jwis.framework.base.util.CollectionUtil;
import cn.jwis.framework.database.core.query.dynamic.Condition;
import cn.jwis.platform.plm.account.repo.user.response.UserWithPositionAndOrg;
import cn.jwis.platform.plm.account.user.service.UserHelper;
import cn.jwis.platform.plm.account.user.service.UserService;
import cn.jwis.platform.plm.datadistribution.service.DataDistributionService;
import cn.jwis.platform.plm.dataengine.common.JWICommonService;
import cn.jwis.platform.plm.file.entity.FileMetadata;
import cn.jwis.platform.plm.file.service.FileService;
import cn.jwis.platform.plm.foundation.attachment.entity.File;
import cn.jwis.platform.plm.foundation.classification.service.ClassificationService;
import cn.jwis.platform.plm.foundation.common.dto.RelatedFuzzyDTO;
import cn.jwis.platform.plm.foundation.common.response.InstanceEntity;
import cn.jwis.platform.plm.foundation.common.service.CommonAbilityHelper;
import cn.jwis.platform.plm.foundation.common.service.CommonAbilityService;
import cn.jwis.platform.plm.foundation.common.service.InstanceHelper;
import cn.jwis.platform.plm.foundation.relationship.Reference;
import cn.jwis.platform.plm.foundation.relationship.enums.RelationConstraint;
import cn.jwis.platform.plm.foundation.versionrule.able.MasterAble;
import cn.jwis.platform.plm.foundation.versionrule.able.VersionAble;
import cn.jwis.platform.plm.sysconfig.collectionrule.service.CollectionRuleHelper;
import cn.jwis.platform.plm.sysconfig.entity.CollectionRule;
import cn.jwis.platform.plm.sysconfig.entity.ConfigItem;
import cn.jwis.platform.plm.sysconfig.entity.Units;
import cn.jwis.platform.plm.sysconfig.preferences.PreferencesService;
import cn.jwis.platform.plm.sysconfig.units.service.UnitsService;
import cn.jwis.platform.plm.workflow.engine.order.entity.ProcessOrder;
import cn.jwis.platform.plm.workflow.engine.service.interf.ProcessOrderHelper;
import cn.jwis.product.pdm.cad.ecad.entity.ECAD;
import cn.jwis.product.pdm.cad.ecad.entity.ECADIteration;
import cn.jwis.product.pdm.cad.ecad.service.ECADService;
import cn.jwis.product.pdm.cad.mcad.service.MCADHelper;
import cn.jwis.product.pdm.cad.mcad.service.PartMcadService;
import cn.jwis.product.pdm.change.service.ChangeHelper;
import cn.jwis.product.pdm.customer.entity.Forming;
import cn.jwis.product.pdm.customer.entity.IntegrationRecord;
import cn.jwis.product.pdm.customer.entity.Screen;
import cn.jwis.product.pdm.customer.repo.CustomerCommonRepo;
import cn.jwis.product.pdm.customer.service.dto.BaseEntityDTO;
import cn.jwis.product.pdm.customer.service.dto.GlobalSubstituteRelation;
import cn.jwis.product.pdm.customer.service.dto.IntegrationFile;
import cn.jwis.product.pdm.customer.service.dto.ItemReleaseDataDTO;
import cn.jwis.product.pdm.customer.service.impl.CustomerCommonServiceImpl;
import cn.jwis.product.pdm.document.entity.DocumentIteration;
import cn.jwis.product.pdm.partbom.part.dto.FuzzySubDTO;
import cn.jwis.product.pdm.partbom.part.entity.Part;
import cn.jwis.product.pdm.partbom.part.entity.PartIteration;
import cn.jwis.product.pdm.partbom.part.response.PartIterationWithRelation;
import cn.jwis.product.pdm.partbom.part.service.PartHelper;
import cn.jwis.product.pdm.partbom.part.service.PartService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import jodd.util.StringUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.activiti.engine.RepositoryService;
import org.activiti.engine.repository.Model;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.math3.util.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 部件BOM发布能力提供者
 * @date 2023/8/15 11:13
 * @Email <EMAIL>
 */
@Data
@EqualsAndHashCode
@Component
@Transactional
public class PartEntityRelease extends EntityRelease{

    private static final Logger logger = LoggerFactory.getLogger(PartEntityRelease.class);

    private final List<String> targetLifecycleStatus = Arrays.asList("Released","Inwork","Design","Draft","UnderReview");

    @Autowired
    DataDistributionService dataDistributionService;

    @Autowired
    PartService partService;

    @Autowired
    PartHelper partHelper;

    @Autowired
    MCADHelper mcadHelper;

    @Autowired
    ECADService ecadService;

    @Autowired
    JWICommonService jwiCommonService;

    @Autowired
    CommonAbilityHelper commonAbilityHelper;

    @Autowired
    CommonAbilityService commonAbilityService;

    @Autowired
    CustomerCommonServiceImpl customerCommonServiceImpl;

    @Autowired
    PartMcadService partMcadService;

    @Autowired
    FileService fileService;

    @Autowired
    UserHelper userHelper;

    @Autowired
    UserService userService;

    @Autowired
    ProcessOrderHelper processOrderHelper;

    @Autowired
    ChangeHelper changeHelper;

    @Autowired
    UnitsService unitsService;

    @Autowired
    ClassificationService classificationService;

    @Autowired
    PreferencesService preferencesService;

    @Autowired
    RepositoryService repositoryService;

    @Resource
    CustomerCommonRepo customerCommonRepo;


    /**
     * 真正的Part发布实现，在此处推送发放的部件到任意外部接口，当前只发MQ。
     * @return
     */
    @Override
    @Async
    public IntegrationRecord release(ProcessOrder processOrder,String entityOid, String recordOid, int isFirst) {
        PartIteration dbPart = partHelper.findByOid(entityOid);
        if(dbPart == null) return null;

        if(!dbPart.isLatest()){
            PartIteration dbPartLatest = partHelper.findLatestByNumber(dbPart.getNumber(), dbPart.getViewOid());
            if(dbPartLatest != null)
                dbPart = partHelper.findByOid(dbPartLatest.getOid());
        }

        //Assert.isTrue(targetLifecycleStatus.contains(dbPart.getLifecycleStatus()),"part[" + dbPart.getNumber() + "] lifecycleStatus is not match!");
        ItemReleaseDataDTO partReleaseData = BeanUtil.copyProperties(dbPart,new ItemReleaseDataDTO());
        partReleaseData.setBusinessOid(type + "_" + dbPart.getNumber() + "_" + dbPart.getVersion()); // 业务ID
        partReleaseData.setProcessOrder(processOrder == null ? "" : processOrder.getNumber());
        partReleaseData.setUpdateTime(dbPart.getUpdateDate());
        // 单位
        partReleaseData.setDefaultUnit(findUnitsCode(partReleaseData.getDefaultUnit()));
        // 发布人
        JSONObject extension = dbPart.getExtensionContent();
        String cn_jwis_xqr, publishUserAccount = "";
        if(extension != null && (cn_jwis_xqr = extension.getString("cn_jwis_xqr")) != null)
            if(cn_jwis_xqr.startsWith("{"))
                publishUserAccount = JSON.parseObject(cn_jwis_xqr).getString("account");
            else if(cn_jwis_xqr.startsWith("[") && cn_jwis_xqr.length() > 2)
                JSON.parseArray(cn_jwis_xqr).getJSONObject(0).getString("account");
        if("".equals(publishUserAccount))
            throw new JWIException("物料编码 [" + dbPart.getNumber() + "] 的编码需求人信息为空，请重新设置。");
        UserWithPositionAndOrg user = userHelper.findDetailByAccount(publishUserAccount);
        initPublisher(user,partReleaseData);
        // 状态 && 修改businessId
        initStatus(processOrder,partReleaseData,recordOid,isFirst,dbPart);
        // 处理分类码
        initClsCode(partReleaseData);
        // 是否下发慧智造
        initSendHZZ(partReleaseData);
        // 慧智造是否生效
        initActiveHZZ(partReleaseData);
        // 初始化部件的参考文档（已发放的文档）
        initReference(dbPart,partReleaseData);
        // 初始化部件的描述文档（已发放的文档）& 元器件关联的datasheet
        initDescribe(dbPart,partReleaseData);
        // 替代件
        initSubstitute(dbPart,partReleaseData);
        // 筛选包含非筛选  成型包含未成型
        initScreen(dbPart,partReleaseData);
        //全局替代
        initGlobalRel(dbPart, partReleaseData);

        //处理物料描述，适应慧致造不能超200字符
        String description = partReleaseData.getDescription();
        if (description != null && description.length() > 180) {
            description = description.substring(0, 180); // 截断到前200字符
        }
        partReleaseData.setDescription(description);

        JSONObject releaseData = JSONObject.parseObject(JSONObject.toJSONString(partReleaseData));
        // 获取集成记录
        IntegrationRecord record = getIntegrationRecord(recordOid,type,processOrder,releaseData);

        logger.info("releaseData 信息------->>>>>>>{}", JSONUtil.toJsonStr(releaseData));
        //根据 releaseData的number属性来判断是否以RM开头 RM开头的为 false， 其他的是true
        record.setIsPartWithOutRM(Boolean.FALSE);
        String number = partReleaseData.getNumber();
        if (number != null && !number.startsWith("RM")) {
            record.setIsPartWithOutRM(Boolean.TRUE);
        }

        if(processOrder != null && processOrder.getName().contains("物料停用流程")){
            if(StringUtils.isNotEmpty(record.getBizName()) && record.getData() != null){
                record.getData().fluentPut("name", "已失效-" + record.getData().getString("name"));
                record.setBizName("已失效-" + record.getBizName());
                if(record.getData().getJSONObject("extensionContent") != null)
                    if(StringUtils.isNotEmpty(record.getData().getJSONObject("extensionContent").getString("cn_jwis_gg")))
                        record.getData().getJSONObject("extensionContent").fluentPut("cn_jwis_gg", "已失效-" + record.getData().getJSONObject("extensionContent").getString("cn_jwis_gg"));
            }
        }

        // 发MQ
        String result = releaseMQ(record,dataDistributionService);
        record.setMqSuccess(StringUtil.isBlank(result) ? true : false);
        // U9处理结果，预设为 waiting
        record.setConsumer("u9");
        record.setIsSuccess(false);
        record.setMsg("waiting");

        logger.info("PartEntityRelease.release record 信息------->>>>>>>{}", JSONUtil.toJsonStr(record));
        return jwiCommonService.update(record);
    }

    private void initGlobalRel(PartIteration dbPart, ItemReleaseDataDTO partReleaseData) {
        FuzzySubDTO dto = new FuzzySubDTO();
        dto.setFromOid(dbPart.getMasterOid());
        dto.setFromType("Part");
        dto.setSearchKey("");

        List<PartIterationWithRelation> partIterations = partHelper.fuzzyGlobalSubstitute(dto);

        // 安全初始化列表
        List<GlobalSubstituteRelation> globalRel = CollectionUtil.isNotEmpty(partIterations)
                ? new ArrayList<>(partIterations.size())
                : new ArrayList<>();

        int seq = 10;
        if (CollectionUtil.isNotEmpty(partIterations)) {
            for (PartIterationWithRelation partIteration : partIterations) {
                GlobalSubstituteRelation globalSubstituteRelation = new GlobalSubstituteRelation();
                globalSubstituteRelation.setRelationItemCode(partIteration.getNumber());
                globalSubstituteRelation.setSequenceNumber(String.valueOf(seq));
                globalRel.add(globalSubstituteRelation);
                seq += 10;
            }
        }

        partReleaseData.setGlobalRel(globalRel);
    }

    private void initActiveHZZ(ItemReleaseDataDTO partReleaseData) {
        // 发慧智造时，设置慧智造生效
        partReleaseData.setActiveHZZ(partReleaseData.getSendHZZ());
        // 停用时，设置慧智造失效
        if(3 == partReleaseData.getLifecycleStatus()){
            partReleaseData.setActiveHZZ(0);
        }
    }

    private void initSendHZZ(ItemReleaseDataDTO partReleaseData) {
        partReleaseData.setSendHZZ(1);
        if(Objects.isNull(partReleaseData.getClsProperty())){
            return;
        }
        List<String> noNeedSendHzz = new ArrayList<>();
        ConfigItem item = preferencesService.queryConfigValue("noNeedSendHzzFlagForU9");
        if(item != null) {
            noNeedSendHzz.addAll(Arrays.asList(item.getValue().split(",")));
        }
        // 除固资类/服务类/软件类/MP类/AIT半成品类/拆解类，其余分类自动同步
        // FA/SM/SW/MP/AI/DA
        if(CollectionUtil.isEmpty(noNeedSendHzz)){
            return;
        }
        // 获取到分类码路径
        JSONObject clsProperty = partReleaseData.getClsProperty();
        String clsCodePath = clsProperty.getString("clsCode");
        // 分类为空也发
        if(StringUtil.isBlank(clsCodePath)){
            return;
        }
        for(String clsType : noNeedSendHzz){
            if(clsCodePath.startsWith(clsType)){
                partReleaseData.setSendHZZ(0);
                return;
            }
        }
    }

    private void initClsCode(ItemReleaseDataDTO partReleaseData) {
        if(Objects.isNull(partReleaseData.getClsProperty())){
            return;
        }
        JSONObject clsProperty = partReleaseData.getClsProperty();
        String clsOid = clsProperty.getString("clsOid");
        String clsCodePath = classificationService.findPathByOid(clsOid);
        if(cn.jwis.framework.base.util.StringUtil.isNotBlank(clsCodePath)) {
            clsProperty.put("clsCode", clsCodePath);
        }
    }

    public String findUnitsCode(String defaultUnit) {
        Units units = unitsService.searchByOid(defaultUnit);
        if(units == null){
            units = unitsService.findByName(defaultUnit);
        }
        return units.getCode();
    }

    private void initPublisher(UserWithPositionAndOrg user, ItemReleaseDataDTO partReleaseData) {
        if(user == null){
            partReleaseData.setUpdatorAccount("sys_admin");
            partReleaseData.setUpdatorNameAndDepartment("PDM系统管理员");
            return;
        }
        partReleaseData.setUpdatorAccount(user.getNumber());
        List<String> orgList = userService.getOrgTreeNameByAccount(user.getAccount());
        StringBuffer orgTree = new StringBuffer(user.getName() + ",");
        String companyName = orgList.get(orgList.size()-1);
        for(int i = orgList.size()-2; i > -1 ; i--){
            // 如果owner在多个部门中，只取第一个
            if(companyName.equals(orgList.get(i))){
                break;
            }
            orgTree.append(orgList.get(i));
            if(i > 0){
                orgTree.append("-");
            }
        }
        String result = orgTree.toString();
        if(result.endsWith("-")){
            result = result.substring(0,result.length()-1);
        }
        partReleaseData.setUpdatorNameAndDepartment(result);
    }

    /**
     * create=1,update=2,disable=3
     * @param partReleaseData
     */
    public void initStatus(ProcessOrder processOrder,ItemReleaseDataDTO partReleaseData,
                           String recordOid,int isFirst,PartIteration dbPart) {

        if(StringUtil.isNotBlank(recordOid)) { // 重发时会传recordOid
            IntegrationRecord integrationRecord = (IntegrationRecord)commonAbilityService.findByOid(recordOid,IntegrationRecord.TYPE);
            if(integrationRecord != null && integrationRecord.getBusinessOid().endsWith("_deactivated")){
                partReleaseData.setLifecycleStatus(3);
                partReleaseData.setBusinessOid(partReleaseData.getBusinessOid() + "_deactivated");
                return;
            }
        }

        if(processOrder != null) {
            Model model = repositoryService.getModel(processOrder.getProcessModelId());
            if("cn_jwis_wlstop".equals(model.getKey())){
                partReleaseData.setLifecycleStatus(3);
                partReleaseData.setBusinessOid(partReleaseData.getBusinessOid() + "_deactivated");
                return;
            }
        }
        if("Deactivated".equals(dbPart.getLifecycleStatus())){
            partReleaseData.setLifecycleStatus(3);
            partReleaseData.setBusinessOid(partReleaseData.getBusinessOid() + "_deactivated");
        }else if(isFirst > 0){ //如果传了值（>0则标识前端传了值），则首次还是更新由isFirst决定，仅用作管理员工具处理
            partReleaseData.setLifecycleStatus(isFirst);
        }else if("A01".equals(partReleaseData.getVersion()) || "A".equals(partReleaseData.getVersion())){
            String number = partReleaseData.getNumber();
            IntegrationRecord integrationRecord = jwiCommonService.dynamicQueryOne(IntegrationRecord.TYPE,
                    Condition.where("bizNumber").eq(number).
                            and(Condition.where("isSuccess").eq(Boolean.TRUE)), IntegrationRecord.class);
            logger.info("当前entity发布记录 integrationRecord---->>>{}", JSONUtil.toJsonStr(integrationRecord));
            partReleaseData.setLifecycleStatus(integrationRecord != null ? 2 : 1);
        }else{
            partReleaseData.setLifecycleStatus(2);
        }
        logger.info("initStatus中 partReleaseData---->>>{}", JSONUtil.toJsonStr(partReleaseData));

    }

    @Value("${part.autoScreen:false}")
    private Boolean autoScreen;

    // 已成型-->未成型
    public void initForming(PartIteration dbPart, ItemReleaseDataDTO partReleaseData) {
//        if(autoScreen && dbPart.getClsProperty() != null && "成型".equals(dbPart.getClsProperty().getString("cn_jwis_sxzldj"))){
        if(autoScreen){
//            partReleaseData.setForming(findScreenFormingPart(dbPart.getOid()).stream()
            List<BaseEntityDTO> formingList = findScreenFormingPart(dbPart.getOid(),false).stream()
                    .map(item -> BeanUtil.copyProperties(item, new BaseEntityDTO())).collect(Collectors.toList());
            if(formingList != null && formingList.size() > 0){
                partReleaseData.getClsProperty().fluentPut("cn_jwis_sxzldj", "是");
                partReleaseData.setScreen(formingList);
            }

        } else if(!autoScreen){
//            partReleaseData.setForming(new ArrayList<>());
            partReleaseData.setScreen(new ArrayList<>());
            List<ModelAble> modelAbleList = commonAbilityService.findMIByFrom(dbPart.getMasterOid(),dbPart.getMasterType(), Forming.TYPE);
            if(CollectionUtil.isEmpty(modelAbleList)){
                return;
            }
            for(ModelAble model : modelAbleList){
                BaseEntityDTO baseEntityDTO = BeanUtil.copyProperties(model,new BaseEntityDTO());
//                partReleaseData.getForming().add(baseEntityDTO);
                partReleaseData.getScreen().add(baseEntityDTO);
            }
        }
    }

    // 为关联物料列表
    public Pair<List<InstanceEntity>,Boolean> findScreenOrFormingPart(String partOid) {
        PartIteration dbPart = partHelper.findByOid(partOid);
        if(dbPart == null) {
            return null;
        }
        // 不包含分类属性，与筛选 成型部件没有关系 直接返回空列表
        if(dbPart.getClsProperty() == null) {
            return null;
        }
        // 为手动创建的料，但不是“成型”也不是“筛选级”，就是未成型
        String sxzldj = dbPart.getClsProperty().getString("cn_jwis_sxzldj");
        String zldj = dbPart.getClsProperty().getString("cn_jwis_zldj");
        // cn_jwis_sxzld 不为否 为自动创建数据
        if("筛选级".equals(dbPart.getClsProperty().getString("cn_jwis_sxzldj"))) {
            return new Pair<>(findScreenFormingPart(dbPart.getOid(),false),false);
        } else if("成型".equals(dbPart.getClsProperty().getString("cn_jwis_sxzldj"))) {
            return new Pair<>(findScreenFormingPart(dbPart.getOid(),true),true);
        //为手动创建数据
        } else if("否".equals(dbPart.getClsProperty().getString("cn_jwis_sxzldj"))) {
            //此为手动创建的筛选级物料
            if("39".equals(dbPart.getClsProperty().getString("cn_jwis_zldj"))) {
                return new Pair<>(findScreenFormingPart(dbPart.getOid(),false),false);
            } else {
                // 手动创建  可能为 成型，未成型 非筛选   使用双向关系查询 再做进一步判断
                List<InstanceEntity> tempList = findScreenFormingPart(dbPart.getOid());
                // 无关联物料 无数据直接退出
                if(CollectionUtils.isEmpty(tempList)) {
                    return null;
                }
                PartIteration detailEntity = partHelper.findByOid(tempList.stream().findAny().get().getOid());
                // 关联物料信息无分类信息 异常数据 直接退出
                if(detailEntity.getClsProperty() == null) {
                    return null;
                // 关联物料质量等级为筛选级 之前为 非筛选物料
                } else if("39".equals(detailEntity.getClsProperty().getString("cn_jwis_zldj"))) {
                    return new Pair<>(Lists.newArrayList(), false);
                // 为手动创建成型物料
                } else {
                    JSONObject extensionContent = dbPart.getExtensionContent();
                    String spec = extensionContent != null && extensionContent.containsKey("cn_jwis_gg")
                            ? String.valueOf(extensionContent.get("cn_jwis_gg"))
                            : "";
                    String name = StringUtils.defaultString(dbPart.getName());

                    boolean isScreening = name.contains("筛选级") || spec.contains("筛选级");
                    boolean isFormed = name.contains("已成型") || spec.contains("已成型");

                    if (isScreening || isFormed) {
                        return new Pair<>(tempList, false);
                    }
                    return new Pair<>(Lists.newArrayList(), false);
                }
            }
        }
        return null;
    }


    //筛选包含非筛选  成型包含未成型
    public void initScreen(PartIteration dbPart, ItemReleaseDataDTO partReleaseData) {
        if(autoScreen){
            Pair<List<InstanceEntity>,Boolean> pair = findScreenOrFormingPart(dbPart.getOid());
            if(pair == null) {
                return;
            }
            if(CollectionUtil.isNotEmpty(pair.getFirst())){
                List<BaseEntityDTO> screenList = pair.getFirst().stream().map(item -> BeanUtil.copyProperties(item, new BaseEntityDTO())).collect(Collectors.toList());
                partReleaseData.getClsProperty().fluentPut("cn_jwis_sxzldj", "是");
                partReleaseData.setScreen(screenList);
            }
        } else if(!autoScreen){
            partReleaseData.setScreen(new ArrayList<>());
            List<ModelAble> modelAbleList = commonAbilityService.findMIByTo(dbPart.getMasterOid(),dbPart.getMasterType(), Screen.TYPE);
            if(CollectionUtil.isEmpty(modelAbleList)){
                return;
            }
            for(ModelAble model : modelAbleList){
                BaseEntityDTO baseEntityDTO = BeanUtil.copyProperties(model,new BaseEntityDTO());
                partReleaseData.getScreen().add(baseEntityDTO);
            }
        }
    }

    @Autowired
    private CollectionRuleHelper collectionRuleHelper;

    @Autowired
    private InstanceHelper instanceHelper;

    public List<InstanceEntity> findScreenFormingPart(String partOid){
       return findScreenFormingPart(partOid,null);
    }

    public List<InstanceEntity> findScreenFormingPart(String partOid,Boolean forward){
        CollectionRule collectionRule = collectionRuleHelper.findByAppliedType("JWIGeneralDocument_Related_Object", "JWIRawMaterial")
                .stream().filter(it -> "筛选级（成型）物料".equals(it.getRelationDisplayName())).findFirst().orElse(null);

        return Optional.ofNullable(collectionRule).map(rule -> {
            RelatedFuzzyDTO relatedFuzzyDTO = new RelatedFuzzyDTO();
            relatedFuzzyDTO.setRelationConstraint(RelationConstraint.MM);
            BeanUtils.copyProperties(collectionRule, relatedFuzzyDTO);
            relatedFuzzyDTO.setMainObjectOid(partOid);
            if(forward != null) {
                relatedFuzzyDTO.setForward(forward);
            }
            return Optional.ofNullable(instanceHelper.fuzzyRelated(relatedFuzzyDTO)).orElse(Collections.emptyList());
        }).orElse(Collections.emptyList());
    }

    public void initSubstitute(PartIteration dbPart, ItemReleaseDataDTO partReleaseData) {
        partReleaseData.setSubstitute(new ArrayList<>());
        ModelInfo part = new ModelInfo();
        part.setOid(dbPart.getOid());
        part.setType(dbPart.getType());

        CollectionRule collectionRule = collectionRuleHelper.findByAppliedType("JWIGeneralDocument_Related_Object", "JWIRawMaterial")
                .stream().filter(it -> "全局替代部件".equals(it.getRelationDisplayName())).findFirst().orElse(null);

        List<InstanceEntity> instanceList = Optional.ofNullable(collectionRule).map(rule -> {
            RelatedFuzzyDTO relatedFuzzyDTO = new RelatedFuzzyDTO();
            relatedFuzzyDTO.setRelationConstraint(RelationConstraint.MM);
            BeanUtils.copyProperties(collectionRule, relatedFuzzyDTO);
            relatedFuzzyDTO.setMainObjectOid(dbPart.getOid());

            return Optional.ofNullable(instanceHelper.fuzzyRelated(relatedFuzzyDTO)).orElse(Collections.emptyList());
        }).orElse(Collections.emptyList());

        partReleaseData.setSubstitute(instanceList.stream().map(it -> BeanUtil.copyProperties(it, new BaseEntityDTO())).collect(Collectors.toList()));

//        List<PartIterationWithRelation> results = partService.fuzzyGlobalSubstitute(dbPart.getOid(), null);
//        if(CollectionUtil.isEmpty(results)){
//            return;
//        }

//        List<BaseEntityDTO> substituteList = new ArrayList<>();
//        for(PartIterationWithRelation pi : results){
//            BaseEntityDTO substitute = BeanUtil.copyProperties(pi,new BaseEntityDTO());
//            substituteList.add(substitute);
//        }
//        partReleaseData.setSubstitute(substituteList);
    }



    public void initDescribe(PartIteration dbPart, ItemReleaseDataDTO partReleaseData) {
        partReleaseData.setDescribeDoc(new ArrayList<>());
        ModelInfo part = new ModelInfo();
        part.setOid(dbPart.getOid());
        part.setType(dbPart.getType());
        List<ModelAble> modelAbles = commonAbilityHelper.findBeDescribed(part);
        List<String> docOids = modelAbles.stream().map(doc->doc.getOid()).collect(Collectors.toList());
        // 文档
        modelAbles = commonAbilityService.findDetailEntity(docOids, DocumentIteration.TYPE);
        modelAbles = withMaxVersionConstraint(modelAbles);
        for(ModelAble modelAble : modelAbles){
            BaseEntityDTO dto = BeanUtil.copyProperties(modelAble,new BaseEntityDTO());
            if(targetLifecycleStatus.contains(((DocumentIteration)modelAble).getLifecycleStatus())) {
                partReleaseData.getDescribeDoc().add(dto);
                List<File> files = new ArrayList<>();
                List<File> primarys = ((DocumentIteration)modelAble).getPrimaryFile();
                if(CollectionUtil.isNotEmpty(primarys)){
                    files.addAll(primarys);
                }
                List<File> secondarys = ((DocumentIteration)modelAble).getSecondaryFile();
                if(CollectionUtil.isNotEmpty(secondarys)){
                    files.addAll(secondarys);
                }
                files.stream().forEach(f->{
                    FileMetadata fileMetadata = fileService.findByOid(f.getOid());
                    IntegrationFile file = new IntegrationFile(fileMetadata);
                    dto.getFile().add(file);
                });
            }
        }
        // ECAD
        List<ECADIteration> ecadIterationList = queryECADIterationByPartMasterOid(dbPart.getMasterOid());
        for(ECADIteration modelAble : ecadIterationList){
            BaseEntityDTO dto = BeanUtil.copyProperties(modelAble,new BaseEntityDTO());
            if(targetLifecycleStatus.contains((modelAble).getLifecycleStatus())) {
                partReleaseData.getDescribeDoc().add(dto);
                List<File> files = new ArrayList<>();
                List<File> primarys = (modelAble).getPrimaryFile();
                if(CollectionUtil.isNotEmpty(primarys)){
                    files.addAll(primarys);
                }
                files.stream().forEach(f->{
                    FileMetadata fileMetadata = fileService.findByOid(f.getOid());
                    IntegrationFile file = new IntegrationFile(fileMetadata);
                    dto.getFile().add(file);
                });
            }
        }
    }


    private List<ECADIteration> queryECADIterationByPartMasterOid(String masterOid) {
        Map<String,List<String>> result = customerCommonRepo.findRelationBothSideByTo(ECAD.TYPE,Reference.TYPE,
                Part.TYPE,
                Lists.newArrayList(masterOid));
        if(!result.containsKey(masterOid)) {
            return Lists.newArrayList();
        }
        return jwiCommonService.dynamicQuery(ECADIteration.TYPE,
                Condition.where("masterOid").in(result.get(masterOid)).and(Condition.where("latest").eq(true)),
                ECADIteration.class);
    }

    public <T extends ModelAble> List<T> withMaxVersionConstraint(List<T> rows) {
        if (CollectionUtil.isEmpty(rows)) {
            return rows;
        }
        Map<String, T> record = new LinkedHashMap<>(rows.size());
        String masterOid = null;
        Integer versionSortId = null;
        T holder = null;
        for (T row : rows) {
            masterOid = ((MasterAble)row).getMasterOid();
            if (cn.jwis.framework.base.util.StringUtil.isBlank(masterOid)) {
                record.put(row.getOid(), row);
                continue;
            }
            versionSortId = ((VersionAble)row).getVersionSortId();
            holder = record.get(masterOid);
            if (holder == null || ((VersionAble)holder).getVersionSortId() < versionSortId) {
                record.put(masterOid, row);
            }
        }
        return new ArrayList<>(record.values());
    }

    public void initReference(PartIteration dbPart, ItemReleaseDataDTO partReleaseData) {
        partReleaseData.setReferenceDoc(new ArrayList<>());
        ModelInfo partMaster = new ModelInfo();
        partMaster.setOid(dbPart.getMasterOid());
        partMaster.setType(dbPart.getMasterType());
        List<ModelAble> modelAbles = commonAbilityHelper.findReference(partMaster);
        List<String> docOids = modelAbles.stream().map(doc->doc.getOid()).collect(Collectors.toList());
        modelAbles = commonAbilityService.findDetailEntity(docOids, DocumentIteration.TYPE);
        for(ModelAble modelAble : modelAbles){
            BaseEntityDTO dto = BeanUtil.copyProperties(modelAble,new BaseEntityDTO());
            if(targetLifecycleStatus.contains(((DocumentIteration)modelAble).getLifecycleStatus())) {
                partReleaseData.getReferenceDoc().add(dto);
                List<File> files = new ArrayList<>();
                List<File> primarys = ((DocumentIteration)modelAble).getPrimaryFile();
                if(CollectionUtil.isNotEmpty(primarys)){
                    files.addAll(primarys);
                }
                List<File> secondarys = ((DocumentIteration)modelAble).getSecondaryFile();
                if(CollectionUtil.isNotEmpty(secondarys)){
                    files.addAll(secondarys);
                }
                files.stream().forEach(f->{
                    FileMetadata fileMetadata = fileService.findByOid(f.getOid());
                    IntegrationFile file = new IntegrationFile(fileMetadata);
                    dto.getFile().add(file);
                });
            }
        }

        // ECAD
        modelAbles = commonAbilityService.findDetailEntity(docOids, ECADIteration.TYPE);
        modelAbles = withMaxVersionConstraint(modelAbles);
        for(ModelAble modelAble : modelAbles){
            BaseEntityDTO dto = BeanUtil.copyProperties(modelAble,new BaseEntityDTO());
            if(targetLifecycleStatus.contains(((ECADIteration)modelAble).getLifecycleStatus())) {
                partReleaseData.getReferenceDoc().add(dto);
                List<File> files = new ArrayList<>();
                List<File> primarys = ((ECADIteration)modelAble).getPrimaryFile();
                if(CollectionUtil.isNotEmpty(primarys)){
                    files.addAll(primarys);
                }
                files.stream().forEach(f->{
                    FileMetadata fileMetadata = fileService.findByOid(f.getOid());
                    IntegrationFile file = new IntegrationFile(fileMetadata);
                    dto.getFile().add(file);
                });
            }
        }

    }

    @Override
    public void afterPropertiesSet() throws Exception {
        type = EntityReleaseFactory.BusinessType.item.name();
        EntityReleaseFactory.register(type,this);
    }

    public static String safeGet(JSONObject jsonObject, String key) {
        if (jsonObject != null && jsonObject.containsKey(key)) {
            Object val = jsonObject.get(key);
            return val != null ? val.toString() : "";
        }
        return "";
    }
}
