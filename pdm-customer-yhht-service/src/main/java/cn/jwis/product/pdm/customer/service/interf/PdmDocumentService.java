package cn.jwis.product.pdm.customer.service.interf;

import cn.jwis.platform.plm.foundation.versionrule.able.LockAbleHelper;
import cn.jwis.product.pdm.customer.service.dto.PdmDocBorrowWorkflowDTO;

/**
 * <AUTHOR> lhm
 * @Date ： Created in 2023/9/19 10:35
 * @Description :
 */
public interface PdmDocumentService extends LockAbleHelper {

    boolean docBorrowWorkflow(PdmDocBorrowWorkflowDTO dto);

}
