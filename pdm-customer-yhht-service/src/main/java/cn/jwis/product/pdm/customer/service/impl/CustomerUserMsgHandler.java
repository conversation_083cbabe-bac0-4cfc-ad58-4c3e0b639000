package cn.jwis.product.pdm.customer.service.impl;

import cn.jwis.framework.base.util.Assert;
import cn.jwis.framework.database.core.query.dynamic.Condition;
import cn.jwis.framework.msgqueue.client.message.definition.event.EventDTO;
import cn.jwis.platform.iam.user.User;
import cn.jwis.platform.plm.account.entity.tenant.Tenant;
import cn.jwis.platform.plm.account.org.msg.handler.UserMsgHandler;
import cn.jwis.platform.plm.dataengine.common.JWICommonService;
import cn.jwis.platform.plm.foundation.relationship.Belong;
import cn.jwis.product.pdm.customer.service.interf.CommonService;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2023/9/3 10:56
 * @Email <EMAIL>
 */
@Service
@Primary
@Transactional
@Slf4j
public class CustomerUserMsgHandler extends UserMsgHandler {

    @Autowired
    JWICommonService jwiCommonService;

    @Autowired
    CommonService commonService;

    public void handler(EventDTO eventDTO) {
        super.handler(eventDTO);
        // 增加自动生效功能
        log.info("CustomerUserMsgHandler.handler.user start");
        String type = eventDTO.getType();
        JSONObject data = eventDTO.getData();
        User user = data.toJavaObject(User.class);
        switch (type) {
            case "create":
            case "grant":
                addUse(user.getOid());
                break;
            case "update":
                break;
            case "delete":
                deleteUse(user.getOid());
        }
    }

    private void addUse(String userOid){
        boolean userExist = jwiCommonService.exist(User.TYPE, Condition.where("oid").eq(userOid));
        Assert.isTrue(userExist, "User does not exists");
        jwiCommonService.mergeRelation(new Belong(User.TYPE, userOid, Tenant.TYPE, commonService.getAloneTenantOid()));
    }

    private void deleteUse(String userOid){
        boolean userExist = this.jwiCommonService.exist("User", Condition.where("oid").eq(userOid));
        Assert.isTrue(userExist, "User does not exists");
        jwiCommonService.deleteRelation(new Belong(User.TYPE, userOid, Tenant.TYPE, commonService.getAloneTenantOid()));

    }

}
