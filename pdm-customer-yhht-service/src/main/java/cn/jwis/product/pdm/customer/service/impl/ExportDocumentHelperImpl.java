package cn.jwis.product.pdm.customer.service.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.jwis.audit.annotation.JWIServiceAudit;
import cn.jwis.audit.enums.Action;
import cn.jwis.framework.base.dto.UserDTO;
import cn.jwis.framework.base.exception.JWIException;
import cn.jwis.framework.base.response.PageResult;
import cn.jwis.framework.base.util.CollectionUtil;
import cn.jwis.framework.base.util.ValidationUtil;
import cn.jwis.platform.plm.account.user.service.UserHelper;
import cn.jwis.platform.plm.container.dto.container.FuzzySubPageDTO;
import cn.jwis.platform.plm.container.entity.response.InstanceEntityWithCatalog;
import cn.jwis.platform.plm.container.service.ContainerService;
import cn.jwis.platform.plm.container.service.FolderHelper;
import cn.jwis.platform.plm.datadistribution.annotation.DataDistribution;
import cn.jwis.platform.plm.file.config.MinIoConfig;
import cn.jwis.platform.plm.file.entity.FileMetadata;
import cn.jwis.platform.plm.file.service.FileService;
import cn.jwis.platform.plm.foundation.attachment.entity.File;
import cn.jwis.platform.plm.foundation.common.service.InstanceHelper;
import cn.jwis.platform.plm.foundation.lifecycle.entity.LifecycleStatus;
import cn.jwis.platform.plm.foundation.lifecycle.service.LifecycleStatusHelper;
import cn.jwis.platform.plm.foundation.table.service.TableViewHelper;
import cn.jwis.platform.plm.security.secret.service.SecretHelper;
import cn.jwis.product.pdm.cad.ecad.entity.ECAD;
import cn.jwis.product.pdm.cad.mcad.entity.CADFile;
import cn.jwis.product.pdm.cad.mcad.entity.MCADIteration;
import cn.jwis.product.pdm.cad.mcad.service.MCADHelper;
import cn.jwis.product.pdm.cad.mcad.service.MCADService;
import cn.jwis.product.pdm.customer.service.dto.ExportDocumentDTO;
import cn.jwis.product.pdm.customer.service.dto.ExportExcelDTO;
import cn.jwis.product.pdm.customer.service.dto.FuzzyContainerInstanceExportDTO;
import cn.jwis.product.pdm.customer.service.interf.ExportDocumentHelper;
import cn.jwis.product.pdm.customer.service.util.TimeUtil;
import cn.jwis.product.pdm.document.entity.DocumentIteration;
import cn.jwis.product.pdm.document.repo.DocumentIterationRepoImpl;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.excel.write.style.column.SimpleColumnWidthStyleStrategy;
import com.alibaba.excel.write.style.row.SimpleRowHeightStyleStrategy;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import io.minio.BucketExistsArgs;
import io.minio.MinioClient;
import io.minio.ObjectStat;
import io.minio.StatObjectArgs;
import io.minio.errors.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.nio.file.Files;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.*;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * @author: 汪江
 * @data: 2024-01-08 13:32
 * @description:
 **/
@Service
@Slf4j
public class ExportDocumentHelperImpl implements ExportDocumentHelper {
    @Resource
    private FolderHelper folderHelper;
    @Autowired
    private FileService fileService;
    @Autowired
    private MinIoConfig minIoConfig;
    @Autowired
    private InstanceHelper instanceHelper;

    @Autowired
    private MCADHelper mcadHelper;

    @Autowired
    private MCADService mcadService;


    @Resource
    private SecretHelper secretHelper;

    @Resource
    private LifecycleStatusHelper lifecycleStatusHelper;

    @Resource
    private TableViewHelper tableViewHelper;

    @Resource
    private UserHelper userHelper;
    @Resource
    private ContainerService containerService;
    @Autowired
    DocumentIterationRepoImpl documentIterationRepoImpl;

    @Resource
    private TriggerAuditServiceImpl triggerAuditService;

    private String containerName = "文档信息";
    private static final String COMMA = ",";
    private static final String SEPARATOR = "/";
    private Set<String> addedEntries = new HashSet<>();


    @Override
    public void exportExcel(HttpServletResponse response, FuzzySubPageDTO fuzzySubPageDTO) {
        log.info("接受前端参数为：" + fuzzySubPageDTO.toString());
        ValidationUtil.validate(fuzzySubPageDTO);
        try {
            response.setContentType("application/zip");
            response.setHeader("Content-Disposition", "attachment;fileName=" +
                    new String("文档导出信息.zip".getBytes("gbk"), "iso8859-1"));
            ServletOutputStream os = response.getOutputStream();
            ZipOutputStream zos = new ZipOutputStream(os);
            List<ExportExcelDTO> excelData = getExcelDataOnly(fuzzySubPageDTO, zos);
            log.info("导出的excel信息为" + excelData.toString());
            zos.putNextEntry(new ZipEntry("文档导出信息\\文档信息.xlsx"));
            setExcelDataCustom(zos, excelData);
            log.info("导出成功啦");
        } catch (Exception e) {
            log.error("导出zip出错了:" + e.getMessage(),e);
        }
    }

    @JWIServiceAudit(
            buzOid = "${fuzzySubPageDTO.fromOid}",
            buzType = "${fuzzySubPageDTO.fromType}",
            action = Action.ADD,
            content = "导出产品容器"
    )
    @DataDistribution(
            dataOpt = "create"
    )
    @Override
    public void exportDocument(HttpServletResponse response, FuzzyContainerInstanceExportDTO fuzzySubPageDTO, boolean isExportAll) {
        long threadId = Thread.currentThread().getId();
        log.info("文档导出流水号:{} 接收参数：{}",threadId,JSONObject.toJSONString(fuzzySubPageDTO));
        ValidationUtil.validate(fuzzySubPageDTO);
        java.io.File tempZipFile = null;
        try{
            tempZipFile = Files.createTempFile("temp", ".zip").toFile();
            log.info("文档导出流水号:{} 临时压缩文件位置:{}", threadId, tempZipFile.getAbsolutePath());
            try (FileOutputStream fos = new FileOutputStream(tempZipFile);ZipOutputStream zos = new ZipOutputStream(fos)){
                List<ExportDocumentDTO> excelData = getExcelData(fuzzySubPageDTO, zos,isExportAll);
                log.info("文档导出流水号:{} 导出的excel文档编码信息 : {}",threadId, excelData.stream().map(ExportDocumentDTO::getNumber).collect(Collectors.toList()));
                zos.putNextEntry(new ZipEntry("文档导出信息\\文档信息.xlsx"));
                log.info("文档导出流水号:{} 开始压缩内容Excel",threadId);
                setExcelData(zos, excelData);
                log.info("文档导出流水号:{} 内容Excel压缩完毕",threadId);
                zos.closeEntry();
                zos.finish();
            } catch (Exception e) {
                throw e;
            }
            long zipFileSize = tempZipFile.length();
            log.info("文档导出流水号:{} 压缩文件大小 : {} M",threadId, zipFileSize/1024/1024);
            // 将压缩文件写入响应
            response.setContentLengthLong(zipFileSize);
            response.setContentType("application/zip");
            response.setHeader("Content-Disposition", "attachment;fileName=" +
                    new String("文档导出信息.zip".getBytes("gbk"), "iso8859-1"));
            log.info("文档导出流水号:{} 压缩文件处理完毕开始进行下载",threadId);
            if(tempZipFile.exists()) {
                try (InputStream fis = new FileInputStream(tempZipFile)) {
                    IOUtils.copy(fis,response.getOutputStream());
                }
            } else {
                log.info("文档导出流水号:{} 压缩生成压缩文件不存在:{}",threadId,tempZipFile.getName());
            }
        } catch (Exception e) {
            throw new JWIException(e);
        } finally {
            if (tempZipFile != null && tempZipFile.exists()) {
                tempZipFile.delete();
            }
        }
    }


    public void setExcelDataCustom(ZipOutputStream zos, List<ExportExcelDTO> excelData) {
        try {
            WriteCellStyle headWriteCellStyle = new WriteCellStyle();
            headWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
            headWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            HorizontalCellStyleStrategy strategy = new HorizontalCellStyleStrategy(headWriteCellStyle, headWriteCellStyle);
            ExcelWriter writer = EasyExcel.write(zos).inMemory(true)
                    .useDefaultStyle(true).relativeHeadRowIndex(0)
                    .registerWriteHandler(new SimpleColumnWidthStyleStrategy(30))
                    .registerWriteHandler(new SimpleRowHeightStyleStrategy((short) 25, (short) 15))
                    .registerWriteHandler(strategy)
                    .build();

            WriteSheet writeSheet = EasyExcel.writerSheet(containerName).head(ExportDocumentDTO.class).build();
            writer.write(excelData, writeSheet);
            writer.finish();
        } catch (Exception e) {
            // 处理异常
            e.printStackTrace();
        }
    }

    public void setExcelData(ZipOutputStream zos, List<ExportDocumentDTO> excelData) {
        try {
            WriteCellStyle headWriteCellStyle = new WriteCellStyle();
            headWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
            headWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            HorizontalCellStyleStrategy strategy = new HorizontalCellStyleStrategy(headWriteCellStyle, headWriteCellStyle);
            ExcelWriter writer = EasyExcel.write(zos).autoCloseStream(false)
                    .useDefaultStyle(true).relativeHeadRowIndex(0)
                    .registerWriteHandler(new SimpleColumnWidthStyleStrategy(30))
                    .registerWriteHandler(new SimpleRowHeightStyleStrategy((short) 25, (short) 15))
                    .registerWriteHandler(strategy)
                    .build();

            WriteSheet writeSheet = EasyExcel.writerSheet(containerName).head(ExportDocumentDTO.class).build();
            writer.write(excelData, writeSheet);
            writer.finish();
        } catch (Exception e) {
            log.info(e.getMessage(),e);
            // 处理异常
            throw new JWIException(e);
        }
    }

    private List<ExportDocumentDTO> getExcelData(FuzzyContainerInstanceExportDTO fuzzySubPageDTO, ZipOutputStream zos, boolean isExportAll) throws Exception {
        List<ExportDocumentDTO> excelData = new ArrayList<>();
        ExportDocumentDTO row1 = new ExportDocumentDTO();
        row1.setNumber("number");
        row1.setName("name");
        row1.setVersion("version");
        row1.setIteratedVersion("iteratedVersion");
        row1.setModelDefinition("modelDefinition");
        row1.setClassificationInfo("classificationInfo");
        row1.setLocationInfo("locationInfo");
        row1.setPrimaryFile("primaryFile");
        row1.setSecondaryFile("secondaryFile");
        row1.setDescription("description");
        row1.setYzjd("cn_jwis_yzjd");
        row1.setSecrecy("cn_jwis_secrecy");
        row1.setSource("source");
        row1.setLifecycleStatus("lifecycleStatus");

        row1.setCreateDate("createDate");
        row1.setCreateBy("createBy");
        row1.setUpdateDate("updateDate");
        row1.setUpdateBy("updateBy");
        row1.setStorageTime("storageTime");

        excelData.add(row1);
        // 查询出来的数据
        PageResult<InstanceEntityWithCatalog> pageResult = this.folderHelper.fuzzySubPage(fuzzySubPageDTO);

        //附件容器
        Set<String> addedEntries = new HashSet<>();

        //下拉值处理
        Map<String, Map> selectOptions = this.getSelectOptions();

        Map<String, String> modelDefinedType = tableViewHelper.findAllDataI18n();
        for (InstanceEntityWithCatalog row : pageResult.getRows()) {
            ExportDocumentDTO exportDocumentDTO = setExportDocumentValues(row, zos,addedEntries,selectOptions, modelDefinedType,isExportAll);
            excelData.add(exportDocumentDTO);
        }
        return excelData;
    }

    private List<ExportExcelDTO> getExcelDataOnly(FuzzySubPageDTO fuzzySubPageDTO, ZipOutputStream zos) throws Exception {
        List<ExportExcelDTO> excelData = new ArrayList<>();
        ExportExcelDTO row1 = new ExportExcelDTO();
        row1.setNumber("number");
        row1.setName("name");
        row1.setVersion("version");
        row1.setIteratedVersion("iteratedVersion");
        row1.setModelDefinition("modelDefinition");
        row1.setClassificationInfo("classificationInfo");
        row1.setLocationInfo("locationInfo");
        row1.setPrimaryFile("primaryFile");
        row1.setSecondaryFile("secondaryFile");
        row1.setDescription("description");
        row1.setYzjd("cn_jwis_yzjd");
        row1.setSecrecy("cn_jwis_secrecy");
        row1.setSource("source");
        row1.setLifecycleStatus("lifecycleStatus");
        row1.setLifecycleStatus("lifecycleStatus");
        row1.setUpdateTime("updateTime");
        row1.setOwner("owner");

        excelData.add(row1);
        // 查询出来的数据
        PageResult<InstanceEntityWithCatalog> pageResult = this.folderHelper.fuzzySubPage(fuzzySubPageDTO);

        //附件容器
        Set<String> addedEntries = new HashSet<>();

        //下拉值处理
        Map<String, Map> selectOptions = this.getSelectOptions();

        Map<String, String> modelDefinedType = tableViewHelper.findAllDataI18n();
        for (InstanceEntityWithCatalog row : pageResult.getRows()) {
            ExportExcelDTO exportExcelDTO = setExportExcelValues(row, zos,addedEntries,selectOptions, modelDefinedType);
            excelData.add(exportExcelDTO);
        }
        return excelData;
    }

    private Map<String,Map> getSelectOptions(){
        Map<String,Map> keyToOptions=new HashMap<>();

        // 生命周期
        List<LifecycleStatus> statusList = this.lifecycleStatusHelper.fuzzy("", (String) null, (String) null);
        Map<String, String> statusMap = statusList.stream().collect(Collectors.toMap(t -> t.getCode(), t -> t.getDisplayName(), (o1, o2) -> {
            return o2;
        }));

        keyToOptions.put("lifecycleStatus",statusMap);

        return keyToOptions;
    }

    private ExportExcelDTO setExportExcelValues(InstanceEntityWithCatalog row, ZipOutputStream zos, Set<String> addedEntries, Map<String, Map> selectOptions, Map<String, String> modelDefinedType) throws Exception {
        ExportExcelDTO exportDocumentDTO = new ExportExcelDTO();

        JSONObject documentInfo = this.instanceHelper.findDetailWithContainer(row.getOid(), row.getType());
        // 给exportDocumentDTO值赋
        exportDocumentDTO.setNumber(row.getNumber());
        exportDocumentDTO.setName(row.getName());
        exportDocumentDTO.setVersion(row.getVersion());
        exportDocumentDTO.setIteratedVersion(String.valueOf(row.getIteratedVersion()));
        exportDocumentDTO.setModelDefinition(modelDefinedType.get(row.getModelDefinition()));
        exportDocumentDTO.setClassificationInfo(documentInfo.getString("clsDisplayName"));
        exportDocumentDTO.setDescription(row.getDescription());

        String owner = row.getOwner();
        if (ObjectUtil.isNotEmpty(owner)) {
            UserDTO user = userHelper.findByAccount(owner);
            exportDocumentDTO.setOwner(null == user ? owner : user.getName());
        }
        long updateDate = row.getUpdateDate();
        String updateTime = "";
        String docOid = row.getOid();
        DocumentIteration byOid = this.documentIterationRepoImpl.findByOid(docOid);
        if (null != byOid.getExtensionContent()) {
            JSONObject extensionContent = byOid.getExtensionContent();
            if(extensionContent != null) {
                updateTime = extensionContent.getString("storageTime");
            }
        }
        if (ObjectUtil.isNotEmpty(updateTime)) {
            DateTime date = DateUtil.date(Long.valueOf(updateTime));
            updateTime = DateUtil.format(date, DatePattern.NORM_DATETIME_PATTERN);
        }
        exportDocumentDTO.setUpdateTime(updateTime);


        //处理下拉值
        String lifecycleStatus = row.getLifecycleStatus();
        if(ObjectUtils.isNotEmpty(lifecycleStatus)){
            Map map = selectOptions.get("lifecycleStatus");
            exportDocumentDTO.setLifecycleStatus(Optional.ofNullable(map.get(lifecycleStatus)).orElse(lifecycleStatus).toString());
        }

        // 获取上下文信息
        StringBuilder locationInfo = new StringBuilder();
        JSONArray containerInfo = documentInfo.getJSONArray("containerInfo");
        if (containerInfo != null) {
            for (int i = 0; i < containerInfo.size(); i++) {
                Object item = containerInfo.get(i);
                if (item instanceof JSONObject) {
                    JSONObject info = (JSONObject) item;
                    locationInfo.append(SEPARATOR).append(info.getString("name"));
                    if (i == 0) {
                        String name = info.getString("name");
                        containerName = name;
                    }
                }
            }
        }
        exportDocumentDTO.setLocationInfo(locationInfo.toString());



        // 拓展属性
        JSONObject extensionContent = documentInfo.getJSONObject("extensionContent");
        if (extensionContent != null) {
            exportDocumentDTO.setYzjd(extensionContent.getString("cn_jwis_yzjd"));
            exportDocumentDTO.setSecrecy(extensionContent.getString("cn_jwis_secrecy"));
            exportDocumentDTO.setSource(extensionContent.getString("source"));
        }
        return exportDocumentDTO;
    }


    private ExportDocumentDTO setExportDocumentValues(InstanceEntityWithCatalog row, ZipOutputStream zos, Set<String> addedEntries, Map<String, Map> selectOptions, Map<String, String> modelDefinedType, boolean isExportAll) throws Exception {
        ExportDocumentDTO exportDocumentDTO = new ExportDocumentDTO();

        JSONObject documentInfo = this.instanceHelper.findDetailWithContainer(row.getOid(), row.getType());
        // 给exportDocumentDTO值赋
        exportDocumentDTO.setNumber(row.getNumber());
        exportDocumentDTO.setName(row.getName());
        exportDocumentDTO.setVersion(row.getVersion());
        exportDocumentDTO.setIteratedVersion(String.valueOf(row.getIteratedVersion()));
        exportDocumentDTO.setModelDefinition(modelDefinedType.get(row.getModelDefinition()));
        exportDocumentDTO.setClassificationInfo(documentInfo.getString("clsDisplayName"));
        exportDocumentDTO.setDescription(row.getDescription());
        exportDocumentDTO.setCreateDate(TimeUtil.formatTimestamp(row.getCreateDate()));
        exportDocumentDTO.setCreateBy(row.getCreateBy());
        exportDocumentDTO.setUpdateDate(TimeUtil.formatTimestamp(row.getUpdateDate()));
        exportDocumentDTO.setUpdateBy(row.getUpdateBy());

        //处理下拉值
        String lifecycleStatus = row.getLifecycleStatus();
        if(ObjectUtils.isNotEmpty(lifecycleStatus)){
            Map map = selectOptions.get("lifecycleStatus");
            exportDocumentDTO.setLifecycleStatus(Optional.ofNullable(map.get(lifecycleStatus)).orElse(lifecycleStatus).toString());
        }

        // 获取上下文信息
        StringBuilder locationInfo = new StringBuilder();
        JSONArray containerInfo = documentInfo.getJSONArray("containerInfo");
        if (containerInfo != null) {
            for (int i = 0; i < containerInfo.size(); i++) {
                Object item = containerInfo.get(i);
                if (item instanceof JSONObject) {
                    JSONObject info = (JSONObject) item;
                    locationInfo.append(SEPARATOR).append(info.getString("name"));
                    if (i == 0) {
                        String name = info.getString("name");
                        containerName = name;
                    }
                }
            }
        }
        exportDocumentDTO.setLocationInfo(locationInfo.toString());

        log.info("是否导出全部文件信息----->>>>{}", isExportAll);
        if (isExportAll) {
            // 获取主文件信息
            List<File> primaryFiles = row.getPrimaryFile();
            if(CollectionUtil.isNotEmpty(primaryFiles)){
                String primaryFilePath = this.exportFileToExcel(primaryFiles, zos, addedEntries,row);
                exportDocumentDTO.setPrimaryFile(primaryFilePath);
            }

            // 获取附件信息
            List<File> secondaryFiles = row.getSecondaryFile();
            if(CollectionUtil.isNotEmpty(secondaryFiles)){
                String secondaryFilePath = this.exportFileToExcel(secondaryFiles, zos, addedEntries, row);
                exportDocumentDTO.setSecondaryFile(secondaryFilePath);
            }

            //MCAD类型处理文件信息
            if(row.getType().equals(MCADIteration.TYPE)){
//            List<CADFile> cadFile = mcadHelper.findCADFile(row.getOid());
//            if(CollectionUtil.isNotEmpty(cadFile)&&cadFile.stream().anyMatch(t->!t.isPrimary())){
//                List<File> secondaryFileMcad = cadFile.stream().filter(t->!t.isPrimary()).map(t->{
//                    File file = new File();
//                    file.setName(t.getFileName());
//                    file.setOid(t.getUrl());
//                    return file;
//                }).collect(Collectors.toList());
//
//                String secondaryFilePath = this.exportFileToExcel(secondaryFileMcad, zos, addedEntries);
//                exportDocumentDTO.setSecondaryFile(secondaryFilePath);
//            }

                String primaryFilePath= this.mcadFilesTOZip(row, zos, addedEntries);
                if(StringUtils.isNotEmpty(primaryFilePath)){
                    exportDocumentDTO.setPrimaryFile(primaryFilePath);
                }
            }
        }

        // 拓展属性
        JSONObject extensionContent = documentInfo.getJSONObject("extensionContent");
        if (extensionContent != null) {
            exportDocumentDTO.setYzjd(extensionContent.getString("cn_jwis_yzjd"));
            exportDocumentDTO.setSecrecy(extensionContent.getString("cn_jwis_secrecy"));
            exportDocumentDTO.setSource(extensionContent.getString("source"));
            exportDocumentDTO.setStorageTime(TimeUtil.formatTimestamp(extensionContent.getString("storageTime")));
            exportDocumentDTO.setProcessBusinessId(extensionContent.getString("processBusinessId"));
        }
        return exportDocumentDTO;
    }

    private String mcadFilesTOZip(InstanceEntityWithCatalog row,ZipOutputStream zos,Set<String> addedEntries) throws Exception{
        String filePath="";

        List<CADFile> slddrwFiles = this.mcadService.findCADFileSLDDRWList(row.getOid());
        if(CollectionUtil.isNotEmpty(slddrwFiles) ){
            String zipName=row.getName()+".zip";

            if (!addedEntries.contains(zipName)) {
                zos.putNextEntry(new ZipEntry("文档导出信息\\附件\\" + zipName));
                addedEntries.add(zipName);

                ByteArrayOutputStream bos = new ByteArrayOutputStream();
                ZipOutputStream zosSub = null;

                zosSub = new ZipOutputStream(bos);


                try {
                    for (CADFile slddrwFile : slddrwFiles) {
                        FileMetadata fileMetadata = this.fileService.findByOid(slddrwFile.getUrl());

                        if (fileMetadata != null) {
                            String fileName = fileMetadata.getFileName();
                            String fileOriginalName = fileMetadata.getFileOriginalName();
                            String bucketName = fileMetadata.getBucketName();
                            MinioClient minioClient = getObjectSite(bucketName, fileName);
                            if (minioClient != null) {
                                InputStream fileInputStream = minioClient.getObject(bucketName, fileName);
                                if (StringUtils.isNotEmpty(fileOriginalName)) {
                                    fileName = fileOriginalName;
                                }

                                    zosSub.putNextEntry(new ZipEntry(fileName));

                                    //使用字节缓冲输入流
                                    BufferedInputStream bis = new BufferedInputStream(fileInputStream);
                                    int len;
                                    byte[] buf = new byte[1024 * 2];
                                    while ((len = bis.read(buf)) != -1) {
                                        zosSub.write(buf, 0, len);
                                    }
                                    zosSub.closeEntry();
                                }
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                } finally {
                    zosSub.close();
                    bos.close();
                }

                zos.write(bos.toByteArray());

            }

            filePath = "附件" + SEPARATOR + zipName;
        }
        return filePath;
    }




    private String exportFileToExcel(List<File> files, ZipOutputStream zos, Set<String> addedEntries, InstanceEntityWithCatalog row) throws Exception {
        StringBuilder fileExcelVal = new StringBuilder();
        if (CollectionUtil.isNotEmpty(files)) {

            for (File file : files) {
                if (fileExcelVal.length() > 0) {
                    fileExcelVal.append(COMMA).append("附件" + SEPARATOR + this.setFileExcelVal(file, zos,addedEntries,row));
                } else {
                    fileExcelVal.append("附件" + SEPARATOR + this.setFileExcelVal(file, zos,addedEntries, row));
                }
            }
        }
        return fileExcelVal.toString();
    }

    private String setFileExcelVal(File fileInfo, ZipOutputStream zos, Set<String> addedEntries, InstanceEntityWithCatalog row) throws Exception {
        long threadId = Thread.currentThread().getId();
        String oid = fileInfo.getOid();
        FileMetadata fileMetadata = this.fileService.findByOid(oid);
        if (fileMetadata != null) {
            String fileName = fileMetadata.getFileName(); // 获取存储中的文件名
            String fileOriginalName = fileMetadata.getFileOriginalName(); // 获取原始文件名
            String bucketName = fileMetadata.getBucketName();
            MinioClient minioClient = getObjectSite(bucketName, fileName);
            if (minioClient != null) {
                try (InputStream inputStream = minioClient.getObject(bucketName, fileName);
                     BufferedInputStream bis = new BufferedInputStream(inputStream)) {
                    if (StringUtils.isNotEmpty(fileOriginalName)) {
                        fileName = fileOriginalName; // 如果有原始文件名，则使用原始文件名
                    }
                    String number = "";
                    String name = "";
                    String version = "";
                    String fileExtension = ""; // 文件后缀

                    // 提取文件后缀
                    if (StringUtils.isNotEmpty(fileOriginalName)) {
                        int lastDotIndex = fileOriginalName.lastIndexOf(".");
                        if (lastDotIndex != -1) {
                            fileExtension = fileOriginalName.substring(lastDotIndex); // 包括点号在内的后缀
                        }
                    }

                    // 拼接文件名前缀
                    if (row != null) {
                        number = row.getNumber();
                        name = row.getName();
                        version = row.getVersion();
                    }

                    // 拼接完整文件名
                    fileName = number + "_" + name + "_" + version + fileExtension;

                    if (!addedEntries.contains(fileName)) {
                        zos.putNextEntry(new ZipEntry("文档导出信息\\附件\\" + fileName)); // 在 ZIP 中创建新条目
                        addedEntries.add(fileName); // 记录已添加的文件
                        log.info("文档导出流水号:{} 开始压缩文件:{}", threadId, fileName);

                        // 使用缓冲区写入文件内容到 ZIP
                        int len;
                        byte[] buf = new byte[1024 * 2];
                        while ((len = bis.read(buf)) != -1) {
                            zos.write(buf, 0, len);
                        }
                        zos.closeEntry(); // 关闭 ZIP 条目
                        log.info("文档导出流水号:{} 文件压缩完毕:{}", threadId, fileName);
                    }
                } catch (Exception e) {
                    throw new JWIException(e);
                }
            }
            return fileName; // 返回最终使用的文件名
        }
        return null; // 如果文件元数据为空，返回 null
    }


    private ExportDocumentDTO setExportDocumentValuesOld(InstanceEntityWithCatalog row, ZipOutputStream zos, Set<String> addedEntries) throws Exception {
        ExportDocumentDTO exportDocumentDTO = new ExportDocumentDTO();

        JSONObject documentInfo = this.instanceHelper.findDetailWithContainer(row.getOid(), row.getType());
        // 给exportDocumentDTO值赋
        exportDocumentDTO.setNumber(row.getNumber());
        exportDocumentDTO.setName(row.getName());
        exportDocumentDTO.setVersion(row.getVersion());
        exportDocumentDTO.setIteratedVersion(String.valueOf(row.getIteratedVersion()));
        exportDocumentDTO.setModelDefinition(row.getType());
        exportDocumentDTO.setClassificationInfo(documentInfo.getString("clsDisplayName"));
        exportDocumentDTO.setDescription(row.getDescription());
        exportDocumentDTO.setLifecycleStatus(row.getLifecycleStatus());

        // 获取上下文信息
        StringBuilder locationInfo = new StringBuilder();
        JSONArray containerInfo = documentInfo.getJSONArray("containerInfo");
        if (containerInfo != null) {
            for (int i = 0; i < containerInfo.size(); i++) {
                Object item = containerInfo.get(i);
                if (item instanceof JSONObject) {
                    JSONObject info = (JSONObject) item;
                    locationInfo.append(SEPARATOR).append(info.getString("name"));
                    if (i == 0) {
                        String name = info.getString("name");
                        containerName = name;
                    }
                }
            }
        }
        exportDocumentDTO.setLocationInfo(locationInfo.toString());

        // 获取主要文件信息
        StringBuilder primaryFile = new StringBuilder();
        JSONArray primaryFiles = documentInfo.getJSONArray("primaryFile");
        if (primaryFiles != null) {
            for (Object primaryFileObj : primaryFiles) {
                if (primaryFile.length() > 0) {
                    primaryFile.append(COMMA).append("附件" + SEPARATOR + setAttachmentFile(primaryFileObj, zos,addedEntries));
                } else {
                    primaryFile.append("附件" + SEPARATOR + setAttachmentFile(primaryFileObj, zos,addedEntries));
                }
            }
        }


        // 获取附件信息
        StringBuilder secondaryFile = new StringBuilder();
        JSONArray secondaryFiles = documentInfo.getJSONArray("secondaryFile");
        if (secondaryFiles != null) {
            for (Object secondaryFileObj : secondaryFiles) {
                if (secondaryFile.length() > 0) {
                    secondaryFile.append(COMMA).append("附件" + SEPARATOR + setAttachmentFile(secondaryFileObj, zos,addedEntries));
                } else {
                    secondaryFile.append("附件" + SEPARATOR + setAttachmentFile(secondaryFileObj, zos,addedEntries));
                }
            }
        }
        exportDocumentDTO.setSecondaryFile(secondaryFile.toString());

        //MCAD类型处理文件信息
        if(row.getType().equals(ECAD.TYPE)){
            List<CADFile> cadFile = mcadHelper.findCADFile(row.getOid());
            if(CollectionUtil.isNotEmpty(cadFile)){
                List<File> primaryFileMcad = cadFile.stream().filter(t->t.isPrimary()).map(t->{
                    File file = new File();
                    file.setName(t.getFileName());
                    file.setOid(t.getOid());
                    return file;
                }).collect(Collectors.toList());
                String primaryFilePath = this.exportFileToExcel(primaryFileMcad, zos, addedEntries, row);
                exportDocumentDTO.setPrimaryFile(primaryFilePath);
                List<File> secondaryFileMcad = cadFile.stream().filter(t->!t.isPrimary()).map(t->{
                    File file = new File();
                    file.setName(t.getFileName());
                    file.setOid(t.getOid());
                    return file;
                }).collect(Collectors.toList());
                String secondaryFilePath = this.exportFileToExcel(secondaryFileMcad, zos, addedEntries, row);
                exportDocumentDTO.setSecondaryFile(secondaryFilePath);
            }
        }


        // 拓展属性
        JSONObject extensionContent = documentInfo.getJSONObject("extensionContent");
        if (extensionContent != null) {
            exportDocumentDTO.setYzjd(extensionContent.getString("cn_jwis_yzjd"));
            exportDocumentDTO.setSecrecy(extensionContent.getString("cn_jwis_secrecy"));
            exportDocumentDTO.setSource(extensionContent.getString("source"));
        }
        return exportDocumentDTO;
    }



    private String setAttachmentFile(Object fileInfo, ZipOutputStream zos,Set<String> addedEntries) throws Exception {
        if (fileInfo instanceof JSONObject) {
            JSONObject fileJSON = (JSONObject) fileInfo;
            String oid = fileJSON.getString("oid");
            FileMetadata fileMetadata = this.fileService.findByOid(oid);
            if (fileMetadata != null) {
                String fileName = fileMetadata.getFileName();
                String fileOriginalName = fileMetadata.getFileOriginalName();
                String bucketName = fileMetadata.getBucketName();
                MinioClient minioClient = getObjectSite(bucketName, fileName);
                if (minioClient != null) {
                    InputStream fileInputStream = minioClient.getObject(bucketName, fileName);
                    if (StringUtils.isNotEmpty(fileOriginalName)) {
                        fileName = fileOriginalName;
                    }
                    if (!addedEntries.contains(fileName)) {
                        zos.putNextEntry(new ZipEntry("文档导出信息\\附件\\" + fileName));
                        addedEntries.add(fileName);
                        //使用字节缓冲输入流
                        BufferedInputStream bis = new BufferedInputStream(fileInputStream);
                        int len;
                        byte[] buf = new byte[1024 * 2];
                        while ((len = bis.read(buf)) != -1) {
                            zos.write(buf, 0, len);
                        }
                        zos.closeEntry();
                    }
                }
                return fileName;
            }
        }
        return null;
    }

    public MinioClient getObjectSite(String bucketName, String objectName) throws IOException, InvalidResponseException, InvalidKeyException, NoSuchAlgorithmException, ServerException, ErrorResponseException, XmlParserException, InvalidBucketNameException, InsufficientDataException, InternalException {
        ObjectStat statObject = null;
        log.info("getObjectSite.bucketName=" + bucketName + ",objectName=" + objectName);
        log.info("getObjectSite.minIoConfig.getMinioClient.size=" + JSONObject.toJSONString(this.minIoConfig.getMinioClient().size()));

        for (int i = 0; i < this.minIoConfig.getMinioClient().size(); ++i) {
            MinioClient minioClient = (MinioClient) this.minIoConfig.getMinioClient().get(i);
            boolean flag = minioClient.bucketExists((BucketExistsArgs) ((BucketExistsArgs.Builder) BucketExistsArgs.builder().bucket(bucketName)).build());
            if (flag) {
                try {
                    statObject = minioClient.statObject((StatObjectArgs) ((io.minio.StatObjectArgs.Builder) ((io.minio.StatObjectArgs.Builder) StatObjectArgs.builder().bucket(bucketName)).object(objectName)).build());
                } catch (Exception var8) {
                    continue;
                }

                if (statObject != null) {
                    log.info("getObjectSite.minIoConfig.target=" + minioClient.getObjectUrl(bucketName, objectName));
                    return minioClient;
                }
            }
        }
        log.info("getObjectSite.minIoConfig.target is null!");
        return null;
    }
}
