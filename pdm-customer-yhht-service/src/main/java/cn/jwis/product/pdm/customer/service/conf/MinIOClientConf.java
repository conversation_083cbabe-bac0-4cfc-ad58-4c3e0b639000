package cn.jwis.product.pdm.customer.service.conf;

import io.minio.MinioClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class MinIOClientConf {

    @Value("${cluster.minio.list[0].endpoint}")
    private String endpoint;

    @Value("${cluster.minio.list[0].accessKey}")
    private String accessKey;

    @Value("${cluster.minio.list[0].secretKey}")
    private String secretKey;

    @Bean
    public MinioClient minioClient(){
        return MinioClient.builder().endpoint(endpoint).credentials(accessKey, secretKey).build();
    }

}
