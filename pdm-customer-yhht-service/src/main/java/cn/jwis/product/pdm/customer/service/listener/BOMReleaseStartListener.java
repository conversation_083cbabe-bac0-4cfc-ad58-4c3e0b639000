package cn.jwis.product.pdm.customer.service.listener;

import cn.hutool.json.JSONUtil;
import cn.jwis.framework.base.annotation.Description;
import cn.jwis.framework.base.dto.UserDTO;
import cn.jwis.framework.base.exception.JWIServiceException;
import cn.jwis.framework.base.util.CollectionUtil;
import cn.jwis.framework.base.util.MapUtil;
import cn.jwis.framework.base.util.SpringContextUtil;
import cn.jwis.framework.base.web.session.SessionHelper;
import cn.jwis.platform.plm.foundation.common.response.InstanceEntity;
import cn.jwis.platform.plm.workflow.engine.dto.ProcessOrderDetail;
import cn.jwis.platform.plm.workflow.engine.order.entity.ProcessOrder;
import cn.jwis.platform.plm.workflow.engine.order.response.TeamRoleWithUser;
import cn.jwis.platform.plm.workflow.engine.service.interf.ProcessOrderHelper;
import cn.jwis.product.pdm.customer.service.interf.DingTalkService;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.ExecutionListener;

import java.util.List;


@Description("开始节点发布钉钉任务监听器")
@Slf4j
public class BOMReleaseStartListener implements ExecutionListener {

    private void doNotify(int times, JSONObject delegateTask) {
        // 尝试 5 次
        if (times == 5) {
            log.info("JWIDingTalkTaskListener failed");
            throw new JWIServiceException("JWIDingTalkTaskListener failed: System busy");
        }
        ProcessOrder processOrder = getProcessOrder(delegateTask);
        if (processOrder == null){
            try {
                Thread.sleep(500);
                times++;
                doNotify(times, delegateTask);
                return ;
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
        String oid = processOrder.getOid();
        ProcessOrderHelper processOrderHelper = SpringContextUtil.getBean(ProcessOrderHelper.class);
        ProcessOrderDetail detail = processOrderHelper.findDetail(oid);
        log.info("detail: {}", JSONUtil.parseObj(detail));

        // 获取 需要授权的对象
        List<InstanceEntity> instanceList = detail.getBizObjects();
        if (CollectionUtil.isEmpty(instanceList)){
            return ;
        }
        log.info("YHHTDingTalkTaskListener.processOrder---->>>{}", JSONUtil.toJsonStr(processOrder));
        log.info("YHHTDingTalkTaskListener.instanceList---->>>{}", JSONUtil.toJsonStr(instanceList));
        DingTalkService dingTalkService = (DingTalkService) SpringContextUtil.getBean("dingTalkServiceImpl");
        // 开始根据事件类型，创建/删除权限
        log.info("YHHTDingTalkTaskListener.delegateTask---->>>{}", JSONObject.toJSONString(delegateTask));
        //发起钉钉 图纸/BOM归档流程
        Boolean isTaskSent = delegateTask.getBooleanValue("isTaskSent");
        log.info("YHHTDingTalkTaskListener.isTaskSent---->>>{}", isTaskSent);
        if (isTaskSent == null || !isTaskSent) {
            try {
                log.info("YHHTDingTalkTaskListener.发起钉钉流程入参 detail---->>>{}", JSONObject.toJSONString(detail));
                dingTalkService.createSendTaskForBomRelease(detail);
                delegateTask.put("isTaskSent", true); // 标记任务已发起
            } catch (Exception e) {
                log.error("YHHTDingTalkTaskListener.delegateTask error", e);
//                throw new JWIServiceException("JWIDingTalkTaskListener failed: 发起钉钉流程失败");
                throw new JWIServiceException("发起钉钉流程失败: " + e.getMessage());
            }
        }

    }

    private ProcessOrder getProcessOrder(JSONObject delegateTask) {
        ProcessOrder processOrder = null;
        JSONObject variableInstances = delegateTask.getJSONObject("variableInstances");
        ProcessOrderHelper processOrderHelper = (ProcessOrderHelper) SpringContextUtil.getBean("processOrderHelperImpl");
        if (MapUtil.isEmpty(variableInstances)){
            String processInstanceId = delegateTask.getString("processInstanceId");
            processOrder = processOrderHelper.findByProcessInstanceId(processInstanceId);
        }else {
            JSONObject processOrderVar = variableInstances.getJSONObject("processOrderOid");
            if (MapUtil.isEmpty(processOrderVar)){
                String processInstanceId = delegateTask.getString("processInstanceId");
                processOrder = processOrderHelper.findByProcessInstanceId(processInstanceId);
            }else {
                String processOrderOid = processOrderVar.getString("value");
                processOrder = processOrderHelper.findByOid(processOrderOid);
            }
        }
        return processOrder;
    }

    /**
     * @Description  获取评审对象
     * @Auther hsy
     */
    private List<InstanceEntity> getInstanceList(ProcessOrder processOrder) {
        ProcessOrderHelper processOrderHelper = SpringContextUtil.getBean(ProcessOrderHelper.class);
        return processOrderHelper.findBizObject(processOrder.getOid());
    }

    @Override
    public void notify(DelegateExecution execution) {
        final UserDTO currentUser = SessionHelper.getCurrentUser();
        final String accessToken = SessionHelper.getAccessToken();
        final String appId = SessionHelper.getAppId();
        final JSONObject finalDelegateTask = JSONObject.parseObject(JSONObject.toJSONString(execution));
        SessionHelper.addCurrentUser(currentUser);
        SessionHelper.setAccessToken(accessToken);
        SessionHelper.setAppId(appId);
        doNotify(0, finalDelegateTask);
    }
}
