package cn.jwis.product.pdm.customer.service.interf;

import cn.jwis.platform.plm.foundation.common.response.InstanceEntity;
import cn.jwis.platform.plm.workflow.engine.dto.ProOrdCreateDTO;
import cn.jwis.platform.plm.workflow.engine.dto.ProcessOrderDetail;
import cn.jwis.platform.plm.workflow.engine.order.entity.ProcessOrder;
import cn.jwis.product.pdm.customer.entity.DingTaskRecord;
import cn.jwis.product.pdm.customer.entity.PermApplyEntity;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.dingtalkworkflow_1_0.models.GetProcessInstanceResponseBody;
import com.dingtalk.api.response.OapiV2UserGetResponse;
import com.taobao.api.ApiException;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:  PDM任务和钉钉任务协同的服务。
 * @date 2023/8/10 13:37
 * @Email <EMAIL>
 */
public interface DingTalkService {

    // 钉钉完成任务之后，自动完成PDM的任务
    boolean autoCompleteTask(JSONObject bizData);

    // PDM生成任务后，给钉钉同步创建任务，并生成任务记录
    String createTask(JSONObject delegateTask,ProcessOrder processOrder, List<InstanceEntity> instanceList);

    String createSendTask(JSONObject dto) throws Exception;

    String createDingTaskForFileOutGoing(JSONObject dto) throws Exception;

    void startMaterial(ProOrdCreateDTO dto) throws Exception;

    //PDM启动流程后，审批人审批通过同时通知钉钉对应流程任务审核通过
    String createTaskComplete(JSONObject delegateTask,ProcessOrder processOrder, List<InstanceEntity> instanceList);

    // PDM流程走完时，给钉钉发布一个通知，这个通知以任务的形式承载
    void createNoticeTask(Collection<String> notifiers,ProcessOrder processOrder, List<? extends InstanceEntity> instanceList) ;

    // PDM流程走完时，给钉钉发布一个通知，这个通知以任务的形式承载(自定义通知主题)
    void createNoticeTask(Collection<String> notifiers,ProcessOrder processOrder, List<? extends InstanceEntity> instanceList,String subject);

    void createOutgoingNoticeTask(Collection<String> notifiers, ProcessOrder processOrder, List<InstanceEntity> instanceList);

    // U9/SMIS消费完MQ的数据，回写到PDM时，如果是消费失败，则发布一个钉钉通知，这个通知以任务的形式承载
    void createNoticeTask(String number,String type,String owner, String errorMsg);

    // PDM完成任务后，给钉钉同步完成任务，并修改任务记录
    // --未用上，实际完成钉钉任务的方法在这：cn.jwis.product.pdm.customer.service.impl.CustomerTaskHelperImpl.finishTask
    String completeTask(JSONObject delegateTask,ProcessOrder processOrder, List<InstanceEntity> instanceList);

    // PDM任务转派后，给钉钉同步指派任务
    String assignment(JSONObject delegateTask,ProcessOrder processOrder, List<InstanceEntity> instanceList);

    Map<String, String> findDingUserId(List<String> userAccounts);

    String startPermApply(PermApplyEntity permApplyEntity);

    void test();

    OapiV2UserGetResponse.UserGetResponse getUserDetail(String userId, String token) throws ApiException;

    String getUserIdByPhone(String phoneNumber, String token);

    String getToken() throws Exception;

    String getTokenNew() throws Exception;

    String getUserName(String userId, String accessToken) throws ApiException;

    String getUserNameWeak(String userId, String accessToken) throws ApiException;

    String getUserUnionid(String userId, String accessToken) throws ApiException;

    void sendRobotDING(String tokenNew, List<String> list, String content);

    void startMaterialNew(ProOrdCreateDTO dto) throws Exception;

    void uploadDINGFormObj(String processInstanceId, DingTaskRecord dingTaskRecord);

    void addCommentWithExcelForWuliaoProcess(DingTaskRecord dingTaskRecord);

    String createSendTaskForIds(JSONObject delegateTask, List<String> stringList)throws Exception;

    String createSendTaskForBomRelease(ProcessOrderDetail delegateTask) throws Exception ;

    String createSendTaskForEcrRelease(ProcessOrderDetail delegateTask) throws Exception ;

    String completeApproval(JSONObject delegateTask, ProcessOrder processOrder, List<InstanceEntity> instanceList);

    void createDataSheet(String processInstanceId, DingTaskRecord dingTaskRecord);

    List<GetProcessInstanceResponseBody.GetProcessInstanceResponseBodyResultTasks> getDingTaskValues(String processInstanceId, String token);
}
