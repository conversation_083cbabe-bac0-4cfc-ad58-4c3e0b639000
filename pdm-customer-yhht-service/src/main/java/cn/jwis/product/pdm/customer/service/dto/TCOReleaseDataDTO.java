package cn.jwis.product.pdm.customer.service.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: Technical Change Order  技术变更单数据DTO
 * @date 2023/8/17 9:09
 * @Email <EMAIL>
 */
@Data
@EqualsAndHashCode
public class TCOReleaseDataDTO {
    private String businessOid;
    // type
    private String type;
    // 关联部件
    private ItemReleaseDataDTO part;
    //执行负责人
    private String executeOwner;
    // 质检负责人
    private String qualityOwner;
    // 生产工单U9
    private String productionOrder;
    // 库存数量U9
    private String inventoryQuantity;
    // 变更前
    private String changeBefore;
    // 变更后
    private String changeAfter;
    // 更改原因
    private String changeReason;
    // 库存（在制品）处理意见
    private String inventoryHandlingOpinions;
    // 变更数量
    private String changeQuantity;





}
