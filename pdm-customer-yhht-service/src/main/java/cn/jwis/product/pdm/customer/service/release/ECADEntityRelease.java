package cn.jwis.product.pdm.customer.service.release;

import cn.jwis.framework.base.bean.util.BeanUtil;
import cn.jwis.framework.base.domain.able.ModelAble;
import cn.jwis.framework.base.domain.able.info.ModelInfo;
import cn.jwis.framework.base.util.CollectionUtil;
import cn.jwis.platform.plm.account.repo.user.response.UserWithPositionAndOrg;
import cn.jwis.platform.plm.account.user.service.UserHelper;
import cn.jwis.platform.plm.account.user.service.UserService;
import cn.jwis.platform.plm.datadistribution.service.DataDistributionService;
import cn.jwis.platform.plm.dataengine.common.JWICommonService;
import cn.jwis.platform.plm.file.entity.FileMetadata;
import cn.jwis.platform.plm.file.service.FileService;
import cn.jwis.platform.plm.foundation.attachment.entity.File;
import cn.jwis.platform.plm.foundation.common.service.CommonAbilityHelper;
import cn.jwis.platform.plm.foundation.common.service.CommonAbilityService;
import cn.jwis.platform.plm.foundation.lifecycle.able.LifecycleAble;
import cn.jwis.platform.plm.workflow.engine.order.entity.ProcessOrder;
import cn.jwis.product.pdm.cad.ecad.entity.ECADIteration;
import cn.jwis.product.pdm.cad.ecad.service.ECADHelper;
import cn.jwis.product.pdm.customer.entity.IntegrationRecord;
import cn.jwis.product.pdm.customer.service.dto.BaseEntityDTO;
import cn.jwis.product.pdm.customer.service.dto.DocReleaseDataDTO;
import cn.jwis.product.pdm.customer.service.dto.IntegrationFile;
import cn.jwis.product.pdm.customer.service.interf.CommonService;
import cn.jwis.product.pdm.partbom.part.entity.PartIteration;
import com.alibaba.fastjson.JSONObject;
import jodd.util.StringUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:文档发布能力提供者
 * @date 2023/8/15 11:13
 * @Email <EMAIL>
 */
@Data
@EqualsAndHashCode
@Component
@Transactional
public class ECADEntityRelease extends EntityRelease{

    private final List<String> targetLifecycleStatus = Arrays.asList("Released","Inwork","Design","Draft","UnderReview");

    @Autowired
    DataDistributionService dataDistributionService;

    @Autowired
    ECADHelper cadHelper;

    @Autowired
    UserHelper userHelper;

    @Autowired
    UserService userService;

    @Autowired
    JWICommonService jwiCommonService;

    @Autowired
    CommonAbilityHelper commonAbilityHelper;

    @Autowired
    CommonAbilityService commonAbilityService;

    @Autowired
    CommonService commonService;

    @Autowired
    FileService fileService;


    /**
     * 真正的Part发布实现，在此处推送发放的部件到任意外部接口，当前只发MQ。
     * @return
     */
    @Override
    @Async
    public IntegrationRecord release(ProcessOrder processOrder,String entityOid, String recordOid, int isFirst) {
        ECADIteration ecad = cadHelper.findByOid(entityOid);
        if(ecad == null) return null;
        //Assert.isTrue(targetLifecycleStatus.contains(ecad.getLifecycleStatus()),"ecad[" + ecad.getNumber() + "] lifecycleStatus is not match!");
        DocReleaseDataDTO docReleaseData = BeanUtil.copyProperties(ecad,new DocReleaseDataDTO());
        docReleaseData.setBusinessOid(type + "_" + ecad.getNumber() + "_" + ecad.getVersion());
        // 分类路径
        docReleaseData.setClsPath(getClsPath(ecad.getType(),ecad.getModelDefinition(),ecad.getClsProperty()));
        // 产品型号
        docReleaseData.setProductName(initProductName(ecad.getContainerOid()));
        // 发布人
        UserWithPositionAndOrg user = userHelper.findDetailByAccount(ecad.getOwner());
        initPublisher(user,docReleaseData);
        docReleaseData.setUpdateTime(ecad.getUpdateDate());
        // 初始化文档实体文件
        initFile(ecad,docReleaseData);
        // 初始化参考部件（已发放的部件）
        initReference(ecad,docReleaseData);
        // 初始化描述部件（已发放的部件）
        initDescribe(ecad,docReleaseData);
        JSONObject releaseData = JSONObject.parseObject(JSONObject.toJSONString(docReleaseData));
        // 获取集成记录
        IntegrationRecord record = getIntegrationRecord(recordOid,type,processOrder,releaseData);
        // 发MQ
        String result = releaseMQ(record,dataDistributionService);
        record.setMqSuccess(StringUtil.isBlank(result) ? true : false);
        // U9处理结果，预设为 waiting
        record.setConsumer("smis");
        record.setIsSuccess(false);
        record.setMsg("waiting");
        return jwiCommonService.update(record);
    }

    private void initPublisher(UserWithPositionAndOrg user, DocReleaseDataDTO docReleaseData) {
        if(user == null){
            docReleaseData.setUpdatorAccount("sys_admin");
            docReleaseData.setUpdatorNameAndDepartment("PDM系统管理员");
            return;
        }
        docReleaseData.setUpdatorAccount(user.getAccount());
        List<String> orgList = userService.getOrgTreeNameByAccount(user.getAccount());
        StringBuffer orgTree = new StringBuffer(user.getName() + ",");
        for(int i = orgList.size()-1; i > -1 ; i--){
            orgTree.append(orgList.get(i));
            if(i > 0){
                orgTree.append("-");
            }
        }
        docReleaseData.setUpdatorNameAndDepartment(orgTree.toString());
    }

    private void initFile(ECADIteration ecad,DocReleaseDataDTO docReleaseData) {
        docReleaseData.setPrimaryFile(new ArrayList<>());
        docReleaseData.setSecondaryFile(new ArrayList<>());
        List<File> primary = ecad.getPrimaryFile();
        if(CollectionUtil.isNotEmpty(primary)){
            primary.stream().forEach(f->{
                FileMetadata fileMetadata = fileService.findByOid(f.getOid());
                IntegrationFile integrationFile = new IntegrationFile(fileMetadata);
                docReleaseData.getPrimaryFile().add(integrationFile);
            });
        }
        List<File> secondary = ecad.getSecondaryFile();
        if(CollectionUtil.isNotEmpty(secondary)){
            secondary.stream().forEach(f->{
                FileMetadata fileMetadata = fileService.findByOid(f.getOid());
                IntegrationFile integrationFile = new IntegrationFile(fileMetadata);
                docReleaseData.getSecondaryFile().add(integrationFile);
            });
        }
    }

    // 再找描述关系， 一般是原理图，PCB图
    private void initDescribe(ECADIteration ecad,DocReleaseDataDTO docReleaseData) {
        ModelInfo modelInfo = BeanUtil.copyProperties(ecad,new ModelInfo());
        List<ModelAble> modelAbles = commonAbilityHelper.findDescribe(modelInfo);
        List<String> partOids = modelAbles.stream().map(part->part.getOid()).collect(Collectors.toList());
        modelAbles = commonAbilityService.findDetailEntity(partOids, PartIteration.TYPE);
        for(ModelAble modelAble : modelAbles){
            if(targetLifecycleStatus.contains(((LifecycleAble)modelAble).getLifecycleStatus())) {
                BaseEntityDTO dto = BeanUtil.copyProperties(modelAble,new BaseEntityDTO());
                dto.setType("Describe");
                docReleaseData.getRelationPart().add(dto);
            }
        }
    }

    // 先找参考关系， 一般是元器件的图符，封装，dataSheet
    private void initReference(ECADIteration ecad,DocReleaseDataDTO docReleaseData) {
        ModelInfo modelInfo = new ModelInfo();
        modelInfo.setOid(ecad.getMasterOid());
        modelInfo.setType(ecad.getMasterType());
        List<ModelAble> modelAbles = commonAbilityHelper.findBeReferenced(modelInfo);
        List<String> partOids = modelAbles.stream().map(part->part.getOid()).collect(Collectors.toList());
        modelAbles = commonAbilityService.findDetailEntity(partOids, PartIteration.TYPE);
        for(ModelAble modelAble : modelAbles){
            if(targetLifecycleStatus.contains(((LifecycleAble)modelAble).getLifecycleStatus())) {
                BaseEntityDTO dto = BeanUtil.copyProperties(modelAble,new BaseEntityDTO());
                dto.setType("Reference");
                docReleaseData.getRelationPart().add(dto);
            }
        }
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        type = EntityReleaseFactory.BusinessType.ecad.name();
        EntityReleaseFactory.register(type,this);
    }
}
