package cn.jwis.product.pdm.customer.service.interf;

import cn.jwis.product.pdm.customer.entity.DataDictionary;
import cn.jwis.product.pdm.customer.service.dto.DataDictionaryDTO;
import cn.jwis.product.pdm.customer.service.dto.LayoutSelectDTO;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

public interface DataDictionaryService {

   DataDictionary save(DataDictionary dataDictionary, boolean editByCode);

    boolean delete(String oid);

   long toggleEnable(String oid, Boolean enable);

   List<DataDictionaryDTO> treeList(String searchKey);

   void exportExcelTemp(HttpServletResponse response);

   void exportExcel(HttpServletResponse response, String searchKey);


    void importExcel(MultipartFile file);

    List<LayoutSelectDTO> getEnableList(String code);
}
