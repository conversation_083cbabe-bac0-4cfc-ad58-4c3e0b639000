package cn.jwis.product.pdm.customer.service.dto;

import cn.jwis.framework.base.annotation.Description;
import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2023/8/15 17:30
 * @Email <EMAIL>
 */
@Data
@EqualsAndHashCode
public class CADReleaseDataDTO {
    @ApiModelProperty("oid")
    private String oid;
    @ApiModelProperty("masterOid")
    private String masterOid;
    @ApiModelProperty("type")
    private String type;
    @ApiModelProperty("modelDefinition")
    private String modelDefinition;
    @ApiModelProperty("容器 oid")
    private String containerOid;
    @ApiModelProperty("容器类型 ")
    private String containerType;
    @ApiModelProperty("文件夹类型")
    private String catalogType;
    @ApiModelProperty("文件夹oid")
    private String catalogOid;
    @ApiModelProperty("名称")
    private String name;
    @ApiModelProperty("编码")
    private String number;
    @ApiModelProperty("扩展属性")
    private JSONObject extensionContent;
    @ApiModelProperty("大版本")
    private String version;
    @ApiModelProperty("迭代版本")
    private int iteratedVersion;
    @ApiModelProperty("是否最新版本")
    private boolean latest;
    @Description("是否分支最新版本")
    private boolean branchLatest;
    @ApiModelProperty(value = "版本序号",notes = "用于版本排序的序号")
    private int versionSortId;
    @ApiModelProperty("版本的显示字段，用于前端展示")
    private String displayVersion;
    @ApiModelProperty("生命周期状态")
    private String lifecycleStatus;
    @ApiModelProperty("cad图档")
    private List<IntegrationFile> cadFile;

}
