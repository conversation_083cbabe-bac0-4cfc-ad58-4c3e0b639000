package cn.jwis.product.pdm.customer.service.dto;

import cn.jwis.framework.base.util.CollectionUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: U9的库存和工单信息
 * @date 2023/9/8 16:03
 * @Email <EMAIL>
 */
@Data
@EqualsAndHashCode
public class U9InventoryResponse {

    private List<U9Stores> Stores = new ArrayList<>();

    private List<U9Transit> Transit = new ArrayList<>();

    public void addAll(U9InventoryResponse response) {
        if(response == null){
            return;
        }
        if(CollectionUtil.isNotEmpty(response.getStores())){
            Stores.addAll(response.getStores());
        }
        if(CollectionUtil.isNotEmpty(response.getTransit())){
            Transit.addAll(response.getTransit());
        }
    }
}
