package cn.jwis.product.pdm.customer.service.release;

import org.springframework.util.Assert;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 提供发布器的工厂类
 * @date 2023/8/15 11:23
 * @Email <EMAIL>
 */
public class EntityReleaseFactory {

    // 指定版本的系统  的能力提供者
    protected static final Map<String, EntityRelease> TYPE2RELEASE2PROVIDER  = new ConcurrentHashMap<>();

    // 合法的businessType，按需拓展，流程中调用发布能力时，需传该字段
    public enum BusinessType{
        item,  // 部件首次发布，更新发布,停用
        bom,   // 部件BOM发布
        doc,   // 文档首次发布，更新发布
        mcad,  // 结构CAD图档发布
        ecad,  // 电子CAD图档发布
        techChange,    // 技术变更单发布
        hzzBOM,  //发送BOM慧致造
        PMS
    }

    // 回调时，判断是否需要修改对象的状态
    public static List<String> needUpdateStatusType = Arrays.asList("item","doc","mcad","ecad");

    // 通过系统信息 匹配到对应的系统
    public static EntityRelease match(String businessType) {
        EntityRelease entityRelease = TYPE2RELEASE2PROVIDER.get(businessType);
        Assert.notNull(entityRelease,"[" + businessType + "] 不是合法的businessType!");
        return entityRelease;
    }

    // 注册 某系统某版本的能力提供者
    public static EntityRelease register(String businessType, EntityRelease provider) {
        return TYPE2RELEASE2PROVIDER.put(businessType,provider);
    }

}
