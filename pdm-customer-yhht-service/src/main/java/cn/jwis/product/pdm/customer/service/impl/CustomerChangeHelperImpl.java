package cn.jwis.product.pdm.customer.service.impl;

import cn.jwis.framework.base.domain.able.ModelAble;
import cn.jwis.framework.base.exception.JWIException;
import cn.jwis.framework.base.util.Assert;
import cn.jwis.framework.base.util.StringUtil;
import cn.jwis.platform.plm.foundation.common.service.CommonAbilityService;
import cn.jwis.platform.plm.foundation.relationship.Review;
import cn.jwis.product.pdm.cad.ecad.entity.ECADIteration;
import cn.jwis.product.pdm.cad.ecad.service.ECADHelper;
import cn.jwis.product.pdm.cad.mcad.entity.MCADIteration;
import cn.jwis.product.pdm.cad.mcad.service.MCADHelper;
import cn.jwis.product.pdm.change.dto.WorkflowDTO;
import cn.jwis.product.pdm.change.entity.ECR;
import cn.jwis.product.pdm.change.entity.Issue;
import cn.jwis.product.pdm.change.response.ChangeInfo;
import cn.jwis.product.pdm.change.service.ChangeCommonAppHelper;
import cn.jwis.product.pdm.change.service.ChangeCommonService;
import cn.jwis.product.pdm.change.service.ChangeHelper;
import cn.jwis.product.pdm.change.service.ChangeService;
import cn.jwis.product.pdm.change.service.ECAService;
import cn.jwis.product.pdm.customer.repo.CustomerCommonRepo;
import cn.jwis.product.pdm.customer.service.interf.CustomerChangeHelper;
import cn.jwis.product.pdm.document.entity.DocumentIteration;
import cn.jwis.product.pdm.document.service.DocumentHelper;
import cn.jwis.product.pdm.partbom.part.entity.PartIteration;
import cn.jwis.product.pdm.partbom.part.service.PartHelper;
import cn.jwis.product.pdm.partbom.part.service.PartService;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR> lhm
 * @Date ： Created in 2023/12/28 22:10
 * @Description :
 */

@Slf4j
@Service
public class CustomerChangeHelperImpl implements CustomerChangeHelper {

    @Autowired
    CommonAbilityService commonAbilityService;

    @Autowired
    ChangeService changeService;

    @Autowired
    ECAService ecaService;

    @Autowired
    PartService partService;

    @Autowired
    PartHelper partHelper;

    @Autowired
    DocumentHelper documentHelper;

    @Autowired
    MCADHelper mcadHelper;

    @Autowired
    ECADHelper ecadHelper;

    @Autowired
    ChangeCommonAppHelper commonAppHelper;

    @Autowired
    ChangeHelper changeHelper;

    @Autowired
    ChangeCommonService changeCommonService;

    @Override
    public boolean updateObject(WorkflowDTO dto) {
        //更新数据
        List<ModelAble> ecrList = commonAbilityService.findIterByFrom(dto.getProcessOrderOid(), dto.getProcessOrderType(), Review.TYPE, ECR.TYPE);
        Assert.notNull(ecrList,"ECR 对象不存在");
        ModelAble ecr = ecrList.get(0);
        log.info("start updateObject ecrOid:{}",ecr.getOid());

        List<ChangeInfo> changeInfo = changeService.findChangeInfo(ecr.getOid(), null);
        Assert.notNull(changeInfo,"根据ECR查询 变更对象不存在");
        this.confirmChangeObjs(changeInfo);

        //更新状态
        changeHelper.releaseObject(dto);
        return true;
    }

    protected void confirmChangeObjs(List<ChangeInfo> changeInfo) {
        //基于变更对象去回填变更方案
        //判断每个变更对象是否存在变更方案，若不存在变更方案返回错误信息
        HashMap<String,String> map = new HashMap();
        changeInfo.forEach(n->{
            String changeOid = n.getOid();
            String type = n.getType();
            String changeSchemeOid = ecaService.findChangeSchemeOid(changeOid, type);
            if (StringUtil.isEmpty(changeSchemeOid)){
                throw new JWIException(n.getNumber()+"不存在变更方案无法变更，请填写变更方案");
            }
            map.put(changeOid,changeSchemeOid);
        });
        log.info("updateObject find changeScheme:{} ", map);

//        changeInfo = changeInfo.stream().sorted(((prev, next) -> "PartIteration".equals(prev.getType()) && "PartIteration".equals(next.getType())
//                ? partService.lastIsParent(prev.getOid(), next.getOid()) : -1)).collect(Collectors.toList());

        for (ChangeInfo n : changeInfo) {
            String changeOid = n.getOid();
            String type = n.getType();
            //将对象和ECA，ECO，ECR的关系字段进行修改
            ecaService.updateImpact(changeOid,type);
            String changeSchemeOid = map.get(changeOid);
            log.info("updateObject confirmChange type:{},oid:{}", type, changeSchemeOid);
            // 根据类型查询当前最新版本的oid,设置lasted属性为false
            updateLastVersion(changeOid, type);
            // 物料
            if (PartIteration.TYPE.equals(type)){
                partHelper.confirmChange(changeOid,changeSchemeOid);
            }
            // 文档
            else if (DocumentIteration.TYPE.equals(type)){
                documentHelper.confirmChange(changeOid,changeSchemeOid);
            }
            // MCAD
            else if (MCADIteration.TYPE.equals(type)) {
                mcadHelper.confirmChange(changeOid,changeSchemeOid);
            }
            // ECAD
            else if (ECADIteration.TYPE.equals(type)){
                ecadHelper.confirmChange(changeOid,changeSchemeOid);
            }
            else {
                try {
                    commonAppHelper.confirmChange(changeOid,changeSchemeOid,type);
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                }
            }
        }
    }

    private void updateLastVersion(String changeOid, String type) {
        ModelAble modelAble = commonAbilityService.findLatestDetailEntity(changeOid, type);
        if (!ObjectUtils.isEmpty(modelAble) && !StringUtils.equals(changeOid, modelAble.getOid())) {
            log.info("change oid :{}  now lasted oid:{}", changeOid, modelAble.getOid());
            changeCommonService.updateLatest(modelAble.getOid(), type, false);
        }
    }

    @Autowired
    private CustomerCommonRepo customerCommonRepo;

    public void closeIssue(String ecrOid){
        ECR ecr = (ECR) commonAbilityService.findByOid(ecrOid, ECR.TYPE);
        Optional.ofNullable(ecr).ifPresent(it -> {
            List<JSONObject> issuList = it.getIssueList();
            if(issuList != null && issuList.size() > 0){
                customerCommonRepo.updateLifeStatus(Issue.TYPE, issuList.stream().map(item -> item.getString("oid")).collect(Collectors.toList()), "Closed");
            }
        });

    }
}
