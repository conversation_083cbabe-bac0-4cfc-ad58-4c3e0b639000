package cn.jwis.product.pdm.customer.service.impl;

import cn.hutool.json.JSONUtil;
import cn.jwis.framework.base.util.StringUtil;
import cn.jwis.platform.plm.file.entity.FileMetadata;
import cn.jwis.platform.plm.foundation.attachment.entity.File;
import cn.jwis.platform.plm.modelexcel.dto.ModelExcelAllCreateDTO;
import cn.jwis.platform.plm.modelexcel.dto.ModelExcelAllPropDTO;
import cn.jwis.platform.plm.modelexcel.exceptions.ExcelException;
import cn.jwis.platform.plm.modelexcel.service.impl.ModelExcelActServiceImpl;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> yefei
 * @Date ： 2024/3/12
 * @Description :
 */

@Slf4j
@Primary
@Service()
public class CustomerModelExcelActServiceImpl extends ModelExcelActServiceImpl {

    boolean isHis;

    @Override
    public List<ModelExcelAllCreateDTO> getCreateListAllData(List<ModelExcelAllPropDTO> list, Map<String,
            java.io.File> fileMap, String bucket,boolean isHis) {
        this.isHis = isHis;
        return super.getCreateListAllData(list,fileMap,bucket);
    }

    @Override
    public List<ModelExcelAllCreateDTO> getCreateListAllData(List<ModelExcelAllPropDTO> list, Map<String,
            java.io.File> fileMap, String bucket) {
        return getCreateListAllData(list,fileMap,bucket,false);
    }

    protected List<File> getFileMeta(String path, String propName, Map<String, FileMetadata> allFile, int line) {
        if (StringUtil.isBlank(path)) {
            return new ArrayList<>();
        }
        // 更新情况
        if (path.startsWith("[")) {
            return JSONObject.parseArray(path, cn.jwis.platform.plm.foundation.attachment.entity.File.class);
        }

        String[] primaryArr = path.split(",|，");
        List<cn.jwis.platform.plm.foundation.attachment.entity.File> res = new ArrayList<>();
        for (String pa : primaryArr) {
            String pathName = pa.startsWith("/") ? pa.substring(1) : pa;
            FileMetadata fileMetadata = allFile.get(pathName);
            if (fileMetadata == null) {
                if(isHis) {
                    return res;
                } else {
                    log.info("当前文件集合信息：{}", JSONUtil.toJsonStr(allFile));
                    throw new ExcelException(line, "根据路径" + pathName + "未找到[" + propName + "]：[" + pa + "]，文件路径请使用例如：附件/xxx.doc (多个使用,分割)");
                }
            }
            cn.jwis.platform.plm.foundation.attachment.entity.File dataFile =
                    new cn.jwis.platform.plm.foundation.attachment.entity.File();
            dataFile.setOid(fileMetadata.getOid());
            dataFile.setName(fileMetadata.getFileOriginalName());
            res.add(dataFile);
        }
        return res;
    }


}
