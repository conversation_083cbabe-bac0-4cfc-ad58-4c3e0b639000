package cn.jwis.product.pdm.customer.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import cn.jwis.framework.base.dto.UserDTO;
import cn.jwis.framework.base.exception.JWIException;
import cn.jwis.framework.base.response.PageResult;
import cn.jwis.framework.base.web.session.SessionHelper;
import cn.jwis.framework.database.core.query.dynamic.Condition;
import cn.jwis.platform.iam.user.User;
import cn.jwis.platform.plm.container.entity.ContainerTemplate;
import cn.jwis.platform.plm.container.file.FileRemoteFeign;
import cn.jwis.platform.plm.dataengine.common.JWICommonService;
import cn.jwis.platform.plm.foundation.common.service.CommonAbilityService;
import cn.jwis.platform.plm.sysconfig.collectionrule.service.CollectionRuleHelper;
import cn.jwis.platform.plm.sysconfig.entity.CollectionRule;
import cn.jwis.platform.plm.sysconfig.entity.Units;
import cn.jwis.platform.plm.sysconfig.units.service.UnitsHelper;
import cn.jwis.product.pdm.customer.entity.assistant.CheckedAssist;
import cn.jwis.product.pdm.customer.entity.assistant.SafeWrapAssist;
import cn.jwis.product.pdm.customer.remote.IamRemote;
import cn.jwis.product.pdm.customer.repo.CustomerCommonRepo;
import cn.jwis.product.pdm.customer.service.dto.BOMEntry;
import cn.jwis.product.pdm.customer.service.dto.DataDictionaryDTO;
import cn.jwis.product.pdm.document.remote.container.DocMgmtContainerRemote;
import cn.jwis.product.pdm.document.remote.container.dto.TemplateFindDTO;
import cn.jwis.product.pdm.partbom.part.dto.ImportParamDTO;
import cn.jwis.product.pdm.partbom.part.dto.PartClassificationDTO;
import cn.jwis.product.pdm.partbom.part.dto.SimplePartBOMNode;
import cn.jwis.product.pdm.partbom.part.entity.PartIteration;
import cn.jwis.product.pdm.partbom.part.service.DefaultPartExportHelperImpl;
import cn.jwis.product.pdm.partbom.part.service.IPartService;
import cn.jwis.product.pdm.partbom.part.service.PartHelper;
import cn.jwis.product.pdm.partbom.part.style.handler.CustomCellWriteHandler;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.builder.ExcelWriterSheetBuilder;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.IOUtils;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.util.*;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@Primary
@Transactional
@Slf4j
public class CustomPartExportHelperImpl extends DefaultPartExportHelperImpl implements CheckedAssist, SafeWrapAssist {


    @Autowired
    private PartHelper partHelper;
    @Resource
    private DocMgmtContainerRemote containerRemote;
    @Autowired
    private FileRemoteFeign fileRemoteFeign;
    @Autowired
    private UnitsHelper unitsHelper;
    @Autowired
    private IPartService partService;
    @Autowired
    CommonAbilityService commonAbilityService;
    @Autowired
    private DataDictionaryServiceImpl dataDictionaryService;
    @Autowired
    private CollectionRuleHelper collectionRuleHelper;
    @Autowired
    JWICommonService jwiCommonService;
    @Resource
    IamRemote iamRemote;
    @Autowired
    CustomerCommonRepo customerCommonRepo;
    @Resource
    DingTalkServiceImpl dingTalkService;


    private Map<String, String> PART_ruleMap = Stream.of(new AbstractMap.SimpleEntry<>("图符维护", "MM"), new AbstractMap.SimpleEntry<>("封装维护", "MM"), new AbstractMap.SimpleEntry<>("datasheet维护", "MM")).collect(Collectors.toMap(it -> it.getKey(), it -> it.getValue(), (v1, v2) -> v1));



    @Override
    public void exportBomData(HttpServletResponse response, String topPartOid, Integer type) throws IOException {
        if (type == null) {
            type = 0;
        }

        // 根据类型选择不同的导出逻辑
        switch (type) {
            case 0:
                exportBomLine(response, topPartOid);
                break;
            case 1:
                exportBomEffectivity(response, topPartOid);
                break;
            case 2:
                exportBomEntityDetail(response, topPartOid);
                break;
            default:
                throw new JWIException("type is error");
        }
    }

    private void exportBomEntityDetail(HttpServletResponse response, String topPartOid) throws IOException {
        this.responseExcel(response, "导出部件详情");
        ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream()).build();

        try {
            Map<String, Map<String, String>> headNameCodeMap = new HashMap(16);
            Map<String, Map<String, Boolean>> headNameIsSystemDefaultMap = new HashMap(16);
            List<PartClassificationDTO> partDtoList = new ArrayList();
            Map<String, List<PartClassificationDTO>> partClsDtoMap = new HashMap(16);
            ImportParamDTO dto = new ImportParamDTO();
            dto.setTopOid(topPartOid);
            Map<String, List<List<String>>> partHeads = this.partService.queryPartHeads(partClsDtoMap, dto, headNameCodeMap, partDtoList, headNameIsSystemDefaultMap);
            Map<String, Map<String, Map<String, String>>> drownData4ModelDefinition = new HashMap();
            Iterator var14 = partHeads.keySet().iterator();

            while(var14.hasNext()) {
                String sheetName = (String)var14.next();
                List<List<String>> headList = (List)partHeads.get(sheetName);
                partDtoList = (List)partClsDtoMap.get(sheetName);
                ExcelWriterSheetBuilder sheetBuilder = (ExcelWriterSheetBuilder)((ExcelWriterSheetBuilder)EasyExcel.writerSheet(sheetName).head(headList)).registerWriteHandler(new CustomCellWriteHandler());
                List<Map<Integer, Object>> dataList = this.partService.queryPartDataByHeadMap(partDtoList, (Map)headNameCodeMap.get(sheetName), headList, (Map)headNameIsSystemDefaultMap.get(sheetName), drownData4ModelDefinition);
                excelWriter.write(dataList, sheetBuilder.build());
            }
        } finally {
            excelWriter.finish();
        }

    }
    private void exportBomEffectivity(HttpServletResponse response, String topPartOid) throws IOException {
        List<List<String>> headList = new ArrayList(this.partService.queryPartBomEffectivityAttributes("Part", ""));
        List<Map<Integer, Object>> dataList = this.partService.getBomDataForAllVersionEffectivity(topPartOid);
        String fileName = "BOM中部件有效性导出";
        this.responseExcel(response, fileName);
        ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream()).build();
        WriteSheet writeSheet = ((ExcelWriterSheetBuilder)((ExcelWriterSheetBuilder)EasyExcel.writerSheet(0, fileName).head(headList)).registerWriteHandler(new CustomCellWriteHandler())).build();
        excelWriter.write(dataList, writeSheet);
        excelWriter.finish();
    }
    public void exportBomLine(HttpServletResponse response, String topPartOid) throws IOException {
        // 获取当前物料的 BOM 信息
        //生成厂家、质量等级的字典
        UserDTO currentUser = SessionHelper.getCurrentUser();
        User createBy = findUserByAccount(currentUser.getAccount());
        String shenqingUserName = createBy.getName();
        String userDepart = customerCommonRepo.getUserDepart(Arrays.asList(currentUser.getAccount()));

        List<DataDictionaryDTO> dataDictionaryList = dataDictionaryService.treeList(null);
        Map<String, Map<String, String>> clsMap = dataDictionaryList.stream().flatMap(it -> it.getChildren().stream())
                .collect(Collectors.groupingBy(item -> {
                            String code = item.getCode();
                            if ("manufacturer".equals(code)) return "cn_jwis_sccj";
                            else if ("PackageType".equals(code)) return "cn_jwis_fzxs";
                            else if ("standardNumber".equals(code)) return "cn_jwis_fzxs"; // code = "cn_jwis_bzh";
                            else if ("msl".equals(code)) return "cn_jwis_smdj";
                            else return "cn_jwis_zldj";
                        }, Collectors.collectingAndThen(Collectors.toList(), itemList ->
                                itemList.stream().flatMap(item -> item.getChildren().stream()).collect(Collectors.toMap(it -> it.getCode(), it -> it.getDisplayName(), (v1, v2) -> v1)))
                ));
        //默认单位的字典
        List<CollectionRule> materialRuleList = collectionRuleHelper.findByAppliedType("Part_Related_Object", "JWIRawMaterial")
                , PART_ruleList = filterCollect(materialRuleList, it -> PART_ruleMap.get(it.getRelationDisplayName()) != null);
        Map<String, String> unitsMap = getDefaultUnitOid2ValueMap(currentUser);

        PartIteration part = (PartIteration) commonAbilityService.findDetailEntity(topPartOid, PartIteration.TYPE);
        List<SimplePartBOMNode> currentBOM = partHelper.findSimpleUseTree(null, topPartOid, null);
        if (currentBOM != null && !currentBOM.isEmpty() && !currentBOM.get(0).getChildren().isEmpty()) {
            log.info("开始上传全量BOM，模板名称: 全量BOM");
            InputStream inputStream = downloadBOMTemplate("全量BOM");

            if (inputStream != null) {
                //处理全量bom逻辑
                log.info("currentBOM信息:{}", JSONUtil.toJsonStr(currentBOM));
                JSONObject extensionContent2 = part.getExtensionContent();
                JSONObject clsProperty1 = part.getClsProperty();
                SimplePartBOMNode simplePartBOMNode = currentBOM.get(0);
                List<SimplePartBOMNode> currentChildren = !currentBOM.isEmpty() ? currentBOM.get(0).getChildren() : Collections.emptyList();
                List<BOMEntry> bomEntries = new ArrayList<>();

                if (!currentChildren.isEmpty()) {
                    String cn_jwis_fzxs = getOrElse(clsMap.get("cn_jwis_fzxs"), map -> map.get(clsProperty1.getString("cn_jwis_fzxs")), clsProperty1.getString("cn_jwis_fzxs"));
                    String cn_jwis_sccj = getOrElse(clsMap.get("cn_jwis_sccj"), map -> map.get(clsProperty1.getString("cn_jwis_sccj")), clsProperty1.getString("cn_jwis_sccj"));
                    String cn_jwis_zldj = getOrElse(clsMap.get("cn_jwis_zldj"), map -> map.get(clsProperty1.getString("cn_jwis_zldj")), clsProperty1.getString("cn_jwis_zldj"));
                    String cn_jwis_smdj = getOrElse(clsMap.get("cn_jwis_smdj"), map -> map.get(clsProperty1.getString("cn_jwis_smdj")), clsProperty1.getString("cn_jwis_smdj"));
                    //构造全量BOM数据
                    BOMEntry bomParent = new BOMEntry();
                    bomParent.setSubmitter(shenqingUserName);
                    bomParent.setDepartmentName(userDepart);
                    bomParent.setSubmitTime(DateUtil.now());
                    bomParent.setBomMaterialNumber(part.getNumber());
                    bomParent.setBomConvertedCode(Optional.ofNullable(extensionContent2.getString("CN_YH_U9CODE")).orElse(""));
                    bomParent.setBomTianTongJingDianCode(Optional.ofNullable(extensionContent2.getString("CN_YH_TTJD")).orElse(""));
                    bomParent.setBomProductName(part.getName());
                    bomParent.setBomProductSpecification(Optional.ofNullable(extensionContent2.getString("cn_jwis_gg")).orElse(""));
                    bomParent.setBomManufacturer(cn_jwis_sccj);
                    bomParent.setBomPackageForm(cn_jwis_fzxs);
                    bomParent.setBomQualityGrade(cn_jwis_zldj);
                    bomParent.setBomUnit(unitsMap.getOrDefault(part.getDefaultUnit(), part.getDefaultUnit()));
                    bomParent.setBomVersion(part.getVersion());
                    bomEntries.add(bomParent);
                    int detailSequenceNumberCounter = 1; // 初始化序号计数器
                    for (SimplePartBOMNode currentChild : currentChildren) {
                        PartIteration partSon = (PartIteration) commonAbilityService.findDetailEntity(currentChild.getOid(), currentChild.getType());
                        JSONObject extensionContent3 = partSon.getExtensionContent();
                        JSONObject clsProperty = partSon.getClsProperty();

                        String cn_jwis_fzxs_son = getOrElse(clsMap.get("cn_jwis_fzxs"), map -> map.get(clsProperty.getString("cn_jwis_fzxs")), clsProperty.getString("cn_jwis_fzxs"));
                        String cn_jwis_sccj_son = getOrElse(clsMap.get("cn_jwis_sccj"), map -> map.get(clsProperty.getString("cn_jwis_sccj")), clsProperty.getString("cn_jwis_sccj"));
                        String cn_jwis_zldj_son = getOrElse(clsMap.get("cn_jwis_zldj"), map -> map.get(clsProperty.getString("cn_jwis_zldj")), clsProperty.getString("cn_jwis_zldj"));
                        String cn_jwis_smdj_son = getOrElse(clsMap.get("cn_jwis_smdj"), map -> map.get(clsProperty.getString("cn_jwis_smdj")), clsProperty.getString("cn_jwis_smdj"));
                        String currentQuantity = safeGet(() -> currentChild.getUse().getQuantity(), "无");
                        String currentPosition = safeGet(() -> currentChild.getUse().getExtensionContent().getString("position"), "无");
                        // 如果 currentPosition 超过 30000，则拆分成多个 BOMEntry
                        if (currentPosition != null && currentPosition.length() > 30000) {
                            List<String> splitPositions = splitPositionsByLength(currentPosition, 30000);
                            // 计算总数量
                            String totalQuantity = String.valueOf(
                                    Arrays.stream(currentPosition.split(","))
                                            .map(String::trim)
                                            .filter(s -> !s.isEmpty())
                                            .count()
                            );
                            BOMEntry bomSon = dingTalkService.buildBomSon(partSon, extensionContent3, unitsMap, totalQuantity, splitPositions,
                                    cn_jwis_fzxs_son, cn_jwis_sccj_son, cn_jwis_zldj_son, cn_jwis_smdj_son, detailSequenceNumberCounter);
                            bomEntries.add(bomSon);
                            detailSequenceNumberCounter++;
                        } else {
                            BOMEntry bomSon = dingTalkService.buildBomSon(partSon, extensionContent3, unitsMap, currentQuantity,
                                    Collections.singletonList(currentPosition),
                                    cn_jwis_fzxs_son, cn_jwis_sccj_son, cn_jwis_zldj_son, cn_jwis_smdj_son, detailSequenceNumberCounter);
                            bomEntries.add(bomSon);
                            detailSequenceNumberCounter++;
                        }
                    }
                }

                File tempFile = DingTalkServiceImpl.writeBomDataAndGetFile(inputStream, bomEntries);

                // 输出文件到浏览器
                this.responseExcel(response, "全量BOM导出");
                try (FileInputStream fileInputStream = new FileInputStream(tempFile);
                     OutputStream outputStream = response.getOutputStream()) {
                    IOUtils.copy(fileInputStream, outputStream);
                    response.flushBuffer();
                }
            } else {
                log.warn("物料 [{}] 没有BOM信息，跳过全量BOM上传", part.getNumber());
            }
        }

    }
    private void responseExcel(HttpServletResponse response, String name) throws UnsupportedEncodingException {
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode(name, "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
    }
    /**
     * 下载全量/差异 BOM模板流
     *
     * @param templateName
     * @return
     */
    private InputStream downloadBOMTemplate(String templateName) {
        java.io.File tempFile = null;
        try {
            TemplateFindDTO templateFindDTO = new TemplateFindDTO();
            templateFindDTO.setCategory("ecadExcelTemplate");
            templateFindDTO.setName(templateName);
            templateFindDTO.setContainerOid(SessionHelper.getCurrentUser().getTenantOid());

            ContainerTemplate signTemplate = containerRemote.findTemplateByName(templateFindDTO);
            if (signTemplate == null || signTemplate.getFile() == null) {
                log.warn("未找到 BOM 模板 [{}]", templateName);
                return null;
            }
            cn.jwis.platform.plm.foundation.attachment.entity.File file = signTemplate.getFile();
            InputStream inputStream = fileRemoteFeign.downloadByOid(file.getOid()).body().asInputStream();
            log.info("读取 BOM [{}]: {}", templateName, JSONUtil.toJsonStr(file));
            return inputStream;
        } catch (Exception e) {
            log.error("上传 BOM [{}] 失败", templateName, e);
            return null;
        }
    }


    private Map<String, String> getDefaultUnitOid2ValueMap(UserDTO currentUser) {
        //查询 当前的单位
        PageResult<Units> pageResult = this.unitsHelper.searchUnits((String) null, 1, 9999, currentUser.getTenantOid());
        List<Units> rows = pageResult.getRows();
        // 过滤掉已禁用的 Units 对象
        List<Units> newRows = rows.stream()
                .filter(item -> item.getDisabled() == 0)
                .collect(Collectors.toList());

        // 将 newRows 转换为 Map，key 为 Oid，value 为 Name
        Map<String, String> unitsMap = newRows.stream()
                .collect(Collectors.toMap(Units::getOid, Units::getName));
        return unitsMap;
    }

    private <T> String safeGet(Supplier<T> supplier, String defaultValue) {
        try {
            T value = supplier.get();
            return value != null ? value.toString() : defaultValue;
        } catch (Exception e) {
            return defaultValue;
        }
    }

    private User findUserByAccount(String account) {
        User user = jwiCommonService.dynamicQueryOne(User.TYPE, Condition.where("account").eq(account),User.class);
        if(user != null) {
            return user;
        }
        return iamRemote.findUserByAccount(Lists.newArrayList(account)).stream().findAny().orElse(null);
    }
    /**
     * 按完整位号切分，保证不超长度
     */
    private List<String> splitPositionsByLength(String positionsStr, int maxLength) {
        List<String> result = new ArrayList<>();
        if (positionsStr == null || positionsStr.isEmpty()) {
            result.add("");
            return result;
        }

        // 统一中英文逗号
        String unified = positionsStr.replaceAll("[，,]", ",");
        String[] positions = unified.split("\\s*,\\s*");

        StringBuilder currentBuilder = new StringBuilder();
        for (String pos : positions) {
            if (currentBuilder.length() > 0) {
                currentBuilder.append(", ");
            }
            currentBuilder.append(pos);

            // 如果超了 maxLength，需要把之前的内容保存下来，新开一个
            if (currentBuilder.length() >= maxLength) {
                result.add(currentBuilder.toString());
                currentBuilder.setLength(0); // 清空重新开始
            }
        }

        // 最后如果还有剩余
        if (currentBuilder.length() > 0) {
            result.add(currentBuilder.toString());
        }

        return result;
    }
    /**
     * 批量导出多个物料的 BOM，每个物料一个sheet
     */
    public void exportMultiBomLine(HttpServletResponse response, List<String> oidList) throws IOException {
        InputStream templateStream = downloadBOMTemplate("全量BOM");
        if (templateStream == null) {
            throw new JWIException("未找到全量BOM模板");
        }

        // 使用 Apache POI 克隆模板 sheet
        XSSFWorkbook poiWorkbook = new XSSFWorkbook(templateStream);
        XSSFSheet templateSheet = poiWorkbook.getSheetAt(0);
        List<String> sheetNames = new ArrayList<>();

        for (String oid : oidList) {
            PartIteration part = (PartIteration) commonAbilityService.findDetailEntity(oid, PartIteration.TYPE);
            String sheetName = part.getName() + "-" + part.getNumber();
            sheetNames.add(sheetName);
            XSSFSheet newSheet = poiWorkbook.cloneSheet(0);
            int index = poiWorkbook.getSheetIndex(newSheet);
            poiWorkbook.setSheetName(index, sheetName);
        }
        poiWorkbook.removeSheetAt(0);

        // 将 POI workbook 写入临时文件
        File tempFile = File.createTempFile("multi_bom_" + DateUtil.format(new Date(), "yyyyMMddHHmmss") + "_", ".xlsx");
        try (FileOutputStream fos = new FileOutputStream(tempFile)) {
            poiWorkbook.write(fos);
        }

        // 获取当前用户信息
        UserDTO currentUser = SessionHelper.getCurrentUser();
        User createBy = findUserByAccount(currentUser.getAccount());
        String shenqingUserName = createBy.getName();
        String userDepart = customerCommonRepo.getUserDepart(Collections.singletonList(currentUser.getAccount()));
        Map<String, String> unitsMap = getDefaultUnitOid2ValueMap(currentUser);

        List<DataDictionaryDTO> dataDictionaryList = dataDictionaryService.treeList(null);
        Map<String, Map<String, String>> clsMap = dataDictionaryList.stream().flatMap(it -> it.getChildren().stream())
                .collect(Collectors.groupingBy(item -> {
                            String code = item.getCode();
                            if ("manufacturer".equals(code)) return "cn_jwis_sccj";
                            else if ("PackageType".equals(code)) return "cn_jwis_fzxs";
                            else if ("standardNumber".equals(code)) return "cn_jwis_fzxs";
                            else if ("msl".equals(code)) return "cn_jwis_smdj";
                            else return "cn_jwis_zldj";
                        }, Collectors.collectingAndThen(Collectors.toList(), itemList ->
                                itemList.stream().flatMap(item -> item.getChildren().stream()).collect(Collectors.toMap(DataDictionaryDTO::getCode, DataDictionaryDTO::getDisplayName, (v1, v2) -> v1)))
                ));

        // 用 EasyExcel 写入
        try (FileInputStream fis = new FileInputStream(tempFile);
             OutputStream out = response.getOutputStream();
             ExcelWriter writer = EasyExcel.write(out).withTemplate(fis).build()) {

            this.responseExcel(response, "多物料BOM导出");

            for (int i = 0; i < oidList.size(); i++) {
                String topPartOid = oidList.get(i);
                PartIteration part = (PartIteration) commonAbilityService.findDetailEntity(topPartOid, PartIteration.TYPE);
                List<SimplePartBOMNode> currentBOM = partHelper.findSimpleUseTree(null, topPartOid, null);
                if (currentBOM == null || currentBOM.isEmpty() || currentBOM.get(0).getChildren().isEmpty()) {
                    continue;
                }

                JSONObject extensionContent2 = part.getExtensionContent();
                JSONObject clsProperty1 = part.getClsProperty();
                List<SimplePartBOMNode> currentChildren = currentBOM.get(0).getChildren();
                List<BOMEntry> bomEntries = new ArrayList<>();

                String cn_jwis_fzxs = getOrElse(clsMap.get("cn_jwis_fzxs"), map -> map.get(clsProperty1.getString("cn_jwis_fzxs")), clsProperty1.getString("cn_jwis_fzxs"));
                String cn_jwis_sccj = getOrElse(clsMap.get("cn_jwis_sccj"), map -> map.get(clsProperty1.getString("cn_jwis_sccj")), clsProperty1.getString("cn_jwis_sccj"));
                String cn_jwis_zldj = getOrElse(clsMap.get("cn_jwis_zldj"), map -> map.get(clsProperty1.getString("cn_jwis_zldj")), clsProperty1.getString("cn_jwis_zldj"));
                String cn_jwis_smdj = getOrElse(clsMap.get("cn_jwis_smdj"), map -> map.get(clsProperty1.getString("cn_jwis_smdj")), clsProperty1.getString("cn_jwis_smdj"));

                BOMEntry bomParent = new BOMEntry();
                bomParent.setSubmitter(shenqingUserName);
                bomParent.setDepartmentName(userDepart);
                bomParent.setSubmitTime(DateUtil.now());
                bomParent.setBomMaterialNumber(part.getNumber());
                bomParent.setBomConvertedCode(Optional.ofNullable(extensionContent2.getString("CN_YH_U9CODE")).orElse(""));
                bomParent.setBomTianTongJingDianCode(Optional.ofNullable(extensionContent2.getString("CN_YH_TTJD")).orElse(""));
                bomParent.setBomProductName(part.getName());
                bomParent.setBomProductSpecification(Optional.ofNullable(extensionContent2.getString("cn_jwis_gg")).orElse(""));
                bomParent.setBomManufacturer(cn_jwis_sccj);
                bomParent.setBomPackageForm(cn_jwis_fzxs);
                bomParent.setBomQualityGrade(cn_jwis_zldj);
                bomParent.setBomUnit(unitsMap.getOrDefault(part.getDefaultUnit(), part.getDefaultUnit()));
                bomParent.setBomVersion(part.getVersion());
                bomEntries.add(bomParent);

                int detailSequenceNumberCounter = 1;
                for (SimplePartBOMNode currentChild : currentChildren) {
                    PartIteration partSon = (PartIteration) commonAbilityService.findDetailEntity(currentChild.getOid(), currentChild.getType());
                    JSONObject extensionContent3 = partSon.getExtensionContent();
                    JSONObject clsProperty = partSon.getClsProperty();

                    String cn_jwis_fzxs_son = getOrElse(clsMap.get("cn_jwis_fzxs"), map -> map.get(clsProperty.getString("cn_jwis_fzxs")), clsProperty.getString("cn_jwis_fzxs"));
                    String cn_jwis_sccj_son = getOrElse(clsMap.get("cn_jwis_sccj"), map -> map.get(clsProperty.getString("cn_jwis_sccj")), clsProperty.getString("cn_jwis_sccj"));
                    String cn_jwis_zldj_son = getOrElse(clsMap.get("cn_jwis_zldj"), map -> map.get(clsProperty.getString("cn_jwis_zldj")), clsProperty.getString("cn_jwis_zldj"));
                    String cn_jwis_smdj_son = getOrElse(clsMap.get("cn_jwis_smdj"), map -> map.get(clsProperty.getString("cn_jwis_smdj")), clsProperty.getString("cn_jwis_smdj"));
                    String currentQuantity = safeGet(() -> currentChild.getUse().getQuantity(), "无");
                    String currentPosition = safeGet(() -> currentChild.getUse().getExtensionContent().getString("position"), "无");

                    if (currentPosition != null && currentPosition.length() > 30000) {
                        List<String> splitPositions = splitPositionsByLength(currentPosition, 30000);
                        String totalQuantity = String.valueOf(Arrays.stream(currentPosition.split(",")).map(String::trim).filter(s -> !s.isEmpty()).count());
                        BOMEntry bomSon = dingTalkService.buildBomSon(partSon, extensionContent3, unitsMap, totalQuantity, splitPositions,
                                cn_jwis_fzxs_son, cn_jwis_sccj_son, cn_jwis_zldj_son, cn_jwis_smdj_son, detailSequenceNumberCounter);
                        bomEntries.add(bomSon);
                        detailSequenceNumberCounter++;
                    } else {
                        BOMEntry bomSon = dingTalkService.buildBomSon(partSon, extensionContent3, unitsMap, currentQuantity,
                                Collections.singletonList(currentPosition),
                                cn_jwis_fzxs_son, cn_jwis_sccj_son, cn_jwis_zldj_son, cn_jwis_smdj_son, detailSequenceNumberCounter);
                        bomEntries.add(bomSon);
                        detailSequenceNumberCounter++;
                    }
                }

                String sheetName = part.getName() + "-" + part.getNumber();
                WriteSheet sheet = EasyExcel.writerSheet(i, sheetName).build();
                writer.write(bomEntries, sheet);
            }
            writer.finish();
        }
        tempFile.delete();
    }

}
