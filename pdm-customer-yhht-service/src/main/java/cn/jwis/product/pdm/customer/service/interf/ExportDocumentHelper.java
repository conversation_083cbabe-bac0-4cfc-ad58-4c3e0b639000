package cn.jwis.product.pdm.customer.service.interf;

import cn.jwis.platform.plm.container.dto.container.FuzzySubPageDTO;
import cn.jwis.product.pdm.customer.service.dto.FuzzyContainerInstanceExportDTO;

import javax.servlet.http.HttpServletResponse;

/**
 * @author: 汪江
 * @data: 2024-01-08 13:32
 * @description:
 **/
public interface ExportDocumentHelper {
    void exportDocument(HttpServletResponse response, FuzzyContainerInstanceExportDTO fuzzySubPageDTO, boolean isExportAll);

    void exportExcel(HttpServletResponse response, FuzzySubPageDTO fuzzySubPageDTO);
}
