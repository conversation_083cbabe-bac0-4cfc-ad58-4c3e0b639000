package cn.jwis.product.pdm.customer.service.impl;

import cn.jwis.framework.base.dto.UserDTO;
import cn.jwis.framework.base.exception.JWIServiceException;
import cn.jwis.framework.base.util.StringUtil;
import cn.jwis.framework.base.web.session.SessionHelper;
import cn.jwis.platform.plm.account.user.service.UserService;
import cn.jwis.platform.plm.file.entity.FileMetadata;
import cn.jwis.platform.plm.file.service.FileService;
import cn.jwis.platform.plm.file.service.FileServiceImpl;
import cn.jwis.platform.plm.file.utils.JwtUtil;
import cn.jwis.platform.plm.file.utils.MinioUtil;
import cn.jwis.platform.plm.sysconfig.entity.ConfigItem;
import cn.jwis.platform.plm.sysconfig.preferences.PreferencesService;
import cn.jwis.product.pdm.customer.repo.CustomerPlmUserNeo4jRepo;
import cn.jwis.product.pdm.customer.repo.DailyDownloadRepo;
import cn.jwis.product.pdm.customer.service.util.CustomerMinioUtil;
import cn.jwis.product.pdm.customer.service.util.FIleOutMailUtil;
import io.jsonwebtoken.Claims;
import io.minio.errors.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;


@Service
@Transactional
@Primary
@Slf4j
public class CustomerFIleServiceImpl extends FileServiceImpl {


    @Resource
    RedisTemplate redisTemplate;
    @Autowired
    MinioUtil minioUtil;
    @Autowired
    private PreferencesService preferencesService;
    @Resource
    UserService userService;
    @Resource
    CustomerPlmUserNeo4jRepo customerPlmUserNeo4jRepo;
    @Resource
    DailyDownloadService dailyDownloadService;
    @Resource
    DailyDownloadRepo dailyDownloadRepo;
    @Autowired
    private FileService fileService;
    @Resource
    CustomerMinioUtil customerMinioUtil;
    @Value("${minio.default.bucket}")
    private String bucketName;





    @Override
    public String getUrlByOidForPreview(String fileOid, String entityType, String entityOid) throws IOException, InvalidKeyException, InvalidResponseException, InvalidExpiresRangeException, NoSuchAlgorithmException, ServerException, InternalException, XmlParserException, InvalidBucketNameException, InsufficientDataException, ErrorResponseException {
        FileMetadata fileMetadata = this.fileService.findByOid(fileOid);
//        this.fileTriggerAuditService.viewFile(fileOid, fileMetadata.getFileOriginalName(), entityType, entityOid);
        String bucktName = (String) Optional.ofNullable(fileMetadata.getBucketName()).orElseThrow(() -> {
            return new JWIServiceException("file not exist");
        });
        String fileName = fileMetadata.getFileName();
        // 重写MiniUtil来生成Minio预签名URL（30分钟有效期）
        String originalURL = customerMinioUtil.getPresignedDownloadUrl(bucketName, fileName, 30);
        log.info("原始Minio URL: {}", originalURL);
        String encodedUrl = Base64.getEncoder().encodeToString(originalURL.getBytes(StandardCharsets.UTF_8));
        log.info("BASE64处理后服务URL: {}", encodedUrl);

        return encodedUrl;
    }



    @Override
    public String getAvailableBucket(boolean startBucketStrategy)
            throws IOException, InvalidResponseException, InvalidKeyException, NoSuchAlgorithmException,
            ServerException, InternalException, XmlParserException, ErrorResponseException,
            InvalidBucketNameException, InsufficientDataException, RegionConflictException, JWIServiceException {

        // 检查 bucketName 是否为 null 或去除前后空格后是否为空
        if (this.bucketName == null || this.bucketName.trim().isEmpty()) {
            // 如果检查不通过，则抛出指定的业务异常
            throw new JWIServiceException("存储桶名称（bucketName）未配置，请联系管理员进行检查！");
        }

        // 检查通过后，安全地返回 bucketName
        return this.bucketName;
    }


    public void downloadByOid(String fileOid, String name, String token, String entityType, String entityOid, HttpServletResponse response)
            throws JWIServiceException, ServerException, InvalidBucketNameException, InsufficientDataException,
            ErrorResponseException, IOException, NoSuchAlgorithmException, InvalidKeyException,
            InvalidResponseException, XmlParserException, InternalException {

        log.info("CustomerFIleServiceImpl start download");
        log.info("fileOid: " + fileOid);
        log.info("name: " + name);
        log.info("token: " + token);
        log.info("entityType: " + entityType);
        log.info("entityOid: " + entityOid);
        log.info("response: " + response);

        // 如果 token 不存在，直接放行
        if (token != null && !token.trim().isEmpty()) {
            Claims claims = JwtUtil.checkJsonWebToken(token);
            Map<String, String> userInfo = (Map) claims.get("user", Map.class);
            UserDTO currentUser = new UserDTO();
            currentUser.setAccount(userInfo.get("account"));
            currentUser.setOid(userInfo.get("oid"));
            currentUser.setName(userInfo.get("name"));
            boolean isWhiteList = isWhiteListRole(currentUser);
            boolean isTenantAdmin = this.userService.assertUserTenantAdmin(currentUser.getOid(), SessionHelper.getCurrentUser().getTenantOid());
            if (isTenantAdmin || isWhiteList) {
                log.info("组织管理员或白名单用户 {} 直接放行", currentUser.getName());
                super.downloadByOid(fileOid, name, token, entityType, entityOid, response);
                return;
            }

            String currentDate = LocalDate.now().toString();
            // 拼接 Redis Key
            String redisKey = "DownloadLimit:" + currentUser.getOid() + "_" + currentUser.getName() + ":" + currentDate;

            // 获取当前下载次数
            Integer currentCount = redisTemplate.opsForValue().get(redisKey) == null
                    ? 0
                    : Integer.parseInt(String.valueOf(redisTemplate.opsForValue().get(redisKey)));

            // 校验下载次数是否超过限制
            int maxDownloadsPerDay = 1;
            maxDownloadsPerDay = getDownLoadNumber(maxDownloadsPerDay);

            if (currentCount >= maxDownloadsPerDay) {
                log.warn("用户 {} 达到每日下载限制", currentUser.getName());
                triggerAlarmIfNeeded(currentUser,maxDownloadsPerDay); // 触发报警逻辑
                throw new JWIServiceException("您今天的下载次数已达每日上限" + maxDownloadsPerDay + "条，请联系管理员！");
            }

            // 计数增加并设置过期时间（24小时）
            redisTemplate.opsForValue().increment(redisKey);

            // 设置 Redis 键的过期时间为明天的零点（即当天 23:59:59）
            LocalDateTime tomorrowMidnight = LocalDate.now().plusDays(1).atStartOfDay();
            Date expiryDate = Date.from(tomorrowMidnight.atZone(ZoneId.systemDefault()).toInstant());
            redisTemplate.expireAt(redisKey, expiryDate);

            log.info("User {} downloaded file {}. Current download count: {}", currentUser.getName(), name, currentCount + 1);
        } else {
            log.info("Token is missing or empty. Downloading without restrictions.");
        }

        // 调用父类下载方法
        super.downloadByOid(fileOid, name, token, entityType, entityOid, response);

        log.info("Download completed for file {} at {}", name, LocalDateTime.now());
    }

    private boolean isWhiteListRole(UserDTO currentUser) {
        boolean isWhiteList = false;
        try {
            ConfigItem item = preferencesService.queryConfigValue("download_white_list");
            if (item != null && StringUtil.isNotBlank(item.getValue())) {
                String value = item.getValue().trim();
                if (StringUtil.isNotBlank(value)) {
                    // 处理分割后的角色列表
                    String[] split = value.split("\\|");
                    List<String> roleList = Arrays.stream(split)
                            .filter(StringUtil::isNotBlank) // 去除空角色名
                            .map(String::trim)              // 去除两端空格
                            .collect(Collectors.toList());
                    if (!roleList.isEmpty()) {
                        // 判断用户是否在白名单中
                        isWhiteList = customerPlmUserNeo4jRepo.assertUserIsAdmin(
                                currentUser.getOid(),
                                SessionHelper.getCurrentUser().getTenantOid(),
                                roleList
                        );
                    }
                }
            }
        } catch (Exception e) {
            log.error("查询用户是否属于白名单角色出错", e);
        }
        return isWhiteList;
    }

    private int getDownLoadNumber(int maxDownloadsPerDay) {
        ConfigItem item = preferencesService.queryConfigValue("download_limit");
        if (item != null && StringUtil.isNotBlank(item.getValue())) {
            try {
                // 检查 item.getValue() 是否为数字
                if (item.getValue().matches("\\d+")) {
                    maxDownloadsPerDay = Integer.parseInt(item.getValue());
                } else {
                    log.warn("配置项值不是有效数字：{}", item.getValue());
                }
            } catch (NumberFormatException e) {
                log.error("解析每日下载限制配置时发生错误，使用默认值: {}", maxDownloadsPerDay, e);
            }
        } else {
            log.info("配置项为空或未定义，使用默认每日下载次数: {}", maxDownloadsPerDay);
        }
        return maxDownloadsPerDay;
    }

    private void trigger(){
        //统计近三天超过10个下载量的所有用户
        //MATCH (n:JWIAudit)
        //WHERE n.action = 'DOWNLOAD'
        //  AND datetime({epochMillis: n.createDate}) >= datetime() - duration({days: 3})
        //WITH n.userName AS user, count(n) AS downloadCount
        //WHERE downloadCount > 2
        //RETURN  user, downloadCount
        //ORDER BY downloadCount DESC


        //当前用户三天内的下载量
        //MATCH (n:JWIAudit)
        //WHERE n.action = 'DOWNLOAD'
        //  AND n.createBy = 'yuchenghui'
        //  AND datetime({epochMillis: n.createDate}) >= datetime() - duration({days: 3})
        //RETURN count(n) AS totalDownloads

    }

    private void triggerAlarmIfNeeded(UserDTO currentUser, int maxDownloadsPerDay) {
        String alarmKey = "DownloadAlarm:" + currentUser.getOid() + "_" + currentUser.getName() + ":" + LocalDate.now();
        Boolean alreadyTriggered = redisTemplate.opsForValue().get(alarmKey) != null;

        if (alreadyTriggered) {
            log.info("用户 {} 今日已触发报警，跳过发送报警邮件", currentUser.getName());
            return;
        }

        List<String> emailList = dailyDownloadService.loadEmailList();
        if (emailList.isEmpty()) {
            log.warn("报警邮件列表为空，无法发送报警邮件");
            return;
        }
        String userDepart = dailyDownloadRepo.getUserDepart(currentUser.getOid());

        String subject = "用户下载次数超限报警";
        String content = String.format("用户下载次数超限报警详情:\n" +
                        "用户: %s\n" +
                        "账号: %s\n" +
                        "部门: %s\n" +
                        "下载限制: %s 次\n",
                currentUser.getName(), currentUser.getAccount(), userDepart, maxDownloadsPerDay);

        log.info("触发报警邮件: 收件人: {}, 主题: {}, 内容:\n{}", emailList, subject, content);

        try {
            FIleOutMailUtil.sendSimpleMail("PDM系统管理员", String.join(",", emailList), subject, content);
            // 标记为已报警
            redisTemplate.opsForValue().set(alarmKey, true);
            LocalDateTime tomorrowMidnight = LocalDate.now().plusDays(1).atStartOfDay();
            Date expiryDate = Date.from(tomorrowMidnight.atZone(ZoneId.systemDefault()).toInstant());
            redisTemplate.expireAt(alarmKey, expiryDate);
        } catch (Exception e) {
            log.error("发送报警邮件失败", e);
        }
    }
}
