package cn.jwis.product.pdm.customer.service.dto;

import com.aliyun.tea.NameInMap;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode
public class GetProcessInstanceResponseBodyResultTasks {
    @NameInMap("activityId")
    public String activityId;
    @NameInMap("createTime")
    public String createTime;
    @NameInMap("finishTime")
    public String finishTime;
    @NameInMap("mobileUrl")
    public String mobileUrl;
    @NameInMap("pcUrl")
    public String pcUrl;
    @NameInMap("processInstanceId")
    public String processInstanceId;
    @NameInMap("result")
    public String result;
    @NameInMap("status")
    public String status;
    @NameInMap("taskId")
    public Long taskId;
    @NameInMap("userId")
    public String userId;
    @NameInMap("activityName")
    public String activityName;
}
