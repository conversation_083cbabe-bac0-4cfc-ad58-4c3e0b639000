package cn.jwis.product.pdm.customer.service.util;

import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import cn.jwis.framework.base.exception.JWIException;
import org.apache.commons.lang3.StringUtils;

import java.io.InputStream;
import java.math.BigDecimal;
import java.util.*;


public class U9BomExcelParserUtil {

    public static class U9BOMItem {
        private final String parentPartNo;
        private final String partNo;
        private final Double qty;
        private final String position;
        private final String version;
        private final String owner;
        private final String lineNumber;

        public U9BOMItem(String parentPartNo, String partNo, Double qty, String position, String version, String owner,String lineNumber) {
            this.parentPartNo = parentPartNo;
            this.partNo = partNo;
            this.qty = qty;
            this.position = position;
            this.version = version;
            this.owner = owner;
            this.lineNumber = lineNumber;
        }

        public String getParentPartNo() { return parentPartNo; }
        public String getPartNo() { return partNo; }
        public Double getQty() { return qty; }
        public String getPosition() { return position; }
        public String getVersion() { return version; }
        public String getOwner() {return owner;}
        public String getLineNumber() {return lineNumber;}

        @Override
        public String toString() {
            return "U9BOMItem{" +
                    "parentPartNo='" + parentPartNo + '\'' +
                    ", partNo='" + partNo + '\'' +
                    ", qty=" + qty +
                    ", position='" + position + '\'' +
                    ", version='" + version + '\'' +
                    ", owner='" + owner + '\''+
                    ", lineNumber='" + lineNumber + '\''+
                    '}';
        }
    }

    public static List<U9BOMItem> parse(InputStream inputStream) {
        ExcelReader reader = ExcelUtil.getReader(inputStream);
        List<List<Object>> rows = reader.read(); // 含表头

        if (rows == null || rows.size() < 2) {
            throw new JWIException("Excel数据为空或无表头");
        }

        List<Object> header = rows.get(1);
        Map<String, Integer> columnIndexMap = getColumnIndexMap(header);

        List<U9BOMItem> result = new ArrayList<>();
        StringBuilder errorBuilder = new StringBuilder();
        //位号重复
        Map<String, Set<String>> parentToPositionsMap = new HashMap<>();
        Map<String, Set<String>> duplicatePositionsMap = new LinkedHashMap<>();
        Set<String> mismatchPartNos = new LinkedHashSet<>();
        //行号重复
        Map<String, Set<String>> parentToLineNumbersMap = new HashMap<>();
        Map<String, Set<String>> duplicateLineNumbersMap = new LinkedHashMap<>();

        int rowNum = 3;
        for (int i = 2; i < rows.size(); i++) {
            List<Object> row = rows.get(i);
            String parentPartNo = getCell(row, columnIndexMap.get("父级料号"));
            String partNo = getCell(row, columnIndexMap.get("子级料号"));
            String qtyStr = getCell(row, columnIndexMap.get("数量"));
            String positionStr = getCell(row, columnIndexMap.getOrDefault("位号", -1));
            String version = getCell(row, columnIndexMap.getOrDefault("版本", -1));
            String owner = getCell(row, columnIndexMap.getOrDefault("所有者", -1));
            String lineNumber = getCell(row, columnIndexMap.getOrDefault("行号", -1));

            boolean hasError = false;
            if (StringUtils.isBlank(parentPartNo)) {
                errorBuilder.append("第 ").append(rowNum).append(" 行缺少 父级料号（必填项）\n");
                hasError = true;
            }
            if (StringUtils.isBlank(partNo)) {
                errorBuilder.append("第 ").append(rowNum).append(" 行缺少 子级料号（必填项）\n");
                hasError = true;
            }

            Double qty = null;
            if (StringUtils.isBlank(qtyStr)) {
                errorBuilder.append("第 ").append(rowNum).append(" 行缺少 数量（必填项）\n");
                hasError = true;
            } else {
                try {
                    BigDecimal qtyDecimal = new BigDecimal(qtyStr.trim());
                    qty = qtyDecimal.doubleValue();
                } catch (NumberFormatException e) {
                    errorBuilder.append("第 ").append(rowNum).append(" 行 数量格式不正确（需为数字）\n");
                    hasError = true;
                }
            }

            // 数量与位号匹配性校验
            if (!hasError && qty != null && StringUtils.isNotBlank(positionStr)) {
                int refCount = positionStr.split("[,，]").length;
                BigDecimal qtyDecimal = BigDecimal.valueOf(qty);
                if (qtyDecimal.compareTo(BigDecimal.valueOf(refCount)) != 0) {
                    mismatchPartNos.add("第 " + rowNum + " 行，父级料号：" + parentPartNo + "（料号：" + partNo + "，数量与位号数不符）");
                }
            }

            // 位号重复校验（按父级料号分组）
            if (StringUtils.isNotBlank(positionStr)) {
                String[] positions = positionStr.split(",\\s*"); // 严格英文逗号分隔

                Set<String> parentPositions = parentToPositionsMap.computeIfAbsent(parentPartNo, k -> new HashSet<>());
                Set<String> duplicatePositions = duplicatePositionsMap.computeIfAbsent(parentPartNo, k -> new LinkedHashSet<>());

                for (String pos : positions) {
                    pos = pos.trim(); // 清理前后空格
                    if (pos.isEmpty()) continue;
                    if (!parentPositions.add(pos)) {
                        duplicatePositions.add(pos);
                    }
                }
            }

            // 行号重复校验（按父级料号分组）
            if (StringUtils.isNotBlank(lineNumber)) {
                Set<String> parentLineNumbers = parentToLineNumbersMap.computeIfAbsent(parentPartNo, k -> new HashSet<>());
                Set<String> duplicateLines = duplicateLineNumbersMap.computeIfAbsent(parentPartNo, k -> new LinkedHashSet<>());

                String line = lineNumber.trim();
                if (!parentLineNumbers.add(line)) {
                    duplicateLines.add(line);
                }
            }

            if (!hasError) {
                result.add(new U9BOMItem(parentPartNo, partNo, qty, positionStr, version, owner,lineNumber));
            }

            rowNum++;
        }

        if (!mismatchPartNos.isEmpty()) {
            errorBuilder.append("位号与数量不匹配：\n");
            mismatchPartNos.forEach(item -> errorBuilder.append("  - ").append(item).append("\n"));
        }
        if (!duplicatePositionsMap.isEmpty()) {
            StringBuilder duplicateError = new StringBuilder("位号重复：\n");
            boolean hasDuplicates = false;
            for (Map.Entry<String, Set<String>> entry : duplicatePositionsMap.entrySet()) {
                String parent = entry.getKey();
                Set<String> positions = entry.getValue();
                if (positions != null && !positions.isEmpty()) {
                    duplicateError.append("  - 父级料号：").append(parent).append("，重复位号：").append(String.join("，", positions)).append("\n");
                    hasDuplicates = true;
                }
            }
            if (hasDuplicates) {
                errorBuilder.append(duplicateError);
            }
        }
        if (!duplicateLineNumbersMap.isEmpty()) {
            StringBuilder duplicateLineError = new StringBuilder("行号重复：\n");
            boolean hasDuplicateLines = false;
            for (Map.Entry<String, Set<String>> entry : duplicateLineNumbersMap.entrySet()) {
                String parent = entry.getKey();
                Set<String> lines = entry.getValue();
                if (lines != null && !lines.isEmpty()) {
                    duplicateLineError.append("  - 父级料号：").append(parent).append("，重复行号：").append(String.join("，", lines)).append("\n");
                    hasDuplicateLines = true;
                }
            }
            if (hasDuplicateLines) {
                errorBuilder.append(duplicateLineError);
            }
        }

        if (errorBuilder.length() > 0) {
            throw new JWIException(errorBuilder.toString().trim());
        }

        return result;
    }

    private static Map<String, Integer> getColumnIndexMap(List<Object> headerRow) {
        Map<String, Integer> map = new HashMap<>();
        for (int i = 0; i < headerRow.size(); i++) {
            String header = headerRow.get(i) != null ? headerRow.get(i).toString().trim() : "";
            map.put(header, i);
        }

        List<String> requiredHeaders = Arrays.asList("父级料号", "子级料号", "数量");
        for (String required : requiredHeaders) {
            if (!map.containsKey(required)) {
                throw new JWIException("Excel缺少必填列: " + required);
            }
        }
        return map;
    }

    private static String getCell(List<Object> row, int index) {
        if (index < 0 || index >= row.size()) return "";
        Object val = row.get(index);
        return val == null ? "" : val.toString().trim();
    }
}