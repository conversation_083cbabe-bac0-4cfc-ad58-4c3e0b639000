package cn.jwis.product.pdm.customer.service.impl;

import cn.jwis.framework.base.exception.JWIException;
import cn.jwis.framework.base.response.PageResult;
import cn.jwis.framework.database.core.entity.SubFilter;
import cn.jwis.framework.database.core.entity.SubPageFilter;
import cn.jwis.platform.plm.container.entity.Folder;
import cn.jwis.platform.plm.container.entity.FolderTreeNode;
import cn.jwis.platform.plm.container.entity.response.FolderForExport;
import cn.jwis.platform.plm.container.entity.response.InstanceEntityWithCatalog;
import cn.jwis.platform.plm.container.service.impl.FolderServiceImpl;
import cn.jwis.platform.plm.foundation.common.response.InstanceEntity;
import cn.jwis.platform.plm.foundation.pipeline.manager.AbleWorkCellPipelineManager;
import cn.jwis.product.pdm.customer.repo.neo4j.CustomFolderRepoImpl;
import com.alibaba.fastjson.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;
import java.util.Map;

@Service
@Transactional
@Primary
public class CustomerFolderServiceImpl extends FolderServiceImpl {

    {
        AbleWorkCellPipelineManager.registerActuatorService(Folder.TYPE, this);
    }

    @Autowired
    CustomFolderRepoImpl folderRepo;

    @Override
    public List<FolderTreeNode> searchFoldersWithPermisson(String containerType, String containerModel, String containerOid, String searchKey) throws JWIException {
        return folderRepo.searchFoldersWithPermisson(containerType, containerModel, containerOid, searchKey);
    }

    @Override
    public List<FolderTreeNode> searchFolders(String containerType, String containerOid, String searchKey) throws JWIException {
        return folderRepo.searchFolders(containerType, containerOid, searchKey);
    }

    @Override
    public Long delete(String oid) throws JWIException {
        return folderRepo.deleteFolder(oid);
    }

    @Override
    public boolean hasContain(String oid) {
        return folderRepo.hasContain(oid);
    }

    @Override
    public List<Folder> findByOids(List<String> oids) {
        return folderRepo.findByOids(oids);
    }

    @Override
    public PageResult<InstanceEntity> dynamicQuerySubPage(SubPageFilter subPageFilter) throws JWIException{
        return folderRepo.dynamicQuerySubPage(subPageFilter);
    }

    @Override
    public boolean refreshRootFolderNameByContainer(String containerType, String containerOid, String name) {
        return folderRepo.refreshRootFolderNameByContainer(containerType, containerOid, name);
    }

    @Override
    public List<Folder> flapFolderTree(String folderOid){
        return folderRepo.flapFolderTree(folderOid);
    }

//    public PageResult<InstanceEntityWithCatalog> fuzzyFolderContentPage(FuzzyPageVO pageVO) {
//        return folderRepo.fuzzyFolderContentPage(pageVO);
//    }

    public PageResult<InstanceEntityWithCatalog> fuzzyFolderContentPage(cn.jwis.product.pdm.customer.entity.FuzzyPageVO pageVO) {
        return folderRepo.fuzzyFolderContentPage(pageVO);
    }

    public List<InstanceEntityWithCatalog> fuzzyFolderContent(cn.jwis.product.pdm.customer.entity.FuzzyPageVO pageVO) {
        PageResult<InstanceEntityWithCatalog> pgrs = folderRepo.fuzzyFolderContentPage(pageVO);
        if(pgrs!=null) {
           return pgrs.getRows();
        } else {
            return null;
        }
    }
    @Override
    public List<Folder> dynamicQueryByFrom(SubFilter subFilter) {
        return folderRepo.dynamicQueryByFrom(subFilter);
    }

    @Override
    public List<JSONObject> searchSecData(String folderOid, String searchKey) {
        return folderRepo.searchSecData(folderOid,searchKey);
    }

    @Override
    public FolderForExport findFolderTreeForExport(String containerOid) {
        return folderRepo.findFolderTreeForExport(containerOid);
    }

    @Override
    public Folder searchRootFolderByContainerOid(String containerOid, String tenantOid) {
        return folderRepo.searchRootFolderByContainerOid(containerOid,tenantOid);
    }

    @Override
    public Folder findFolderByPdmOid(String oid) {
        return folderRepo.findFolderByPdmOid(oid);
    }

    @Override
    public Long fuzzyFolderSubCount(Collection<String> folderOids, String searchKey, List<String> subTypes) {
        return folderRepo.fuzzyFolderSubCount(folderOids,searchKey,subTypes);
    }

    public Map<String, Long> fuzzyFolderSubCountMap(Collection<String> folderOids, String searchKey, List<String> subTypes) {
        return folderRepo.fuzzyFolderSubCountMap(folderOids, searchKey, subTypes);
    }



    @Override
    public Folder findFolderByCLS(String clsOid, String containerOid) {
        return folderRepo.findFolderByCLS(clsOid,containerOid);
    }

    @Override
    public List<Folder> searchRootFolder(String type, String searchKey) {
        return folderRepo.searchRootFolder(type,searchKey);
    }

    @Override
    public Folder rename(String oid, String name) {
        return folderRepo.rename(oid, name);
    }

}
