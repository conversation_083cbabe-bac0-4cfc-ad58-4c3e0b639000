package cn.jwis.product.pdm.customer.service.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 全局替代关系项
 */
@Data
@EqualsAndHashCode
public class GlobalSubstituteRelation {

    /**
     * 关系类型：1 = 全局替代
     */
    private Integer relationType = 1;

    /**
     * 替代料的物料编码（替代料号）
     */
    private String relationItemCode;

    /**
     * 序号，比如 10, 20, 30
     */
    private String sequenceNumber;

    /**
     * 状态：
     * -1 = 默认不区分
     *  1 = 新增
     *  2 = 变更
     *  3 = 删除
     */
    private Integer status = -1;
}