package cn.jwis.product.pdm.customer.service.dto;

import cn.jwis.platform.plm.file.entity.FileMetadata;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2023/8/10 13:43
 * @Email <EMAIL>
 */
@Data
@EqualsAndHashCode
public class DingTalkTaskCreateDTO {

    @ApiModelProperty("流程名称")
    private String pdmProcessName;
    @ApiModelProperty("任务名称")
    private String pdmTaskName;
    @ApiModelProperty("流程节点名称")
    private String currentFlowElement;
    @ApiModelProperty("pdm的任务链接")
    private String pdmTaskUrl;
    @ApiModelProperty("任务发起人的钉钉ID")
    private String originatorUserId;
    @ApiModelProperty("任务发起人的钉钉UionId")
    private String originatorUserUionId;
    @ApiModelProperty("任务责任人的钉钉ID")
    private String approverUserId;
    @ApiModelProperty("评审对象列表")
    private List<DingTaskReviewEntity> pdmReviewList = new ArrayList<>();
    @ApiModelProperty("评审文件对象列表")
    private List<FileMetadata> pdmReviewFileList = new ArrayList<>();
    @ApiModelProperty("审批模版的code")
    private String processCode;
    @ApiModelProperty("钉钉应用的AgentId")
    private Long agentId;

}
