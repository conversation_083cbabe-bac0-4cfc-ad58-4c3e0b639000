package cn.jwis.product.pdm.customer.service.impl;

import cn.jwis.framework.base.domain.entity.BaseEntity;
import cn.jwis.platform.plm.foundation.common.service.CommonAbilityHelper;
import cn.jwis.platform.plm.permission.filter.service.factory.BasePermissionFilter;
import cn.jwis.platform.plm.permission.permission.dto.FilterParameterDTO;
import cn.jwis.platform.plm.permission.permission.entity.BaseCatalogue;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;


@Component
@Slf4j
public class CustomerPartReviseFilter implements BasePermissionFilter {

    @Autowired
    private CommonAbilityHelper commonAbilityHelper;

    @Override
    public List<JSONObject> execute(List<JSONObject> filters, FilterParameterDTO dto) {
        // 不处理按钮结构，原样返回
        return filters;
    }

    @Override
    public Boolean doFilter(BaseCatalogue baseCatalogue, FilterParameterDTO dto) {
        Boolean flag = true;
        if (dto.getState().equalsIgnoreCase("Draft")
                || dto.getState().equalsIgnoreCase("Design")
                || dto.getState().equalsIgnoreCase("UnderReview")) {
            flag = false;
        }
        return flag;
    }

    @Override
    public List<BaseEntity> getEntity(FilterParameterDTO dto) {
        return Collections.emptyList();
    }
}
