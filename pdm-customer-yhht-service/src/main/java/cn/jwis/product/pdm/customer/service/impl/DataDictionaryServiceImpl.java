package cn.jwis.product.pdm.customer.service.impl;

import cn.jwis.framework.base.bean.util.BeanUtil;
import cn.jwis.framework.base.domain.able.ModelAble;
import cn.jwis.framework.base.domain.able.info.ModelInfo;
import cn.jwis.framework.base.exception.JWIException;
import cn.jwis.framework.base.util.CollectionUtil;
import cn.jwis.framework.base.util.StringUtil;
import cn.jwis.framework.base.util.ValidationUtil;
import cn.jwis.framework.database.core.query.dynamic.Condition;
import cn.jwis.framework.database.core.query.dynamic.Order;
import cn.jwis.platform.plm.dataengine.common.JWICommonService;
import cn.jwis.platform.plm.foundation.classification.entity.Classification;
import cn.jwis.platform.plm.foundation.common.service.CommonAbilityHelper;
import cn.jwis.product.pdm.customer.entity.DataDictionary;
import cn.jwis.product.pdm.customer.service.dto.DataDictionaryDTO;
import cn.jwis.product.pdm.customer.service.dto.DataDictionaryExcelDTO;
import cn.jwis.product.pdm.customer.service.dto.LayoutSelectDTO;
import cn.jwis.product.pdm.customer.service.excel.DataDictionaryExcelListener;
import cn.jwis.product.pdm.customer.service.interf.DataDictionaryService;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 数据字典
 */
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class DataDictionaryServiceImpl implements DataDictionaryService {


    @Resource
    private CommonAbilityHelper commonAbilityHelper;

    @Resource
    private JWICommonService jwiCommonService;

    @Resource
    private RedisTemplate<String, String> redisTemplate;


    @Override
    public DataDictionary save(DataDictionary dataDictionary, boolean editByCode) {
        ValidationUtil.validate(dataDictionary);
        DataDictionary oldData = StringUtil.isNotBlank(dataDictionary.getOid()) ? (DataDictionary) commonAbilityHelper.findDetailEntity(dataDictionary.getOid(), DataDictionary.TYPE) : null;
        if(editByCode && oldData == null && StringUtil.isNotBlank(dataDictionary.getCode())){
            ModelAble modelAble;
            if(Objects.equals(dataDictionary.getDataType(), "data")){
                modelAble = jwiCommonService.dynamicQuery(DataDictionary.TYPE,
                        Condition.where(DataDictionary::getCode).eq(dataDictionary.getCode())
                                .and(Condition.where(DataDictionary::getParentCode).eq(dataDictionary.getParentCode()))
                ).stream().findFirst().orElse(null);
            }else{
                modelAble = jwiCommonService.dynamicQuery(DataDictionary.TYPE, Condition.where(DataDictionary::getCode).eq(dataDictionary.getCode())).stream().findFirst().orElse(null);
            }

            if(modelAble != null){
                oldData = (DataDictionary) modelAble;
            }
        }

        switch (dataDictionary.getDataType()){
            case "group": {
                dataDictionary.setParentCode(null);
                break;
            }
            case "dictionary":
            case "data": {
                if(StringUtils.isBlank(dataDictionary.getParentCode())){
                    throw new JWIException("上级code不能为空ParentCode");
                }
                break;
            }
        }

        if(oldData == null){
            List<ModelAble> list;
            if(Objects.equals("data", dataDictionary.getDataType())){
               list = jwiCommonService.dynamicQuery(DataDictionary.TYPE,
                       Condition.where(DataDictionary::getCode).eq(dataDictionary.getCode())
                               .and(Condition.where(DataDictionary::getParentCode)
                                       .eq(dataDictionary.getParentCode()))
               );
            }else{
                list = jwiCommonService.dynamicQuery(DataDictionary.TYPE, Condition.where(DataDictionary::getCode).eq(dataDictionary.getCode()));
            }
            if(CollectionUtil.isNotEmpty(list)){
                throw new JWIException("已存在唯一标识：" + dataDictionary.getCode());
            }
            return commonAbilityHelper.doCreate(dataDictionary);
        }else{
            oldData.setEnable(dataDictionary.getEnable());
            oldData.setParentCode(dataDictionary.getParentCode());
            oldData.setDisplayName(dataDictionary.getDisplayName());
            oldData.setDataType(dataDictionary.getDataType());
            return commonAbilityHelper.doUpdate(oldData);
        }
    }

    @Override
    public boolean delete(String oid) {
        DataDictionary dataDictionary = (DataDictionary) commonAbilityHelper.findDetailEntity(oid, DataDictionary.TYPE);
        if(dataDictionary != null){
            List<ModelAble> list = jwiCommonService.dynamicQuery(DataDictionary.TYPE, Condition.where(DataDictionary::getParentCode).eq(dataDictionary.getCode()));
            if(!list.isEmpty()){
                throw new JWIException("存在子节点数据，请先删除所有子节点数据");
            }
            return jwiCommonService.delete(DataDictionary.TYPE, oid) > 0;
        }else{
            throw new JWIException("未找到要删除的oid");
        }
    }

    @Override
    public long toggleEnable(String oid, Boolean enable) {
        DataDictionary dataDictionary = (DataDictionary) commonAbilityHelper.findDetailEntity(oid, DataDictionary.TYPE);
        if(dataDictionary != null){
            dataDictionary.setEnable(enable);
            commonAbilityHelper.doUpdate(dataDictionary);
        }else{
            throw new JWIException("未找到字典信息");
        }
        return 1L;
    }

    @Override
    public List<DataDictionaryDTO> treeList(String searchKey) {
        List<DataDictionary> dataDictionaries = jwiCommonService.dynamicQuery(DataDictionary.TYPE, Condition.empty(), Order.desc(DataDictionary::getCreateDate), DataDictionary.class);
        List<DataDictionary> filters = dataDictionaries.stream().filter(item ->
                Objects.equals("data", item.getDataType())
                        || StringUtil.isBlank(searchKey)
                        || item.getCode().contains(searchKey)
                        || item.getDisplayName().contains(searchKey))
                .collect(Collectors.toList());
        List<DataDictionaryDTO> res = new ArrayList<>(filters.size());

        List<DataDictionaryDTO> listData = filters.stream().map(item -> {
            DataDictionaryDTO dto = new DataDictionaryDTO();
            BeanUtil.copyProperties(item, dto);
            dto.setChildren(new ArrayList<>());
            return dto;
        }).collect(Collectors.toList());

        Map<String, DataDictionaryDTO> map = listData.stream().collect(Collectors.toMap(DataDictionary::getCode, (o) -> o, (o1, o2) -> o2, LinkedHashMap::new));

        listData.forEach((filter) -> {
            if(StringUtils.isBlank(filter.getParentCode()) || !map.containsKey(filter.getParentCode())){
                res.add(filter);
            }else{
                DataDictionaryDTO dataDictionary = map.get(filter.getParentCode());
                dataDictionary.getChildren().add(filter);
            }
        });

        return res;
    }

    @Override
    public void exportExcel(HttpServletResponse response, String searchKey) {
        List<DataDictionary> dataDictionaries = jwiCommonService.dynamicQuery(DataDictionary.TYPE, Condition.empty(), Order.desc(DataDictionary::getCreateDate), DataDictionary.class);
        List<DataDictionary> filters = dataDictionaries.stream().filter(item ->
                        Objects.equals("data", item.getDataType())
                                || searchKey == null
                                || item.getCode().contains(searchKey)
                                || item.getDisplayName().contains(searchKey))
                .collect(Collectors.toList());

        List<DataDictionaryExcelDTO> listData = new ArrayList<>(filters.size());
        for (DataDictionary filter : filters) {
            DataDictionaryExcelDTO excelDTO = new DataDictionaryExcelDTO();
            BeanUtil.copyProperties(filter, excelDTO);
            listData.add(excelDTO);
        }

        // 提取所有 parentCode
        Set<String> parentCodes = listData.stream()
                .map(DataDictionaryExcelDTO::getParentCode)
                .collect(Collectors.toSet());

        // 批量查询所有 parentCode 对应的 DataDictionary
        Map<String, DataDictionary> parentMap = parentCodes.stream()
                .collect(Collectors.toMap(
                        parentCode -> parentCode,
                        parentCode -> {
                            DataDictionary parent = jwiCommonService.dynamicQueryOne(DataDictionary.TYPE,
                                    Condition.where(DataDictionary::getCode).eq(parentCode), DataDictionary.class);
                            return parent != null ? parent : new DataDictionary();
                        }
                ));

        // 设置 parentName
        for (DataDictionaryExcelDTO listDatum : listData) {
            DataDictionary parent = parentMap.get(listDatum.getParentCode());
            if (parent != null) {
                listDatum.setParentName(parent.getDisplayName());
            }
        }


        try {
            EasyExcel.write(response.getOutputStream(), DataDictionaryExcelDTO.class).sheet().doWrite(listData);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public void exportExcelTemp(HttpServletResponse response) {
        try {
            EasyExcel.write(response.getOutputStream(), DataDictionaryExcelDTO.class).sheet().doWrite(new ArrayList<>());
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public void importExcel(MultipartFile file) {
        try {
            EasyExcel.read(file.getInputStream(), DataDictionaryExcelDTO.class, new DataDictionaryExcelListener()).sheet().doRead();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public List<LayoutSelectDTO> getEnableList(String code) {
        if(StringUtils.isBlank(code)){
            return new ArrayList<>();
        }

        String json = redisTemplate.opsForValue().get("system_data_dictionary" + code);
        if(StringUtils.isNotBlank(json)){
            return JSON.parseArray(json, LayoutSelectDTO.class);
        }
        List<DataDictionary> dataDictionaries = jwiCommonService.dynamicQuery(DataDictionary.TYPE,
                Condition.where(DataDictionary::getEnable).eq(Boolean.TRUE)
                .and(Condition.where(DataDictionary::getParentCode).eq(code)),new Order().desc("createDate"),
            DataDictionary.class);
        List<LayoutSelectDTO> res = dataDictionaries.stream().map(item -> {
            LayoutSelectDTO selectDTO = new LayoutSelectDTO();
            selectDTO.setTxt(item.getDisplayName());
            selectDTO.setVal(item.getCode());
            return selectDTO;
        }).collect(Collectors.toList());

        redisTemplate.opsForValue().set("system_data_dictionary" + code, JSON.toJSONString(res), 10, TimeUnit.SECONDS);

        return res;
    }
}
