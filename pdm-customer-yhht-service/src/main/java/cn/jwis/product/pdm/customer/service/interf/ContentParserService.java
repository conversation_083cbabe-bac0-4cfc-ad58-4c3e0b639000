package cn.jwis.product.pdm.customer.service.interf;

import cn.jwis.product.pdm.customer.entity.SignInfo;
import com.itextpdf.awt.geom.Rectangle2D;
import com.itextpdf.text.Rectangle;
import com.itextpdf.text.pdf.PdfReader;
import com.itextpdf.text.pdf.parser.TextRenderInfo;

public interface ContentParserService {

    SignInfo contentParser(PdfReader reader) throws Exception;

    Rectangle rectangle(String signName, Rectangle2D.Float boundingRectange, TextRenderInfo textRenderInfo, Boolean calcu, SignInfo signInfo);

}
