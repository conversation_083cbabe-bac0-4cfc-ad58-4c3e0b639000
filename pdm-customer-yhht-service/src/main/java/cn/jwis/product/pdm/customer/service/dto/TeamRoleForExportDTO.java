package cn.jwis.product.pdm.customer.service.dto;

import cn.jwis.platform.plm.container.entity.response.UserForExport;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * @author: 汪江
 * @data: 2024-01-04 16:30
 * @description:
 **/
@Data
@EqualsAndHashCode
public class TeamRoleForExportDTO {
    private String teamRoleOid;
    private String name;
    private List<UserForExport> users;
}
