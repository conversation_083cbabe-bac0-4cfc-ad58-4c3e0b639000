package cn.jwis.product.pdm.customer.service.dto;

public enum PdfSignatureTemplate {
    A4("pdf签名模板A4新"),
    A4x3("pdf签名模板A4x3"),
    A3("pdf签名模板A3新"),
    A3x4("pdf签名模板A3x4"),
    A2("pdf签名模板A2新"),
    A2x3("pdf签名模板A2x3"),
    A1("pdf签名模板A1新"),
    A1x3("pdf签名模板A1x3"),
    A1x4("pdf签名模板A1x4"),
    A0("pdf签名模板A0新"),
    A0x2("pdf签名模板A0x2"),
    A0x3("pdf签名模板A0x3"),
    ECAD_A2("ecad-pdf签名模板A2新"),
    ECAD_A3("ecad-pdf签名模板A3新");

    private final String key;

    PdfSignatureTemplate(String key) {
        this.key = key;
    }

    public String getKey() {
        return key;
    }

    @Override
    public String toString() {
        return this.key;
    }
}