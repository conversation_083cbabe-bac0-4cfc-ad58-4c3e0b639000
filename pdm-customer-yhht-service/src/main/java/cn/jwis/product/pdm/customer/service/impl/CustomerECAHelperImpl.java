package cn.jwis.product.pdm.customer.service.impl;

import cn.hutool.json.JSONUtil;
import cn.jwis.framework.base.domain.able.info.ModelInfo;
import cn.jwis.framework.base.exception.JWIException;
import cn.jwis.framework.base.util.Assert;
import cn.jwis.framework.base.util.SpringContextUtil;
import cn.jwis.framework.base.util.StringUtil;
import cn.jwis.framework.base.web.session.SessionHelper;
import cn.jwis.platform.plm.foundation.common.service.CommonAbilityHelper;
import cn.jwis.platform.plm.foundation.common.service.InstanceService;
import cn.jwis.platform.plm.foundation.location.able.info.LocationInfo;
import cn.jwis.platform.plm.foundation.model.related.ModelWorkflowAssign;
import cn.jwis.platform.plm.foundation.model.service.ModelAssignHelper;
import cn.jwis.platform.plm.permission.dynamic.service.IDynamicPermissionService;
import cn.jwis.platform.plm.workflow.engine.service.interf.ProcessOrderHelper;
import cn.jwis.product.pdm.cad.ecad.service.ECADHelper;
import cn.jwis.product.pdm.cad.mcad.entity.MCADIteration;
import cn.jwis.product.pdm.cad.mcad.service.MCADHelper;
import cn.jwis.product.pdm.change.dto.SearchChangeInfoDTO;
import cn.jwis.product.pdm.change.dto.WorkflowDTO;
import cn.jwis.product.pdm.change.entity.ECA;
import cn.jwis.product.pdm.change.response.ChangeInfo;
import cn.jwis.product.pdm.change.service.ChangeCommonAppHelper;
import cn.jwis.product.pdm.change.service.ChangeService;
import cn.jwis.product.pdm.change.service.ECAHelperImpl;
import cn.jwis.product.pdm.change.service.ECAService;
import cn.jwis.product.pdm.change.workflow.PDMChangeWorkflowRemote;
import cn.jwis.product.pdm.change.workflow.dto.ProOrdCreateDTO;
import cn.jwis.product.pdm.change.workflow.dto.TeamRoleUserDTO;
import cn.jwis.product.pdm.customer.repo.CustomerCommonRepo;
import cn.jwis.product.pdm.document.entity.DocumentIteration;
import cn.jwis.product.pdm.document.service.DocumentHelper;
import cn.jwis.product.pdm.partbom.part.entity.PartIteration;
import cn.jwis.product.pdm.partbom.part.service.PartHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Primary;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2023/10/12 16:48
 * @Email <EMAIL>
 */
@Service
@Primary
public class CustomerECAHelperImpl extends ECAHelperImpl {

    private static final Logger logger = LoggerFactory.getLogger(CustomerECAHelperImpl.class);

    @Value("${eca.workflow.excute.role}")
    String ecaWorkflowExcutor;

    @Autowired
    ECAService ecaService;

    @Autowired
    ModelAssignHelper modelAssignHelper;

    @Autowired
    PDMChangeWorkflowRemote pdmChangeWorkflowRemote;

    @Autowired
    ECADHelper ecadHelper;

    @Autowired
    MCADHelper mcadHelper;

    @Autowired
    PartHelper partHelper;

    @Autowired
    DocumentHelper documentHelper;

    @Autowired
    ChangeCommonAppHelper commonAppHelper;

    @Autowired
    CustomerCommonRepo customerCommonRepo;

    @Override
    public boolean startWorkflow(WorkflowDTO dto) {
        String processOrderType = dto.getProcessOrderType();
        String processInstanceId = dto.getProcessInstanceId();
        List<ECA> ecas = ecaService.findByECRWorkflow(processOrderType, processInstanceId);
        Assert.notEmpty(ecas, "ECA does not exists ");
        // 获取 ECA 关联的模型
        ModelWorkflowAssign ecaWorkflow = modelAssignHelper.findWorkflow(ECA.TYPE, SessionHelper.getCurrentUser().getTenantOid());
        Assert.notNull(ecaWorkflow, "The work of ECA does not exists ");
        ProOrdCreateDTO proOrdCreateDTO = null;
        LocationInfo locationInfo = null;
        List<TeamRoleUserDTO> teamContent = null;
        TeamRoleUserDTO roleUserDTO = null;
        List<ProOrdCreateDTO> batchCreate = new ArrayList<>(ecas.size());
        ModelInfo modelInfo = null;
        for (ECA eca : ecas) {
            proOrdCreateDTO = new ProOrdCreateDTO();
            // location
            locationInfo = new LocationInfo();
            locationInfo.setCatalogOid(eca.getCatalogOid());
            locationInfo.setCatalogType(eca.getCatalogType());
            locationInfo.setContainerOid(eca.getContainerOid());
            locationInfo.setContainerType(eca.getContainerType());
            locationInfo.setContainerModelDefinition(eca.getContainerModelDefinition());
            proOrdCreateDTO.setLocationInfo(locationInfo);
            // 流程对象
            modelInfo = new ModelInfo();
            modelInfo.setOid(eca.getOid());
            modelInfo.setType(ECA.TYPE);
            proOrdCreateDTO.setBizObjects(Arrays.asList(modelInfo));
            //流程基本信息
            proOrdCreateDTO.setName(eca.getNumber() + "，"+ eca.getName());
            proOrdCreateDTO.setModelDefinition("ProcessOrder");
            proOrdCreateDTO.setProcessModelId(ecaWorkflow.getWorkflowModelId());
            // 角色成员
            teamContent = new ArrayList<>(2);
            roleUserDTO = new TeamRoleUserDTO();
            roleUserDTO.setRoleName(ecaWorkflowExcutor);
            roleUserDTO.setUserAccounts(Arrays.asList(eca.getPersonLiable().getAccount()));
            teamContent.add(roleUserDTO);
            proOrdCreateDTO.setTeamContent(teamContent);
            batchCreate.add(proOrdCreateDTO);
        }
        // 批量启动
        pdmChangeWorkflowRemote.batchCreateThenStartProcessOrder(batchCreate);
        return true;
    }

    @Override
    public void confirmChange(String oid) {
        //通过ECA获取变更对象信息
        SearchChangeInfoDTO searchChangeInfoDTO = new SearchChangeInfoDTO();
        searchChangeInfoDTO.setOid(oid);
        searchChangeInfoDTO.setList(null);
        List<ChangeInfo> changeInfo = findChangeInfo(searchChangeInfoDTO);
        //判断每个变更对象是否存在变更方案，若不存在变更方案返回错误信息
        HashMap<String,String> map = new HashMap();
        changeInfo.forEach(n->{
            String changeOid = n.getOid();
            String type = n.getType();
            String changeSchemeOid = ecaService.findChangeSchemeOid(changeOid, type);
            if (StringUtil.isEmpty(changeSchemeOid)){
                throw new JWIException(n.getNumber()+"不存在变更方案无法变更，请填写变更方案");
            }
            map.put(changeOid,changeSchemeOid);
        });
        CustomerECAHelperImpl customerECAHelper = (CustomerECAHelperImpl) SpringContextUtil.getBean("customerECAHelperImpl");
        // 先排序，父项在前，子项在后，为了解决父子同在一个事务中，子先修订父后修订导致的BOM异常

        changeInfo = changeInfo.stream().sorted(((prev, next) -> "PartIteration".equals(prev.getType()) && "PartIteration".equals(next.getType())
                ? customerCommonRepo.lastIsParent(prev.getOid(), next.getOid()) : -1)).collect(Collectors.toList());
        logger.info("confirmChange.changeInfo.sort.result=" +
                JSONUtil.toJsonStr(changeInfo.stream().map(c->c.getName()).collect(Collectors.toList())));

        changeInfo.forEach(n->{
            Future<String> result = customerECAHelper.execute(n,map);
            try {
                result.get();
            } catch (Exception e) {
                e.printStackTrace();
            }
        });
    }

    @Async
    public Future<String> execute(ChangeInfo n, HashMap<String,String> map){
        String changeOid = n.getOid();
        String type = n.getType();
        //将对象和ECA，ECO，ECR的关系字段进行修改
        ecaService.updateImpact(changeOid,type);
        String changeSchemeOid = map.get(changeOid);
        // 物料
        if (PartIteration.TYPE.equals(type)){
            partHelper.confirmChange(changeOid,changeSchemeOid);
        }
        // 文档
        else if (DocumentIteration.TYPE.equals(type)){
            documentHelper.confirmChange(changeOid,changeSchemeOid);
        }
        // MCAD
        else if (MCADIteration.TYPE.equals(type)) {
            mcadHelper.confirmChange(changeOid,changeSchemeOid);
        }
        // ECAD
        else if (MCADIteration.TYPE.equals(type)){
            ecadHelper.confirmChange(changeOid,changeSchemeOid);
        }
        else {
            try {
                commonAppHelper.confirmChange(changeOid,changeSchemeOid,type);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return new AsyncResult<String>("success");
    }

}
