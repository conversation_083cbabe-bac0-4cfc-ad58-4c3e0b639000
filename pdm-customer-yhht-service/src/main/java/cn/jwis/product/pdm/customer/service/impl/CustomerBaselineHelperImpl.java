package cn.jwis.product.pdm.customer.service.impl;

import cn.jwis.framework.base.exception.JWIException;
import cn.jwis.product.pdm.baseline.baseline.service.BaselineHelperImpl;
import cn.jwis.product.pdm.baseline.entity.AddBaselineItemDTO;
import cn.jwis.product.pdm.baseline.entity.BatchDeleteDTO;
import lombok.SneakyThrows;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR> yefei
 * @Date ： 2024/3/28
 * @Description :
 */

@Component
@Primary
public class CustomerBaselineHelperImpl extends BaselineHelperImpl {


    @Resource
    TriggerAuditServiceImpl triggerAuditService;
    @Override
    @SneakyThrows
    public void batchLink(AddBaselineItemDTO dto) {
        super.batchLink(dto);
        dto.getSecObjList().forEach(item -> triggerAuditService.batchLink(item, dto.getOid()));
    }

    @Override
    public void batchDeleteLink(BatchDeleteDTO dto) throws JWIException {
        super.batchDeleteLink(dto);
        dto.getSubOidList().forEach(item -> triggerAuditService.batchDeleteLink(item, dto.getOid()));
    }
}
