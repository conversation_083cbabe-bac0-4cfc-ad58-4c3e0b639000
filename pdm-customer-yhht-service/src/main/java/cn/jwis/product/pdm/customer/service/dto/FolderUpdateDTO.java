package cn.jwis.product.pdm.customer.service.dto;

import cn.jwis.platform.plm.foundation.location.able.info.LocationInfo;
import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;

@Data
@EqualsAndHashCode
@Valid
public class FolderUpdateDTO{

    private String oid;

    @NotBlank(message = "名称不能为空")
    private String name;

    private LocationInfo locationInfo;

    private JSONObject extensionContent;

}
