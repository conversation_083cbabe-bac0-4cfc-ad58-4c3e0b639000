package cn.jwis.product.pdm.customer.service.release;

import cn.jwis.framework.base.annotation.Description;
import cn.jwis.framework.base.util.SpringContextUtil;
import cn.jwis.platform.plm.container.entity.Container;
import cn.jwis.platform.plm.datadistribution.service.DataDistributionService;
import cn.jwis.platform.plm.dataengine.common.JWICommonService;
import cn.jwis.platform.plm.dataengine.common.JWICommonServiceImpl;
import cn.jwis.platform.plm.foundation.classification.service.ClassificationService;
import cn.jwis.platform.plm.foundation.common.service.CommonAbilityService;
import cn.jwis.platform.plm.workflow.engine.order.entity.ProcessOrder;
import cn.jwis.product.pdm.cad.ecad.entity.ECADIteration;
import cn.jwis.product.pdm.cad.mcad.entity.MCADIteration;
import cn.jwis.product.pdm.customer.entity.IntegrationRecord;
import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;

import java.io.PrintWriter;
import java.io.StringWriter;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:  实体发布器: 发布到MQ等
 * @date 2023/8/15 11:10
 * @Email <EMAIL>
 */
@Data
@EqualsAndHashCode
@Description("抽象发布能力提供者")
@Slf4j
public abstract class EntityRelease implements InitializingBean {

    public String type;

    // 数据发布总入口,item发布时，增加isFirst，1=首次发布，2=更新发布
    public abstract IntegrationRecord release(ProcessOrder processOrder,String entityOid,String recordOid, int isFirst);

    // 数据发布MQ入口
    protected String releaseMQ(IntegrationRecord record, DataDistributionService dataDistributionService) {
        try {
            String businessType = record.getBusinessType();
            dataDistributionService.sendDataToMq(record.getData(), "release", businessType);
        } catch (Exception e) {
            log.error("EntityRelease error:", e);
            StringWriter sw = new StringWriter();
            PrintWriter pw = new PrintWriter(sw);
            e.printStackTrace(pw);
            String exceptionString = sw.toString();
            return exceptionString.substring(0,3000);
        }
        return "";
    }

    protected IntegrationRecord getIntegrationRecord(String recordOid, String type, ProcessOrder processOrder, JSONObject releaseData) {
        IntegrationRecord integrationRecord = null;
        JWICommonService jwiCommonService = SpringContextUtil.getBean(JWICommonServiceImpl.class);
//        String businessOid = releaseData.getString("businessOid");
//        if(StringUtil.isNotBlank(recordOid)) { // 重发时会传recordOid
//            CommonAbilityService commonAbilityService = (CommonAbilityService) SpringContextUtil.getBean("commonAbilityServiceImpl");
//            integrationRecord = (IntegrationRecord)commonAbilityService.findByOid(recordOid,IntegrationRecord.TYPE);
//            integrationRecord.setData(releaseData);
//        }else if(StringUtil.isNotBlank(businessOid)){ // 按编码发时，可能已经发过（发布流程走到了推送下游节点），也可能没发过（发布流程异常？取消？）
//            EntityFilter filter = new EntityFilter();
//            filter.setType(IntegrationRecord.TYPE);
//            filter.setFilter(Condition.where("businessOid").eq(businessOid));
//            integrationRecord = CollectionUtil.getFirst(jwiCommonService.dynamicQuery(filter,IntegrationRecord.class));
//            if(integrationRecord != null){
//                integrationRecord.setData(releaseData);
//            }
//        }
        // 不存在再新建
        if(integrationRecord == null){
            integrationRecord = new IntegrationRecord(
                    type,
                    processOrder == null ? "tool" : "process",
                    processOrder == null ? "" : processOrder.getNumber(),
                    releaseData.getString("businessOid"),  // 业务OID，标识此条数据，用来接收下游的集成结果
                    releaseData);
            integrationRecord = jwiCommonService.create(integrationRecord);
        }
        //执行实例Id，回调函数中据此更新流程中变量
        if(processOrder != null) {
            integrationRecord.setExecuteId(processOrder.getProcessInstanceId());
        }
        return integrationRecord;
    }


    protected String initProductName(String containerOid) {
        CommonAbilityService commonAbilityService = (CommonAbilityService) SpringContextUtil.getBean("commonAbilityServiceImpl");
        Container container = (Container)commonAbilityService.findByOid(containerOid, Container.TYPE);
        return container.getName();
    }

    //todo  cad的分类怎么指定
    protected String getClsPath(String type,String modelDefinition,JSONObject clsProperty) {
        if(clsProperty != null){
            String oid = clsProperty.getString("clsOid");
            ClassificationService classificationService = (ClassificationService) SpringContextUtil.getBean("defaultClassificationServiceImpl");
            return classificationService.findClsNamePathByOid(oid);
        }
        if(MCADIteration.TYPE.equals(type)) {
            return "文档-MCAD";
        } else if (ECADIteration.TYPE.equals(type)) {
            return "文档-ECAD";
        }else{
            return "文档";
        }
    }

}
