package cn.jwis.product.pdm.customer.service.delegate;

import cn.jwis.framework.base.bean.util.BeanUtil;
import cn.jwis.framework.base.domain.able.ModelAble;
import cn.jwis.framework.base.util.CollectionUtil;
import cn.jwis.framework.base.util.SpringContextUtil;
import cn.jwis.framework.base.util.StringUtil;
import cn.jwis.platform.plm.foundation.common.dto.InstanceBasicDTO;
import cn.jwis.platform.plm.foundation.common.response.InstanceEntity;
import cn.jwis.platform.plm.foundation.common.service.CommonAbilityService;
import cn.jwis.platform.plm.workflow.engine.order.entity.ProcessOrder;
import cn.jwis.platform.plm.workflow.engine.service.interf.ProcessOrderHelper;
import cn.jwis.product.pdm.change.entity.Issue;
import cn.jwis.product.pdm.change.response.IssueInfo;
import cn.jwis.product.pdm.customer.entity.Forming;
import cn.jwis.product.pdm.customer.entity.IntegrationRecord;
import cn.jwis.product.pdm.customer.entity.Screen;
import cn.jwis.product.pdm.customer.service.impl.CustomerCommonServiceImpl;
import cn.jwis.product.pdm.customer.service.interf.IntegrationMonitorService;
import cn.jwis.product.pdm.customer.service.release.EntityRelease;
import cn.jwis.product.pdm.customer.service.release.EntityReleaseFactory;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.RuntimeService;
import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.Expression;
import org.activiti.engine.delegate.JavaDelegate;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 工作流执行代理：检查所有评审对象关联的问题是否都已关闭
 * @date 2023/8/15 10:44
 * @Email <EMAIL>
 */
@Slf4j
public class CheckAllProblemPassDelegate implements JavaDelegate {

    // 流程模板中配置的代理类参数，按需使用即可
    private Expression businessType;

    @Override
    public void execute(DelegateExecution execution) {
        String processInstanceId = execution.getProcessInstanceId();
        log.info("CheckAllProblemPassDelegate execute procId={}", processInstanceId);
        RuntimeService runtimeService = SpringContextUtil.getBean(RuntimeService.class);
        try {
            ProcessOrderHelper processOrderHelper = (ProcessOrderHelper) SpringContextUtil.getBean("processOrderHelperImpl");
            // processOrder：流程申请单对象
            ProcessOrder processOrder = processOrderHelper.findByProcessInstanceId(processInstanceId);
            log.info("CheckAllProblemPassDelegate  processOrder==>{}", JSONObject.toJSONString(processOrder));
            // instanceEntityList：评审对象集合（部件，文档等）
            List<InstanceEntity> instanceEntityList = processOrderHelper.findBizObject(processOrder.getOid());
            // 评审对象为空则不处理
            if(CollectionUtil.isEmpty(instanceEntityList)){
                log.info("CheckAllProblemPassDelegate  instanceEntity is empty! ");
                runtimeService.setVariable(execution.getId(), "allProblemPass", "Y");
                return;
            }
            boolean allPass = checkAllEntity(instanceEntityList);
            runtimeService.setVariable(execution.getId(), "allProblemPass", allPass ? "Y":"N");
        } catch (Exception e) {
            log.error("CheckAllProblemPassDelegate execute error==>", e);
            runtimeService.setVariable(execution.getId(), "allProblemPass", "N");
            StringBuffer buffer = new StringBuffer();
            StackTraceElement[] stackTrace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : stackTrace) {
                buffer.append(stackTraceElement.toString() + "\n");
            }
            runtimeService.setVariable(execution.getId(), "CheckAllProblemPassError",buffer);
        }
    }

    private boolean checkAllEntity(List<InstanceEntity> instanceEntityList) throws Exception {
        CustomerCommonServiceImpl customerCommonService =
                (CustomerCommonServiceImpl) SpringContextUtil.getBean("customerCommonServiceImpl");
        List<InstanceBasicDTO> instances = new ArrayList<>();
        for(InstanceEntity entity : instanceEntityList){
            instances.add(BeanUtil.copyProperties(entity,new InstanceBasicDTO()));
        }
        List<Issue> issueInfos = customerCommonService.findIssueByEntity(instances,true);
        if(CollectionUtil.isEmpty(issueInfos)){
            return true;
        }
        List<Issue> unClosed = issueInfos.stream().filter(i->!"Closed".equals(i.getLifecycleStatus())).collect(Collectors.toList());
        if(CollectionUtil.isEmpty(unClosed)){
            return true;
        }
        return false;
    }
}
