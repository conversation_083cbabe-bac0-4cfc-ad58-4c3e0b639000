package cn.jwis.product.pdm.customer.service.interf;


import cn.jwis.platform.plm.modelexcel.dto.ModelExcelAllImportDTO;
import cn.jwis.product.pdm.partbom.part.dto.PartClassificationDTO;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * @author: 汪江
 * @data: 2023-12-21 14:00
 * @description:
 **/
public interface CustomerPartService3 {
    void importExcelTemp(ModelExcelAllImportDTO importDTO, HttpServletRequest request);


    List<Map<Integer, Object>> queryPartDataByHeadMap(List<PartClassificationDTO> partDtoList, Map<String, String> headNameCodeMap
            , List<List<String>> headList, Map<String, Boolean> headNameIsSystemDefaultMap, Map<String, Map<String, Map<String, String>>> drownData4ModelDefinition);

}
