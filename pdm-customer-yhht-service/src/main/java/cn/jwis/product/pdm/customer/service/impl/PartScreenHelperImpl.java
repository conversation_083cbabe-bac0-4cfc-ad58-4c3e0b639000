package cn.jwis.product.pdm.customer.service.impl;

import cn.jwis.framework.base.bean.util.BeanUtil;
import cn.jwis.framework.base.domain.able.RelationAble;
import cn.jwis.framework.base.util.OidGenerator;
import cn.jwis.platform.plm.dataengine.common.JWICommonService;
import cn.jwis.platform.plm.foundation.common.service.CommonAbilityHelper;
import cn.jwis.product.pdm.customer.entity.Forming;
import cn.jwis.product.pdm.customer.entity.Screen;
import cn.jwis.product.pdm.customer.service.interf.PartScreenHelper;
import cn.jwis.product.pdm.partbom.part.dto.BatchCopyPartDTO;
import cn.jwis.product.pdm.partbom.part.dto.BatchCopyPartDocumentBatchItemDTO;
import cn.jwis.product.pdm.partbom.part.dto.BatchCopyResultDTO;
import cn.jwis.product.pdm.partbom.part.entity.Part;
import cn.jwis.product.pdm.partbom.part.entity.PartIteration;
import cn.jwis.product.pdm.partbom.part.service.PartService;
import com.alibaba.fastjson.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class PartScreenHelperImpl implements PartScreenHelper {

    @Autowired
    JWICommonService jWICommonService;

    @Autowired
    PartService partService;

    @Autowired
    CommonAbilityHelper commonAbilityHelper;

    @Override
    public void partBatchCopy(BatchCopyPartDTO batchCopyPartDTO) {
        Map<String, String[]> oidCorrespond = new HashMap<>();
        Map<String, String[]> oidScreenCorrespond = new HashMap<>();
        List<PartIteration> parts = new ArrayList<>();
        if ("screen".equals(batchCopyPartDTO.getType())) {
            for (String sourceOid : batchCopyPartDTO.getSourceOids()) {
                PartIteration iteration = (PartIteration) commonAbilityHelper.findDetailEntity(sourceOid, PartIteration.TYPE);
                if (iteration != null) {
                    JSONObject clsProperty = iteration.getClsProperty();
                    clsProperty.put("cn_jwis_sxzldj", "是");
                    clsProperty.put("cn_jwis_zldj", "39");
                    JSONObject extensionContent = iteration.getExtensionContent();
                    // 图号/规格默认添加(筛选级)关键字
                    extensionContent.put("cn_jwis_gg", extensionContent.getString("cn_jwis_gg") + "（筛选级）");
                    parts.add(iteration);
                }
            }
        } else if ("forming".equals(batchCopyPartDTO.getType())) {
            for (String sourceOid : batchCopyPartDTO.getSourceOids()) {
                PartIteration oldData = new PartIteration();
                PartIteration iteration = (PartIteration) commonAbilityHelper.findDetailEntity(sourceOid, PartIteration.TYPE);
                BeanUtil.copyProperties(iteration, oldData);
                oldData.setName(oldData.getName() + "(已成型)");
                // 修改元数据名称
                commonAbilityHelper.doUpdate(oldData);
                parts.add(iteration);
            }
        }
        //保存基本数据
        List<PartIteration> newParts = this.savePartList(parts, batchCopyPartDTO, oidCorrespond, oidScreenCorrespond);
        //保存结构关系和文档关系
        this.savePartRel(batchCopyPartDTO, oidCorrespond, parts, oidScreenCorrespond);
        BatchCopyResultDTO resultDTO = new BatchCopyResultDTO();
        resultDTO.setList(newParts);
    }

    private List<PartIteration> savePartList(List<PartIteration> parts, BatchCopyPartDTO dto, Map<String, String[]> oidCorrespond, Map<String, String[]> oidScreenCorrespond) {
        List<PartIteration> list = new ArrayList<>(dto.getSourceOids().size());
        Map<String, String> newNames = dto.getNames().stream().collect(Collectors.toMap(BatchCopyPartDocumentBatchItemDTO::getSourceOid, BatchCopyPartDocumentBatchItemDTO::getNewName));
        for (PartIteration partIteration : parts) {
            String sourceOid = partIteration.getOid();
            String sourceMasterOid = partIteration.getMasterOid();
            PartIteration copyPartDTO = BeanUtil.copyProperties(partIteration, new PartIteration());
            if (newNames.containsKey(sourceOid)) {
                copyPartDTO.setName(newNames.get(sourceOid));
            }
            copyPartDTO.setCurrentStage(dto.getCurrentStage());
            copyPartDTO.setNumber(null);
            copyPartDTO.setThumbnailOid(null);
            partService.setLocation(copyPartDTO, dto.getLocationInfo());
            copyPartDTO.setOid(OidGenerator.newOid());
            copyPartDTO.setMasterOid(OidGenerator.newOid());
            //存储新旧数据对应关系
            String[] str = new String[]{copyPartDTO.getOid(), copyPartDTO.getMasterOid(), partIteration.getMasterOid()};
            oidCorrespond.put(sourceOid, str);
            oidScreenCorrespond.put(sourceMasterOid, str);
            list.add(copyPartDTO);
        }
        commonAbilityHelper.doCreate(list);
        return list;
    }

    private void savePartRel(BatchCopyPartDTO dto, Map<String, String[]> oidCorrespond, List<PartIteration> oldList, Map<String, String[]> oidScreenCorrespond) {
        List<RelationAble> rel = new ArrayList<>();
        //bom结构关系Use复制
//        List<RelationAble> rel = partService.getPartBomRelation(dto.getSourceOids()).stream().map(use -> {
//            Use newUse = BeanUtil.copyProperties(use, new Use());
//            newUse.setOid(OidGenerator.newOid());
//            newUse.setFromOid(oidCorrespond.get(use.getFromOid())[0]);
//            newUse.setToOid(oidCorrespond.get(use.getToOid())[0]);
//            return newUse;
//        }).collect(Collectors.toList());
        //bom关联文档复制
        //历史masteroid 和 oid 对应关系
        //       Map<String, String> masterAndOid = oldList.stream().collect(Collectors.toMap(PartIteration::getMasterOid, PartIteration::getOid, (o1, o2) -> o1));
//        List<Reference> references = partService.findDocumentRel(dto.getSourceOids()).stream().map(reference -> {
//            Reference newReference = BeanUtil.copyProperties(reference, new Reference());
//            newReference.setOid(OidGenerator.newOid());
//            String oldSourceOid = masterAndOid.get(reference.getFromOid());
//            //新masteroid
//            newReference.setFromOid(oidCorrespond.get(oldSourceOid)[1]);
//            return newReference;
//        }).collect(Collectors.toList());
//        rel.addAll(references);

        // 另存关系
//        for (String[] oids : oidCorrespond.values()) {
//            SaveAs saveAs = new SaveAs(Part.TYPE, oids[2], PartIteration.TYPE, oids[0]);
//            rel.add(saveAs);
//        }
        for (String sourceMasterOid : oidScreenCorrespond.keySet()) {
            if ("screen".equals(dto.getType())) {
                Screen screen = new Screen();
                screen.setFromOid(sourceMasterOid);
                screen.setToOid(oidScreenCorrespond.get(sourceMasterOid)[1]);
                screen.setType(Screen.TYPE);
                screen.setFromType(Part.TYPE);
                screen.setToType(Part.TYPE);
                rel.add(screen);
            } else if ("forming".equals(dto.getType())) {
                Forming form = new Forming();
                form.setFromOid(sourceMasterOid);
                form.setToOid(oidScreenCorrespond.get(sourceMasterOid)[1]);
                form.setType(Forming.TYPE);
                form.setFromType(Part.TYPE);
                form.setToType(Part.TYPE);
                rel.add(form);
            }
        }
        jWICommonService.createRelation(rel);
    }

}
