package cn.jwis.product.pdm.customer.service.delegate;

import cn.jwis.framework.base.util.MapUtil;
import cn.jwis.framework.base.util.SpringContextUtil;
import cn.jwis.platform.plm.foundation.common.response.InstanceEntity;
import cn.jwis.platform.plm.workflow.engine.order.entity.ProcessOrder;
import cn.jwis.platform.plm.workflow.engine.service.interf.ProcessOrderHelper;
import cn.jwis.product.pdm.change.entity.Issue;
import cn.jwis.product.pdm.change.service.IssueService;
import cn.jwis.product.pdm.change.service.IssueServiceI;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.JavaDelegate;
import org.activiti.engine.impl.persistence.entity.VariableInstance;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2023/11/5 10:36
 * @Email <EMAIL>
 */
@Slf4j
public class CloseDateIssueDelegate implements JavaDelegate {

    @Override
    public void execute(DelegateExecution execution) {
        String processInstanceId = execution.getProcessInstanceId();
        log.info("CloseIssueDelegate execute procId={}", processInstanceId);
        try {
            // 获取评审对象
            ProcessOrder processOrder = getProcessOrder(execution);
            ProcessOrderHelper processOrderHelper = (ProcessOrderHelper) SpringContextUtil.getBean("processOrderHelperImpl");
            IssueServiceI issueService = (IssueServiceI) SpringContextUtil.getBean("issueServiceImpl");
            List<InstanceEntity> instanceEntityList = processOrderHelper.findBizObject(processOrder.getOid());
            instanceEntityList = instanceEntityList.stream().filter(entity->
                    Issue.TYPE.equals(entity.getType())).collect(Collectors.toList());
            for(InstanceEntity entity : instanceEntityList){
                issueService.updateIssueCloseDate(entity.getOid());
            }
        } catch (Exception e) {
            log.error("CloseIssueDelegate execute error==>", e);
        }
    }

    private ProcessOrder getProcessOrder(DelegateExecution delegateTask) {
        ProcessOrder processOrder = null;
        Map<String, VariableInstance> variableInstances = delegateTask.getVariableInstances();
        ProcessOrderHelper processOrderHelper = (ProcessOrderHelper) SpringContextUtil.getBean("processOrderHelperImpl");
        if (MapUtil.isEmpty(variableInstances)){
            String processInstanceId = delegateTask.getProcessInstanceId();
            processOrder = processOrderHelper.findByProcessInstanceId(processInstanceId);
        }else {
            VariableInstance processOrderOid = variableInstances.get("processOrderOid");
            if (processOrderOid == null){
                String processInstanceId = delegateTask.getProcessInstanceId();
                processOrder = processOrderHelper.findByProcessInstanceId(processInstanceId);
            }else {
                String processOrderOidStr = processOrderOid.getValue().toString();
                processOrder = processOrderHelper.findByOid(processOrderOidStr);
            }
        }
        return processOrder;
    }

}
