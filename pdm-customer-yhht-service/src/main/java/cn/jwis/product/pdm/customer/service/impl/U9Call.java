package cn.jwis.product.pdm.customer.service.impl;

import cn.jwis.product.pdm.customer.service.util.HttpClientUtil;
import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2023/9/18 16:24
 * @Email <EMAIL>
 */
@Service
@Slf4j
@Data
@EqualsAndHashCode
public class U9Call {

    // u9的值集值更新接口
    @Value("${u9.cls.update.url}")
    private String clsUpdateUrl;

    @Value("${u9.workOrder.url}")
    private String workOrderUrl;

    @Value("${u9.inventory.url}")
    private String inventoryUrl;

    // u9的context
    @Value("${u9.context.inventory.entCode}")
    private String u9_context_inventory_entCode;

    @Value("${u9.context.workOrder.entCode}")
    private String u9_context_workOrder_entCode;

    @Value("${u9.context.CultureName}")
    private String u9_context_cultureName;

    @Value("${u9.context.OrgCode}")
    private String u9_context_orgCode;

    @Value("${u9.context.UserCode}")
    private String u9_context_userCode;

    private Map<String, String> getU9Header(){
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        return headers;
    }

    private JSONObject getU9Context(String url){
        JSONObject context = new JSONObject();
        context.put("CultureName",u9_context_cultureName);
        if(workOrderUrl.equals(url)){
            context.put("EntCode",u9_context_workOrder_entCode);
        }else {
            context.put("EntCode", u9_context_inventory_entCode);
        }
        context.put("OrgCode",u9_context_orgCode);
        context.put("UserCode",u9_context_userCode);
        return context;
    }

    public String send(String url,JSONObject body){
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        body.put("context",getU9Context(url));
        log.info("U9Call.send.body=" + body.toJSONString());
        String result = HttpClientUtil.postJson(
                url, null, getU9Header(), body.toJSONString(), false);
        log.info("U9Call.send.response=" + result);
        return result;
    }

}
