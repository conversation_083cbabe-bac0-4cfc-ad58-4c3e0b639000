package cn.jwis.product.pdm.customer.service.dto;

import cn.jwis.platform.plm.container.entity.response.TeamRoleForExport;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * @author: 汪江
 * @data: 2024-01-02 10:00
 * @description:
 **/
@Data
@EqualsAndHashCode
public class ComtainerTemplateExportDTO {
    private List<TeamRoleForExport> teamRoles;
    private List<FolderForExportDTO> folderTree;
}
