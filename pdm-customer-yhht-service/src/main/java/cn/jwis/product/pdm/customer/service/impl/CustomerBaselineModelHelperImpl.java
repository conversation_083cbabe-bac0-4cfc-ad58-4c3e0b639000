package cn.jwis.product.pdm.customer.service.impl;

import cn.jwis.audit.annotation.JWIParam;
import cn.jwis.audit.annotation.JWIServiceAudit;
import cn.jwis.audit.enums.Action;
import cn.jwis.product.pdm.baseline.baselineModel.service.BaselineModelHelperImpl;
import cn.jwis.product.pdm.baseline.baselineModel.service.dto.BaselineCreateDTO;
import cn.jwis.product.pdm.baseline.baselineModel.service.dto.BaselineDTO;
import cn.jwis.product.pdm.baseline.baselineModel.service.dto.BaselineUpdateDTO;
import cn.jwis.product.pdm.baseline.entity.Baseline;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR> yefei
 * @Date ： 2024/3/28
 * @Description :
 */

@Component
@Primary
public class CustomerBaselineModelHelperImpl extends BaselineModelHelperImpl {

    @Resource
    TriggerAuditServiceImpl triggerAuditService;

    @JWIServiceAudit(
            buzOid = "${result.oid}",
            bizNumber = "${result.number}",
            bizName = "${result.name}",
            buzType = "${result.type}",
            action = Action.ADD,
            content = "创建基线节点,名称:${result.name}"
    )
    @JWIParam("result")
    @Override
    public BaselineDTO create(BaselineCreateDTO dto) {
        return super.create(dto);
    }

    @Override
    public Long delete(String oid) {
        Long result = super.delete(oid);
        triggerAuditService.deleteBaseLine(oid);
        return result;
    }


    @JWIServiceAudit(
            buzOid = "${result.oid}",
            bizNumber = "${result.number}",
            bizName = "${result.name}",
            buzType = "${result.type}",
            action = Action.UPDATE,
            content = "修改基线节点：${result.name}"
    )
    @JWIParam("result")
    @Override
    public Baseline update(BaselineUpdateDTO dto) {
        return super.update(dto);
    }

}
