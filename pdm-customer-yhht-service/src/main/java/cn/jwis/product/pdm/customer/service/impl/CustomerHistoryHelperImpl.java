package cn.jwis.product.pdm.customer.service.impl;

import cn.jwis.platform.plm.account.user.service.UserHelper;
import cn.jwis.platform.plm.foundation.classification.service.ClassificationService;
import cn.jwis.platform.plm.workflow.engine.dto.HistoricTaskDTO;
import cn.jwis.platform.plm.workflow.engine.dto.TaskDetailResponseDTO;
import cn.jwis.platform.plm.workflow.engine.service.impl.HistoryHelperImpl;
import com.alibaba.fastjson.JSONObject;
import org.activiti.bpmn.model.SequenceFlow;
import org.activiti.rest.service.api.RestResponseFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@Primary
@Transactional
public class CustomerHistoryHelperImpl extends HistoryHelperImpl {

    @Autowired
    protected RestResponseFactory restResponseFactory;
    @Autowired
    ClassificationService classificationService;

    public TaskDetailResponseDTO findTaskDetail(String taskId) {
        TaskDetailResponseDTO result = super.findTaskDetail(taskId);

        // 排序 同意 通过在前面 驳回在后面
        List<SequenceFlow> outgoingFlowsNum = result.getOutgoingFlows();
        List<SequenceFlow> outgoingFlowsNumSorted = outgoingFlowsNum.stream().sorted(Comparator.comparing(sequenceFlow -> {
            if ("通过".equals(sequenceFlow.getName()))
                return 10;
            else if ("同意".equals(sequenceFlow.getName()))
                return 20;
            else if ("提交".equals(sequenceFlow.getName()))
                return 30;
            else if ("快速变更".equals(sequenceFlow.getName()))
                return 40;
            else if ("驳回".equals(sequenceFlow.getName()))
                return 50;
            else
                return 99;
        })).collect(Collectors.toList());

        result.setOutgoingFlows(outgoingFlowsNumSorted);
        return result;
    }

    @Override
    public List<JSONObject> getHistoricTasktaskComment(HistoricTaskDTO dto) {
        return super.getHistoricTasktaskComment(dto);
    }
}
