package cn.jwis.product.pdm.customer.service.delegate;

import cn.jwis.framework.base.exception.JWIException;
import cn.jwis.framework.base.exception.JWIServiceException;
import cn.jwis.framework.base.util.Assert;
import cn.jwis.framework.base.util.MapUtil;
import cn.jwis.framework.base.util.SpringContextUtil;
import cn.jwis.framework.base.util.StringUtil;
import cn.jwis.framework.base.web.session.SessionHelper;
import cn.jwis.platform.plm.workflow.engine.order.entity.ProcessOrder;
import cn.jwis.platform.plm.workflow.engine.service.interf.ProcessOrderHelper;
import cn.jwis.product.pdm.document.dto.DwgSignWorkflowDTO;
import cn.jwis.product.pdm.document.service.DocumentHelper;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.activiti.engine.RuntimeService;
import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.Expression;
import org.activiti.engine.delegate.JavaDelegate;
import org.activiti.engine.impl.persistence.entity.VariableInstance;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Description;
import org.springframework.stereotype.Component;
import java.util.Map;

@Component
@Description("图纸签署流程结束")
public class PdfSignDelegate implements JavaDelegate {

    private static final Logger log = LoggerFactory.getLogger(PdfSignDelegate.class);

    private Expression url;

    private Expression processTemplateData; // 其他数据

    @Override
    public void execute(DelegateExecution execution) {
        log.info("PdfSignDelegate.execute=" + execution.getId());
        RuntimeService runtimeService = SpringContextUtil.getBean(RuntimeService.class);
        try {
            String urlValue = this.url.getExpressionText();
            Assert.notBlank(urlValue, "url can not be null");
            JSONObject dataJSON = this.fetchExpressionParamJSON(this.processTemplateData);
            ProcessOrder processOrder = this.getProcessOrder(execution);
            String processInstanceId = processOrder.getProcessInstanceId();
            Map<String, VariableInstance> variableInstances = execution.getVariableInstances();
            Assert.isFalse(MapUtil.isEmpty(variableInstances), "VariableInstances of this workflow can not be empty");
//            String accesstoken = this.fetchAccessToken(variableInstances);
//            String tenantAlias = this.fetchTenantAlias(variableInstances);
//            String tenantOid = this.fetchTenantOid(variableInstances);
            String processor = this.fetchProcessor(variableInstances);
            JSONObject requestBody = new JSONObject();
            requestBody.put("tenantOid", execution.getTenantId());
            requestBody.put("processOrderType", "ProcessOrder");
            requestBody.put("processInstanceId", processInstanceId);
            requestBody.put("processOrderOid", processOrder.getOid());
            requestBody.put("data", JSON.parseObject(processor) );
            requestBody.put("processTemplateData", dataJSON);
//            SessionHelper.setAppId("pdm");
//            SessionHelper.setAccessToken(accesstoken);
            DocumentHelper documentHelper = SpringContextUtil.getBean(DocumentHelper.class);
            documentHelper.degSignWorkflow(JSONObject.toJavaObject(requestBody,DwgSignWorkflowDTO.class));
        } catch (JWIException e) {
            log.error("PdfSignDelegate execute error==>", e);
            StringBuffer buffer = new StringBuffer();
            StackTraceElement[] stackTrace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : stackTrace) {
                buffer.append(stackTraceElement.toString() + "\n");
            }
            runtimeService.setVariable(execution.getId(), "PdfSignError", "处理pdf签名异常，请联系IT！\r\n" + buffer);
        }
    }

    private ProcessOrder getProcessOrder(DelegateExecution delegateTask) {
        ProcessOrder processOrder = null;
        Map<String, VariableInstance> variableInstances = delegateTask.getVariableInstances();
        ProcessOrderHelper processOrderHelper = SpringContextUtil.getBean(ProcessOrderHelper.class);
        if (MapUtil.isEmpty(variableInstances)) {
            String processInstanceId = delegateTask.getProcessInstanceId();
            processOrder = processOrderHelper.findByProcessInstanceId(processInstanceId);
        } else {
            VariableInstance processOrderOid = variableInstances.get("processOrderOid");
            String processInstanceId;
            if (processOrderOid == null) {
                processInstanceId = delegateTask.getProcessInstanceId();
                processOrder = processOrderHelper.findByProcessInstanceId(processInstanceId);
            } else {
                processInstanceId = processOrderOid.getValue().toString();
                processOrder = processOrderHelper.findByOid(processInstanceId);
            }
        }

        return processOrder;
    }

    private String fetchTenantOid(Map<String, VariableInstance> variableInstances){
        String tenantOid = null;
        if (variableInstances.get("tenantOid") != null) {
            VariableInstance tenantAliasInstance = (VariableInstance)variableInstances.get("tenantOid");
            tenantOid = tenantAliasInstance.getTextValue();
            if (StringUtil.isBlank(tenantOid)) {
                Object value = tenantAliasInstance.getValue();
                if (value != null) {
                    tenantOid = String.valueOf(value);
                }
            }
        }
        return tenantOid;
    }

    private String fetchTenantAlias(Map<String, VariableInstance> variableInstances) {
        String alias = null;
        if (variableInstances.get("tenantAlias") != null) {
            VariableInstance tenantAliasInstance = (VariableInstance)variableInstances.get("tenantAlias");
            alias = tenantAliasInstance.getTextValue();
            if (StringUtil.isBlank(alias)) {
                Object value = tenantAliasInstance.getValue();
                if (value == null) {
                    alias = SessionHelper.getCurrentUser().getTenantAlias();
                } else {
                    alias = String.valueOf(value);
                }
            }
        }

        return alias;
    }

    private String fetchAccessToken(Map<String, VariableInstance> variableInstances) {
        String accesstoken = null;
        if (variableInstances.get("accesstoken") != null) {
            VariableInstance accesstokenInstance = (VariableInstance)variableInstances.get("accesstoken");
            accesstoken = accesstokenInstance.getTextValue();
            if (StringUtil.isBlank(accesstoken)) {
                Object value = accesstokenInstance.getValue();
                if (value == null) {
                    accesstoken = SessionHelper.getAccessToken();
                } else {
                    accesstoken = String.valueOf(value);
                }
            }
        }

        return accesstoken;
    }

    private String fetchGateway(Map<String, VariableInstance> variableInstances) {
        VariableInstance variableInstance = (VariableInstance)variableInstances.get("gateway");
        if (variableInstance == null) {
            throw new JWIServiceException("gateway url can not be null.");
        } else {
            String gateway = variableInstance.getTextValue();
            if (StringUtil.isBlank(gateway)) {
                Object value = variableInstance.getValue();
                if (value == null) {
                    gateway = SpringContextUtil.getProperty("workflow.callback.gateway.path");
                } else {
                    gateway = String.valueOf(value);
                }
            }

            return gateway;
        }
    }

    private String fetchProcessor(Map<String, VariableInstance> variableInstances) {
        VariableInstance variableInstance = (VariableInstance)variableInstances.get("processor");
        if (variableInstance == null) {
            throw new JWIServiceException("processor can not be null.");
        } else {
            String processor = variableInstance.getTextValue();
            if (StringUtil.isBlank(processor)) {
                Object value = variableInstance.getValue();
                if (null==value) {
                    throw new JWIServiceException("processor can not be null.");
                } else {
                    processor = String.valueOf(value);
                }
            }

            return processor;
        }
    }

    private String buildRequestURL(String urlValue, String gateway) {
        String url = gateway + urlValue;
        if (!url.contains("http")) {
            url = "http://" + url;
        }

        System.out.println("*************** Document Sign Delegate URL : " + url);
        return url;
    }

    private JSONObject fetchExpressionParamJSON(Expression expression) {
        Assert.notNull(expression,"流程参数 processTemplateData 不能为空");
        if (expression == null) {
            return new JSONObject();
        } else {
            String content = null;
            if (expression.getExpressionText() != null) {
                content = expression.getExpressionText();
            }

            return StringUtils.isBlank(content) ? new JSONObject() : JSONObject.parseObject(content);
        }
    }

}
