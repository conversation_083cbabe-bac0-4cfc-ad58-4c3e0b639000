package cn.jwis.product.pdm.customer.service.delegate;

import cn.jwis.framework.base.dto.UserDTO;
import cn.jwis.framework.base.util.MapUtil;
import cn.jwis.framework.base.util.SpringContextUtil;
import cn.jwis.framework.base.web.session.SessionHelper;
import cn.jwis.platform.plm.account.user.service.UserHelper;
import cn.jwis.platform.plm.foundation.common.response.InstanceEntity;
import cn.jwis.platform.plm.workflow.engine.order.entity.ProcessOrder;
import cn.jwis.platform.plm.workflow.engine.service.interf.ProcessOrderHelper;
import cn.jwis.product.pdm.customer.service.impl.DingTalkServiceImpl;
import cn.jwis.product.pdm.customer.service.interf.DingTalkService;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.RuntimeService;
import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.JavaDelegate;
import org.activiti.engine.impl.persistence.entity.VariableInstance;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2023/10/26 14:25
 * @Email <EMAIL>
 */
@Slf4j
public class CollectionPCBDesignerNotifierDelegate implements JavaDelegate {
    @Override
    public void execute(DelegateExecution execution) {
        String processInstanceId = execution.getProcessInstanceId();
        log.info("CollectionPCBDesignerNotifierDelegate execute procId={}", processInstanceId);
        RuntimeService runtimeService = SpringContextUtil.getBean(RuntimeService.class);
        initContext(runtimeService,execution.getId());
        try {
            // 获取原通知人
            Object notifiers = runtimeService.getVariable(execution.getId(),"cn_jwis_notifier");
            List<String> accounts = new ArrayList<>();
            if(!ObjectUtils.isEmpty(notifiers)){
                if(notifiers instanceof Collection){
                    accounts.addAll((Collection<? extends String>) notifiers);
                }else{
                    accounts.add(notifiers.toString());
                }
            }
            // 增加PCB设计者通知人
            ProcessOrder processOrder = getProcessOrder(execution);
            ProcessOrderHelper processOrderHelper = (ProcessOrderHelper) SpringContextUtil.getBean("processOrderHelperImpl");
            List<InstanceEntity> instanceEntityList = processOrderHelper.findBizObject(processOrder.getOid());
            accounts.addAll(instanceEntityList.stream().map(i->i.getCreateBy()).collect(Collectors.toList()));
            // 增加评审人
            Object shz = runtimeService.getVariable(execution.getId(),"cn_jwis_shz");
            if(!ObjectUtils.isEmpty(shz)){
                accounts.addAll((Collection<? extends String>) shz);
            }
            Object shzwuliao = runtimeService.getVariable(execution.getId(),"cn_jwis_shz_wuliao");
            if(!ObjectUtils.isEmpty(shzwuliao)){
                accounts.addAll((Collection<? extends String>) shzwuliao);
            }
            // 去重
            accounts = new ArrayList<String>(new TreeSet<>(accounts));
            log.info("CollectionPCBDesignerNotifierDelegate new accounts={}", JSON.toJSONString(accounts));
            runtimeService.setVariable(execution.getId(), "cn_jwis_notifier", accounts);
            // 发布通知
            DingTalkService dingTalkService = (DingTalkServiceImpl) SpringContextUtil.getBean("dingTalkServiceImpl");
            dingTalkService.createNoticeTask(accounts,processOrder,instanceEntityList);
        } catch (Exception e) {
            log.error("CollectionPCBDesignerNotifierDelegate execute error==>", e);
        }
    }

    private void initContext(RuntimeService runtimeService, String id) {
        Object owner = runtimeService.getVariable(id,"owner");
        UserHelper userHelper = (UserHelper)SpringContextUtil.getBean("defaultUserHelperImpl");
        UserDTO user = userHelper.findByAccount(owner.toString());
        Object tenantOid = runtimeService.getVariable(id,"tenantOid");
        user.setAccount(owner.toString());
        user.setOid(owner.toString());
        user.setTenantOid(tenantOid.toString());
        SessionHelper.addCurrentUser(user);
    }

    private ProcessOrder getProcessOrder(DelegateExecution delegateTask) {
        ProcessOrder processOrder = null;
        Map<String, VariableInstance> variableInstances = delegateTask.getVariableInstances();
        ProcessOrderHelper processOrderHelper = (ProcessOrderHelper) SpringContextUtil.getBean("processOrderHelperImpl");
        if (MapUtil.isEmpty(variableInstances)){
            String processInstanceId = delegateTask.getProcessInstanceId();
            processOrder = processOrderHelper.findByProcessInstanceId(processInstanceId);
        }else {
            VariableInstance processOrderOid = variableInstances.get("processOrderOid");
            if (processOrderOid == null){
                String processInstanceId = delegateTask.getProcessInstanceId();
                processOrder = processOrderHelper.findByProcessInstanceId(processInstanceId);
            }else {
                String processOrderOidStr = processOrderOid.getValue().toString();
                processOrder = processOrderHelper.findByOid(processOrderOidStr);
            }
        }
        return processOrder;
    }

}
