package cn.jwis.product.pdm.customer.service.dto;

import cn.jwis.platform.plm.file.entity.FileMetadata;
import cn.jwis.product.pdm.document.dto.DocumentCreateDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2023/10/18 11:22
 * @Email <EMAIL>
 */
@Data
@EqualsAndHashCode
public class DocumentMilkyWayDTO extends DocumentCreateDTO {
    private String account;
    private String classificationName;
    private Map<String, FileMetadata> file;
    private String number;

}
