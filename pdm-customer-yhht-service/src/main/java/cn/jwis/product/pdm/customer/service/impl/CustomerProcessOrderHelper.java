package cn.jwis.product.pdm.customer.service.impl;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.jwis.framework.base.bean.util.BeanUtil;
import cn.jwis.framework.base.domain.able.ModelAble;
import cn.jwis.framework.base.domain.entity.BaseEntity;
import cn.jwis.framework.base.dto.PageSimpleDTO;
import cn.jwis.framework.base.dto.UserDTO;
import cn.jwis.framework.base.exception.JWIException;
import cn.jwis.framework.base.response.PageResult;
import cn.jwis.framework.base.util.Assert;
import cn.jwis.framework.base.util.CollectionUtil;
import cn.jwis.framework.base.util.StringUtil;
import cn.jwis.framework.base.web.session.SessionHelper;
import cn.jwis.framework.database.core.query.dynamic.Condition;
import cn.jwis.platform.plm.account.entity.team.TeamTemplateRole;
import cn.jwis.platform.plm.account.entity.team.response.TeamTemplateRoleWithUser;
import cn.jwis.platform.plm.account.user.service.UserHelper;
import cn.jwis.platform.plm.account.user.service.UserService;
import cn.jwis.platform.plm.container.entity.Folder;
import cn.jwis.platform.plm.dataengine.common.JWICommonService;
import cn.jwis.platform.plm.foundation.classification.entity.Classification;
import cn.jwis.platform.plm.foundation.classification.service.ClassificationHelper;
import cn.jwis.platform.plm.foundation.common.dto.RelatedFuzzyDTO;
import cn.jwis.platform.plm.foundation.common.response.InstanceEntity;
import cn.jwis.platform.plm.foundation.common.service.CommonAbilityService;
import cn.jwis.platform.plm.foundation.common.service.InstanceHelper;
import cn.jwis.platform.plm.foundation.numberrule.able.NumberAble;
import cn.jwis.platform.plm.foundation.relationship.enums.RelationConstraint;
import cn.jwis.platform.plm.sysconfig.collectionrule.service.CollectionRuleHelper;
import cn.jwis.platform.plm.sysconfig.entity.CollectionRule;
import cn.jwis.platform.plm.sysconfig.entity.ConfigItem;
import cn.jwis.platform.plm.sysconfig.preferences.PreferencesService;
import cn.jwis.platform.plm.sysconfig.preferences.dto.ConfigGroupDTO;
import cn.jwis.platform.plm.sysconfig.preferences.service.IPreferencesService;
import cn.jwis.platform.plm.workflow.engine.dto.*;
import cn.jwis.platform.plm.workflow.engine.order.entity.ProcessOrder;
import cn.jwis.platform.plm.workflow.engine.service.impl.ProcessOrderHelperImpl;
import cn.jwis.platform.plm.workflow.engine.service.interf.ProcessOrderHelper;
import cn.jwis.platform.plm.workflow.engine.service.interf.TaskHelper;
import cn.jwis.product.pdm.cad.ecad.entity.ECADIteration;
import cn.jwis.product.pdm.cad.mcad.entity.MCADIteration;
import cn.jwis.product.pdm.change.entity.ECA;
import cn.jwis.product.pdm.change.entity.Issue;
import cn.jwis.product.pdm.change.response.ChangeInfo;
import cn.jwis.product.pdm.change.service.ECAService;
import cn.jwis.product.pdm.customer.entity.CustomerInstanceEntity;
import cn.jwis.product.pdm.customer.entity.DingTaskRecord;
import cn.jwis.product.pdm.customer.entity.assistant.SafeWrapAssist;
import cn.jwis.product.pdm.customer.remote.SysconfigRemote;
import cn.jwis.product.pdm.customer.repo.CustomerCommonRepo;
import cn.jwis.product.pdm.customer.service.dto.ProcessTeamUpdateDTO;
import cn.jwis.product.pdm.document.entity.Document;
import cn.jwis.product.pdm.document.entity.DocumentIteration;
import cn.jwis.product.pdm.partbom.part.dto.SimplePartBOMNode;
import cn.jwis.product.pdm.partbom.part.entity.Part;
import cn.jwis.product.pdm.partbom.part.entity.PartIteration;
import cn.jwis.product.pdm.partbom.part.service.PartHelper;
import org.activiti.engine.RuntimeService;
import org.activiti.engine.task.Task;
import org.activiti.engine.task.TaskInfo;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2023/9/25 14:17
 * @Email <EMAIL>
 */
@Service
@Primary
@Transactional
public class CustomerProcessOrderHelper extends ProcessOrderHelperImpl implements SafeWrapAssist {

    private static final Logger logger = LoggerFactory.getLogger(CustomerProcessOrderHelper.class);

    @Resource
    CommonAbilityService commonAbilityService;

    @Resource
    InstanceHelper instanceHelper;

    @Autowired
    PartHelper partHelper;

    @Autowired
    PreferencesService preferencesService;

    @Autowired
    RuntimeService runtimeService;

    @Autowired
    CustomerCommonServiceImpl customerCommonService;

    @Autowired
    CustomerCommonRepo customerCommonRepo;

    @Resource
    TaskHelper taskHelper;

    @Autowired
    ECAService ecaService;

    @Autowired
    JWICommonService jwiCommonService;


    @Resource
    ClassificationHelper classificationHelper;
    @Resource
    CollectionRuleHelper collectionRuleHelper;

    private static final String STANDARD_PART_CODE = "cn_jwis_bzj";

    //借用文档分类码
    private static final String DOC_REFERENCE_CODE = "cn_jwis_jybg";

    @Override
    public ProcessOrder createThenStart(ProOrdCreateDTO dto) {
        checkWorkflowParam(dto);
        return super.createThenStart(dto);
    }

    public void checkWorkflowParam(ProOrdCreateDTO dto){
        if (CollectionUtil.isNotEmpty(dto.getBizObjects())) {
            logger.info("bizObjects不为空 对dto参数进行处理==>" + JSONUtil.toJsonStr(dto));
            ConfigItem itemRole = this.preferencesService.queryConfigValue("nonMandatoryRole");
            if (itemRole != null) {
                Map<String, List<PartIteration>> partMap = new HashMap<>();
                String configStr = itemRole.getValue();
                String[] split = configStr.split("\\|");
                List<String> roleList = Arrays.asList(split);
                List<TeamRoleUserDTO> teamContent = dto.getTeamContent();
                // 解析 extensionContent，获取 isOutgoing 的值
                com.alibaba.fastjson.JSONObject extensionContent = dto.getExtensionContent();
                boolean isOutgoingYes = false;
                if (extensionContent != null && extensionContent.containsKey("additionalTranslation")) {
                    com.alibaba.fastjson.JSONObject additionalTranslation = extensionContent.getJSONObject("additionalTranslation");
                    if (additionalTranslation != null && additionalTranslation.containsKey("isOutgoing")) {
                        String isOutgoingValue = additionalTranslation.getString("isOutgoing");
                        isOutgoingYes = "是".equals(isOutgoingValue);
                    }
                }

                // 只有 dto.getName() 是 "物料BOM及图纸发布流程" 时才进行特殊处理
                // 0228 暂时屏蔽掉BOM及图纸发布流程中的 文件接收人的相关校验
                /*boolean isMaterialBomProcess = "物料BOM及图纸发布流程".equals(dto.getName());
                if (CollectionUtil.isNotEmpty(teamContent)) {
                    for (TeamRoleUserDTO teamRoleUserDTO : teamContent) {
                        String roleName = teamRoleUserDTO.getRoleName();

                        // 如果是"物料BOM及图纸发布流程" 并且 isOutgoing 存在
                        if (isMaterialBomProcess) {
                            if (!isOutgoingYes && "cn_jwis_outgoing_fileReciever".equals(roleName)) {
                                // isOutgoing 为 "否" 时，跳过 "cn_jwis_outgoing_fileReciever" 角色校验
                                continue;
                            }
                        }

                        // 其他角色校验逻辑
                        if (CollectionUtil.isEmpty(teamRoleUserDTO.getUserAccounts()) && !roleList.contains(roleName)) {
                            throw new JWIException("请先添加除会签外对应的角色责任人");
                        }
                    }
                }*/
             /*   if (CollectionUtil.isNotEmpty(teamContent))
                    for (TeamRoleUserDTO teamRoleUserDTO : teamContent)
                        if (CollectionUtil.isEmpty(teamRoleUserDTO.getUserAccounts()) && !roleList.contains(teamRoleUserDTO.getRoleName()))
                            throw new JWIException("请先添加除会签外对应的角色责任人");*/
            }
            List<String> userAccounts = Arrays.asList(SessionHelper.getCurrentUser().getAccount(), "sys_admin");
            List<String> reviewTypes = Arrays.asList(MCADIteration.TYPE, ECADIteration.TYPE, PartIteration.TYPE, DocumentIteration.TYPE);
            StringBuffer checkResult = new StringBuffer();
            // 先收集数据
            Map<String, ModelAble> bizObjectMap = new HashMap<>();
            for (LifecycleDTO bizObject : dto.getBizObjects()) {
                ModelAble modelAble = commonAbilityService.findDetailEntity(bizObject.getOid(), bizObject.getType());
                JSONObject objJson = new JSONObject(modelAble);
                // 尝试获取 isLatest 属性，并做 boolean 安全转换
                String isLatestStr = objJson.getStr("latest");
                boolean isLatest = "true".equalsIgnoreCase(isLatestStr); // 避免空指针 & 更宽容的判断
                String processName = dto.getName();
                // 同时排除“文档变更流程”和“变更流程” 对最新版本的检查
                if (!isLatest && !"文档变更流程".equals(processName) && !"变更流程".equals(processName)) {

                    String name = objJson.getStr("name");
                    String number = objJson.getStr("number");
                    String displayVersion = objJson.getStr("displayVersion");

                    // 安全处理 null 值，避免拼接 null
                    name = name != null ? name : "";
                    number = number != null ? number : "";
                    displayVersion = displayVersion != null ? displayVersion : "";

                    checkResult.append("当前对象【")
                            .append(name)
                            .append(" - ")
                            .append(number)
                            .append(" - ")
                            .append(displayVersion)
                            .append("】不是最新版本。\n");
                }
                bizObjectMap.put(getModelKey(modelAble), modelAble);
            }
            // 再检查数据
            for (Map.Entry<String, ModelAble> entry : bizObjectMap.entrySet()) {
                // 检查流程启动者是不是owner
                ModelAble modelAble = entry.getValue();
                if (!userAccounts.contains(modelAble.getOwner()) && reviewTypes.contains(modelAble.getType()) && !"文档变更流程".equals(dto.getName())
                        && !"变更流程".equals(dto.getName()) && !"问题处理流程".equals(dto.getName()) && !"文件外发流程".equals(dto.getName())){
//                    checkResult.append("你不是【").append(entry.getKey()).append("】的所有者！\r");
                }
                JSONObject objJson = new JSONObject(modelAble);

                if ("技术文档发布流程".equals(dto.getName()) && "DocumentIteration".equals(objJson.getStr("type")) && objJson.get("primaryFile") == null)
                    checkResult.append("文档【").append(entry.getKey()).append("】没有主文件！\r");

                // 部件发布流程中，检查部件子项是不是已发放，或者子项也在此次发布流程中
                ConfigItem item = preferencesService.queryConfigValue("needCheckSubPartsProcessName");
                if (item != null) {
                    List<String> needCheckSubParts = Arrays.asList(item.getValue().split(","));
                    if (needCheckSubParts.contains(dto.getName()))
                        checkBom(modelAble, bizObjectMap.keySet(), checkResult);
                }
                // 新器件引入流程，物料BOM及图纸发布流程，物料编码申请流程启动之前，检查部件必须是正式分类
                item = preferencesService.queryConfigValue("needCheckPartClsProcessName");
                if (item != null) {
                    List<String> needCheckPartCls = Arrays.asList(item.getValue().split(","));
                    if (needCheckPartCls.contains(dto.getName()))
                        checkPartCls(modelAble, checkResult);
                }
                Assert.isFalse(DocumentIteration.TYPE.equals(modelAble.getType()) && isReferenceDoc(modelAble.getOid()) && !hasDocRelation(modelAble.getOid()),
                        "借用文档类型必须包含借用关系");
//                Assert.isFalse(PartIteration.TYPE.equals(modelAble.getType()) && isStandardPart(modelAble.getOid()) && !has3D(modelAble.getOid()), "标准件启动流程必须包含三维图");
            }
            // 提示
            Assert.isBlank(checkResult.toString(), checkResult.toString());
        }else
            throw new JWIException("未找到发起流程的相关对象，请添加审批对象");

        logger.info("流程发起参数==>" + JSONUtil.toJsonStr(dto));
        if(StringUtils.isEmpty(dto.getProcessModelId()))
            throw new JWIException("未找到要发起流程的ID，请选择流程");
    }
    //是否为标准件
    private boolean isStandardPart(String oid) {
        PartIteration partIteration = jwiCommonService.findByOid(PartIteration.TYPE, oid, PartIteration.class);
        Classification classification = classificationHelper.findByCode(STANDARD_PART_CODE);
        Assert.notNull(classification, "未找到标准件分类");
        Set<String> oidSet =
                classificationHelper.findByParent(classification.getOid()).stream().map(Classification::getOid).collect(Collectors.toSet());
        return classification.getOid().equals(partIteration.getClsOid()) || oidSet.contains(partIteration.getClsOid());
    }

    private boolean has3D(String oid) {
        return queryWithCollectionRule(oid,"Part_Related_Object","相关MCAD图档", Part.TYPE,RelationConstraint.II).size() > 0;
    }

    private boolean isReferenceDoc(String oid) {
        DocumentIteration documentIteration = jwiCommonService.findByOid(DocumentIteration.TYPE, oid,
                DocumentIteration.class);
        Assert.notNull(documentIteration, "文档对象不存在:" + oid);
        Classification classification = classificationHelper.findByOid(documentIteration.getClsOid());
        if (classification == null) {
            return false;
        }
        return DOC_REFERENCE_CODE.equals(classification.getCode());
    }

    private boolean hasDocRelation(String oid) {
        return queryWithCollectionRule(oid,"Document_Related_Object","借用文档",Document.TYPE,RelationConstraint.MI).size() > 0;
    }

    private List<InstanceEntity> queryWithCollectionRule(String oid,String appliedType,String ruleName,
                                                         String mainObjectType,RelationConstraint relationConstraint) {
        CollectionRule collectionRule = collectionRuleHelper.findByAppliedType(appliedType, mainObjectType)
                .stream().filter(it -> ruleName.equals(it.getRelationDisplayName())).findFirst().orElse(null);

        return Optional.ofNullable(collectionRule).map(rule -> {
            RelatedFuzzyDTO relatedFuzzyDTO = new RelatedFuzzyDTO();
            relatedFuzzyDTO.setRelationConstraint(relationConstraint);
            BeanUtils.copyProperties(collectionRule, relatedFuzzyDTO);
            relatedFuzzyDTO.setMainObjectOid(oid);
            return Optional.ofNullable(instanceHelper.fuzzyRelated(relatedFuzzyDTO)).orElse(Collections.emptyList());
        }).orElse(Collections.emptyList());
    }


    private void checkPartCls(ModelAble modelAble,  StringBuffer checkResult) {
        if (PartIteration.TYPE.equals(modelAble.getType())) {
            PartIteration part = partHelper.findByOid(modelAble.getOid());
            String clsCode = part.getClsCode();
            if(StringUtil.isBlank(clsCode) || "CADCHECKIN".equals(clsCode))
            checkResult.append("【").append(part.getNumber()).append("】未指定正式分类，请处理！\r");
        }
    }

    private String getModelKey(ModelAble modelAble) {
        return ((NumberAble) modelAble).getNumber();
    }

    private void checkBom(ModelAble modelAble, Set<String> keySet, StringBuffer checkResult) {
        if (PartIteration.TYPE.equals(modelAble.getType())) {
            List<SimplePartBOMNode> bom = partHelper.findSimpleUseTree("", modelAble.getOid(), 1);
            List<SimplePartBOMNode> children = bom.get(0).getChildren();
            if (CollectionUtil.isEmpty(children)) {
                return;
            }
            String parentKey = getModelKey(modelAble);
            for (SimplePartBOMNode subPart : children) {
                // 子项状态不是已发放，且不在此次流程中，则父项不能发布
                if (!"Released".equals(subPart.getLifecycleStatus()) && !keySet.contains(subPart.getNumber())) {
                    checkResult.append("【").append(parentKey).append("】的子项【")
                            .append(subPart.getNumber()).append("】未发布且不在当前流程中！\r");
                }
            }
        }
    }

    public String updateProcessTeam(ProcessTeamUpdateDTO dto) {
        String processInstanceId = dto.getProcessInstanceId();
        Assert.notEmpty(dto.getTeamContent(),"角色人员为空！");
        Map<String,Object> variables = new HashMap<>();
        for(TeamRoleUserDTO roleUserDTO : dto.getTeamContent()){
            List<String> userAccounts = roleUserDTO.getUserAccounts();
            if (CollectionUtil.isEmpty(userAccounts)) {
                continue;
            }
            if(userAccounts.size()==1 && !"cn_jwis_countersigner".equals(roleUserDTO.getRoleName())){
                variables.put(roleUserDTO.getRoleName(),userAccounts.get(0));
            }else{
                variables.put(roleUserDTO.getRoleName(),userAccounts);
            }
        }
        runtimeService.setVariables(processInstanceId,variables);
        return "success";
    }

    public final static Map<String, String> ancestorClsUserAccountMap;

    static {
        ancestorClsUserAccountMap = new HashMap<>();
        ancestorClsUserAccountMap.put("SB", "liyiming");
        ancestorClsUserAccountMap.put("EQ", "liyiming");
        ancestorClsUserAccountMap.put("cn_jwis_rkl", "et");
        ancestorClsUserAccountMap.put("cn_jwis_dp", "wxq");
        ancestorClsUserAccountMap.put("cn_jwis_gp", "wxq");
        ancestorClsUserAccountMap.put("cn_jwis_dljq", "wxq");
        ancestorClsUserAccountMap.put("cn_jwis_dxdl", "wxq");
        ancestorClsUserAccountMap.put("cn_jwis_yqj", "anxiaoxiao,xuzetao");
        ancestorClsUserAccountMap.put("cn_jwis_bzj", "liuwei");
    }

    @Autowired
    SysconfigRemote sysconfigRemote;

    @Autowired
    private UserService userService;

    @Autowired
    private ProcessOrderHelper processOrderHelper;

    @Resource
    UserHelper userHelper;

    @Autowired
    private IPreferencesService preferencesServiceImpl;

    public List<TeamTemplateRoleWithUser> autoUser(List<CustomerInstanceEntity> instanceEntityList) throws Exception {
        List<TeamTemplateRoleWithUser> result = new ArrayList<>();
        if(CollectionUtil.isEmpty(instanceEntityList)){
            return result;
        }
        // 文件夹Oid或产品容器Oid
        List<String> contentOidList = new ArrayList<>();
        List<CustomerInstanceEntity> trueInstanceEntityList = new ArrayList<>();
        for(CustomerInstanceEntity instanceEntity : instanceEntityList){
            if(ECA.TYPE.equals(instanceEntity.getType())){
                List<ChangeInfo> list =  ecaService.findChangeInfo(instanceEntity.getOid(),new ArrayList<>());
                list.stream().forEach(changeInfo -> {
                    trueInstanceEntityList.add(BeanUtil.copyProperties(changeInfo,new CustomerInstanceEntity()));
                });
            }else {
                trueInstanceEntityList.add(instanceEntity);
            }
        }

        Map<String, TeamTemplateRoleWithUser> autoUserMap = new HashMap<>();
        for(CustomerInstanceEntity instanceEntity : trueInstanceEntityList){
            List<Folder> folders = customerCommonService.getFolderPath(instanceEntity.getType(),instanceEntity.getOid());
            if((folders == null || folders.size() == 0) && instanceEntity.getContainerName() != null)
                folders = jwiCommonService.dynamicQuery(Folder.TYPE, Condition.where("name").eq(instanceEntity.getContainerName())
                        .and(Condition.where("containerType").eq("Container")), Folder.class);
//            folders.stream().forEach(f -> contentOidList.add(f.getOid()));
//            if(CollectionUtil.isNotEmpty(folders)){
//                contentOidList.add(folders.get(0).getContainerOid());
//            }
            folders.stream().forEach(f -> {
                List<TeamTemplateRoleWithUser> teamRoleList = customerCommonRepo.autoUser(Arrays.asList(f.getOid()));
//                filterTeamRoles(teamRoleList);
                teamRoleList.forEach(teamRole -> {
                    if(teamRole.getUsers() != null && teamRole.getUsers().size() > 0)
                        autoUserMap.putIfAbsent(teamRole.getDisplayName(), teamRole);
                });
            });
            if(CollectionUtil.isNotEmpty(folders)){
                List<TeamTemplateRoleWithUser> teamRoleList = customerCommonRepo.autoUser(Arrays.asList(folders.get(0).getContainerOid()));
//                filterTeamRoles(teamRoleList);
                teamRoleList.forEach(teamRole -> {
                    if(teamRole.getUsers() != null && teamRole.getUsers().size() > 0)
                        autoUserMap.putIfAbsent(teamRole.getDisplayName(), teamRole);
                });
            }
        }
        result.addAll(autoUserMap.values());

        TeamTemplateRoleWithUser cbTeam = null, bzhTeam = null; int bzhTeamIndex = 0;
        for (int i = 0; i < result.size(); i++) {
            TeamTemplateRoleWithUser it = result.get(i);
            if("产保".equals(it.getDisplayName()) || "cn_jwis_cb".equals(it.getName()))
                cbTeam = it;
            else if("标准化".equals(it.getDisplayName()) || "cn_jwis_bzh".equals(it.getName())){
                bzhTeam = it;
                bzhTeamIndex = i;
            }
        }

        if(cbTeam != null){
            if(bzhTeam != null){
                cbTeam.setName(bzhTeam.getName());
                cbTeam.setDisplayName(bzhTeam.getDisplayName());
                result.remove(bzhTeamIndex);
            }else {
                cbTeam.setName("cn_jwis_bzh");
                cbTeam.setDisplayName("标准化");
            }
        }

        Map<String,TeamTemplateRoleWithUser> roleWithUserMap = CollectionUtil.splitToMap(result,TeamTemplateRoleWithUser::getName);
        // 增加owner
        TeamTemplateRoleWithUser owner = roleWithUserMap.get("owner");
        if(owner == null){
            CustomerInstanceEntity instance = instanceEntityList.get(0);
            processOrderHelper.findTeamContent(instance.getOid());
            owner = new TeamTemplateRoleWithUser();
            owner.setName("owner");
            owner.setDisplayName("所有者");
            owner.setDescription("所有者");
            owner.setType(TeamTemplateRole.TYPE);
            result.add(owner);
            UserDTO userDTO = userHelper.findByAccount(instance.getOwner());
            // 问题管理审核人为提交人，owner为申请人  其他owner为对象owner
            if(Issue.TYPE.equals(instance.getType())) {
                owner.setUsers(Arrays.asList(SessionHelper.getCurrentUser()));
                TeamTemplateRoleWithUser shenheUser =
                        result.stream().filter(item-> "cn_jwis_shz".equalsIgnoreCase(item.getName())).findAny().orElse(null);
                if(ObjectUtils.isEmpty(shenheUser)) {
                    shenheUser = new TeamTemplateRoleWithUser();
                    shenheUser.setName("cn_jwis_shz");
                    shenheUser.setDisplayName("审核");
                    shenheUser.setDescription("审核");
                    shenheUser.setType(TeamTemplateRole.TYPE);
                    result.add(shenheUser);
                }
                shenheUser.setUsers(Arrays.asList(userDTO));
            } else {
                owner.setUsers(Arrays.asList(userDTO));
            }
        }
//        TeamTemplateRoleWithUser owner_bg = new TeamTemplateRoleWithUser();
//        owner_bg.setName("owner_bg");
//        owner_bg.setDisplayName("变更人");
//        owner_bg.setDescription("变更人");
//        owner_bg.setType(TeamTemplateRole.TYPE);
//        result.add(owner_bg);
//        owner_bg.setUsers(Arrays.asList(SessionHelper.getCurrentUser()));


        String userAccount = "";
        for (int i = 0; i < instanceEntityList.size(); i++) {
            InstanceEntity instance = instanceEntityList.get(i);
            if("PartIteration".equals(instance.getType())){
                List<ConfigGroupDTO> configGroupList = preferencesServiceImpl.queryPreferencesConfig("");
                ConfigGroupDTO ddMaterialConfig = configGroupList.stream().filter(configGroup -> "物料编码审批流程默认审批人".equals(configGroup.getName())).findFirst().orElse(null);
                Set<String> clsSet = Optional.ofNullable(ddMaterialConfig).map(it -> Optional.ofNullable(it.getItemDTOList())
                        .map(itemList -> itemList.stream().map(item -> item.getCode().replace("approver_", ""))
                            .collect(Collectors.toSet())).orElse(ancestorClsUserAccountMap.keySet())).orElse(ancestorClsUserAccountMap.keySet());

                String cls = customerCommonRepo.queryInCls(clsSet, instance.getOid());
//                userAccount = userAccount.length() == 0 ? ancestorClsUserAccountMap.getOrDefault(cls, "") : userAccount + "," + ancestorClsUserAccountMap.getOrDefault(cls, "");
                try {
                    if(cls != null && cls.length() > 0){
                        List<JSONObject> config = sysconfigRemote.findConfigVal("approver_" + cls);
                        if(config != null && config.size() > 0)
                            userAccount = config.stream().flatMap(it -> Stream.of(it.getStr("name"), it.getStr("value"))).distinct().collect(Collectors.joining(","));
                    }
                } catch (Exception e){
                    logger.error("从配置文件获取审批人失败" + e.getMessage(), e);
                    userAccount = userAccount.length() == 0 ? ancestorClsUserAccountMap.getOrDefault(cls, "") : userAccount + "," + ancestorClsUserAccountMap.getOrDefault(cls, "");
                }
            }
        }

        if(userAccount.length() > 0){
            List<UserDTO> userList = userService.searchByAccounts(Arrays.stream(userAccount.split(",")).collect(Collectors.toList()))
                    .stream().map(user -> BeanUtil.copyProperties(user, new UserDTO())).collect(Collectors.toList());
            TeamTemplateRoleWithUser shzRoleWithUser = roleWithUserMap.get("cn_jwis_shz_wuliao");
            if(shzRoleWithUser == null){
                shzRoleWithUser = new TeamTemplateRoleWithUser();
                shzRoleWithUser.setName("cn_jwis_shz_wuliao");
                shzRoleWithUser.setDisplayName("审核者");
                shzRoleWithUser.setDescription("审核者");
                shzRoleWithUser.setType(TeamTemplateRole.TYPE);
                shzRoleWithUser.setUsers(userList);
                result.add(shzRoleWithUser);

                shzRoleWithUser = new TeamTemplateRoleWithUser();
                shzRoleWithUser.setName("cn_jwis_shz");
                shzRoleWithUser.setDisplayName("审核");
                shzRoleWithUser.setDescription("审核");
                shzRoleWithUser.setType(TeamTemplateRole.TYPE);
                shzRoleWithUser.setUsers(userList);
                result.add(shzRoleWithUser);
            }
        }
        return result;
    }

    //    @Override
    public PageResult<ProcessOrderDetail> fuzzyPageByBiz(ProOrdOfBizObjFuzzyPageDTO dto) {
        PageResult<ProcessOrderDetail> pageResult = customerCommonRepo.fuzzyPageByBiz(dto);
        fillPdmProcessDetail(pageResult);
        return pageResult;
    }

    private void fillPdmProcessDetail(PageResult<ProcessOrderDetail> pageResult) {
        LinkedHashMap<String,ProcessOrderDetail> waitResetMap = new LinkedHashMap<>();
        for (ProcessOrderDetail processOrderDetail : pageResult.getRows()) {
            waitResetMap.put(processOrderDetail.getOid(),processOrderDetail);
            if(DingTaskRecord.TYPE.equals(processOrderDetail.getType()) && StringUtils.isBlank(processOrderDetail.getName())) {
                processOrderDetail.setName("钉钉流程");
            }
        }
        this.fillProcessOrderDetail(waitResetMap.values().stream().filter(item->ProcessOrder.TYPE.equals(item.getType())).collect(Collectors.toList()));
    }

    @Override
    public PageResult<ProcessOrderDetail> fuzzyPageByCreator(PageSimpleDTO dto) {
        PageResult<ProcessOrderDetail> pageResult =
                customerCommonRepo.queryProcessOrderByCreator(SessionHelper.getCurrentUser().getAccount(),
                        dto.getIndex(),
                        dto.getSize());

        fillPdmProcessDetail(pageResult);
        return pageResult;
    }

    private void fillProcessOrderDetail(Collection<ProcessOrderDetail> rows) {
        List<String> processInstanceIds = rows.stream().filter((item) -> StringUtil.isNotBlank(item.getProcessInstanceId())).map(ProcessOrder::getProcessInstanceId).collect(Collectors.toList());
        Map<String, List<Task>> processId2Tasks = this.taskHelper.processInstanceCurrentTask(processInstanceIds);
        rows.stream().forEach((item) -> {
            String processInstanceId = item.getProcessInstanceId();
            List<Task> tasks = processId2Tasks.get(processInstanceId);
            if (!CollectionUtil.isEmpty(tasks)) {
                item.setCurrentTaskNames(CollectionUtil.mapToSet(tasks, TaskInfo::getName));
                item.setCurrentTaskAssignees(CollectionUtil.mapToSet(tasks, TaskInfo::getAssignee));
            }
        });
    }

    public void preNextStepCheck(List<BaseEntity> baseEntityList) {
        for (ModelAble modelAble : baseEntityList) {
            Assert.isFalse(DocumentIteration.TYPE.equals(modelAble.getType()) && isReferenceDoc(modelAble.getOid()) && !hasDocRelation(modelAble.getOid()),
                    "请在借用中添加被借用文档关联");
        }
    }

    /**
     * 过滤团队模版用户，保留在Tenant中能查到的用户
     * @param teamRoleList 团队用户列表
     */
    public void filterTeamRoles(List<TeamTemplateRoleWithUser> teamRoleList) {
        logger.info("filterTeamRoles  start---->>>>>{}", JSONUtil.toJsonStr(teamRoleList));
        String tenantOid = SessionHelper.getCurrentUser().getTenantOid();
        logger.info("当前tenantOid: " + tenantOid);
        for (TeamTemplateRoleWithUser teamTemplateRoleWithUser : teamRoleList) {
            List<UserDTO> filteredUsers = teamTemplateRoleWithUser.getUsers().stream()
                    .filter(user -> {
                        List<UserDTO> userDTOS = userHelper.queryUserByOids(Arrays.asList(user.getOid()), tenantOid);
                        return null != userDTOS && !userDTOS.isEmpty();
                    })
                    .collect(Collectors.toList());
            teamTemplateRoleWithUser.setUsers(filteredUsers);
        }
        logger.info("filterTeamRoles  end" );
    }
}
