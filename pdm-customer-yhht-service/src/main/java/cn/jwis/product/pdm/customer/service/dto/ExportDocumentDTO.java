package cn.jwis.product.pdm.customer.service.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * @author: 汪江
 * @data: 2024-01-08 16:45
 * @description:
 **/
@AllArgsConstructor
@NoArgsConstructor
@Data
@EqualsAndHashCode
public class ExportDocumentDTO {

    @ExcelProperty(value = "编码", index = 0)
    @ApiModelProperty("编码")
    private String number;

    @ExcelProperty(value = "名称", index = 1)
    @ApiModelProperty("名称")
    private String name;

    @ExcelProperty(value = "大版本", index = 2)
    @ApiModelProperty("大版本")
    private String version;

    @ExcelProperty(value = "小版本", index = 3)
    @ApiModelProperty("小版本")
    private String iteratedVersion;

    @ExcelProperty(value = "文档类型", index = 4)
    @ApiModelProperty("文档类型")
    private String modelDefinition;

    @ExcelProperty(value = "最小分类", index = 5)
    @ApiModelProperty("最小分类")
    private String classificationInfo;

    @ExcelProperty(value = "上下文", index = 6)
    @ApiModelProperty("上下文")
    private String locationInfo;

    @ExcelProperty(value = "主文件", index = 7)
    @ApiModelProperty("主文件")
    private String primaryFile;

    @ExcelProperty(value = "附件", index = 8)
    @ApiModelProperty("附件")
    private String secondaryFile;

    @ExcelProperty(value = "描述", index = 9)
    @ApiModelProperty("描述")
    private String description;

    @ExcelProperty(value = "研制阶段", index = 10)
    @ApiModelProperty("研制阶段")
    private String yzjd;

    @ExcelProperty(value = "商业密级", index = 11)
    @ApiModelProperty("商业密级")
    private String secrecy;

    @ExcelProperty(value = "来源", index = 12)
    @ApiModelProperty("来源")
    private String source;

    @ExcelProperty(value = "状态", index = 13)
    @ApiModelProperty("状态")
    private String lifecycleStatus;

    @ExcelProperty(value = "创建时间", index = 14)
    @ApiModelProperty("创建时间")
    private String createDate;

    @ExcelProperty(value = "创建人", index = 15)
    @ApiModelProperty("创建人")
    private String createBy;

    @ExcelProperty(value = "修改时间", index = 16)
    @ApiModelProperty("修改时间")
    private String updateDate;

    @ExcelProperty(value = "修改人", index = 17)
    @ApiModelProperty("修改人")
    private String updateBy;

    @ExcelProperty(value = "发布时间", index = 18)
    @ApiModelProperty("发布时间")
    private String storageTime;

    @ExcelProperty(value = "流程业务ID", index = 19)
    @ApiModelProperty("流程业务ID")
    private String processBusinessId;



//    @ExcelProperty(value = "密级", index = 14)
//    @ApiModelProperty("密级")
//    private String levelForSecrecy;

}
