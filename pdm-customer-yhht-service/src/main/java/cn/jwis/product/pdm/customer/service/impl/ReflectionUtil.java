package cn.jwis.product.pdm.customer.service.impl;

import java.lang.reflect.Field;

public class ReflectionUtil {

    /**
     * 从对象中获取字段值，包括继承的字段
     *
     * @param object    对象
     * @param fieldName 字段名
     * @return 字段值
     * @throws NoSuchFieldException   如果字段不存在
     * @throws IllegalAccessException 如果字段不可访问
     */
    public static Object getFieldValue(Object object, String fieldName)
            throws NoSuchFieldException, IllegalAccessException {
        Class<?> clazz = object.getClass();
        Field field = getField(clazz, fieldName);
        if (field == null) {
            throw new NoSuchFieldException("Field '" + fieldName + "' not found in class hierarchy of " + clazz.getName());
        }
        field.setAccessible(true);
        return field.get(object);
    }

    /**
     * 递归查找字段，包括父类的字段
     *
     * @param clazz     类
     * @param fieldName 字段名
     * @return 字段对象，如果未找到则返回 null
     */
    public static Field getField(Class<?> clazz, String fieldName) {
        while (clazz != null) {
            try {
                return clazz.getDeclaredField(fieldName);
            } catch (NoSuchFieldException e) {
                // 当前类没有找到字段，继续向父类查找
                clazz = clazz.getSuperclass();
            }
        }
        return null;
    }
}