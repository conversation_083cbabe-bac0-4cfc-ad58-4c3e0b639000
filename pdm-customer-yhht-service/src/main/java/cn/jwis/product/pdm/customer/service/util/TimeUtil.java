package cn.jwis.product.pdm.customer.service.util;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;

public class TimeUtil {

    /**
     * 将13位毫秒时间戳转为格式化时间字符串
     * @param timestampObj 原始时间戳对象
     * @return 格式化后的时间字符串，如"2025-05-06 14:00:42"
     */
    public static String formatTimestamp(Object timestampObj) {
        if (ObjectUtil.isEmpty(timestampObj)) {
            return null;
        }

        try {
            long timestamp = Long.parseLong(String.valueOf(timestampObj));
            return DateUtil.formatDateTime(DateUtil.date(timestamp));
        } catch (Exception e) {
            // 如果不是合法的long类型时间戳
            return null;
        }
    }
}