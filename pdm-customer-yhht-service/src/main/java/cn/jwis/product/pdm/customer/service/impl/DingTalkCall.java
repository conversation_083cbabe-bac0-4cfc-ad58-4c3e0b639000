package cn.jwis.product.pdm.customer.service.impl;

import cn.hutool.core.io.FileUtil;
import cn.jwis.framework.base.bean.util.BeanUtil;
import cn.jwis.framework.base.dto.UserDTO;
import cn.jwis.framework.base.util.SpringContextUtil;
import cn.jwis.framework.base.web.session.SessionHelper;
import cn.jwis.framework.database.neo4j.session.Neo4jSession;
import cn.jwis.platform.plm.account.user.service.UserHelper;
import cn.jwis.platform.plm.dataengine.common.JWICommonService;
import cn.jwis.platform.plm.file.entity.FileMetadata;
import cn.jwis.platform.plm.workflow.engine.dto.ProcessOrderDetail;
import cn.jwis.platform.plm.workflow.engine.order.entity.ProcessOrder;
import cn.jwis.platform.plm.workflow.engine.order.response.TeamRoleWithUser;
import cn.jwis.platform.plm.workflow.engine.service.interf.ProcessModelHelper;
import cn.jwis.platform.plm.workflow.engine.service.interf.ProcessTeamHelper;
import cn.jwis.platform.plm.workflow.engine.workflow.service.interf.ProcessOrderService;
import cn.jwis.product.pdm.customer.entity.DingTaskRecord;
import cn.jwis.product.pdm.customer.entity.assistant.CheckedAssist;
import cn.jwis.product.pdm.customer.entity.assistant.SafeWrapAssist;
import cn.jwis.product.pdm.customer.service.dto.DingTalkTaskCreateDTO;
import cn.jwis.product.pdm.customer.service.dto.DingTaskReviewEntity;
import cn.jwis.product.pdm.customer.service.dto.TasksListDTO;
import cn.jwis.product.pdm.customer.service.interf.DingTalkService;
import cn.jwis.product.pdm.customer.service.interf.IntegrationMonitorService;
import cn.jwis.product.pdm.customer.service.interf.PDMFolderServiceI;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.dingtalkworkflow_1_0.models.*;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.teautil.models.RuntimeOptions;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiProcessinstanceCreateRequest;
import com.dingtalk.api.request.OapiProcessinstanceExecuteV2Request;
import com.dingtalk.api.request.OapiProcessinstanceGetRequest;
import com.dingtalk.api.response.OapiProcessinstanceCreateResponse;
import com.dingtalk.api.response.OapiProcessinstanceExecuteV2Response;
import com.dingtalk.api.response.OapiProcessinstanceGetResponse;
import com.dingtalk.open.app.api.OpenDingTalkStreamClientBuilder;
import com.dingtalk.open.app.api.security.AuthClientCredential;
import com.google.common.base.Joiner;
import com.rabbitmq.client.Channel;
import com.taobao.api.ApiException;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.RepositoryService;
import org.activiti.engine.RuntimeService;
import org.activiti.rest.service.api.repository.ModelResponse;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2023/8/11 15:48
 * @Email <EMAIL>
 */
@Service
@Slf4j
@Getter
@Setter
public class DingTalkCall implements InitializingBean, CheckedAssist, SafeWrapAssist {

    private static String dingtalk_token_redis_key = "pdm.integration.dingtalk.token";

    private static String dingtalk_token_redis_key_ding = "dingtalk.token";
    // 应用名：PDM  用于与PDM的审批任务集成
    @Value("${dingTalk.appKey}")
    private String dingTalkAppKey;

    @Value("${dingTalk.appSecret}")
    private String dingTalkAppSecret;

    @Value("${dingTalk.agentId}")
    private long dingTalkAgentId;

    @Value("${dingTalk.switch.on:false}")
    private boolean dingTalkSwitch;

    @Value("${dingTalk.name.taskName:cn_jwis_bzh}")
    private String dingTalkNameTaskName;

    @Autowired
    RedisTemplate redisTemplate;

    @Autowired
    DingTalkCall dingTalkCall;
    @Autowired
    ProcessModelHelper processModelHelper;
    @Autowired
    ProcessTeamHelper processTeamHelper;
    @Autowired
    ProcessOrderService processOrderService;
    @Autowired
    Neo4jSession neo4jSession;

    @Autowired
    PDMFolderServiceI pDMFolderServiceI;
    @Autowired
    RepositoryService repositoryService;
    @Autowired
    private DingTalkService dingTalkService;

    @Autowired
    private IntegrationMonitorService integrationMonitorService;

    @Resource
    private UserHelper userHelper;

    @Autowired
    JWICommonService jwiCommonService;

    public static void main(String[] args) throws Exception {

        DingTalkCall dingTalkCall = new DingTalkCall();

    }

    public Long findTaskId(String processInstanceId) {
        try {
            com.aliyun.teaopenapi.models.Config config = new com.aliyun.teaopenapi.models.Config();
            config.protocol = "https";
            config.regionId = "central";
            com.aliyun.dingtalkworkflow_1_0.Client client = new com.aliyun.dingtalkworkflow_1_0.Client(config);
            com.aliyun.dingtalkworkflow_1_0.models.GetProcessInstanceHeaders getProcessInstanceHeaders = new com.aliyun.dingtalkworkflow_1_0.models.GetProcessInstanceHeaders();
            getProcessInstanceHeaders.xAcsDingtalkAccessToken = dingTalkService.getToken();
            com.aliyun.dingtalkworkflow_1_0.models.GetProcessInstanceRequest getProcessInstanceRequest = new com.aliyun.dingtalkworkflow_1_0.models.GetProcessInstanceRequest()
                    .setProcessInstanceId(processInstanceId);
            GetProcessInstanceResponse response = client.getProcessInstanceWithOptions(getProcessInstanceRequest, getProcessInstanceHeaders, new com.aliyun.teautil.models.RuntimeOptions());
            String status = response.getBody().getResult().getStatus();
            if (!Arrays.asList("RUNNING", "NEW").contains(status)) {
                return -1L; // 此任务无需处理了
            }
            Long taskId = response.getBody().getResult().getTasks().get(0).getTaskId();
            return taskId;
        } catch (Exception err) {
            log.info("dingTalkCall findTaskId processInstanceId:", processInstanceId);
            log.error("dingTalkCall findTaskId error:", err);
        }
        return 0L;
    }

    public void finishTask(DingTaskRecord record, String phone) {
        try {
            long taskId = findTaskId(record.getDingProcessInstanceId());
            if (taskId < 1) { // 要么异常了，要么任务已经完成了
                return;
            }
            com.aliyun.teaopenapi.models.Config config = new com.aliyun.teaopenapi.models.Config();
            config.protocol = "https";
            config.regionId = "central";
            com.aliyun.dingtalkworkflow_1_0.Client client = new com.aliyun.dingtalkworkflow_1_0.Client(config);
            com.aliyun.dingtalkworkflow_1_0.models.ExecuteProcessInstanceHeaders executeProcessInstanceHeaders = new com.aliyun.dingtalkworkflow_1_0.models.ExecuteProcessInstanceHeaders();
            String token = dingTalkService.getToken();
            executeProcessInstanceHeaders.xAcsDingtalkAccessToken = token;

            com.aliyun.dingtalkworkflow_1_0.models.ExecuteProcessInstanceRequest executeProcessInstanceRequest = new com.aliyun.dingtalkworkflow_1_0.models.ExecuteProcessInstanceRequest()
                    .setProcessInstanceId(record.getDingProcessInstanceId())
                    .setRemark("已在PDM完成该任务！")
                    .setResult("agree")
                    .setActionerUserId(dingTalkService.getUserIdByPhone(phone, token))
                    .setTaskId(taskId);
            client.executeProcessInstanceWithOptions(executeProcessInstanceRequest, executeProcessInstanceHeaders, new com.aliyun.teautil.models.RuntimeOptions());
        } catch (Exception err) {
            log.info("dingTalkCall finishTask processInstanceId:", record.getDingProcessInstanceId());
            log.error("dingTalkCall finishTask error:", err);
        }
    }

    public String sendTask(DingTalkTaskCreateDTO dto) {
        try {
            Config config = new Config();
            config.protocol = "https";
            config.regionId = "central";
            String accessToken = dingTalkService.getToken();
            com.aliyun.dingtalkworkflow_1_0.Client client = new com.aliyun.dingtalkworkflow_1_0.Client(config);
            StartProcessInstanceHeaders startProcessInstanceHeaders = new StartProcessInstanceHeaders();
            startProcessInstanceHeaders.xAcsDingtalkAccessToken = accessToken;
            StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValues formComponentValues0 = new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValues()
                    .setName("流程名称")
                    .setValue(dto.getPdmProcessName())
                    .setComponentType("TextField");
            StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValues formComponentValues1 = new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValues()
                    .setName("任务名称")
                    .setValue(dto.getPdmTaskName())
                    .setComponentType("TextField");
            StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValues formComponentValues2 = new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValues()
                    .setName("PDM任务链接")
                    .setValue(dto.getPdmTaskUrl())
                    .setComponentType("TextField");

            // 组装评审对象
            StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValues formComponentValues3 = new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValues()
                    .setName("评审对象").setValue(initReviewEntity(dto.getPdmReviewList()));

            StartProcessInstanceRequest.StartProcessInstanceRequestApprovers approvers = new StartProcessInstanceRequest.StartProcessInstanceRequestApprovers()
                    .setActionType("NONE").setUserIds(java.util.Arrays.asList(dto.getApproverUserId()));
            StartProcessInstanceRequest startProcessInstanceRequest = new StartProcessInstanceRequest().setOriginatorUserId(dto.getOriginatorUserId()).setProcessCode(dto.getProcessCode())
                    .setMicroappAgentId(dto.getAgentId()).setApprovers(java.util.Arrays.asList(approvers));

            if ("钉钉发送文件通知".equals(dto.getCurrentFlowElement())) {
//                StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValues formComponentValues2_1 = dingTalkCallService.builFileForm(accessToken, client, config, dto);
//                StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValues formComponentValues2_1 = builFileForm(accessToken, client, config, dto);
//                startProcessInstanceRequest.setFormComponentValues(java.util.Arrays.asList(formComponentValues0, formComponentValues1, formComponentValues2, formComponentValues2_1, formComponentValues3));
            } else
                startProcessInstanceRequest.setFormComponentValues(java.util.Arrays.asList(formComponentValues0, formComponentValues1, formComponentValues2, formComponentValues3));

            StartProcessInstanceResponse response = client.startProcessInstanceWithOptions(startProcessInstanceRequest, startProcessInstanceHeaders, new RuntimeOptions());
            String processInstanceId = response.getBody().getInstanceId();
            log.info("dingTalkCall sendTask processInstanceId:" + processInstanceId);
            return processInstanceId;
        } catch (Exception err) {
            log.info("dingTalkCall sendTask dto:" + JSONObject.toJSONString(dto));
            log.error("dingTalkCall sendTask error:", err);
            return "";
        }
    }

    public ProcessOrder findByOid(String oid) {
        return (ProcessOrder) this.processOrderService.findDetailEntity(oid, "ProcessOrder");
    }

    //往钉钉发送流程信息
    public String sendTaskFlow(DingTalkTaskCreateDTO dto, ProcessOrder processOrder) {
        try {

            log.error("发钉钉成功" + processOrder.getCreateBy() + "," + processOrder.getOid());

            String oid = processOrder.getOid();
            log.error("oid" + oid);
            ProcessOrder byOid = this.findByOid(oid);
            if (byOid == null) {
                return null;
            } else {
                log.error("发钉钉成功1" + processOrder.getCreateBy() + "," + processOrder.getOid());

                ProcessOrderDetail detail = (ProcessOrderDetail) BeanUtil.copyProperties(byOid, new ProcessOrderDetail());

                String processModelId = detail.getProcessModelId();
                ModelResponse modelResponse = this.processModelHelper.findById(processModelId);

                detail.setProcessDefinitionId(modelResponse.getKey());
                List<TeamRoleWithUser> roleUserByProcessOrder = this.processTeamHelper.findRoleUserByProcessOrder(oid);

                detail.setTeamRoleWithUsers(roleUserByProcessOrder);
                List<TeamRoleWithUser> teamRoleWithUsers = detail.getTeamRoleWithUsers();
                String account = "";
                ArrayList<String> list = new ArrayList<String>();
                for (TeamRoleWithUser teamRoleWithUser : teamRoleWithUsers) {
                    String name = teamRoleWithUser.getName();
                    if (dingTalkNameTaskName.equals(name)) {
                        List<UserDTO> users = teamRoleWithUser.getUsers();
                        for (UserDTO user : users) {
                            account = user.getAccount();
                            list.add(account);
                        }

                    }

                }
                String approvers = Joiner.on(",").join(list);
                log.error("发钉钉用户信息" + processOrder.getCreateBy() + "," + approvers);
                //phone
                //List<String> roleNames = new ArrayList<>();
                //String approvers = teamRoleWithUsers.toString();
                String accessToken = dingTalkService.getToken();
                DingTalkClient client = new DefaultDingTalkClient("https://api.dingtalk.com/v1.0/workflow/processInstances");
                OapiProcessinstanceCreateRequest req = new OapiProcessinstanceCreateRequest();
                req.setAgentId(dto.getAgentId());
                com.aliyun.dingtalkworkflow_1_0.models.StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValues formComponentValues0 = new com.aliyun.dingtalkworkflow_1_0.models.StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValues()
                        .setName("单选框")
                        .setValue("通过");
                com.aliyun.dingtalkworkflow_1_0.models.StartProcessInstanceRequest startProcessInstanceRequest = new com.aliyun.dingtalkworkflow_1_0.models.StartProcessInstanceRequest()
                        .setFormComponentValues(java.util.Arrays.asList(
                                formComponentValues0
                        ));
                req.setFormComponentValues((List<OapiProcessinstanceCreateRequest.FormComponentValueVo>) startProcessInstanceRequest);
                req.setProcessCode(dto.getProcessCode());
                req.setOriginatorUserId(dto.getOriginatorUserId());
                req.setDeptId(-1L);
                //设置审批人
                List<OapiProcessinstanceCreateRequest.ProcessInstanceApproverVo> processInstanceApproverVoList = new ArrayList<OapiProcessinstanceCreateRequest.ProcessInstanceApproverVo>();
                OapiProcessinstanceCreateRequest.ProcessInstanceApproverVo processInstanceApproverVo = new OapiProcessinstanceCreateRequest.ProcessInstanceApproverVo();
                processInstanceApproverVo.setUserIds(Arrays.asList(approvers));
                processInstanceApproverVoList.add(processInstanceApproverVo);
                req.setApproversV2(processInstanceApproverVoList);
                OapiProcessinstanceCreateResponse rsp = client.execute(req, accessToken);
                System.out.println(JSON.toJSONString(rsp));
                //返回钉钉流程的实例id，一个流程对应一个实例id
                String processInstanceId = rsp.getProcessInstanceId();
                log.error("发钉钉返回钉钉流程实例id" + processInstanceId );

                return processInstanceId;
            }

        } catch (Exception err) {
            log.info("dingTalkCall sendTask dto:" + JSONObject.toJSONString(dto));
            log.error("dingTalkCall sendTask error:" , err);
            return "";
        }

    }

    public static com.aliyun.dingtalkworkflow_1_0.Client createClient() throws Exception {
        com.aliyun.teaopenapi.models.Config config = new com.aliyun.teaopenapi.models.Config();
        config.protocol = "https";
        config.regionId = "central";
        return new com.aliyun.dingtalkworkflow_1_0.Client(config);
    }

    public String sendTaskComplete(String processInstanceId) {
        try {

            String accessToken = dingTalkService.getToken();
            if (processInstanceId == null) {
                return null;
            } else {
                DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/process/instance/execute");
                OapiProcessinstanceExecuteV2Request req = new OapiProcessinstanceExecuteV2Request();
                OapiProcessinstanceExecuteV2Request.ExecuteTaskRequest executeTaskRequest = new OapiProcessinstanceExecuteV2Request.ExecuteTaskRequest();
                List<TasksListDTO> stringsList = taskIds(processInstanceId, accessToken);
                for (int i = 0; i < stringsList.size(); i++) {
                    String taskid = stringsList.get(i).getTaskid();
                    Long aLong = Long.valueOf(taskid);
                    String userid = stringsList.get(i).getUserid();
                    executeTaskRequest.setProcessInstanceId(processInstanceId);
                    executeTaskRequest.setActionerUserid(userid);
                    executeTaskRequest.setTaskId(aLong);
                    executeTaskRequest.setRemark("同意");
                    executeTaskRequest.setResult("agree");
                    req.setRequest(executeTaskRequest);
                    OapiProcessinstanceExecuteV2Response rsp = client.execute(req, accessToken);
                }
                return "";
            }
        } catch (Exception err) {

            return "";
        }
    }

    private List<TasksListDTO> taskIds(String processInstanceId, String accessToken) throws ApiException {
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/processinstance/get");
        OapiProcessinstanceGetRequest req = new OapiProcessinstanceGetRequest();
        req.setProcessInstanceId(processInstanceId);
        OapiProcessinstanceGetResponse rsp = client.execute(req, accessToken);
        System.out.println(rsp.getBody());
        OapiProcessinstanceGetResponse.ProcessInstanceTopVo processInstance = rsp.getProcessInstance();
        List<OapiProcessinstanceGetResponse.TaskTopVo> tasks = processInstance.getTasks();
        ArrayList<TasksListDTO> list = new ArrayList<TasksListDTO>();
        TasksListDTO tasksListDTO = new TasksListDTO();
        for (int i = 0; i < tasks.size(); i++) {
            //判断钉钉系统中任务的状态，先找到未完成的状态对应的任务
            String taskStatus = tasks.get(i).getTaskStatus();
            if (taskStatus.equals("NEW")) {
                String taskid = tasks.get(i).getTaskid();
                String userid = tasks.get(i).getUserid();
                tasksListDTO.setTaskid(taskid);
                tasksListDTO.setUserid(userid);
                list.add(tasksListDTO);
            }
        }
        return list;
    }

    private String initReviewEntity(List<DingTaskReviewEntity> pdmReviewList){
        JSONArray array1 = new JSONArray();
        for(DingTaskReviewEntity entity : pdmReviewList){
            JSONArray array2 = new JSONArray();
            JSONObject obj1 = new JSONObject();
            obj1.put("name","编码");
            obj1.put("value",entity.getNumber());
            JSONObject obj2 = new JSONObject();
            obj2.put("name","名称");
            obj2.put("value",entity.getName());
            JSONObject obj3 = new JSONObject();
            obj3.put("name","版本");
            obj3.put("value",entity.getVersion());
            JSONObject obj4 = new JSONObject();
            obj4.put("name","状态");
            obj4.put("value",entity.getStatus());
            JSONObject obj5 = new JSONObject();
            obj5.put("name","创建人");
            obj5.put("value",entity.getCreator());
            JSONObject obj6 = new JSONObject();
            obj6.put("name","创建时间");
            obj6.put("value",entity.getCreateTime());
            JSONObject obj7 = new JSONObject();
            obj7.put("name","规格");
            obj7.put("value",entity.getGg() == null ? "" : entity.getGg());
            JSONObject obj8 = new JSONObject();
            obj8.put("name","生产厂家");
            obj8.put("value",entity.getSccj() == null ? "" : entity.getSccj());
            JSONObject obj9 = new JSONObject();
            obj9.put("name","质量等级");
            obj9.put("value",entity.getZldj() == null ? "" : entity.getZldj());
            JSONObject obj10 = new JSONObject();
            obj10.put("name","封装形式");
            obj10.put("value",entity.getFzxs() == null ? "" : entity.getFzxs());
            JSONObject obj11 = new JSONObject();
            obj11.put("name","材质");
            obj11.put("value",entity.getCz() == null ? "" : entity.getCz());
            JSONObject obj12 = new JSONObject();
            obj12.put("name","表面处理");
            obj12.put("value",entity.getBmcl() == null ? "" : entity.getBmcl());
            JSONObject obj13 = new JSONObject();
            obj13.put("name","datasheet");
            obj13.put("value",getDingFile(entity.getDatasheet()));
            array2.addAll(Arrays.asList(obj1,obj2,obj3,obj4,obj5,obj6,obj7,obj8,obj9,obj10,obj11,obj12,obj13));
            array1.add(array2);
        }
        return JSONArray.toJSONString(array1);
    }

    private String getDingFile(FileMetadata datasheet) {
        //[{\"spaceId\": \"163xxxx658\", \"fileName\": \"2644.JPG\", \"fileSize\": \"333\", \"fileType\": \"jpg\", \"fileId\": " +
        //    "\"643xxxx140\"}]
//        StringBuffer sb = new StringBuffer("");
//        if(datasheet == null){
//            datasheet = new FileMetadata();
//            datasheet.setFileName("示例文档.xlsx");
//            datasheet.setFilePath("http://172.16.8.233:9000/mybucket/1695002299302_Part%E5%AF%BC%E5%85%A5%E6%A8%A1%E6%9D%BF%20%281%29.xlsx");
//        }
//        sb.append("<a href=\"").append(datasheet.getFilePath()).append("\">").append(datasheet.getFileName()).append("</a>");
        return datasheet != null ? datasheet.getFilePath() : null;
    }

    public Map<String, String> getUserDetail(Map<String, String> userAccountDingIdMap) {
        try {
            String token = dingTalkService.getToken();
            Map<String, String> userIdDetailMap = userAccountDingIdMap.entrySet().stream().collect(Collectors.toMap(entry -> entry.getKey(), applyNoThrow(entry -> dingTalkService.getUserUnionid(entry.getValue(), token))));
            return userIdDetailMap;
        }catch (Exception e){
            log.error(e.getMessage(), e);
        }
        return Collections.emptyMap();
    }

    @Value("${fkTeamRoleId:53e1be21-aab0-4ff8-adca-16d08beed7a4}")
    private String fkTeamRoleId;

    @Value("${tdcyTeamRoleId:d1ef2575-7e9d-4ac7-9e07-f2cc5a46e65a}")
    private String tdcyTeamRoleId;

    @Autowired
    private CustomerCommonServiceImpl customerCommonService;

    private void initContext(RuntimeService runtimeService,String tenantOid,String owner) {
        UserHelper userHelper = (UserHelper)SpringContextUtil.getBean("defaultUserHelperImpl");
        UserDTO user = userHelper.findByAccount(owner.toString());
        user.setAccount(owner.toString());
        user.setOid(owner.toString());
        user.setTenantOid(tenantOid.toString());
        SessionHelper.addCurrentUser(user);
    }

    public String getDingWorkflowCode(String processInstanceId) {
        try {
//            String tokenDing = dingTalkService.getToken();
            String tokenDing = dingTalkService.getTokenNew();
            com.aliyun.dingtalkworkflow_1_0.Client client = DingTalkCall.createClient();
            com.aliyun.dingtalkworkflow_1_0.models.GetProcessInstanceHeaders getProcessInstanceHeaders = new com.aliyun.dingtalkworkflow_1_0.models.GetProcessInstanceHeaders();
            ProcessForecastHeaders processForecastHeaders = new ProcessForecastHeaders();
            getProcessInstanceHeaders.xAcsDingtalkAccessToken = tokenDing;
            processForecastHeaders.xAcsDingtalkAccessToken = tokenDing;
            com.aliyun.dingtalkworkflow_1_0.models.GetProcessInstanceRequest getProcessInstanceRequest = new com.aliyun.dingtalkworkflow_1_0.models.GetProcessInstanceRequest()
                    .setProcessInstanceId(processInstanceId);
            GetProcessInstanceResponse processInstanceWithOptions = client.getProcessInstanceWithOptions(getProcessInstanceRequest, getProcessInstanceHeaders, new RuntimeOptions());
            return processInstanceWithOptions.getBody().getResult().getBusinessId();
        }catch (Exception e){
            log.error(e.getMessage(), e);
        }
        return null;
    }

    public void addProcessInstanceCommentWithOptions(String token, String spaceId, String fileSize, String fileId, String fileName, String instanceId, String userId){
        try {
            com.aliyun.dingtalkworkflow_1_0.Client client = DingTalkCall.createClient();
            com.aliyun.dingtalkworkflow_1_0.models.AddProcessInstanceCommentHeaders addProcessInstanceCommentHeaders = new com.aliyun.dingtalkworkflow_1_0.models.AddProcessInstanceCommentHeaders();
            addProcessInstanceCommentHeaders.xAcsDingtalkAccessToken = token;
            com.aliyun.dingtalkworkflow_1_0.models.AddProcessInstanceCommentRequest.AddProcessInstanceCommentRequestFileAttachments fileAttachments0 = new com.aliyun.dingtalkworkflow_1_0.models.AddProcessInstanceCommentRequest.AddProcessInstanceCommentRequestFileAttachments()
                    .setSpaceId(spaceId).setFileSize(fileSize).setFileId(fileId).setFileName(fileName).setFileType("file");
            com.aliyun.dingtalkworkflow_1_0.models.AddProcessInstanceCommentRequest.AddProcessInstanceCommentRequestFile file = new com.aliyun.dingtalkworkflow_1_0.models.AddProcessInstanceCommentRequest.AddProcessInstanceCommentRequestFile()
//                    .setPhotos(java.util.Arrays.asList("https://url1"))
                    .setAttachments(java.util.Arrays.asList(fileAttachments0));
            com.aliyun.dingtalkworkflow_1_0.models.AddProcessInstanceCommentRequest addProcessInstanceCommentRequest = new com.aliyun.dingtalkworkflow_1_0.models.AddProcessInstanceCommentRequest()
                    .setProcessInstanceId(instanceId).setText("外发回执清单附件:").setCommentUserId(userId).setFile(file);
                client.addProcessInstanceCommentWithOptions(addProcessInstanceCommentRequest, addProcessInstanceCommentHeaders, new com.aliyun.teautil.models.RuntimeOptions());
        } catch (Exception e){
            log.error(e.getMessage(), e);
        }
    }

    //    public StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValues builFileForm(String accessToken, com.aliyun.dingtalkworkflow_1_0.Client client, Config config, DingTalkTaskCreateDTO dto) {
//
//        CustomerCommonServiceImpl customerCommonService = (CustomerCommonServiceImpl) SpringContextUtil.getBean("customerCommonServiceImpl");
//
//        StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValues formComponentValues2_1 = new StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValues();
//
//        List<JSONObject> attachmentJsonList = safeWrapList(dto.getPdmReviewFileList()).parallelStream()
//                .flatMap(applyNoThrow(fileMetadata -> {
//                    String filePath = CustomerCommonServiceImpl.tmpPath + fileMetadata.getFileName();
//                    customerCommonService.download2TmpDir(fileMetadata, filePath);
//                    String fileCompleteName = filePath.substring(filePath.lastIndexOf(File.separator) + 1), fileSimpleName = fileCompleteName.substring(14);
//                    log.info("filePath==>" + filePath + "fileCompleteName==>" + fileCompleteName + " fileSimpleName==>" + fileSimpleName);
//
//                    Collection<UploadDDFileEntity> uploadList = filePath.endsWith(".zip") ? ZipAssistant.unzipFile(new ZipFile(new File(filePath)), biApplyNoThrow((zipEntry, inputStream) -> {
//                        String zipEntryName = zipEntry.getName();
//                        if (zipEntryName.endsWith(".PcbDoc")) { // schdoc
//                            log.info("zipEntryName==>" + zipEntryName);
//                            zipEntryName = zipEntryName.substring(zipEntryName.lastIndexOf("/") + 1);
//                            log.info("zipEntryName subStr==>" + zipEntryName);
//                            return new UploadDDFileEntity(inputStream, System.currentTimeMillis() + "_" + zipEntryName, zipEntryName);
//                        }
//                        inputStream.close();
//                        return null;
////                    }), new ArrayList<>()) : Arrays.asList(new UploadDDFileEntity(new FileInputStream(filePath), fileCompleteName, fileSimpleName));
//                    }), new ArrayList<>()) : Arrays.asList(new UploadDDFileEntity(new FileInputStream(filePath), fileSimpleName, fileSimpleName));
//
//                    return uploadList.stream().filter(Objects::nonNull).map(applyNoThrow(uploadDDFileEntity -> {
//                        Long spaceIdLong = client.getAttachmentSpaceWithOptions(new GetAttachmentSpaceRequest().setUserId(dto.getOriginatorUserId()).setAgentId(dto.getAgentId())
//                                , new GetAttachmentSpaceHeaders().setXAcsDingtalkAccessToken(accessToken), new RuntimeOptions()).getBody().getResult().getSpaceId();
//                        String spaceId = spaceIdLong == null ? null : String.valueOf(spaceIdLong);
//
//                        com.aliyun.dingtalkstorage_1_0.Client storageClient = new com.aliyun.dingtalkstorage_1_0.Client(config);
//                        GetFileUploadInfoRequest getFileUploadInfoRequest = new GetFileUploadInfoRequest().setUnionId(dto.getOriginatorUserUionId()).setProtocol("HEADER_SIGNATURE").setMultipart(false)
//                                .setOption(new GetFileUploadInfoRequest.GetFileUploadInfoRequestOption().setStorageDriver("DINGTALK").setPreferRegion("SHANGHAI")
//                                        .setPreCheckParam(new GetFileUploadInfoRequest.GetFileUploadInfoRequestOptionPreCheckParam().setSize(fileMetadata.getFileSize()).setParentId("0").setName(uploadDDFileEntity.getFileCompleteName())));
//                        log.info("getFileUploadInfoRequest==>" + JSONUtil.toJsonStr(getFileUploadInfoRequest));
//                        //执行上传操作
//                        GetFileUploadInfoResponseBody body = storageClient.getFileUploadInfoWithOptions(spaceId, getFileUploadInfoRequest, new GetFileUploadInfoHeaders().setXAcsDingtalkAccessToken(accessToken), new RuntimeOptions()).getBody();
//
//                        URL url = new URL(body.headerSignatureInfo.getResourceUrls().get(0));
//                        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
//                        Map<String, String> headers = body.headerSignatureInfo.headers;
//                        if (headers != null)
//                            for (Map.Entry<String, String> entry : headers.entrySet())
//                                connection.setRequestProperty(entry.getKey(), entry.getValue());
//
//                        connection.setDoOutput(true);
//                        connection.setRequestMethod("PUT");
//                        connection.setUseCaches(false);
//                        connection.setRequestProperty("Accept-Charset", "UTF-8");
//                        connection.setRequestProperty("contentType", "UTF-8");
//                        connection.setRequestProperty("Authorization", body.headerSignatureInfo.headers.get("Authorization"));
//                        connection.setRequestProperty("x-oss-date", body.headerSignatureInfo.headers.get("x-oss-date"));
//                        connection.setReadTimeout(10000);
//                        connection.setConnectTimeout(10000);
//                        connection.connect();
//                        try (OutputStream out = connection.getOutputStream(); InputStream is = uploadDDFileEntity.getInputStream()) {
//                            if (is != null) {
//                                byte[] b = new byte[1024];
//                                int temp;
//                                while ((temp = is.read(b)) != -1)
//                                    out.write(b, 0, temp);
//                                int responseCode = connection.getResponseCode();
//                            }
//                            connection.disconnect();
//                        } catch (Exception e) {
//                            log.error(e.getMessage(), e);
//                        }
//
//                        String uniqId = UUID.randomUUID().toString();
//                        CommitFileRequest commitFileRequest = new CommitFileRequest().setUnionId(dto.getOriginatorUserUionId()).setUploadKey(body.getUploadKey()).setName(uploadDDFileEntity.getFileCompleteName()).setParentId("0")
//                                .setOption(new CommitFileRequest.CommitFileRequestOption().setSize(fileMetadata.getFileSize()).setConflictStrategy("RETURN_DENTRY_IF_EXISTS")
//                                        .setAppProperties(Arrays.asList(new CommitFileRequest.CommitFileRequestOptionAppProperties().setName(uniqId).setValue(uniqId).setVisibility("PUBLIC"))));
//
//                        CommitFileResponseBody.CommitFileResponseBodyDentry dentry = storageClient.commitFileWithOptions(spaceId, commitFileRequest, new CommitFileHeaders().setXAcsDingtalkAccessToken(accessToken), new RuntimeOptions()).getBody().getDentry();
//                        log.info("完成上传==>" + JSONUtil.toJsonStr(dentry));
//                        return new JSONObject().fluentPut("fileId", dentry.getId()).fluentPut("fileName", uploadDDFileEntity.getFileSimpleName())
//                                .fluentPut("fileType", dentry.getExtension()).fluentPut("spaceId", dentry.getSpaceId()).fluentPut("fileSize", dentry.getSize());
//                    }));
//
//                })).collect(Collectors.toList());
//
//        formComponentValues2_1.setName("附件").setValue(JSON.toJSONString(attachmentJsonList)).setComponentType("DDAttachment");
//        return formComponentValues2_1;
//    }

    //    private List<WorkflowActivityRules> targetSelectActioners(DingTalkTaskCreateDTO dto, ProcessOrder processOrder) throws Exception {
//
//        String accessToken = getTokenDing();
//        com.aliyun.dingtalkworkflow_1_0.Client client = DingTalkCall.createClient();
//        ProcessForecastHeaders processForecastHeaders = new ProcessForecastHeaders();
//        processForecastHeaders.xAcsDingtalkAccessToken = accessToken;
//
//        ProcessForecastRequest.ProcessForecastRequestFormComponentValues formComponentValues0 = new ProcessForecastRequest.ProcessForecastRequestFormComponentValues()
//
//                .setName("单选框")
//                .setValue("通过");
//        ProcessForecastRequest processForecastRequest = new ProcessForecastRequest()
//                .setProcessCode(dto.getProcessCode())
//                .setDeptId(1)
//                .setUserId(dto.getOriginatorUserId())
//                .setFormComponentValues(java.util.Arrays.asList(
//                        formComponentValues0
//                ));
//
//        try {
//            ArrayList<WorkflowActivityRules> list = new ArrayList<>();
//            String actorKey = "";
//            String activityName = "";
//            ProcessForecastResponse processForecastResponse = client.processForecastWithOptions(processForecastRequest, processForecastHeaders, new RuntimeOptions());
//
//            List<ProcessForecastResponseBody.ProcessForecastResponseBodyResultWorkflowActivityRules> workflowActivityRules = processForecastResponse.getBody().result.workflowActivityRules;
//
//            for (ProcessForecastResponseBody.ProcessForecastResponseBodyResultWorkflowActivityRules workflowActivityRule : workflowActivityRules) {
//                WorkflowActivityRules workflowActivityRules1 = new WorkflowActivityRules();
//                actorKey = workflowActivityRule.getWorkflowActor().getActorKey();
//                activityName = workflowActivityRule.activityName;
//                workflowActivityRules1.setActivityId(actorKey);
//                workflowActivityRules1.setActivityName(activityName);
//                list.add(workflowActivityRules1);
//            }
//
//            return list;
//        } catch (TeaException err) {
//            ArrayList<WorkflowActivityRules> list = new ArrayList<>();
//            if (!com.aliyun.teautil.Common.empty(err.code) && !com.aliyun.teautil.Common.empty(err.message)) {
//                // err 中含有 code 和 message 属性，可帮助开发定位问题
//            }
//            return list;
//        }
//
//    }


    @Override
    public void afterPropertiesSet() throws Exception {
//        if(dingTalkSwitch) { // 启动钉钉的回调监听
//            startListening();
//            log.info("start dingTalk Listening success");
//        }
    }

    @Value("${prod.env:false}")
    private Boolean prodEnv;

    @PostConstruct
    public void initListen() throws Exception {
        DingTalkCallListen listen = getDingTalkCallListen();
//        if(dingTalkSwitch && !prodEnv && "dingadrceximdb5kslds".equals(listen.dingTalkAppKey)) { // 启动钉钉的回调监听
        if(dingTalkSwitch && !prodEnv && "dingfdki2z2pdzzzmny5".equals(listen.dingTalkAppKey)) { // 启动钉钉的回调监听
            startListening();
            log.info("start dingTalk Listening success" + LocalDateTime.now());
        }
        if(dingTalkSwitch && !prodEnv && "dinglsctunufddsknl74".equals(listen.dingTalkAppKeyNew)) { // 启动钉钉的回调监听
            startListeningNew();
            log.info("start dingTalk Listening New success" + LocalDateTime.now());
        }
    }

    @Autowired
    DingTalkCallListen dingTalkCallListen;

    public DingTalkCallListen getDingTalkCallListen(){
        return SpringContextUtil.getBean(DingTalkCallListen.class);
    }

    public void startListening() throws Exception {
        DingTalkCallListen listen = getDingTalkCallListen();
        log.info("dingTalkAppKey==>" + listen.dingTalkAppKey);
        log.info("dingTalkAppSecret==>" + listen.dingTalkAppSecret + "==>" + dingTalkCallListen + "==>" + getDingTalkCallListen());
        OpenDingTalkStreamClientBuilder
                .custom()
                .credential(new AuthClientCredential(listen.dingTalkAppKey, listen.dingTalkAppSecret)).maxConnectionCounts(1)
                //注册事件监听
                .registerAllEventListener(event -> {
                    log.info("DingTalk onEvent:{}", event);
                    return getDingTalkCallListen().listening(event);
                }).build().start();
    }

    public void startListeningNew() throws Exception {
        DingTalkCallListen listen = getDingTalkCallListen();
        log.info("dingTalkAppKeyNew==>" + listen.dingTalkAppKeyNew);
        log.info("dingTalkAppSecretNew==>" + listen.dingTalkAppSecretNew + "==>" + dingTalkCallListen + "==>" + getDingTalkCallListen());
        OpenDingTalkStreamClientBuilder
                .custom()
                .credential(new AuthClientCredential(listen.dingTalkAppKeyNew, listen.dingTalkAppSecretNew)).maxConnectionCounts(1)
                //注册事件监听
                .registerAllEventListener(event -> {
                    log.info("DingTalk onEvent:{}", event);
                    return getDingTalkCallListen().listening(event);
                }).build().start();
    }

    @Value("${prod.mq.queue.name:bpms_instance_pdm_test}")
    private String prodMqQueueName;

    @Value("${prod.mq.queue.switch.on:false}")
    private Boolean prodMqQueueSwitchOn;

    @RabbitListener(queues="${prod.mq.queue.name:bpms_instance_pdm_test}", containerFactory = "rabbitListenerContainerFactory")    //监听器监听指定的Queue
    public void process(Message message, Channel channel) {
        log.info("prodMqQueueName==>" + prodMqQueueName + " message==>" + message.getBody());
        if("bpms_instance_pdm_test".equals(prodMqQueueName) && prodMqQueueSwitchOn){
            getDingTalkCallListen().listeningMq(message, channel);
        }else if("bpms_instance_pdm".equals(prodMqQueueName))
            getDingTalkCallListen().listeningMq(message, channel);
    }


    public void addProcessInstanceCommentWithMessage(String token, JSONObject fileJson, String instanceId, String userId, String commentMessage) {

        try {
            String spaceId = fileJson.getString("spaceId");
            String fileSize = fileJson.getString("fileSize");
            String fileId = fileJson.getString("fileId");
            String fileName = FileUtil.getPrefix(fileJson.getString("fileName"));

            com.aliyun.dingtalkworkflow_1_0.Client client = DingTalkCall.createClient();
            com.aliyun.dingtalkworkflow_1_0.models.AddProcessInstanceCommentHeaders addProcessInstanceCommentHeaders = new com.aliyun.dingtalkworkflow_1_0.models.AddProcessInstanceCommentHeaders();
            addProcessInstanceCommentHeaders.xAcsDingtalkAccessToken = token;
            com.aliyun.dingtalkworkflow_1_0.models.AddProcessInstanceCommentRequest.AddProcessInstanceCommentRequestFileAttachments fileAttachments0 = new com.aliyun.dingtalkworkflow_1_0.models.AddProcessInstanceCommentRequest.AddProcessInstanceCommentRequestFileAttachments()
                    .setSpaceId(spaceId).setFileSize(fileSize).setFileId(fileId).setFileName(fileName).setFileType("file");
            com.aliyun.dingtalkworkflow_1_0.models.AddProcessInstanceCommentRequest.AddProcessInstanceCommentRequestFile file = new com.aliyun.dingtalkworkflow_1_0.models.AddProcessInstanceCommentRequest.AddProcessInstanceCommentRequestFile()
                    .setAttachments(java.util.Arrays.asList(fileAttachments0));
            com.aliyun.dingtalkworkflow_1_0.models.AddProcessInstanceCommentRequest addProcessInstanceCommentRequest = new com.aliyun.dingtalkworkflow_1_0.models.AddProcessInstanceCommentRequest()
                    .setProcessInstanceId(instanceId).setText(commentMessage + ":").setCommentUserId(userId).setFile(file);
            client.addProcessInstanceCommentWithOptions(addProcessInstanceCommentRequest, addProcessInstanceCommentHeaders, new com.aliyun.teautil.models.RuntimeOptions());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }
}
