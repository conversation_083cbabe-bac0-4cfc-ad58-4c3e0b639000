package cn.jwis.product.pdm.customer.service.impl;

import cn.jwis.product.pdm.customer.dos.UserDo;
import cn.jwis.product.pdm.customer.repo.PdmUserRepo;
import cn.jwis.product.pdm.customer.service.interf.CustomerUserHelper;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.zip.GZIPOutputStream;

@Slf4j
@Service
public class CustomerUserHelperImpl implements CustomerUserHelper {

    @Resource
    PdmUserRepo pdmUserRepo;

    @Override
    public String getAllUserInfo() {
        List<UserDo> result = pdmUserRepo.queryUserAndOrgInfo();
        return compressString(JSON.toJSONString(result));
    }

    //压缩
    public static String compressString(String input) {
        try {
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            GZIPOutputStream gzip = new GZIPOutputStream(bos);
            gzip.write(input.getBytes(StandardCharsets.UTF_8));
            gzip.close();
            return bos.toString("ISO-8859-1");
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }
}
