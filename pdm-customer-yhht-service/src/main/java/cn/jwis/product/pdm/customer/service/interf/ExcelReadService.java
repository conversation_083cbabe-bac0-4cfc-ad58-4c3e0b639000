package cn.jwis.product.pdm.customer.service.interf;

import cn.hutool.json.JSONObject;
import cn.jwis.product.pdm.customer.entity.assistant.uncheckinterface.UnCheckSupplier;

import java.io.InputStream;
import java.util.List;
import java.util.Map;
import java.util.Set;

public interface ExcelReadService {

    Map<Integer, List<JSONObject>> horizonReadGetSheetDataJsonMap(UnCheckSupplier<InputStream> fileInputStreamSupplier, Set<Integer> sheetIndexSet
            , Map<Integer, Integer> titleAndDataAndIndexMap, Map<Integer, String> titleCvtMap);

}




