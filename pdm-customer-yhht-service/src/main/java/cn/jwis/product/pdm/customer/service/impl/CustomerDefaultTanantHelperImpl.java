package cn.jwis.product.pdm.customer.service.impl;

import cn.jwis.framework.base.bean.util.BeanUtil;
import cn.jwis.framework.base.web.session.SessionHelper;
import cn.jwis.framework.database.core.query.dynamic.Condition;
import cn.jwis.platform.iam.structure.TreeAbleEx;
import cn.jwis.platform.iam.structure.TreeNode;
import cn.jwis.platform.plm.account.tenant.service.service.DefaultTanantHelperImpl;
import cn.jwis.product.pdm.customer.repo.CustomerCommonRepo;
import cn.jwis.product.pdm.customer.service.dto.TreeAbleExDto;
import com.alibaba.excel.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> yefei
 * @Date ： 2024/7/22
 * @Description :
 */

@Slf4j
@Component
@Primary
public class CustomerDefaultTanantHelperImpl extends DefaultTanantHelperImpl {

    @Resource
    CustomerCommonRepo customerCommonRepo;

    @Override
    public TreeNode searchUserOrgTree(String searchKey) {
        Condition condition = Condition.where("markForDelete").eq(false).and(new Condition[]{this.userHelper.buildSearchCondition(searchKey)});
        long start = System.currentTimeMillis();
        List<TreeAbleEx> treeAbleExList =
                customerCommonRepo.searchUserTree(SessionHelper.getCurrentUser().getTenantOid(),condition);
        long end = System.currentTimeMillis();
        long queryCost = end - start;
        log.info("queryCost:{}",queryCost);
        start = end;
        TreeNode treeNode = makeOrgTree(treeAbleExList);
        end = System.currentTimeMillis();
        long copyCost = end - start;
        log.info("copyCost:{}",copyCost);
        return treeNode;
    }

//    @Override
//    public TreeNode searchUserOrgTree(String searchKey) {
//        Condition condition = Condition.where("markForDelete").eq(false).and(new Condition[]{this.userHelper.buildSearchCondition(searchKey)});
//        return this.tenantService.searchUserTree(SessionHelper.getCurrentUser().getTenantOid(), condition);
//    }

    public TreeNode makeOrgTree(Collection<TreeAbleEx> treeAbleExList) {
        if (CollectionUtils.isEmpty(treeAbleExList)) {
            return null;
        }
        Map<String, TreeNode> treeNodeOidMap = new HashMap<>();

        treeAbleExList.forEach(item -> {
            TreeNode parentNode = treeNodeOidMap.get(item.getParentNodeId());
            if (parentNode == null) {
                parentNode = new TreeNode();
                treeNodeOidMap.put(item.getParentNodeId(), parentNode);
            }

            TreeNode currentNode = treeNodeOidMap.get(item.getCurrentNodeId());
            if (currentNode == null) {
                currentNode = new TreeNode();
                treeNodeOidMap.put(item.getCurrentNodeId(), currentNode);
            }
            currentNode.setCurrent(trans(item));
            parentNode.addChildren(currentNode);
        });
        return treeNodeOidMap.get(StringUtils.EMPTY).getChildren().first();
    }

    private TreeAbleExDto trans(TreeAbleEx from) {
        return BeanUtil.copyProperties(from,new TreeAbleExDto());
    }
}
