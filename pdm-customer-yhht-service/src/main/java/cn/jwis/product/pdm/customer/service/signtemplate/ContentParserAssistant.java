package cn.jwis.product.pdm.customer.service.signtemplate;

import cn.jwis.framework.base.util.SpringContextUtil;
import cn.jwis.framework.base.web.session.SessionHelper;
import cn.jwis.platform.plm.foundation.attachment.entity.File;
import cn.jwis.product.pdm.customer.entity.SignInfo;
import cn.jwis.product.pdm.customer.service.dto.PdfSignatureTemplate;
import cn.jwis.product.pdm.customer.service.impl.CustomerDocHelperImpl;
import cn.jwis.product.pdm.customer.service.interf.ContentParserService;
import com.itextpdf.awt.geom.Rectangle2D;
import com.itextpdf.text.Rectangle;
import com.itextpdf.text.pdf.PdfReader;
import com.itextpdf.text.pdf.parser.PdfReaderContentParser;
import com.itextpdf.text.pdf.parser.TextRenderInfo;
import org.springframework.stereotype.Service;

import java.util.LinkedHashMap;
import java.util.Map;

@Service
public class ContentParserAssistant implements ContentParserService {

    public SignInfo contentParser(PdfReader reader) throws Exception {
        Rectangle rectangle = reader.getPageSize(1);
        float width = rectangle.getWidth(), height = rectangle.getHeight();

        CustomerDocHelperImpl customerDocHelper = (CustomerDocHelperImpl) SpringContextUtil.getBean("customerDocHelperImpl");
        Boolean revert = Boolean.FALSE;
        String pdfTemplateCode = null;

        // 定义尺寸模板的条件和相应的模板代码
        Map<String, float[]> templates = new LinkedHashMap<>();
        templates.put(PdfSignatureTemplate.A4.getKey(), new float[]{590, 600, 836, 846});
        templates.put(PdfSignatureTemplate.A4x3.getKey(), new float[]{1780, 1790, 836, 846});
        templates.put(PdfSignatureTemplate.A3.getKey(), new float[]{1185, 1195, 836, 846});
        templates.put(PdfSignatureTemplate.A3x4.getKey(), new float[]{2520, 2530, 1185, 1195});
        templates.put(PdfSignatureTemplate.A2.getKey(), new float[]{1678, 1688, 1185, 1195});
        templates.put(PdfSignatureTemplate.A2x3.getKey(), new float[]{3566, 3576, 1678, 1688});
        templates.put(PdfSignatureTemplate.A1.getKey(), new float[]{2378, 2388, 1678, 1688});
        templates.put(PdfSignatureTemplate.A1x3.getKey(), new float[]{5046, 5056, 2378, 2388});
        templates.put(PdfSignatureTemplate.A1x4.getKey(), new float[]{6735, 6745, 2378, 2388});
        templates.put(PdfSignatureTemplate.A0.getKey(), new float[]{3365, 3375, 2378, 2388});
        templates.put(PdfSignatureTemplate.A0x2.getKey(), new float[]{4762, 4772, 3365, 3375});
        templates.put(PdfSignatureTemplate.A0x3.getKey(), new float[]{7146, 7156, 3365, 3375});
        templates.put(PdfSignatureTemplate.ECAD_A2.getKey(), new float[]{1186, 1196, 1679, 1689});
//        templates.put(PdfSignatureTemplate.ECAD_A3.getKey(), new float[]{837, 847, 1186, 1196});
        templates.put(PdfSignatureTemplate.ECAD_A3.getKey(), new float[]{837, 847, 590, 600});

        // 遍历模板并检查尺寸匹配
        for (Map.Entry<String, float[]> entry : templates.entrySet()) {
            float[] dims = entry.getValue();
            if (width > dims[0] && width < dims[1] && height > dims[2] && height < dims[3]) {
                pdfTemplateCode = entry.getKey();
                if (pdfTemplateCode.startsWith("ecad")) {
                    //0911-调试，暂时去掉revert,反转 x,y轴
//                    revert = Boolean.TRUE;
                }
                break;
            }
        }

        File templateFile = pdfTemplateCode == null ? null : customerDocHelper.getSignTemplateFile(pdfTemplateCode, SessionHelper.getCurrentUser().getTenantOid());
        PdfReader pdfReader = templateFile == null ? null : new PdfReader(customerDocHelper.transformResponseInput2Bytes(templateFile.getOid()));
        return new SignInfo(pdfReader == null ? null : new PdfReaderContentParser(pdfReader), pdfReader, revert, pdfTemplateCode);
    }


    public Rectangle rectangle(String signName, Rectangle2D.Float boundingRectange, TextRenderInfo textRenderInfo, Boolean calcu, SignInfo signInfo){
        float leftX = (float)boundingRectange.getMinX();
        float leftY = (float)boundingRectange.getMinY();// - 1.0F;
        Rectangle2D.Float rectAscen = textRenderInfo.getAscentLine().getBoundingRectange();
        float rightX = (float)rectAscen.getMaxX();
        float rightY = (float)rectAscen.getMaxY() ;//+ 1.0F;
        float H = rightY - leftY;
        float textLen = textRenderInfo.getText().length(), decreteXLen = ((rightX-leftX)*10/((textLen))), decreteYLen = ((rightX-leftX)*10/((textLen)));
        float x = signInfo.getRevert() ? boundingRectange.y : boundingRectange.x, y = signInfo.getRevert() ? signInfo.getPdfReader().getPageSize(1).getWidth() - boundingRectange.x : boundingRectange.y;
        Rectangle rectangle2;
        if(textRenderInfo.getText().contains("日期")){
            if(calcu)
//                rectangle2 = new Rectangle(boundingRectange.x+((rightX-leftX)/(textLen)), boundingRectange.y-H/4, boundingRectange.x + (rightX-leftX)*(signName.length() - 4)/(textLen), boundingRectange.y + H);
//                rectangle2 = new Rectangle(boundingRectange.x+((rightX-leftX)/(textLen)), leftY, boundingRectange.x + (rightX-leftX)*(signName.length() - 4)/(textLen) - decreteXLen, boundingRectange.y + H - decreteYLen);
                rectangle2 = new Rectangle(x+((rightX-leftX)/(textLen)), leftY- H/4, x + (rightX-leftX)*(signName.length() - 4)/(textLen), y + H);
            else
                rectangle2 = new Rectangle(boundingRectange.x+20.0F, boundingRectange.y - 2.0F, boundingRectange.x + 70.0F, boundingRectange.y + H);
        }else{
            if(calcu)
//                rectangle2 = new Rectangle(boundingRectange.x+((rightX-leftX)/(textLen)), boundingRectange.y-H/4, boundingRectange.x + (rightX-leftX)*(signName.length())/(textLen), boundingRectange.y + H);
//                rectangle2 = new Rectangle(boundingRectange.x+((rightX-leftX)/(textLen)), leftY, boundingRectange.x + (rightX-leftX)*(signName.length())/(textLen) - decreteXLen, boundingRectange.y + H - decreteYLen);
                rectangle2 = new Rectangle(x+((rightX-leftX)/(textLen)), leftY - H/4, x + (rightX-leftX)*(signName.length())/(textLen), y + H);
            else
                rectangle2 = new Rectangle(boundingRectange.x+10.0F, boundingRectange.y - 2.0F, boundingRectange.x + 50.0F, boundingRectange.y + H);
        }

        return rectangle2;
    }

}
