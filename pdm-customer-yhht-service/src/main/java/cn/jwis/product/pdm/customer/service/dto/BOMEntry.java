package cn.jwis.product.pdm.customer.service.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode
public class BOMEntry {

    private String submitter; // 提交者
    private String departmentName; // 所属部门
    private String submitTime; // 提交时间
    private String bomMaterialNumber; // BOM 料号
    private String bomConvertedCode; // BOM 转换后编码
    private String bomTianTongJingDianCode; // BOM 天通精电编码
    private String bomProductName; // BOM 产品名称
    private String bomProductSpecification; // BOM 产品规格
    private String bomManufacturer; // BOM 生产厂家
    private String bomPackageForm; // BOM 封装形式
    private String bomQualityGrade; // BOM 质量等级
    private String bomUnit; // BOM 单位
    private String bomVersion; // BOM 版本

    private String detailSequenceNumber; // 明细序号
    private String detailMaterialCode; // 明细物料编码
    private String detailConvertedU9Code; // 明细转化后 U9 编码
    private String detailTianTongJingDianCode; // 明细天通精电编码
    private String detailProductName; // 明细产品名称
    private String detailProductSpecification; // 明细产品规格
    private String detailUnit; // 明细单位
    private String detailQuantity; // 明细数量
    private String detailInstallationPosition; // 明细安装位号0
    private String detailInstallationPosition1; // 明细安装位号1
    private String detailInstallationPosition2; // 明细安装位号2
    private String detailInstallationPosition3; // 明细安装位号3
    private String detailInstallationPosition4; // 明细安装位号4
    private String detailInstallationPosition5; // 明细安装位号5
    private String detailInstallationPosition6; // 明细安装位号6
    private String detailInstallationPosition7; // 明细安装位号7
    private String detailInstallationPosition8; // 明细安装位号8
    private String detailInstallationPosition9; // 明细安装位号9
    private String detailDescription; // 明细描述
    private String detailManufacturer; // 明细生产厂家
    private String detailPackageForm; // 明细封装形式
    private String detailQualityGrade; // 明细质量等级
    private String detailMslLevel; // 明细 MSL（湿敏等级）
}