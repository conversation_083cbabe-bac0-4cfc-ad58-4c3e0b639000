package cn.jwis.product.pdm.customer.service.impl;

import cn.jwis.audit.annotation.JWIParam;
import cn.jwis.audit.annotation.JWIServiceAudit;
import cn.jwis.audit.enums.Action;
import cn.jwis.framework.base.domain.able.info.ModelInfo;
import cn.jwis.framework.base.exception.JWIException;
import cn.jwis.framework.base.util.Assert;
import cn.jwis.platform.plm.dataengine.common.JWICommonService;
import cn.jwis.product.pdm.change.dto.ChangeInfoDTO;
import cn.jwis.product.pdm.change.dto.ECRCreateDTO;
import cn.jwis.product.pdm.change.dto.UpdateChangeInfoDTO;
import cn.jwis.product.pdm.change.entity.ECR;
import cn.jwis.product.pdm.change.response.ChangeInfo;
import cn.jwis.product.pdm.change.service.ChangeHelperImpl;
import cn.jwis.product.pdm.change.service.ChangeService;
import cn.jwis.product.pdm.document.entity.DocumentIteration;
import com.google.common.collect.Lists;
import lombok.SneakyThrows;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR> yefei
 * @Date ： 2024/3/21
 * @Description :
 */

@Component
@Primary
public class CustomerEcrChangeHelperImpl extends ChangeHelperImpl {

    @Resource
    ChangeService changeService;

    @Resource
    JWICommonService commonService;

    @Resource
    TriggerAuditServiceImpl triggerAuditService;

    @SneakyThrows
    @Transactional(rollbackFor = Exception.class)
    @JWIServiceAudit(buzOid = "${result.oid}",
            bizNumber = "${result.number}",
            bizName = "${result.name}",
            buzType = "${result.type}",
            action = Action.ADD,content = "创建变更申请单,名称${result.name}")
    @JWIParam("result")
    @Override
    public ECR create(ECRCreateDTO ecrCreateDTO) {
        Set<String> typeSet =
                ecrCreateDTO.getChangeList().stream().map(ChangeInfoDTO::getType).collect(Collectors.toSet());
        if(typeSet.size() > 1 && typeSet.contains(DocumentIteration.TYPE)) {
            throw new JWIException("文档变更不允许与其他对象一起进行");
        }
        return super.create(ecrCreateDTO);
    }

    @Override
    public void updateChangeInfo(UpdateChangeInfoDTO updateChangeInfoDTO) {
        ECR ecr = commonService.findByOid(ECR.TYPE,updateChangeInfoDTO.getEcrOid(),ECR.class);
        Assert.notNull(ecr,"变更单不存在");
        List<ChangeInfo> list = this.changeService.findChangeInfo(updateChangeInfoDTO.getEcrOid(),
                Lists.newArrayList());

        Set<String> addTypeSet =
                updateChangeInfoDTO.getAddList().stream().map(ModelInfo::getType).collect(Collectors.toSet());
        addTypeSet.addAll(list.stream().map(ChangeInfo::getType).collect(Collectors.toSet()));

        if(addTypeSet.size() > 1 && addTypeSet.contains(DocumentIteration.TYPE)) {
            throw new JWIException("文档变更不允许与其他对象一起进行");
        }
        super.updateChangeInfo(updateChangeInfoDTO);

        for (ModelInfo changeInfo : updateChangeInfoDTO.getAddList()) {
            triggerAuditService.addChangeInfo(ecr,changeInfo);
        }

        for (ModelInfo changeInfo : updateChangeInfoDTO.getDeleteList()) {
            triggerAuditService.deleteChangeInfo(ecr,changeInfo);
        }
    }
}
