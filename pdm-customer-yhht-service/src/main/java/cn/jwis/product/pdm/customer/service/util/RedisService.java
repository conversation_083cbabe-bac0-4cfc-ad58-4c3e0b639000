package cn.jwis.product.pdm.customer.service.util;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.Cursor;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ScanOptions;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Component
public class RedisService {

    @Autowired
    private  RedisTemplate redisTemplate;

    /**
     * 获取 Redis 中的键对应的值，如果不存在返回 null
     * @param key Redis 键
     * @return 键对应的值
     */
    public Object get(String key) {
        return redisTemplate.opsForValue().get(key);
    }

    /**
     * 设置 Redis 键值对并设置过期时间（单位：秒）
     * @param key Redis 键
     * @param value 值
     * @param timeout 过期时间（秒）
     */
    public void set(String key, Object value, long timeout) {
        redisTemplate.opsForValue().set(key, value, timeout, TimeUnit.SECONDS);
    }

    /**
     * 设置 Redis 键值对并设置到期时间点
     * @param key Redis 键
     * @param value 值
     * @param expireAt 过期时间点
     */
    public void setWithExpireAt(String key, Object value, Date expireAt) {
        redisTemplate.opsForValue().set(key, value);
        redisTemplate.expireAt(key, expireAt);
    }

    /**
     * 增加值并返回增加后的结果
     * @param key Redis 键
     * @param increment 增加的数量
     * @return 增加后的值
     */
    public Long increment(String key, long increment) {
        return redisTemplate.opsForValue().increment(key, increment);
    }

    /**
     * 删除 Redis 键
     * @param key Redis 键
     */
    public void delete(String key) {
        redisTemplate.delete(key);
    }

    /**
     * 获取键的过期时间
     * @param key Redis 键
     * @return 剩余时间（秒），如果永久有效返回 -1，如果不存在返回 -2
     */
    public Long getExpire(String key) {
        return redisTemplate.getExpire(key, TimeUnit.SECONDS);
    }

    /**
     * 设置键的过期时间（单位：秒）
     * @param key Redis 键
     * @param timeout 过期时间（秒）
     */
    public void expire(String key, long timeout) {
        redisTemplate.expire(key, timeout, TimeUnit.SECONDS);
    }

    /**
     * 设置键的过期时间到某个时间点
     * @param key Redis 键
     * @param expireAt 到期时间点
     */
    public void expireAt(String key, Date expireAt) {
        redisTemplate.expireAt(key, expireAt);
    }

    /**
     * 获取当前日期结束时间的 Date 对象
     * @return 当天 23:59:59 的时间点
     */
    public Date getEndOfToday() {
        LocalDateTime endOfDay = LocalDate.now().plusDays(1).atStartOfDay();
        return Date.from(endOfDay.atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * 使用 Scan 方式查询符合条件的 Redis 键
     * @param pattern 键的匹配模式，例如 "user:*"
     * @return 符合条件的键列表
     */
    public List<String> scan(String pattern) {
        List<String> keys = new ArrayList<>();
        ScanOptions options = ScanOptions.scanOptions().match(pattern).count(1000).build();
        try (Cursor<byte[]> cursor = redisTemplate.getConnectionFactory()
                .getConnection()
                .scan(options)) {
            while (cursor.hasNext()) {
                keys.add(new String(cursor.next()));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return keys;
    }
}