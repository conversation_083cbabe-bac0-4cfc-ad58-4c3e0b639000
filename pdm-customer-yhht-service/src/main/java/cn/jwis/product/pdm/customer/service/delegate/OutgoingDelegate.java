package cn.jwis.product.pdm.customer.service.delegate;

import cn.jwis.framework.base.dto.UserDTO;
import cn.jwis.framework.base.util.MapUtil;
import cn.jwis.framework.base.util.SpringContextUtil;
import cn.jwis.framework.base.web.session.SessionHelper;
import cn.jwis.platform.plm.account.user.service.UserHelper;
import cn.jwis.platform.plm.foundation.common.response.InstanceEntity;
import cn.jwis.platform.plm.workflow.engine.order.entity.ProcessOrder;
import cn.jwis.platform.plm.workflow.engine.service.interf.ProcessOrderHelper;
import cn.jwis.product.pdm.customer.repo.CustomerCommonRepo;
import cn.jwis.product.pdm.customer.service.impl.DingTalkServiceImpl;
import cn.jwis.product.pdm.customer.service.interf.DingTalkService;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.RuntimeService;
import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.JavaDelegate;
import org.activiti.engine.impl.persistence.entity.VariableInstance;

import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 工作流执行代理
 * @date 2023/9/11 11:44
 * @Email <EMAIL>
 */
@Slf4j
public class OutgoingDelegate implements JavaDelegate {

    private static ExecutorService executors = Executors.newCachedThreadPool();

    @Override
    public void execute(DelegateExecution execution) {
        String processInstanceId = execution.getProcessInstanceId();
        log.info("OutgoingDelegate execute procId={}", processInstanceId);
        RuntimeService runtimeService = SpringContextUtil.getBean(RuntimeService.class);
        initContext(runtimeService,execution.getId());
        try {
            // 获取原通知人 外发就是后置抄送人
            Object ccNotifiers = Optional.ofNullable(runtimeService.getVariable(execution.getId(),"cn_jwis_notifier")).orElse(Collections.emptyList());
            Object fileRecievers = Optional.ofNullable(runtimeService.getVariable(execution.getId(),"cn_jwis_outgoing_fileReciever")).orElse(Collections.emptyList());
            Collection<String> ccAccounts = ccNotifiers instanceof Collection ? (Collection<String>) ccNotifiers : Arrays.asList(ccNotifiers.toString());
            Collection<String> fileRecieverAccounts = fileRecievers instanceof Collection ? (Collection<String>) fileRecievers : Arrays.asList(fileRecievers.toString());
            log.info("OutgoingDelegate origin ccAccounts={}, fileRecieverAccounts={}", JSON.toJSONString(ccAccounts), JSON.toJSONString(fileRecieverAccounts));

            ProcessOrder processOrder = getProcessOrder(execution);
            CustomerCommonRepo customerCommonRepo = (CustomerCommonRepo) SpringContextUtil.getBean("customerCommonNeo4jRepoImpl");
            List<InstanceEntity> instanceEntityList = customerCommonRepo.queryReviewsDetail(processOrder.getOid());

            // 发布通知
            DingTalkService dingTalkService = (DingTalkServiceImpl) SpringContextUtil.getBean("dingTalkServiceImpl");
            dingTalkService.createNoticeTask(ccAccounts,processOrder,instanceEntityList);

            String accessToken = SessionHelper.getAccessToken();
            UserDTO currUser = SessionHelper.getCurrentUser();

            executors.submit(() -> {
                SessionHelper.setAccessToken(accessToken);
                SessionHelper.addCurrentUser(currUser);
                dingTalkService.createOutgoingNoticeTask(fileRecieverAccounts, processOrder, instanceEntityList);
            });

//            throw new RuntimeException("123");
        } catch (Exception e) {
            log.error("OutgoingDelegate execute error==>", e);
        }
    }

    private void initContext(RuntimeService runtimeService, String id) {
        Object owner = runtimeService.getVariable(id,"owner");
        UserHelper userHelper = (UserHelper)SpringContextUtil.getBean("defaultUserHelperImpl");
        UserDTO user = userHelper.findByAccount(owner.toString());
        Object tenantOid = runtimeService.getVariable(id,"tenantOid");
        user.setAccount(owner.toString());
        user.setOid(owner.toString());
        user.setTenantOid(tenantOid.toString());
        SessionHelper.addCurrentUser(user);
    }

    private ProcessOrder getProcessOrder(DelegateExecution delegateTask) {
        ProcessOrder processOrder = null;
        Map<String, VariableInstance> variableInstances = delegateTask.getVariableInstances();
        ProcessOrderHelper processOrderHelper = (ProcessOrderHelper) SpringContextUtil.getBean("processOrderHelperImpl");
        if (MapUtil.isEmpty(variableInstances)){
            String processInstanceId = delegateTask.getProcessInstanceId();
            processOrder = processOrderHelper.findByProcessInstanceId(processInstanceId);
        }else {
            VariableInstance processOrderOid = variableInstances.get("processOrderOid");
            if (processOrderOid == null){
                String processInstanceId = delegateTask.getProcessInstanceId();
                processOrder = processOrderHelper.findByProcessInstanceId(processInstanceId);
            }else {
                String processOrderOidStr = processOrderOid.getValue().toString();
                processOrder = processOrderHelper.findByOid(processOrderOidStr);
            }
        }
        return processOrder;
    }

}
