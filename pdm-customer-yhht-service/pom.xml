<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>pdm-customer-yhht-server</artifactId>
        <groupId>cn.jwis.product.pdm</groupId>
        <version>1.0.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>pdm-customer-yhht-service</artifactId>
    <dependencies>

        <dependency>
            <groupId>cn.jwis.product.pdm</groupId>
            <artifactId>partbom-helper</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.jwis.product.pdm</groupId>
            <artifactId>doc-mgmt-helper</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.jwis.platform.plm</groupId>
            <artifactId>foundation-helper</artifactId>
            <version>${jwi.product.pdm.foundation.version}</version>
        </dependency>
        <dependency>
            <groupId>cn.jwis.platform.plm</groupId>
            <artifactId>data-distribution-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.jwis.product.pdm</groupId>
            <artifactId>pdm-customer-yhht-repo</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.jwis.product.pdm</groupId>
            <artifactId>pdm-customer-yhht-repo-neo4j</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.jwis.product.pdm</groupId>
            <artifactId>pdm-customer-yhht-remote</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.jwis.platform.plm</groupId>
            <artifactId>container-helper</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.jwis.product.pdm</groupId>
            <artifactId>pdm-customer-yhht-remote-service</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.jwis.platform.plm</groupId>
            <artifactId>workflow-engine-helper</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.jwis.product.pdm</groupId>
            <artifactId>change-helper</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.jwis.product.pdm</groupId>
            <artifactId>change-service</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.jwis.product.pdm</groupId>
            <artifactId>baseline-helper</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.jwis.platform.plm</groupId>
            <artifactId>sysconfig-helper</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.jwis.platform.plm</groupId>
            <artifactId>search-engine-helper</artifactId>
<!--            <version>3.5.0</version>-->
        </dependency>

        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>dingtalk</artifactId>
<!--            <version>2.0.14</version>-->
            <version>2.1.29</version>
        </dependency>
        <dependency>
            <groupId>com.dingtalk.open</groupId>
            <artifactId>app-stream-client</artifactId>
            <version>1.0.5</version>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.jwis.product.pdm</groupId>
            <artifactId>product-delivery-helper</artifactId>
            <version>3.5.0</version>
            <scope>compile</scope>
        </dependency>

        <!-- sign template  文档签名服务-->
        <dependency>
            <groupId>cn.jwis.platform.plm</groupId>
            <artifactId>sign-template-web</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.jwis.platform.plm</groupId>
            <artifactId>sign-template-repo-neo4j</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.jwis.platform.plm</groupId>
            <artifactId>sign-template-remote-service</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.jwis.product.pdm</groupId>
            <artifactId>cad-helper</artifactId>
        </dependency>

        <dependency>
            <groupId>com.aspose</groupId>
            <artifactId>aspose-words-jdk17</artifactId>
            <version>22.5.0</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-amqp</artifactId>
        </dependency>
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>alibaba-dingtalk-service-sdk</artifactId>
            <version>2.0.0</version>
        </dependency>

        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-api</artifactId>
            <version>0.11.5</version>
        </dependency>

        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-impl</artifactId>
            <version>0.11.5</version>
        </dependency>

        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-jackson</artifactId>
            <version>0.11.5</version>
        </dependency>

        <!--easyExcel-->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>3.2.1</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>2.0.0</version>
            <scope>compile</scope>
        </dependency>

    </dependencies>

</project>
