<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>pdm-customer-yhht-server</artifactId>
        <groupId>cn.jwis.product.pdm</groupId>
        <version>1.0.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>pdm-customer-yhht-repo-neo4j</artifactId>
    <dependencies>
        <dependency>
            <groupId>cn.jwis.framework</groupId>
            <artifactId>neo4j-persistence-orm</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.jwis.product.pdm</groupId>
            <artifactId>pdm-customer-yhht-repo</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.jwis.product.pdm</groupId>
            <artifactId>doc-mgmt-entity</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.jwis.product.pdm</groupId>
            <artifactId>product-delivery-entity</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.jwis.platform.plm</groupId>
            <artifactId>search-engine-repo-neo4j</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.jwis.platform.plm</groupId>
            <artifactId>foundation-helper</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.jwis.product.pdm</groupId>
            <artifactId>container-entity</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.jwis.product.pdm</groupId>
            <artifactId>container-repo</artifactId>
        </dependency>

    </dependencies>

</project>