package cn.jwis.product.pdm.customer.repo.neo4j.util;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import okhttp3.*;
import okhttp3.Request.Builder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.Iterator;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * OKHttpUtils 工具类
 */
public class OKHttpUtils {
    public static final Logger LOGGER          = LoggerFactory.getLogger(OKHttpUtils.class);

    public static final int       CONNECT_TIMEOUT = 30;
    public static final int       READ_TIMEOUT    = 30;
    public static final int       WRITE_TIMEOUT   = 30;
    public static final MediaType JSON_TYPE       = MediaType.parse("application/json; charset=utf-8");
    public static final MediaType URLENCODE_TYPE       = MediaType.parse("application/x-www-form-urlencoded");

    private OKHttpUtils(){}

    /**
     * get请求
     * @param url 接口
     * @param header 请求头
     * @return 结果
     */
    public static String sendGet(String url, Map<String, String> header) {
        OkHttpClient client = getConnClient();

        Request request = null;
        Builder builder2 = new Builder().url(url).get();
        if(null != header){
            Set<String> keySet = header.keySet();
            Iterator<String> iterator = keySet.iterator();
            while(iterator.hasNext()){
                String key = iterator.next();
                String value = header.get(key);
                builder2.addHeader(key, value);
            }
        }
        request = builder2.build();
        try {
            Response response = client.newCall(request).execute();
            if (response.isSuccessful()) {
                return response.body().string();
            } else {
                LOGGER.info("请求响应异常3 {}", response.toString());
                return null;
            }
        } catch (IOException e) {
            LOGGER.error("get请求出现异常 {}",e);
        }
        return null;
    }

    /**
     * post请求
     * @param url 接口
     * @param paramMap 参数
     * @return 结果
     */
    public static String sendPost(String url, Map<String, String> paramMap) {
        return sendPost(url, paramMap, null);
    }

    /**
     * 请求
     * @param url 接口
     * @return 结果
     */
    public static String sendPostJson(String url, Map<String,String> headerMap, Map<String, String> paramMap) {
        OkHttpClient client = getConnClient();
        String paramJson = JSON.toJSONString(paramMap);
        RequestBody body = RequestBody.create(JSON_TYPE, paramJson);
        Builder builder = new Builder();
        for (Entry<String, String> entry : headerMap.entrySet()) {
            builder.addHeader(entry.getKey(),entry.getValue());
        }
        Request request = builder.url(url).post(body).build();
        try {
            Response response = client.newCall(request).execute();
            if (response.isSuccessful()) {
                ResponseBody responseBody = response.body();
                return responseBody.string();
            } else {
                LOGGER.info("请求响应异常1 {}", response.toString());
            }
        } catch (Exception ex) {
            LOGGER.error("请求出现异常 {}",ex);
        }
        return null;
    }
    /**
     * post请求
     * @param url 接口
     * @param paramMap 参数
     * @param header 请求头
     * @return 结果
     */
    public static String sendPost(String url, Map<String, String> paramMap, Map<String, String> header) {
        OkHttpClient client = getConnClient();
        FormBody.Builder builder = new FormBody.Builder();
        if (paramMap != null) {
            String paramName = null;
            String paramValue = null;
            for (Entry<String, String> entry : paramMap.entrySet()) {
                paramName = entry.getKey();
                paramValue = entry.getValue();
                builder.add(paramName, paramValue);
            }
        }
        String headerName = "user-agent";
        String headeValue = "nodata";
        if (header != null) {
            for (Entry<String, String> entry : header.entrySet()) {
                headerName = entry.getKey();
                headeValue = entry.getValue();
            }
        }
        FormBody body = builder.build();
        Request request = new Builder().url(url).addHeader(headerName, headeValue).post(body).build();
        try {
            Response response = client.newCall(request).execute();
            if (response.isSuccessful()) {
                return response.body().string();
            } else {
                LOGGER.info("请求响应异常{}", response.toString());
                return "服务异常";
            }
        } catch (IOException e) {
            LOGGER.error("post请求出现异常 {}",e);
        }
        return null;
    }

    /**
     * 请求
     * @param url 接口
     * @param e 枚举
     * @param t t
     * @param <T> 泛型
     * @param <E> 枚举
     * @return 结果
     */
    public static <T, E> T sendPostJson(String url, E e, Class<T> t) {
        OkHttpClient client = getConnClient();
        T object = null;
        String paramJson = JSON.toJSONString(e);
        RequestBody body = RequestBody.create(JSON_TYPE, paramJson);
        Request request = new Builder().url(url).post(body).build();
        try {
            Response response = client.newCall(request).execute();
            if (response.isSuccessful()) {
                ResponseBody responseBody = response.body();
                String string = responseBody.string();
                JSONObject parseObject = JSON.parseObject(string);
                object = JSON.toJavaObject(parseObject, t);
                return object;
            } else {
                LOGGER.info("请求响应异常1 {}", response.toString());
            }
        } catch (Exception ex) {
            LOGGER.error("请求出现异常 {}",ex);
        }
        return object;
    }

    /**
     * 请求
     * @param url 接口
     * @param t 对象参数
     * @param <T> 泛型
     * @return 结果
     */
    public static <T> String sendPostJson(String url, T t,boolean isRetry) {
        OkHttpClient client = getConnClient();
        String paramJson = JSON.toJSONString(t);
        RequestBody body = RequestBody.create(JSON_TYPE, paramJson);
        Request request = new Builder().url(url).post(body).build();
        try {
            return executeCall(client,request);
        } catch (Exception ex) {
            if(isRetry){
                try {
                    return executeCall(client,request);
                } catch (Exception e) {
                    addExpectionLog("sendPostJson再次请求异常", ex);
                }
            }else {
                addExpectionLog("sendPostJson请求异常", ex);
            }
            return exceptionJson();
        }
    }

    private static String exceptionJson(){
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("code", 500);
        jsonObject.put("message", "Request exception, please try again later");
        return jsonObject.toString();
    }

    private static String executeCall(OkHttpClient client,Request request) throws Exception {
        Response response = client.newCall(request).execute();
        if (response.isSuccessful()) {
            ResponseBody responseBody = response.body();
            return responseBody.string();
        } else {
            LOGGER.info("请求响应异常了{}", response.toString());
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("code", 500);
            jsonObject.put("message", "Request exception, please try again later");
            return jsonObject.toString();
        }
    }

    private static void addExpectionLog(String exceptionDesc, Exception e) {
        StringWriter sw = new StringWriter();
        e.printStackTrace(new PrintWriter(sw, true));
        LOGGER.error(exceptionDesc + "--->" + sw.toString());
    }

    public static OkHttpClient getConnClient() {
        return getClient();
    }

    private static final class InnerOKHttp {
        private static OkHttpClient client = new OkHttpClient.Builder()
                .connectionPool(new ConnectionPool(100,5,TimeUnit.MILLISECONDS)).readTimeout(READ_TIMEOUT, TimeUnit.SECONDS)
                .writeTimeout(WRITE_TIMEOUT, TimeUnit.SECONDS).connectTimeout(CONNECT_TIMEOUT, TimeUnit.SECONDS).build();
        private InnerOKHttp(){}
    }

    public static OkHttpClient getClient() {
        return InnerOKHttp.client;
    }

    /**
     * 请求 json
     * @param url 接口
     * @return 结果
     */
    public static String sendPostJson(String url, Map<String,String> headerMap, String paramJson) {
        OkHttpClient client = getConnClient();
        RequestBody body = RequestBody.create(JSON_TYPE, paramJson);
        Builder builder = new Builder();
        for (Entry<String, String> entry : headerMap.entrySet()) {
            builder.addHeader(entry.getKey(),entry.getValue());
        }
        Request request = builder.url(url).post(body).build();
        try {
            Response response = client.newCall(request).execute();
            if (response.isSuccessful()) {
                ResponseBody responseBody = response.body();
                return responseBody.string();
            } else {
                LOGGER.info("请求响应异常1 {}", response.toString());
            }
        } catch (Exception ex) {
            LOGGER.error("请求出现异常 {}",ex);
        }
        return null;
    }

    /**
     * 请求  表单
     * @param url 接口
     * @return 结果
     */
/*    public static String sendPostForm(String url, Map<String, String> paramMap, List<DatacenterIdsArchiveApplyFileReq> files) {
        OkHttpClient client = getConnClient();
        // 创建RequestBody对象，用于构建Multipart请求
        MultipartBody.Builder builder = new MultipartBody.Builder();
        builder.setType(MultipartBody.FORM);
        for(DatacenterIdsArchiveApplyFileReq file:files){
            builder.addFormDataPart("file", file.getFileName(), RequestBody.create(file.getBytes(), MediaType.parse("application/octet-stream")));
        }
        for (Entry<String, String> entry : paramMap.entrySet()) {
            builder.addFormDataPart(entry.getKey(),entry.getValue());
        }

        RequestBody requestBody = builder.build();
        Request request = new Builder()
                .url(url)
                .post(requestBody)
                .build();
        try {
            Response response = client.newCall(request).execute();
            if (response.isSuccessful()) {
                ResponseBody responseBody = response.body();
                return responseBody.string();
            } else {
                LOGGER.info("请求响应异常1 {}", response.toString());
            }
        } catch (Exception ex) {
            LOGGER.error("请求出现异常 {}",ex);
        }
        return null;
    }*/

}