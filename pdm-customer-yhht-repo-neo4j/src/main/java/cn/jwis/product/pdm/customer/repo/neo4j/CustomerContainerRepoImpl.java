package cn.jwis.product.pdm.customer.repo.neo4j;

import cn.jwis.framework.database.neo4j.condition.Neo4jEnableConditional;
import cn.jwis.framework.database.neo4j.execution.ExecutionNeo4jRepository;
import cn.jwis.platform.plm.container.entity.Container;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.neo4j.driver.Record;
import org.neo4j.driver.Result;
import org.springframework.context.annotation.Conditional;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.Map;


@Slf4j
@Transactional
@Component
@Conditional(Neo4jEnableConditional.class)
public class CustomerContainerRepoImpl extends ExecutionNeo4jRepository<Container> {

    public Container findContainerByProjectCode(String projectCode) {
        Map<String, Object> params = new HashMap<>();
        params.put("projectCode", projectCode);

        String cypher = "MATCH (c:Container)-[:ATTRIBUTE]->(p:PropertyGroup) " +
                "WHERE p.cn_pms_project_number = $projectCode " +
                "RETURN c.oid as oid, p as props";

        Result result = this.run(cypher, params);
        Container container = null;
        if (result.hasNext()) {
            Record record = result.next();
            container = new Container(); //
            container.setOid(record.get("oid").asString());
            // 把PropertyGroup转成JSONObject
            Map<String, Object> propsMap = record.get("props").asMap();
            container.setExtensionContent(new JSONObject(propsMap));

        }
        return container;
    }

}