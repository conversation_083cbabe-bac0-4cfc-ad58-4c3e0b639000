//package cn.jwis.product.pdm.customer.repo.neo4j;
//
//import cn.jwis.framework.base.domain.able.TreeAble;
//import cn.jwis.framework.base.dto.UserDTO;
//import cn.jwis.framework.base.exception.JWIException;
//import cn.jwis.framework.base.util.Assert;
//import cn.jwis.framework.base.util.CollectionUtil;
//import cn.jwis.framework.base.util.MapUtil;
//import cn.jwis.framework.base.util.StringUtil;
//import cn.jwis.framework.base.web.session.SessionHelper;
//import cn.jwis.framework.database.neo4j.condition.Neo4jEnableConditional;
//import cn.jwis.framework.database.neo4j.execution.CommonEntityTemplate;
//import cn.jwis.framework.database.neo4j.util.JSONUtil;
//import cn.jwis.platform.plm.container.entity.Container;
//import cn.jwis.platform.plm.container.entity.ProductCatalog;
//import cn.jwis.platform.plm.foundation.relationship.Attribute;
//import cn.jwis.platform.plm.foundation.relationship.Contain;
//import cn.jwis.platform.plm.foundation.relationship.Reference;
//import cn.jwis.product.pdm.customer.repo.DeliveryServiceRepo;
//import cn.jwis.product.pdm.delivery.entity.Delivery;
//import cn.jwis.product.pdm.delivery.entity.DeliveryTreeNode;
//import org.apache.commons.lang3.StringUtils;
//import org.neo4j.driver.Record;
//import org.neo4j.driver.Result;
//import org.neo4j.driver.Value;
//import org.neo4j.driver.types.Node;
//import org.springframework.context.annotation.Conditional;
//import org.springframework.context.annotation.Primary;
//import org.springframework.stereotype.Component;
//import org.springframework.transaction.annotation.Transactional;
//import org.springframework.util.CollectionUtils;
//
//import java.util.*;
//import java.util.stream.Collectors;
//
//@Component
//@Conditional(Neo4jEnableConditional.class)
//@Transactional
//@Primary
//public class CustomerDeliveryServiceRepoImpl extends CommonEntityTemplate<Delivery> implements DeliveryServiceRepo {
//    @Override
//    public Long delete(String oid, String level) {
//        return deleteByOid(Arrays.asList(oid), level);
//    }
//
//    @Override
//    public Long deleteByOid(List<String> oids, String level) throws JWIException {
//        if(StringUtils.isNotEmpty(level)) {
//            level = "10";
//        }
//        //删除交付清单及其分类属性和扩展属性。
//        StringBuffer cypher = new StringBuffer();
//        Map<String, Object> map = new HashMap<>(8);
//        map.put("oids", oids);
//        cypher.append(" match (s").append(entityLabelCypher).append( ")-[:CONTAIN*0..").append(level).append("]->(d:Delivery) where s.oid in $oids with d ")
//                .append(" optional match (d)-[:").append(Attribute.TYPE).append("]->(f) ")
//                .append(" detach Delete d,f " )
//                .append(" return count(d) as cnt ");
//        return excuteCount(cypher.toString(), map, "cnt");
//    }
//
//    @Override
//    public List<DeliveryTreeNode> findByProductCatalogOid(String productCatalogOid) {
//        //型谱节点关联交付清单模板，不需要下面的实例。
//        StringBuffer cypher = new StringBuffer();
//        Map<String, Object> map = new HashMap<>(8);
//        UserDTO currentUser = SessionHelper.getCurrentUser();
//        map.put("tenantOid", currentUser.getTenantOid());
//        map.put("userOid", currentUser.getOid());
//        map.put("productCatalogOid", productCatalogOid);
//        cypher.append("match (s").append(dealLabel(Delivery.TYPE)).append(") <- [:CONTAIN] - (m").append(dealLabel(ProductCatalog.TYPE)).append(") where m.oid=$productCatalogOid ")
//                .append("and m.tenantOid=$tenantOid and s.root = true ");
//        cypher.append(" with s Match path=(s)-[r").append(dealLabel(Contain.TYPE)).append("*0..]->(i").append(dealLabel(Delivery.TYPE)).append(")")
//                .append(" with nodes(path) as nodes, i optional  match (i)-[:REFERENCE]->(obj) ")
//                .append(" where obj.latest = true and ( obj.lockOwnerOid is null or (obj.lockOwnerOid=$userOid and obj.lockSourceOid is not null)")
//                .append(" or (obj.lockOwerOid<>$userOid and obj.lockSourceOid is null ))");
//        cypher.append(" return nodes, obj");
//        Result run = run(cypher.toString(), map);
//        return nodes2Tree(run, DeliveryTreeNode.class);
//    }
//
//    @Override
//    public List<DeliveryTreeNode> findStructureTree(String catalogType, String catalogOid, Integer level,  String nodeSearch, String objectSearch, boolean hasObject) {
//        //容器节点关联交付清单模板，需要下面的实例，所以深度查询之后仍需要往下查一层实例
//        Assert.notBlank(catalogOid, "catalogOid can not be null");
//        if (level != null && level < 1) {
//            level = 1;
//        }
//        StringBuffer cypher = new StringBuffer();
//        Map<String, Object> map = new HashMap(8);
//        UserDTO currentUser = SessionHelper.getCurrentUser();
//        map.put("tenantOid", currentUser.getTenantOid());
//        map.put("userOid", currentUser.getOid());
//        map.put("containerOid", catalogOid);
//
//        cypher.append("call { match (s").append(dealLabel(Delivery.TYPE)).append(") <- [:CONTAIN] - (m").append(dealLabel(catalogType)).append(") where m.oid=$containerOid ")
//                .append("and m.tenantOid=$tenantOid and s.root = true return s order by id(s) asc } ");
//        cypher.append(" with s Match path=(s)-[r").append(dealLabel(Contain.TYPE)).append("*0..").append(level == null ? "" : level).append("]->(i").append(dealLabel(Delivery.TYPE)).append(")");
//        if (StringUtils.isNotEmpty(nodeSearch)) {
//            objectSearch = nodeSearch;
//            map.put("nodeSearch", nodeSearch);
//            cypher.append("where  i.name contains $nodeSearch");
//        }
//        cypher.append(" with nodes(path) as nodes, i optional  match (i)-[:REFERENCE]->(master)-[:ITERATE]->(obj) ")
//                .append(" where obj.latest = true and ( obj.lockOwnerOid is null or (obj.lockOwnerOid=$userOid and obj.lockSourceOid is not null)")
//                .append(" or (obj.lockOwerOid<>$userOid and obj.lockSourceOid is null ))");
//        if (StringUtils.isNotEmpty(objectSearch)) {
//            map.put("objectSearch", objectSearch);
//            cypher.append(" and (obj.name contains $objectSearch or obj.number contains $objectSearch)");
//        }
//        cypher.append(" return nodes, collect(obj) as obj");
//        Result run = run(cypher.toString(), map);
//        List<DeliveryTreeNode> treeNodes = nodes2TreeCustomer(run, DeliveryTreeNode.class, hasObject);
//        if (StringUtils.isNotEmpty(nodeSearch)) {
//            if (CollectionUtils.isEmpty(treeNodes)) {
//                return findStructureTree(catalogType, catalogOid, level, null, nodeSearch, false);
//            }
//            return treeNodes;
//        } else {
//            return treeNodes;
//        }
//    }
//
//    private  <R extends TreeAble> List nodes2TreeCustomer(Result run, Class<R> clazz, boolean hasObject) {
//        Map<String, R> nodeMap = new HashMap(8);
//        Record next = null;
//        R node = null;
//        R master = null;
//        String nodeOid = null;
//        List<R> children = null;
//        List<String> rootOids = new ArrayList<>();
//        List<Object> nodes = null;
//        List<Object> objList = null;
//        List<Object> instance = null;
//        while(true) {
//            instance = new ArrayList<Object>();
//            do {
//                if (!run.hasNext()) {
//                    return (List) rootOids.stream().map((oid) -> {
//                        return (TreeAble) nodeMap.get(oid);
//                    }).collect(Collectors.toList());
//                }
//                next = run.next();
//                nodes = new ArrayList<>(next.get("nodes").asList());
//                objList = next.get("obj").asList();
//                for (int i = 0; i < objList.size(); ++i) {
//                    node = JSONUtil.parseObject(objList.get(i), clazz);
//                    if(!instance.contains(node)){
//                        instance.add(node);
//                    }
//                }
//            } while (CollectionUtil.isEmpty(nodes));
//
//            master = null;
//            if (hasObject || !CollectionUtils.isEmpty(objList)) {
//                for (int i = 0; i < nodes.size(); ++i) {
//                    //强转(TreeAble)
//                    node = JSONUtil.parseObject(nodes.get(i), clazz);
//                    nodeOid = node.getOid();
//                    node = MapUtil.getOrPutGet(nodeMap, nodeOid, node);
//                    if (master == null) {
//                        master = node;
//                        if (!rootOids.contains(node.getOid())) {
//                            rootOids.add(node.getOid());
//                            // 添加根节点的文档信息
//                            if (i == nodes.size() - 1 && instance != null && instance.size() > 0) {
//                                master.setChildren(instance);
//                            }
//                        }
//                    } else {
//                        children = master.getChildren();
//                        if (children == null) {
//                            children = new ArrayList();
//                            master.setChildren((List) children);
//                        }
//
//                        if (!((List) children).contains(node)) {
//                            ((List) children).add(node);
//                        }
//
//                        master = node;
//
//                        //如果实例不为空，且再最后一层时，当作子集合平铺上去。
//                        if (i == nodes.size() - 1 && instance != null && instance.size() > 0) {
//                            master.setChildren(instance);
//                        }
//                    }
//                }
//            }
//        }
//    }
//
//
//    @Override
//    public List<DeliveryTreeNode> findNodesByCatalogOid(String catalogType, String catalogOid, Integer level) {
//        Assert.notBlank(catalogOid, "catalogOid can not be null");
//        if (level != null &&  level < 1){
//            level = 1;
//        }
//        StringBuffer cypher = new StringBuffer();
//        Map<String, Object> map = new HashMap();
//        UserDTO currentUser = SessionHelper.getCurrentUser();
//        map.put("tenantOid", currentUser.getTenantOid());
//        map.put("userOid", currentUser.getOid());
//        map.put("catalogOid", catalogOid);
//        cypher.append("match (s").append(dealLabel(Delivery.TYPE)).append(") <- [:CONTAIN] - (m").append(dealLabel(catalogType)).append(") where m.oid=$catalogOid ")
//                .append("and m.tenantOid=$tenantOid and s.root = true");
//        cypher.append(" with s Match path=(s)-[r").append(dealLabel(Contain.TYPE)).append("*0..").append(level==null? "" : level).append("]->(i").append(dealLabel(Delivery.TYPE)).append(")");
//        cypher.append(" return nodes(path) as nodes");
//
//        Result run = run(cypher.toString(), map);
//        return nodes2Tree(run, DeliveryTreeNode.class);
//    }
//
//    @Override
//    public List<DeliveryTreeNode> findChildrenByDeliveryOid(String oid) {
//        Assert.notBlank(oid, "oid can not be null");
//        StringBuffer cypher = new StringBuffer();
//        Map<String, Object> map = new HashMap();
//        UserDTO currentUser = SessionHelper.getCurrentUser();
//        map.put("tenantOid", currentUser.getTenantOid());
//        map.put("userOid", currentUser.getOid());
//        map.put("oid", oid);
//        //optional match (s:Delivery)-[:CONTAIN|REFERENCE]->(i) where s.oid = 'bd86d50c-c773-4027-b90c-aa45c3c5a380' and s.tenantOid='346d0eb9-f6e5-4f76-8884-0f1806fb9cb4' with s,i optional match (i)-[:ITERATE]->(obj) return s,i,obj
//        cypher.append("match (s").append(dealLabel(Delivery.TYPE)).append(")-[r").append(dealLabel(Contain.TYPE)).append("|").append(Reference.TYPE).append("]->(i)");
//        cypher.append(" where s.oid = $oid and s.tenantOid=$tenantOid ")
//                .append(" with s, i optional  match (i)-[:ITERATE]->(obj)")
//                .append(" where obj.latest = true and ( obj.lockOwnerOid is null or (obj.lockOwnerOid=$userOid and obj.lockSourceOid is not null)")
//                .append(" or (obj.lockOwerOid<>$userOid and obj.lockSourceOid is null ))");
//        cypher.append(" return s,i,obj");
//        List<DeliveryTreeNode> res = new ArrayList();
//        Result result = run(cypher.toString(), map);
//        List<Record> list = result.list();
//        Iterator var7 = list.iterator();
//        while(var7.hasNext()) {
//            Record record = (Record)var7.next();
//            Node i = record.get("i").asNode();
//            DeliveryTreeNode deliveryTreeNode = JSONUtil.parseObject(i, DeliveryTreeNode.class);
//            if(StringUtil.isNotEmpty(deliveryTreeNode.getNumber())){//不返回给前端master对象，依据master没有编码判断
//                res.add(deliveryTreeNode);
//            }
//            if(!record.get("obj").isNull()){
//                Node obj = record.get("obj").asNode();
//                res.add(JSONUtil.parseObject(obj,DeliveryTreeNode.class));
//            }
//        }
//        return res;
//    }
//
//    @Override
//    public void createContainChild(String parentOid, String oid) {
//        Assert.notBlank(parentOid, "parentOid can not be null");
//        StringBuffer cypher = new StringBuffer();
//        Map<String, Object> map = new HashMap();
//        UserDTO currentUser = SessionHelper.getCurrentUser();
//        map.put("tenantOid", currentUser.getTenantOid());
//        map.put("userOid", currentUser.getOid());
//        map.put("parentOid", parentOid);
//        map.put("oid", oid);
//        cypher.append("Match (n:").append(Delivery.TYPE).append(" {oid:$oid}), ").append("(m:")
//                .append(Delivery.TYPE).append(" {oid:$parentOid}) ").append("create (n)<-[r:").append("CONTAIN")
//                .append("]-(m)");
//        excute(cypher.toString(), map);
//    }
//
//    @Override
//    public Delivery findByClsOidAndName(String catalogOid, String oid, String clsOid, String name, String modelDefinition) {
//        StringBuffer cypher = new StringBuffer();
//        Map<String, Object> map = new HashMap();
//        //当前组织下，具体位置,相同子类型交付清单，不允许重名和分类重复。（对应参数：tenantOid-catalogOid-oid-name-clsOid）
//        UserDTO currentUser = SessionHelper.getCurrentUser();
//        map.put("tenantOid", currentUser.getTenantOid());
//        map.put("userOid", currentUser.getOid());
//        map.put("catalogOid", catalogOid);
//        map.put("oid", oid);
//        map.put("clsOid", clsOid);
//        map.put("name", name);
//        map.put("modelDefinition", modelDefinition);
//        cypher.append("match (s").append(dealLabel(Delivery.TYPE)).append(")");
//        cypher.append(" where s.tenantOid=$tenantOid and s.catalogOid =$catalogOid and s.modelDefinition =$modelDefinition");
//        if(StringUtils.isNotBlank(oid)){
//            cypher.append(" and s.oid <> $oid");
//        }
//        if (StringUtils.isBlank(name) && StringUtils.isBlank(clsOid)) {
//            return null;
//        }
//        if (StringUtils.isNotBlank(name) && StringUtils.isNotBlank(clsOid)) {
//            cypher.append(" and (s.name = $name or s.clsOid = $clsOid)");
//        } else if (StringUtils.isNotBlank(name)) {
//            cypher.append(" and s.name = $name");
//        } else if (StringUtils.isNotBlank(clsOid)) {
//            cypher.append(" and s.clsOid = $clsOid");
//        }
//        cypher.append(" return s");
//
//        return (Delivery) excuteOne(cypher.toString(), map, "s", Delivery.class);
//    }
//
//    @Override
//    public Delivery findByClsOid(String catalogOid, String clsOid) {
//        Assert.notBlank(catalogOid, "catalogOid can not be null");
//        StringBuffer cypher = new StringBuffer();
//        Map<String, Object> map = new HashMap();
//        //当前组织下，具体位置的交付清单，不允许重名和分类重复。（对应参数：tenantOid-catalogOid-oid-name-clsOid）
//        UserDTO currentUser = SessionHelper.getCurrentUser();
//        map.put("tenantOid", currentUser.getTenantOid());
//        map.put("userOid", currentUser.getOid());
//        map.put("catalogOid", catalogOid);
//        map.put("clsOid", clsOid);
//        cypher.append("match (s").append(dealLabel(Delivery.TYPE)).append(")");
//        cypher.append(" where s.tenantOid=$tenantOid and s.catalogOid =$catalogOid ");
//        if (StringUtils.isNotBlank(clsOid)) {
//            cypher.append(" and s.clsOid = $clsOid");
//        }
//        cypher.append(" return s");
//
//        return (Delivery) excuteOne(cypher.toString(), map, "s", Delivery.class);
//    }
//
//    @Override
//    public List<Delivery> findLeafNodes(String containerOid, String searchKey) {
//        Assert.notBlank(containerOid, "containerOid can not be null");
//        StringBuffer cypher = new StringBuffer();
//        Map<String, Object> map = new HashMap<>(8);
//        UserDTO currentUser = SessionHelper.getCurrentUser();
//        map.put("tenantOid", currentUser.getTenantOid());
//        map.put("userOid", currentUser.getOid());
//        map.put("containerOid", containerOid);
//        map.put("searchKey", searchKey);
//        cypher.append("match (s").append(dealLabel(Delivery.TYPE)).append(") <- [:CONTAIN] - (m").append(dealLabel(Container.TYPE)).append(") where m.oid=$containerOid ")
//                .append("and m.tenantOid=$tenantOid and s.root = true");
//        cypher.append(" with s Match path=(s)-[r").append(dealLabel(Contain.TYPE)).append("*0..]->(i").append(dealLabel(Delivery.TYPE)).append(")")
//                .append(" where i.modelDefinition='Category'");
//        if(StringUtils.isNotEmpty(searchKey)) {
//            cypher.append(" and (i.name contains $searchKey or i.number contains $searchKey)");
//        }
//        cypher.append(" return distinct i");
//
//        return excuteList(cypher.toString(), map, "i", Delivery.class);
//    }
//
//    @Override
//    public List<DeliveryTreeNode> findAllChildrenObjByDeliveryOid(String oid, String level) {
//        Assert.notBlank(oid, "oid can not be null");
//        StringBuffer cypher = new StringBuffer();
//        Map<String, Object> map = new HashMap();
//        UserDTO currentUser = SessionHelper.getCurrentUser();
//        map.put("tenantOid", currentUser.getTenantOid());
//        map.put("userOid", currentUser.getOid());
//        map.put("oid", oid);
//        //optional match (s:Delivery)-[:CONTAIN|REFERENCE]->(i) where s.oid = 'bd86d50c-c773-4027-b90c-aa45c3c5a380' and s.tenantOid='346d0eb9-f6e5-4f76-8884-0f1806fb9cb4' with s,i optional match (i)-[:ITERATE]->(obj) return s,i,obj
//        cypher.append("match (s").append(dealLabel(Delivery.TYPE)).append(")-[r").append(dealLabel(Contain.TYPE)).append("*0..").append(level).append("]->(d").append(dealLabel(Delivery.TYPE)).append(") ");
//        cypher.append(" where s.oid = $oid and s.tenantOid=$tenantOid ")
//                .append(" with d optional match(d)-[:REFERENCE]-(i) with d, i optional  match (i)-[:ITERATE]->(obj) ")
//                .append(" where obj.latest = true and ( obj.lockOwnerOid is null or (obj.lockOwnerOid=$userOid and obj.lockSourceOid is not null)")
//                .append(" or (obj.lockOwerOid<>$userOid and obj.lockSourceOid is null ))");
//        cypher.append(" return d,i,obj");
//        List<DeliveryTreeNode> res = new ArrayList();
//        Result result = run(cypher.toString(), map);
//        List<Record> list = result.list();
//        Iterator var7 = list.iterator();
//        Value value;
//        while(var7.hasNext()) {
//            Record record = (Record) var7.next();
//            value = record.get("i");
//            if (value.isNull()) {
//                continue;
//            }
//            Node i = value.asNode();
//            DeliveryTreeNode deliveryTreeNode = JSONUtil.parseObject(i, DeliveryTreeNode.class);
//            if (StringUtil.isNotEmpty(deliveryTreeNode.getNumber())) {//不返回给前端master对象，依据master没有编码判断
//                res.add(deliveryTreeNode);
//            }
//            if (!record.get("obj").isNull()) {
//                Node obj = record.get("obj").asNode();
//                res.add(JSONUtil.parseObject(obj, DeliveryTreeNode.class));
//            }
//        }
//        return res;
//    }
//
//    @Override
//    public Delivery findByParentOidAndName(String name, String parentOid, Boolean isRoot) {
//        StringBuffer cypher = new StringBuffer();
//        Map<String, Object> map = new HashMap();
//        map.put("name", name);
//        map.put("parentOid",parentOid);
//        // 匹配下一级的同名节点,如果是根节点，匹配容器，否则匹配节点
//        if (isRoot){
//            cypher.append("match (p").append(dealLabel(Container.TYPE)).append("{oid:$parentOid})");
//        }else {
//            cypher.append("match (p").append(dealLabel(Delivery.TYPE)).append("{oid:$parentOid})");
//        }
//        cypher.append("match(p)-[:CONTAIN]->(c").append(dealLabel(Delivery.TYPE)).append("{name:$name})");
//        cypher.append("return c");
//        return (Delivery) excuteOne(cypher.toString(),map,"c",Delivery.class);
//    }
////    @Override
////    public Delivery findByOid(String oid) throws JWIException {
////        StringBuffer cypher = new StringBuffer();
////        Map<String, Object> map = new HashMap<>();
////        map.put("oid", oid);
////        cypher.append(" match (m").append(this.entityLabelCypher).append(") where m.oid = $oid with m  ");
////        cypher.append(" optional match (m) -[").append(dealLabel(Attribute.TYPE))
////                .append("]->(f) return m, f, labels(f)[0] as fLabel");
////        // 执行
////        Result run = run(cypher.toString(), map);
////        // 解析
////        Map<String, Delivery> cache = new HashMap<>();
////        Record next = null;
////        JSONObject nodeJSON = null;
////        Delivery node = null;
////        String docOid = null;
////        while (run.hasNext()){
////            next = run.next();
////            nodeJSON = JSONUtil.toJSON(next.get("m"));
////            docOid = nodeJSON.getString("oid");
////            node = cache.get(docOid);
////            if (node == null){
////                node = nodeJSON.toJavaObject(Delivery.class);
////                cache.put(docOid, node);
////            }
////            node.fillBackField(JSONUtil.toJSON(next.get("f")), next.get("fLabel").asString(""));
////        }
////        return cache.get(oid);
////    }
//}
