package cn.jwis.product.pdm.customer.repo.neo4j;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONArray;
import cn.jwis.framework.base.dto.UserDTO;
import cn.jwis.framework.base.enhance.schema.SchemaClassMapping;
import cn.jwis.framework.base.exception.JWIException;
import cn.jwis.framework.base.response.PageResult;
import cn.jwis.framework.base.util.CollectionUtil;
import cn.jwis.framework.base.util.StringUtil;
import cn.jwis.framework.base.web.session.SessionHelper;
import cn.jwis.framework.database.core.query.dynamic.Condition;
import cn.jwis.framework.database.neo4j.condition.Neo4jEnableConditional;
import cn.jwis.framework.database.neo4j.execution.CommonEntityTemplate;
import cn.jwis.framework.database.neo4j.util.BeanUtil;
import cn.jwis.framework.database.neo4j.util.ConditionParserNeo4j;
import cn.jwis.framework.database.neo4j.util.JSONUtil;
import cn.jwis.platform.iam.organization.entity.Company;
import cn.jwis.platform.iam.organization.entity.Department;
import cn.jwis.platform.iam.organization.entity.Position;
import cn.jwis.platform.iam.structure.TreeAbleEx;
import cn.jwis.platform.iam.user.User;
import cn.jwis.platform.plm.account.entity.team.response.TeamTemplateRoleWithUser;
import cn.jwis.platform.plm.account.repo.user.response.UserTreeAbleEx;
import cn.jwis.platform.plm.container.entity.Folder;
import cn.jwis.platform.plm.container.entity.TeamRole;
import cn.jwis.platform.plm.foundation.classification.entity.Classification;
import cn.jwis.platform.plm.foundation.classification.related.ClsLayout;
import cn.jwis.platform.plm.foundation.common.response.InstanceEntity;
import cn.jwis.platform.plm.foundation.common.service.CommonAbilityService;
import cn.jwis.platform.plm.permission.administrativedomain.entity.AdministrativeDomain;
import cn.jwis.platform.plm.workflow.engine.dto.ProOrdOfBizObjFuzzyPageDTO;
import cn.jwis.platform.plm.workflow.engine.dto.ProcessOrderDetail;
import cn.jwis.platform.plm.workflow.engine.order.entity.ProcessOrder;
import cn.jwis.product.pdm.cad.ecad.entity.ECADIteration;
import cn.jwis.product.pdm.change.entity.Issue;
import cn.jwis.product.pdm.customer.dos.PathDo;
import cn.jwis.product.pdm.customer.dos.UserRoleInfo;
import cn.jwis.product.pdm.customer.entity.*;
import cn.jwis.product.pdm.customer.repo.CustomerCommonRepo;
import cn.jwis.product.pdm.customer.repo.neo4j.util.DingUtils;
import cn.jwis.product.pdm.customer.repo.neo4j.util.MeetingDDUserVO;
import cn.jwis.product.pdm.delivery.entity.Delivery;
import cn.jwis.product.pdm.delivery.entity.DeliveryTreeNode;
import cn.jwis.product.pdm.document.entity.DocumentIteration;
import cn.jwis.product.pdm.partbom.part.dto.ConfigDTO;
import cn.jwis.product.pdm.partbom.part.entity.PartIteration;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.neo4j.driver.Record;
import org.neo4j.driver.Result;
import org.neo4j.driver.Value;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Conditional;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2023/8/16 10:45
 * @Email <EMAIL>
 */
@Slf4j
@Component
@Conditional(Neo4jEnableConditional.class)
public class CustomerCommonNeo4jRepoImpl extends CommonEntityTemplate<PartIteration> implements CustomerCommonRepo {

    private static final Map<String, Class> TYPE_CLASS_MAP = new HashMap<String, Class>() {{
        put(Company.TYPE, Company.class);
        put(Department.TYPE, Department.class);
        put(Position.TYPE, Position.class);
        put(User.TYPE, UserTreeAbleEx.class);
    }};

    private static final String FIND_BY_NAME_URL = "http://meeting-management.prod.yhroot.com/meeting-management/api/meeting/user/findByName?name=";
    private static final String FIND_BY_EMAIL_URL = "http://meeting-management.prod.yhroot.com/meeting-management/api/meeting/user/findByEmail?email=";
    @Autowired
    DingUtils dingUtils;
    @Resource
    CommonAbilityService commonAbilityService;
    @Autowired
    RedisTemplate redisTemplate;

    public CustomerCommonNeo4jRepoImpl(DingUtils dingUtils) {
    }

    @Override
    @SneakyThrows
    public List<PartIteration> querySearch(List<ConfigDTO> systemProp, List<ConfigDTO> extendProp, List<ConfigDTO> classificationProp, JSONObject partIteration) {
        StringBuffer cypher = new StringBuffer();
        Map<String, Object> param = new HashMap();
        param.put("tenantOid", SessionHelper.getCurrentUser().getTenantOid());
        cypher.append(" match (m:").append(PartIteration.TYPE).append(") with m where m.tenantOid = $tenantOid and m.latest = true ");
        if (partIteration != null) {
            param.put("userOid", SessionHelper.getCurrentUser().getOid());
            param.put("number", partIteration.get("number"));
            cypher.append("and m.number<>$number and ( m.lockOwnerOid is null or (m.lockOwnerOid=$userOid and m.lockSourceOid is not null) or (m.lockOwerOid<>$userOid and m.lockSourceOid is null )) ");
        }
        if (!CollectionUtil.isEmpty(systemProp)) {
            cypher.append("and ");
            for (ConfigDTO configDTO : systemProp) {
                param.put(configDTO.getValue(), dealSearchKey(configDTO.getAttrValue().toString()));
                cypher.append("(m.").append(configDTO.getValue()).append("=~ $").append(configDTO.getValue());
                cypher.append(" or '").append(configDTO.getAttrValue().toString()).append("'=~ '(?is).*' + m.").append(configDTO.getValue()).append(" + '.*'").append(")");
                if (systemProp.indexOf(configDTO) != systemProp.size() - 1) {
                    cypher.append(" and ");
                }
            }
        }
        if (!CollectionUtil.isEmpty(extendProp)) {
            cypher.append(" match (m) -[:ATTRIBUTE]->(f:PropertyGroup) where ");
            for (ConfigDTO configDTO : extendProp) {
                param.put(configDTO.getValue(), dealSearchKey(configDTO.getAttrValue().toString()));
                cypher.append("(f.").append(configDTO.getValue()).append("=~ $").append(configDTO.getValue());
//                cypher.append(" or '").append(configDTO.getAttrValue().toString()).append("'=~ '(?is).*' + f.").append(configDTO.getValue()).append(" + '.*'").append(")");
                cypher.append(")");
                if (extendProp.indexOf(configDTO) != extendProp.size() - 1) {
                    cypher.append(" and ");
                }
            }
            cypher.append(" with m,f ");
        } else {
            cypher.append(" match (m) -[:ATTRIBUTE]->(f:PropertyGroup) with m,f ");
        }
        if (!CollectionUtil.isEmpty(classificationProp)) {
            cypher.append(" match (m) -[:ATTRIBUTE]->(n:ClsAttrContent) where ");
            for (ConfigDTO configDTO : classificationProp) {
                param.put(configDTO.getValue(), dealSearchKey(configDTO.getAttrValue().toString()));
                cypher.append("(n.").append(configDTO.getValue()).append("=~ $").append(configDTO.getValue());
                cypher.append(" or '").append(configDTO.getAttrValue().toString()).append("'=~ '(?is).*' + n.").append(configDTO.getValue()).append(" + '.*'").append(")");
                if (classificationProp.indexOf(configDTO) != classificationProp.size() - 1) {
                    cypher.append(" and ");
                }
            }
            cypher.append(" with m,n");
        } else {
            cypher.append(" match (m) -[:ATTRIBUTE]->(n:ClsAttrContent)  with m,f,n");
        }
        cypher.append(" return m,f,n");

        Result run = run(cypher.toString(), param);
        List<PartIteration> result = new ArrayList<>();
        Record next = null;
        PartIteration part = null;
        JSONObject extensionContent = null;
        JSONObject clsProperty = null;
        while (run.hasNext()) {
            next = run.next();
            part = BeanUtil.map2bean(JSONUtil.toJSON(next.get("m")), PartIteration.class);
            if(ObjectUtils.isNotEmpty(next.get("f"))) {
                extensionContent = JSONUtil.toJSON(next.get("f"));
            }
            if(ObjectUtils.isNotEmpty(next.get("n"))) {
                clsProperty = JSONUtil.toJSON(next.get("n"));
            }
            part.setExtensionContent(extensionContent);
            part.setClsProperty(clsProperty);
            result.add(part);
        }
        return result;
    }

    @Override
    public List<Folder> getFolderPath(String type, String oid) throws Exception {
        Map<String, Object> params = new HashMap();
        params.put("oid", oid);
        StringBuffer cypher = new StringBuffer();
        //match(r:Folder)-[:CONTAIN*0..]->(d:DocumentIteration) where d.number = '010003'
        //optional match (r) -[:ATTRIBUTE]->(f) return r,f;
        cypher.append("Match (r:").append(Folder.TYPE).append(")-[")
                .append(this.dealLabel("CONTAIN")).append("*0..]->(s:")
                .append(type).append(")").append(" where s.oid=$oid ")
                .append(" optional match (r) -[:ATTRIBUTE]->(f:PropertyGroup) return r,f ");
        // 执行
        Result run = run(cypher.toString(), params);
        List<Folder> result = new ArrayList<>();
        Record next = null;
        Folder folder = null;
        JSONObject extensionContent = null;
        while (run.hasNext()) {
            next = run.next();
            folder = BeanUtil.map2bean(JSONUtil.toJSON(next.get("r")), Folder.class);
            extensionContent = JSONUtil.toJSON(next.get("f"));
            folder.setExtensionContent(extensionContent);
            result.add(folder);
        }
        return result;
    }

    @Override
    public List<Issue> findIssueByEntity(String type, List<String> oids,boolean onlyUnClosed) throws Exception {
        //match(p:PartIteration)<-[:IMPACT]-(i:Issue) where p.oid in ['e7c5d681-1473-438a-bac5-48546efe10d9'] return i order by i.updateDate desc;
        Map<String, Object> params = new HashMap();
        params.put("oids",oids);
        StringBuffer cypher = new StringBuffer();
        //match(r:Folder)-[:CONTAIN*0..]->(d:DocumentIteration) where d.number = '010003'
        //optional match (r) -[:ATTRIBUTE]->(f) return r,f;
        cypher.append("Match (i:").append(Issue.TYPE).append(")-[")
                .append(this.dealLabel("IMPACT")).append("]->(e:")
                .append(type).append(")").append(" where e.oid in $oids ");
        if(onlyUnClosed){
            cypher.append(" and not i.lifecycleStatus = 'Closed' ");
        }
        cypher.append(" return distinct i order by i.updateDate desc ");
        // 执行
        Result run = run(cypher.toString(), params);
        List<Issue> result = new ArrayList<>();
        Record next = null;
        Issue issue = null;
        while (run.hasNext()) {
            next = run.next();
            issue = BeanUtil.map2bean(JSONUtil.toJSON(next.get("i")), Issue.class);
            result.add(issue);
        }
        return result;
    }

    @Override
    public List<TeamTemplateRoleWithUser> autoUser(List<String> contentOidList) {
        //match(p)-[:ASSIGN]->(t:Team)-[:CONTAIN]->(r:TeamRole)
        //where p.oid in ['63ef4ee8-7eb4-46a8-bc2e-e37111452a15','81b379ee-716b-4e0f-b244-8e4a5da23622'] with r
        //optional match(r)-[:CONTAIN]->(u:User)
        //return r, collect(u) as users
        List<TeamTemplateRoleWithUser> result = new ArrayList();
        Map<String, TeamTemplateRoleWithUser> onlyOneMap = new HashMap<>();
        Map<String, Object> map = new HashMap();
        map.put("contentOidList", contentOidList);
        map.put("tenantOid", SessionHelper.getCurrentUser().getTenantOid());

        StringBuffer cypher = new StringBuffer();
        cypher.append(" MATCH (p)-[:ASSIGN]->(:Team)-[:CONTAIN]->(r:TeamRole) ")
                .append(" WHERE p.oid IN $contentOidList ")
                .append(" WITH r ")
                .append(" OPTIONAL MATCH (r)-[:CONTAIN]->(u:User)-[:BELONG]->(ten:Tenant) ")
                .append(" WHERE ten.oid = $tenantOid ")
                .append(" RETURN r, collect(u) AS users ");
        Result run = this.run(cypher.toString(), map);
        Record next = null;
        TeamTemplateRoleWithUser row = null;
        List<UserDTO> users = null;
        while (run.hasNext()) {
            next = run.next();
            row = JSONUtil.parseObject(next.get("r").asNode(), TeamTemplateRoleWithUser.class);
            users = next.get("users").asList((item) -> {
                return JSONUtil.parseObject(item.asNode(), UserDTO.class);
            });
            // 有同名角色就把所有user归于一个角色下
            if (onlyOneMap.containsKey(row.getName())) {
                insertUser(onlyOneMap, row.getName(), users);
            } else {
                List<UserDTO> temp = new ArrayList<>();
                temp.addAll(users);
                row.setUsers(temp);
                onlyOneMap.put(row.getName(), row);
            }
        }
        result.addAll(onlyOneMap.values());
        return result;
    }

    @Override
    public Map<String, List<TeamRole>> queryContainerRole(String tenantOid, String userOid) {
        StringBuilder cypher = new StringBuilder();
        Map<String, Object> paramMap = new HashMap<>(8);
        paramMap.put("tenantOid", tenantOid);
        paramMap.put("userOid", userOid);
        cypher.append("match(a:AdministrativeDomain)<-[:POINT]-(c{tenantOid:$tenantOid})-[:ASSIGN]->" +
                "(t:Team)-[:CONTAIN]->(tr:TeamRole)-[:CONTAIN]->(u:User{oid:$userOid}) return a.oid as oid,tr");
        Result run = this.run(cypher.toString(), paramMap);
        Record next;
        String oid;
        Map<String, Object> teamRoleMap;
        Map<String, List<TeamRole>> teamRoleData = new HashMap<>(16);
        TeamRole teamRole;
        List<TeamRole> teamRoleList;
        while (run.hasNext()) {
            next = run.next();
            oid = next.get("oid").asString(null);
            teamRoleMap = next.get("tr").asMap();
            teamRole = JSONObject.parseObject(JSONObject.toJSONString(teamRoleMap), TeamRole.class);
            teamRoleList = teamRoleData.get(oid);
            if (CollectionUtils.isEmpty(teamRoleList)) {
                teamRoleList = new ArrayList<>();
                teamRoleData.put(oid, teamRoleList);
            }
            teamRoleList.add(teamRole);
        }
        return teamRoleData;
    }

    @Override
    public List<AdministrativeDomain> queryFolderDomain(String tenantOid) {
        StringBuffer cypher = new StringBuffer();
        Map<String, Object> paramMap = new HashMap(8);
        paramMap.put("oid", tenantOid);
        cypher.append("match (c:Folder{tenantOid:$oid})-[:POINT]->(a:AdministrativeDomain) return distinct(a) as a");
        return this.excuteList(cypher.toString(), paramMap, "a", AdministrativeDomain.class);
    }

    private void insertUser(Map<String, TeamTemplateRoleWithUser> onlyOneMap, String name, List<UserDTO> users) {
        if(CollectionUtil.isEmpty(users)){
            return;
        }
        TeamTemplateRoleWithUser roleWithUser = onlyOneMap.get(name);
        List<UserDTO> currUsers = roleWithUser.getUsers();
        if(CollectionUtil.isEmpty(currUsers)){
            currUsers = new ArrayList<>();

        }
        List<String> currUserAccounts = currUsers.stream().map(u->u.getAccount()).collect(Collectors.toList());
        if(CollectionUtil.isEmpty(currUserAccounts)){
            currUserAccounts = new ArrayList<>();
        }
        for(UserDTO u : users){
            if(!currUserAccounts.contains(u.getAccount())){
                currUsers.add(u);
            }
        }
        roleWithUser.setUsers(currUsers);
    }

    public int lastIsParent(String prevOid, String nextOid){
        StringBuilder cypher = new StringBuilder();
        Map<String, Object> paramMap = new HashMap<>(8);
        paramMap.put("nextOid",nextOid);
        paramMap.put("prevOid",prevOid);
        cypher.append(" match(p:PartIteration)-[:USE]->(s:PartIteration) where p.oid = $nextOid and s.oid= $prevOid return p,s limit 1 ");
        Result run = this.run(cypher.toString(), paramMap);
        return run.hasNext() ? 1 : -1;
    }

//    @Resource
//    private CvtNormalService cvtNormalService;

    public List<InstanceEntity> queryReviewsDetail(String processOrderOid) {
        StringBuffer cypher = new StringBuffer();
        Map<String, Object> map = new HashMap();
        map.put("fromOid", processOrderOid);
        cypher.append(" Match (m").append(this.dealLabel("ProcessOrder")).append(")-[l").append(this.dealLabel("REVIEW_FOR")).append("]->(s) where m.oid=$fromOid ");
        cypher.append(" return s,l.main as isMain");
        Result run = this.run(cypher.toString(), map);

//        List<Map<String, Object>> recordMapList = run.list().stream().map(record -> record.get("s").asMap()).collect(Collectors.toList());
//        List<InstanceEntity> priSecFileList = cvtNormalService.json2TNoValRef(recordMapList, InstanceEntity.class);
        List<Value> recordMapList = run.list().stream().map(record -> record.get("s")).collect(Collectors.toList());
        List<InstanceEntity> priSecFileList = recordMapList.stream().map(it -> JSONUtil.parseObject(it, InstanceEntity.class)).collect(Collectors.toList());

        return priSecFileList;
    }

    public List<ProcessOrder> queryReviewDocProcessOrder(String docOid) {
        StringBuffer cypher = new StringBuffer();
        Map<String, Object> map = new HashMap();
        map.put("docOid", docOid);
        cypher.append(" Match (m").append(this.dealLabel("ProcessOrder")).append(")-[l").append(this.dealLabel("REVIEW_FOR"))
                .append("]->(s) where s.oid=$docOid and m.processState='done' and m.name='技术文档发布流程' ");
        cypher.append(" return s,l.main as isMain,m");
        Result run = this.run(cypher.toString(), map);

//        List<Map<String, Object>> recordMapList = run.list().stream().map(record -> record.get("s").asMap()).collect(Collectors.toList());
//        List<InstanceEntity> processOrderList = cvtNormalService.json2TNoValRef(recordMapList, InstanceEntity.class);
        List<Value> recordMapList = run.list().stream().map(record -> record.get("m")).collect(Collectors.toList());
        List<ProcessOrder> processOrderList = recordMapList.stream().map(it -> JSONUtil.parseObject(it, ProcessOrder.class)).collect(Collectors.toList());

        return processOrderList;
    }

    public List<String> stopStatusSetPartName(List<String> oidList) {
        StringBuffer cypher = new StringBuffer();
        Map<String, Object> map = new HashMap();
        map.put("oidList", oidList);
        cypher.append(" Match (m:PartIteration) where m.oid in $oidList and left(m.name, 4) <> '已失效-' set m.name='已失效-' + m.name return m.oid ");
        Result run = this.run(cypher.toString(), map);
        cypher.setLength(0);
        cypher.append(" Match (m:PartIteration)-[:ATTRIBUTE]->(f:PropertyGroup) where m.oid in $oidList and f.cn_jwis_gg is not null and left(f.cn_jwis_gg, 4)<>'已失效-' set f.cn_jwis_gg='已失效-' + f.cn_jwis_gg ");
        Result run1 = this.run(cypher.toString(), map);
        List<Record> list = run.list();
        return list.stream().map(it -> (String)it.asMap().get("m.oid")).collect(Collectors.toList());
    }

    public List<String> unStopStatusSetPartName(List<String> oidList) {
        StringBuffer cypher = new StringBuffer();
        Map<String, Object> map = new HashMap();
        map.put("oidList", oidList);
        cypher.append(" Match (m:PartIteration) where m.oid in $oidList and left(m.name, 4) = '已失效-' set m.name=substring(m.name,4) return m.oid ");
        Result run = this.run(cypher.toString(), map);
        cypher.setLength(0);
        cypher.append(" Match (m:PartIteration)-[:ATTRIBUTE]->(f:PropertyGroup) where m.oid in $oidList and f.cn_jwis_gg is not null and left(f.cn_jwis_gg, 4)='已失效-' set f.cn_jwis_gg=substring(f.cn_jwis_gg,4) ");
        Result run1 = this.run(cypher.toString(), map);
        List<Record> list = run.list();
        return list.stream().map(it -> (String)it.asMap().get("m.oid")).collect(Collectors.toList());
    }

    public List<DocumentIteration> queryDoc(List<String> numberList) {
        StringBuffer cypher = new StringBuffer();
        Map<String, Object> map = new HashMap();

        cypher.append(" Match (s").append(this.dealLabel("DocumentIteration")).append(") -[r:ATTRIBUTE] -> (p:PropertyGroup)");
        if(numberList != null && numberList.size() > 0){
            map.put("numberList", numberList);
            cypher.append(" where s.number in $numberList and s.latest=true");
        }
        cypher.append(" return s,p");
        Result run = this.run(cypher.toString(), map);

        List<Map<String, Object>> recordMapList = run.list().stream().map(record -> {
            Map<String, Object> sMap = new HashMap<>(record.get("s").asMap());
            Map<String, Object> pMap = record.get("p").asMap();
            sMap.put("extensionContent", pMap);
            return sMap;
        }).collect(Collectors.toList());
//        List<DocumentIteration> priSecFileList = cvtNormalService.json2TNoValRef(recordMapList, DocumentIteration.class);
        List<DocumentIteration> priSecFileList = recordMapList.stream().map(it -> {
            Object extension = it.get("extensionContent");
            it.remove("extensionContent");
            DocumentIteration document = JSONUtil.parseObject(it, DocumentIteration.class);
            document.setExtensionContent((JSONObject) JSONObject.toJSON(extension));
            return document;
        }).collect(Collectors.toList());

        return priSecFileList;
    }

//    public List<FileMetadata> queryFile() {
//        StringBuffer cypher = new StringBuffer();
//        Map<String, Object> map = new HashMap();
//        cypher.append(" Match (s").append(this.dealLabel("FileMetadata")).append(") ");
//        cypher.append(" return s");
//        Result run = this.run(cypher.toString(), map);
//
//        List<Map<String, Object>> recordMapList = run.list().stream().map(record -> record.get("s").asMap()).collect(Collectors.toList());
//        List<FileMetadata> priSecFileList = cvtNormalService.json2TNoValRef(recordMapList, FileMetadata.class);
//
//        return priSecFileList;
//    }

    public void setSecondFile(String docOid, List<String> secondFileStrList) {
        StringBuffer cypher = new StringBuffer();
        Map<String, Object> map = new HashMap();
        map.put("oid", docOid);
        map.put("secondFileStrList", secondFileStrList);
        cypher.append(" Match (s").append(this.dealLabel("DocumentIteration")).append(") where s.oid=$oid set s.secondaryFile=$secondFileStrList ");
        cypher.append(" return s");
        Result run = this.run(cypher.toString(), map);
    }

    public List<DocumentIteration> queryFileDocumentIteration(String docOid) {
        StringBuffer cypher = new StringBuffer();
        Map<String, Object> map = new HashMap();
        map.put("docOid", docOid);
        cypher.append(" Match (s").append(this.dealLabel("DocumentIteration")).append(") where s.oid=$docOid");
        cypher.append(" return s");
        Result run = this.run(cypher.toString(), map);

//        List<Map<String, Object>> recordMapList = run.list().stream().map(record -> record.get("s").asMap()).collect(Collectors.toList());
//        List<DocumentIteration> priSecFileList = cvtNormalService.json2TNoValRef(recordMapList, DocumentIteration.class);
        List<DocumentIteration> priSecFileList = run.list().stream().map(record -> JSONUtil.parseObject(record.get("s"), DocumentIteration.class)).collect(Collectors.toList());

        return priSecFileList;
    }

    public List<DocumentIteration> queryBorrowDocs(String docOid) {
        StringBuffer cypher = new StringBuffer();
        Map<String, Object> map = new HashMap();
        map.put("docOid", docOid);
//        cypher.append(" MATCH (n:DocumentIteration)<-[:ITERATE]-()<-[:DESCRIBE]-(s)  WHERE s.oid=$docOid AND s:DocumentIteration  return distinct n ");
        cypher.append(" MATCH (n:DocumentIteration)<-[:ITERATE]-()<-[:DESCRIBE]->(s)  WHERE s.oid=$docOid AND s:DocumentIteration  return distinct n ");
        Result run = this.run(cypher.toString(), map);

        List<DocumentIteration> docList = run.list().stream().map(record -> JSONUtil.parseObject(record.get("n"), DocumentIteration.class)).collect(Collectors.toList());

        return docList;
    }

    public void setSecondFile(String docOid, String secondFileStr) {
//        StringBuffer cypher = new StringBuffer();
//        Map<String, Object> map = new HashMap();
//        map.put("oid", docOid);
//        map.put("secondFileStr", secondFileStr);
//        cypher.append(" Match (s").append(this.dealLabel("DocumentIteration")).append(") where s.oid=$oid set s.secondaryFile=$secondFileStr ");
//        cypher.append(" return s");
//        Result run = this.run(cypher.toString(), map);
    }

    private static final SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");


    public List<DeliveryReportCustom> getDeliveryDataForExport(String containerOid, boolean isNormal){
        //根据邮箱或 用户名查询钉钉 UserID 接口的 token
        String token = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1c2VyTm8iOiJGeDM1RDQzQ25pUDRDbDZWaWlLSzduVndpRWlFIiwiZXhwaXJlVGltZSI6IjIxMDAtMDEtMDEgMTA6MTA6MTAifQ.rjQF8ytqy3CS_Lit3gmzbtbX3Fc06iMXkh26920ZQfg";
        StringBuffer cypher = new StringBuffer();
        HashMap<String, Object> paramMap = new HashMap<>();
        paramMap.put("containerOid", containerOid);
        cypher.append(" match (s:Delivery) where s.root = true and s.catalogOid=$containerOid with s optional match path=(s)-[r:CONTAIN*0..]->(i:Delivery) where s<>i with nodes(path) as nodes, s,i optional match (i)-[:REFERENCE]->(master)-[:ITERATE]->(obj)  where obj.latest = true and ( obj.lockOwnerOid is null or (obj.lockOwnerOid='sys_admin' and obj.lockSourceOid is not null) or (obj.lockOwerOid<>'sys_admin' and obj.lockSourceOid is null )) with nodes,s,i,master,obj  optional match (i)-[:ATTRIBUTE]->(prop) where prop.FZR <> '' return nodes,s,i,prop,obj ");
        Result run = this.run(cypher.toString(), paramMap);
        List<Record> recordList = run.list();

        List<DeliveryReportCustom> deliveryReportList = recordList.stream().map(it -> {
                    List<DeliveryTreeNode> nodeList = it.get("nodes").isNull() ? Collections.emptyList() : it.get("nodes").asList().stream().map(node -> JSONUtil.parseObject(node, DeliveryTreeNode.class)).collect(Collectors.toList());
                    if (nodeList.size() == 0 && !it.get("s").isNull()) {
                        nodeList = Arrays.asList(JSONUtil.parseObject(it.get("s"), DeliveryTreeNode.class));
                    }
                    //判断如果当前要找 产保相关，name只找nodeList中第一个节点属性为 产品保证的数据，非产保数据要过滤
                    if (!isNormal) {
                        if (null != nodeList && nodeList.size() > 0) {
                            DeliveryTreeNode deliveryTreeNode = nodeList.get(0);
                            String name = deliveryTreeNode.getName();
                            if (StrUtil.isNotEmpty(name) && !name.contains("产品保证")) {
                                return null;
                            }
                        }
                    }
                    Map<String, Object> instanceMap = it.get("obj").isNull() ? Collections.emptyMap() : it.get("obj").asMap();
                    Map<String, Object> propMap = it.get("prop").isNull() ? Collections.emptyMap() : it.get("prop").asMap(p -> {
                        try {
                            return p.asList();
                        } catch (Exception e) {
                            logger.warn("prop转换类型不对 prop==>" + p);
                            return p.asString();
                        }
                    }, Collections.emptyMap());
                    Object fzr = (fzr = propMap.get("FZR")) == null ? "{}" : fzr;
                    if (fzr instanceof String && StringUtils.isBlank((String) fzr)) {
                        fzr = "{}";
                    }
                    List<cn.hutool.json.JSONObject> responList = (fzr instanceof List ? new JSONArray(fzr) : new JSONArray().put(new cn.hutool.json.JSONObject(fzr)))
                            .stream().map(json -> new cn.hutool.json.JSONObject(json)).collect(Collectors.toList());
                    String resonPerson = responList.stream().map(resonJson -> resonJson.getStr("name")).collect(Collectors.joining(","));
                    String oid = responList.stream().map(resonJson -> resonJson.getStr("oid")).collect(Collectors.joining(","));
                    String position = "";
                    String parentPosition = "";
                    String email = "";
                    if (!StrUtil.isNullOrUndefined(oid) && StrUtil.isNotEmpty(oid) && !"null".equals(oid)) {
                        User user = (User) this.commonAbilityService.findByOid(oid, "User");
                        email = user.getEmail();
                        if ("刘心心".equals(user.getName())) {
                            email = "<EMAIL>";
                        }
                    }

                    try {
                        if (StrUtil.isNotEmpty(email) && !StrUtil.isNullOrUndefined(email) && !"null".equals(email)) {
                            // 先查 Redis 缓存
                            String userIdKey = "DeliveryReportNew:UserId:" + email;
                            String cachedUserId = (String) redisTemplate.opsForValue().get(userIdKey);

                            String userId = "";
                            if (StrUtil.isNotEmpty(cachedUserId)) {
                                userId = cachedUserId;
                            } else {
                                // 缓存未命中，请求钉钉接口获取 UserID
                                String url = FIND_BY_EMAIL_URL + email;
                                HttpResponse response = HttpUtil.createGet(url).header("token", token).execute();
                                String res = response.body();
                                if (StrUtil.isNotEmpty(res)) {
                                    MeetingDDUserVO data = JSON.parseObject(JSON.parseObject(res).getString("data"), MeetingDDUserVO.class);
                                    if (data != null) {
                                        userId = data.getUserId();
                                        // 缓存 UserID，2 天有效期
                                        redisTemplate.opsForValue().set(userIdKey, userId, 2, TimeUnit.DAYS);
                                    }
                                }
                            }
                            // 如果拿到了 UserID，再查询部门信息
                            position = dingUtils.findDepartById(userId);
                        }
                    } catch (Exception e) {
                        logger.warn("获取用户钉钉UserID错误==>" + e);
                    }

                    DeliveryReportCustom deliveryReport = new DeliveryReportCustom().setDeliverydocument((String) instanceMap.get("name"))
                            .setResponPerson((resonPerson == null || "null".equals(resonPerson)) ? "" : resonPerson).setPosition(position)
                            .setStatus(cvtLifcycleStatus((String) instanceMap.get("lifecycleStatus")))
                            .setJhsj((String) propMap.getOrDefault("jhsj", ""))
                            .setWcsj("Released".equals(instanceMap.get("lifecycleStatus")) ? simpleDateFormat.format(new Date((Long) instanceMap.get("updateDate"))) : "");


                    if (null != nodeList) {
                        if (nodeList.size() == 2) {
                            nodeList.stream().collect(
                                    () -> deliveryReport
                                    , (u, t) -> {
                                        if (u.getDeliveryBigType() == null) {
                                            u.setDeliveryBigType(t.getName());
                                        }  else {
                                            log.info("当前节点的信息:" + u);
                                            if (StrUtil.isEmpty(u.getPosition())) {
                                                u.setDeliveryFourthType(null);
                                            } else {
                                                u.setDeliveryFourthType(t.getName());
                                            }
                                        }
                                    }
                                    , (u1, u2) -> {
                                    });
                        } else if ((nodeList.size() == 3) && isNormal) {
                            nodeList.stream().collect(
                                    () -> deliveryReport
                                    , (u, t) -> {
                                        //兼容灵犀07A

                                        if (containerOid.equals("b3dc4cfe-9675-42c9-a6b9-46d29bd0e88b")) {
                                            if (u.getDeliveryBigType() == null
                                                    && (t.getName().equals("星务软件输入")
                                                    || t.getName().equals("结构布局输入")
                                                    || t.getName().equals("综合测试输入"))) {
                                                // 执行相关逻辑
                                                u.setDeliveryBigType(t.getName());
                                            } else if (u.getDeliverySecondType() == null && u.getDeliveryBigType() != null) {
                                                u.setDeliverySecondType(t.getName());
                                            } else if (u.getDeliveryFourthType() == null && u.getDeliveryBigType() != null) {
                                                u.setDeliveryFourthType(t.getName());
                                            }
                                        }else {
                                            if (u.getDeliverySecondType() == null) {
                                                u.setDeliverySecondType(t.getName());
                                            } else if (u.getDeliverySmallType() == null) {
                                                u.setDeliverySmallType(t.getName());
                                            } else {
                                                //没有交付文档，那么直接设置交付清单为null
                                                if (null == u.getDeliverydocument() && StrUtil.isEmpty(u.getDeliverydocument())) {
                                                    u.setDeliveryFourthType(null);
                                                } else {
                                                    u.setDeliveryFourthType(t.getName());
                                                }
                                            }
                                        }
                                    }
                                    , (u1, u2) -> {
                                    });
                        } else {
                            nodeList.stream().collect(
                                    () -> deliveryReport
                                    , (u, t) -> {
                                        if (u.getDeliveryBigType() == null) {
                                            u.setDeliveryBigType(t.getName());
                                        } else if (u.getDeliverySecondType() == null) {
                                            u.setDeliverySecondType(t.getName());
                                        } else if (u.getDeliverySmallType() == null) {
                                            u.setDeliverySmallType(t.getName());
                                        } else {
                                            if (t.getModelDefinition().equals("Category")) {
                                                u.setDeliveryFourthType(t.getName());
                                            }
                                        }
                                    }
                                    , (u1, u2) -> {
                                    });
                        }
                    }

                    if (isNormal) {
                        return deliveryReport.getDeliveryFourthType() == null ? null : deliveryReport;
                    } else {
                        return deliveryReport != null && StrUtil.isNotEmpty(deliveryReport.getDeliveryBigType()) && deliveryReport.getDeliveryBigType().contains("产品保证") && null != deliveryReport.getDeliverySmallType() ? deliveryReport : null;
                    }
                }).filter(Objects::nonNull)
                .collect(Collectors.toList());
        return deliveryReportList;
    }

    public List<DeliveryReport> getDeliveryData(String containerOid){
        StringBuffer cypher = new StringBuffer();
        HashMap<String, Object> paramMap = new HashMap<>();
        paramMap.put("containerOid", containerOid);
        cypher.append(" match (s:Delivery) where s.root = true and s.catalogOid=$containerOid with s optional match path=(s)-[r:CONTAIN*0..]->(i:Delivery) where s<>i with nodes(path) as nodes, s,i optional match (i)-[:REFERENCE]->(master)-[:ITERATE]->(obj)  where obj.latest = true and ( obj.lockOwnerOid is null or (obj.lockOwnerOid='sys_admin' and obj.lockSourceOid is not null) or (obj.lockOwerOid<>'sys_admin' and obj.lockSourceOid is null )) with nodes,s,i,master,obj  optional match (i)-[:ATTRIBUTE]->(prop) where prop.FZR <> '' return nodes,s,i,prop,obj ");
        Result run = this.run(cypher.toString(), paramMap);
        List<Record> recordList = run.list();
        List<DeliveryReport> deliveryReportList = recordList.stream().map(it -> {
            List<DeliveryTreeNode> nodeList = it.get("nodes").isNull() ? Collections.emptyList() : it.get("nodes").asList().stream().map(node -> JSONUtil.parseObject(node, DeliveryTreeNode.class)).collect(Collectors.toList());
            if(nodeList.size() == 0 && !it.get("s").isNull())
                nodeList = Arrays.asList(JSONUtil.parseObject(it.get("s"), DeliveryTreeNode.class));

            Map<String, Object> instanceMap = it.get("obj").isNull() ? Collections.emptyMap() : it.get("obj").asMap();
            Map<String, Object> propMap = it.get("prop").isNull() ? Collections.emptyMap() : it.get("prop").asMap(p -> {
                try {
                    return p.asList();
                } catch (Exception e) {
                    logger.warn("prop转换类型不对 prop==>" + p);
                    return p.asString();
                }
            }, Collections.emptyMap());

            Object fzr = (fzr =  propMap.get("FZR")) == null ? "{}" : fzr;
            if(fzr instanceof String && StringUtils.isBlank((String)fzr))
                fzr = "{}";
            List<cn.hutool.json.JSONObject> responList = (fzr instanceof List ? new JSONArray(fzr) : new JSONArray().put(new cn.hutool.json.JSONObject(fzr)))
                    .stream().map(json -> new cn.hutool.json.JSONObject(json)).collect(Collectors.toList());

            String resonPerson = responList.stream().map(resonJson -> resonJson.getStr("name")).collect(Collectors.joining(","));
            List accountList = responList.stream().map(resonJson -> resonJson.getStr("account")).filter(Objects::nonNull).collect(Collectors.toList());

            String depart = accountList.size() > 0 ? getUserDepart(accountList) : "";

            DeliveryReport deliveryReport = new DeliveryReport().setDeliverydocument((String) instanceMap.get("name")).setStatus(cvtLifcycleStatus((String) instanceMap.get("lifecycleStatus")))
                    .setResponPerson((resonPerson == null || "null".equals(resonPerson)) ? "" : resonPerson ).setPosition(depart).setDocumentOid((String) instanceMap.get("oid"))
                    .setJhsj((String) propMap.getOrDefault("jhsj", ""))
//                    .setWcsj((String) propMap.getOrDefault("wcsj", ""));
                    .setWcsj("Released".equals(instanceMap.get("lifecycleStatus")) ? simpleDateFormat.format(new Date((Long) instanceMap.get("updateDate"))) : "");

            nodeList.stream().collect(
                    () -> deliveryReport
                    , (u, t) -> {
                        if (u.getDeliveryBigType() == null)
                            u.setDeliveryBigType(t.getName());
                        else {
                            if (u.getDeliverySmallType() == null)
                                u.setDeliverySmallType(t.getName());
                            else
                                u.setDeliverySmallType(u.getDeliverySmallType() + "-" + t.getName());
                        }
                    }
                    , (u1, u2) -> {});

//            return deliveryReport.getDeliverySmallType() == null ? null : deliveryReport.setDeliveryBigType(deliveryReport.getDeliverySmallType());
            return deliveryReport.getDeliverySmallType() == null ? null : deliveryReport;
        }).filter(Objects::nonNull).collect(Collectors.toList());
        return deliveryReportList;
    }

    public Boolean batchSendERPPermission(String account){
        StringBuffer cypher = new StringBuffer();
        HashMap<String, Object> paramMap = new HashMap<>();
        paramMap.put("account", account);
        cypher.append(" MATCH (n:SystemRole)-[r]->(u:User) where n.name in['TenantAdministrators'] and u.account=$account RETURN n,u ");
        Result run = this.run(cypher.toString(), paramMap);
        List<Record> recordList = run.list();

        StringBuffer cypher1 = new StringBuffer();
        cypher1.append(" MATCH (n:TeamRole)-[r]->(u:User) where n.name in['cn_jwis_DA'] and u.account=$account RETURN n,u ");
        Result run1 = this.run(cypher1.toString(), paramMap);
        List<Record> recordList1 = run1.list();

        recordList.addAll(recordList1);

        return recordList.size() > 0 ? Boolean.TRUE : Boolean.FALSE;
    }

    public List<Map> findByOidList(List<String> oidList){
        HashMap<String, Object> paramMap = new HashMap<>();
        paramMap.put("oidList", oidList);
        StringBuffer cypher = new StringBuffer();
        cypher.append(" MATCH (n) where n.oid in $oidList RETURN n ");
        Result run = this.run(cypher.toString(), paramMap);
        List<Record> recordList = run.list();

        return recordList.stream().map(it -> it.get("n").isNull() ? Collections.emptyMap() : it.get("n").asMap()).collect(Collectors.toList());

    }

    public HashMap<String, String> getUserDepartAndParentDepartByOid(String oid){
        HashMap<String, String> resultMap = new HashMap<>();

        HashMap<String, String> paramMap = new HashMap<>();
        paramMap.put("oid", oid);
        StringBuffer cypher = new StringBuffer();
        cypher.append("MATCH p1=(u:User)-[r*2]->(d:Department)" +
                " WHERE u.oid = $oid" +
                " RETURN  d, length(p1) AS pathLength" +
                " ORDER BY d.updateDate DESC" +
                " LIMIT 1" +
                " UNION" +
                " MATCH p2=(u:User)-[r*3]->(d:Department)" +
                " WHERE u.oid = $oid" +
                " RETURN  d, length(p2) AS pathLength" +
                " ORDER BY d.updateDate DESC" +
                " LIMIT 1");
        Result result = this.run(cypher.toString(), paramMap);
        while (result.hasNext()) {
            Record record = result.next();
            int pathLength = record.get("pathLength").asInt();
            Department department = JSONUtil.parseObject(record.get("d").asNode(), Department.class);
            String departmentName = department.getName();
            if (pathLength == 3) {
                resultMap.put("parentPosition", departmentName);
            }else {
                resultMap.put("position", departmentName);
            }
        }

        return resultMap;
    }
    public String getUserDepartByOid(String oid){
        HashMap<String, Object> paramMap = new HashMap<>();
        paramMap.put("oid", oid);
        StringBuffer cypher = new StringBuffer();
        cypher.append(" MATCH (u:User)-[r*1..2]->(d:Department) where u.oid=$oid RETURN u,d order by d.updateDate desc limit 1");
        Result run = this.run(cypher.toString(), paramMap);
        List<Record> recordList = run.list();

        String departStr = recordList.stream().map(it -> {
            Map<String, Object> departMap = Optional.ofNullable(it.get("d")).map(depart -> depart.asMap()).orElse(Collections.emptyMap());
            return (String) departMap.getOrDefault("name", "");
        }).collect(Collectors.joining(""));

        return departStr;
    }

    public String getUserDepart(List<String> accountList){
        HashMap<String, Object> paramMap = new HashMap<>();
        paramMap.put("accountList", accountList);
        StringBuffer cypher = new StringBuffer();
        cypher.append(" MATCH (u:User)-[r*1..2]->(d:Department) where u.account in $accountList RETURN u,d ");
        Result run = this.run(cypher.toString(), paramMap);
        List<Record> recordList = run.list();

        String departStr = recordList.stream().map(it -> {
            Map<String, Object> departMap = Optional.ofNullable(it.get("d")).map(depart -> depart.asMap()).orElse(Collections.emptyMap());
            return (String) departMap.getOrDefault("name", "");
        }).collect(Collectors.joining(""));

        return departStr;
    }

    public List<Map<String, Object>> getUserList(List<String> accountList){
        HashMap<String, Object> paramMap = new HashMap<>();
        paramMap.put("accountList", accountList);
        StringBuffer cypher = new StringBuffer();
        cypher.append(" MATCH (u:User) where u.account in $accountList RETURN u ");
        Result run = this.run(cypher.toString(), paramMap);
        List<Record> recordList = run.list();

        List<Map<String, Object>> userList = recordList.stream().map(it -> {
            Map<String, Object> userMap = Optional.ofNullable(it.get("u")).map(user -> user.asMap()).orElse(Collections.emptyMap());
            return userMap;
        }).collect(Collectors.toList());

        return userList;
    }

//    Classification

    public String cvtLifcycleStatus(String lifecycleStatus){
        if(lifecycleStatus == null)
            return "";
        switch (lifecycleStatus){
            case "Design":
                return "设计中";
            case "UnderReview":
                return "审阅中";
            case "Draft":
                return "草稿";
            case "Released":
                return "已发布";
            case "Deactivated":
                return "已停用";
            default:
                return "";
        }
    }

    public String queryInCls(Collection<String> ancestorClsList, String instanceOid) {
        StringBuffer cypher = new StringBuffer();
        Map<String, Object> map = new HashMap();
        map.put("ancestorClsList",ancestorClsList);
        map.put("instanceOid", instanceOid);
        cypher.append(" MATCH path=(t:Classification)-[r1:SUB*0..]->(sub:Classification)-[r2]->(i:PartIteration) where t.code in $ancestorClsList and i.oid=$instanceOid RETURN t ");
        List<Record> recordList = this.run(cypher.toString(), map).list();

        if(recordList != null && recordList.size() > 0)
            return (String) recordList.get(0).get("t").asMap().getOrDefault("code", "");

        return "";
    }

    public String queryClsType(Collection<String> ancestorClsDisplaNameList, String instanceOid) {
        StringBuffer cypher = new StringBuffer();
        Map<String, Object> map = new HashMap();
        map.put("ancestorClsList",ancestorClsDisplaNameList);
        map.put("instanceOid", instanceOid);
//        cypher.append(" MATCH (i:PartIteration)<-[r]-(sub:Classification)<-[r2:SUB*1..]-(t:Classification)<-[r3]-(top:Classification) where i.oid=$instanceOid and top.displayName='部件' RETURN t ");
        cypher.append(" MATCH path=(t:Classification)-[r1:SUB*0..]->(sub:Classification)-[r2]->(i:PartIteration) where t.displayName in $ancestorClsList and i.oid=$instanceOid RETURN t ");
        List<Record> recordList = this.run(cypher.toString(), map).list();

        if(recordList != null && recordList.size() > 0)
            return (String) recordList.get(0).get("t").asMap().getOrDefault("displayName", null);

        return "";
    }

    public  String  queryDingProcessOrderId(String processInstanceId) {

        Map<String, Object> params = new HashMap();
        params.put("processInstanceId", processInstanceId);
        StringBuffer cypher = new StringBuffer();
        cypher.append("match(p:DingTaskRecord) where p.dingProcessInstanceId =  $processInstanceId  return p");
        Result run = run(cypher.toString(), params);
        List<Record> recordList = run.list();

        String pdmProcessOrderId = recordList.stream().map(it -> {
            Map<String, Object> departMap = Optional.ofNullable(it.get("p")).map(depart -> depart.asMap()).orElse(Collections.emptyMap());
            return (String) departMap.getOrDefault("pdmProcessOrderId", "");
        }).collect(Collectors.joining(""));
        return pdmProcessOrderId;
    }

    @Override
    public void updateLifeStatus(String type, List<String> oidList, String lifecycleStatus) {
        StringBuffer cypher = new StringBuffer();
        Map<String, Object> map = new HashMap();
        map.put("oidList", oidList);
        map.put("lifecycleStatus", lifecycleStatus);
        cypher.append("match (n:").append(type).append(") where n.oid in $oidList set n" +
                ".lifecycleStatus=$lifecycleStatus ");
        this.excute(cypher.toString(), map);
    }

    @Override
    public void updateContainerModelDefinition(Collection<String> oidList, String containerModelDefinition) {
        StringBuffer cypher = new StringBuffer();
        Map<String, Object> map = new HashMap();
        map.put("oidList", oidList);
        map.put("containerModelDefinition", containerModelDefinition);
        cypher.append("match (n:").append(DocumentIteration.TYPE).append(") where n.oid in $oidList  set n" +
                ".containerModelDefinition = $containerModelDefinition ");
        this.excute(cypher.toString(), map);
    }

    @Override
    public Map<String, String> queryRepeatOidLink() {
        StringBuffer cypher = new StringBuffer();
        cypher.append("MATCH (m:ProcessOrder)-[r:REVIEW_FOR]->(s:DocumentIteration) ");
        cypher.append("WITH m, s, COUNT(r) AS relCount WHERE relCount > 1 RETURN m.oid as proOid,s.oid as docOid");
        Result run = run(cypher.toString(), new HashMap<>());
        Map<String,String> result = new HashMap<>();
        Record next;
        while (run.hasNext()) {
            next = run.next();
            String proOid = next.get("proOid","");
            String docOid = next.get("docOid","");
            if(StringUtils.isNotBlank(proOid) && StringUtils.isNotBlank(docOid)) {
                result.put(proOid,docOid);
            }
        }
        return result;
    }

    @Override
    public DocumentIteration dynamicQueryByFrom(String processOrderOid) throws JWIException {
        /*Map<String, Object> params = new HashMap();
        params.put("processOrderOid", processOrderOid);
        StringBuffer cypher = new StringBuffer();
        cypher.append("match(p:DocumentIteration) where p.oid =  $processOrderOid  return p");
        Result run = run(cypher.toString(), params);
        List<Record> recordList = run.list();

   *//*     String pdmProcessOrderId = recordList.stream().map(it -> {
            Map<String, Object> departMap = Optional.ofNullable(it.get("p")).map(depart -> depart.asMap()).orElse(Collections.emptyMap());
            return (String) departMap.getOrDefault("pdmProcessOrderId", "");
        }).collect(Collectors.joining(""));
        return pdmProcessOrderId;*//*
        List<Map<String, Object>> recordMapList = run.list().stream().map(record -> record.get("p").asMap()).collect(Collectors.toList());
        List<DocumentIteration> priSecFileList = cvtNormalService.json2TNoValRef(recordMapList, DocumentIteration.class);

        return priSecFileList;*/
        StringBuffer cypher = new StringBuffer();
        Map<String, Object> map = new HashMap();
        map.put("processOrderOid", processOrderOid);
/*        cypher.append("match (n").append(this.dealLabel("Document")).append(")-[r:").append("ITERATE").append("]->");
        cypher.append("(m").append(this.entityLabelCypher).append(") where m.oid=$oid with m,n match (n").append(this.dealLabel("Document"));
        cypher.append(")-[r:").append("ITERATE").append("]->");
        cypher.append("(s").append(this.entityLabelCypher).append(") where s.viewOid=m.viewOid and s.latest = true with s optional match ");
        cypher.append("(s) -[r:HAS]->(c:").append("ClsAttrContent").append(") with s,c optional match ");
        cypher.append("(s) -[r1:HAS]-(e:").append("PropertyGroup").append(") return apoc.map.merge(s,{clsProperty:c,extensionContent:e}) as s");*/
        cypher.append("match(p:DocumentIteration) where p.oid =  $processInstanceId  return p");

        List<DocumentIteration> list = this.excuteList(cypher.toString(), map, "p", DocumentIteration.class);
        return CollectionUtil.isEmpty(list) ? null : (DocumentIteration)list.get(0);
    }

    @Override
    public List<PathDo> queryAllPath(List<String> nodeOidList, String type) {
        StringBuffer cypher = new StringBuffer();
        Map<String, Object> params = new HashMap();
        params.put("oidList", nodeOidList);
        cypher.append("match (d:" + type + ")<-[:CONTAIN*0..]-(s) where d.oid in $oidList and s.name is not null return s.name as name,s.type as type," +
                "s.oid as oid,s.catalogOid as parentOid");
        Result run = run(cypher.toString(), params);
        List<PathDo> result = new ArrayList<>();
        Record next;
        PathDo pathDo;
        while (run.hasNext()) {
            next = run.next();
            pathDo = new PathDo();
            pathDo.setName(next.get("name", StringUtils.EMPTY));
            pathDo.setType(next.get("type", StringUtils.EMPTY));
            pathDo.setOid(next.get("oid", StringUtils.EMPTY));
            pathDo.setParentOid(next.get("parentOid", StringUtils.EMPTY));
            result.add(pathDo);
        }
        return result;
    }


    public String queryName(String userPhone) {

        Map<String, Object> params = new HashMap();
        params.put("userPhone", userPhone);
        StringBuffer cypher = new StringBuffer();
        cypher.append("match(p:User) where p.phone contains  $userPhone  return p");
        Result run = run(cypher.toString(), params);


        List<Record> recordList = run.list();

        String departStr = recordList.stream().map(it -> {
            Map<String, Object> departMap = Optional.ofNullable(it.get("p")).map(depart -> depart.asMap()).orElse(Collections.emptyMap());
            return (String) departMap.getOrDefault("name", "");
        }).collect(Collectors.joining(""));
        return departStr;
    }

    public Delivery findByClsOidAndName(String catalogOid, String oid, String clsOid, String name, String modelDefinition, String parentOid) {
        StringBuffer cypher = new StringBuffer();
        Map<String, Object> map = new HashMap();
        UserDTO currentUser = SessionHelper.getCurrentUser();
        map.put("tenantOid", currentUser.getTenantOid());
        map.put("userOid", currentUser.getOid());
        map.put("catalogOid", catalogOid);
        map.put("oid", oid);
        map.put("parentOid", parentOid);
        map.put("clsOid", clsOid);
        map.put("name", name);
        map.put("modelDefinition", modelDefinition);
        if(StringUtils.isEmpty(parentOid)){
            cypher.append("match (s").append(this.dealLabel("Delivery")).append(")");
            cypher.append(" where s.tenantOid=$tenantOid and s.catalogOid =$catalogOid and s.modelDefinition =$modelDefinition");
        }else{
            cypher.append("match (p)-[r]->(s").append(this.dealLabel("Delivery")).append(")");
            cypher.append(" where s.tenantOid=$tenantOid and s.catalogOid =$catalogOid and s.modelDefinition =$modelDefinition and p.oid=$parentOid");
        }

        if (StringUtils.isNotBlank(oid)) {
            cypher.append(" and s.oid <> $oid");
        }

        if (StringUtils.isBlank(name) && StringUtils.isBlank(clsOid)) {
            return null;
        } else {
            if (StringUtils.isNotBlank(name) && StringUtils.isNotBlank(clsOid)) {
                cypher.append(" and (s.name = $name or s.clsOid = $clsOid)");
            } else if (StringUtils.isNotBlank(name)) {
                cypher.append(" and s.name = $name");
            } else if (StringUtils.isNotBlank(clsOid)) {
                cypher.append(" and s.clsOid = $clsOid");
            }

            cypher.append(" return s");
            return (Delivery)this.excuteOne(cypher.toString(), map, "s", Delivery.class);
        }
    }

    @Override
    public DingTaskRecord queryDingTaskRecored(String documentOid) throws JWIException {
        StringBuffer cypher = new StringBuffer();
        Map<String, Object> map = new HashMap();
        map.put("documentOid", documentOid);
        cypher.append("match(p:DingTaskRecord) where $documentOid in p.documentIterationOidList return p ");

        List<DingTaskRecord> list = this.excuteList(cypher.toString(), map, "p", DingTaskRecord.class);
        return CollectionUtil.isEmpty(list) ? null : list.get(0);
    }

    @Override
    public Map queryEcrDingTaskRecored(String documentOid) throws JWIException {
        StringBuffer cypher = new StringBuffer();
        Map<String, Object> map = new HashMap();
        map.put("documentOid", documentOid);
        cypher.append("match(p:DingTaskRecord) where $documentOid in p.changeDocumentIterationOidList and p.isChange=true and (p.result is null or not p.result in ['agree', 'terminate', 'refuse','error']) return p ");

        List<Record> list = this.run(cypher.toString(), map).list();
//        List<DingTaskRecord> list = this.excuteList(cypher.toString(), map, "p", DingTaskRecord.class);
        return CollectionUtil.isEmpty(list) ? null : list.get(0).get("p").asMap();
    }

    @Override
    public List<CustomerDeliveryTreeNode> querySelectDeliveryTree(String containerOid, String userOid) {
        StringBuffer cypher = new StringBuffer();
        cypher.append("match (d:Delivery)-[r:CONTAIN*0..]->(c:Delivery) where d.containerOid = $containerOid and d" +
                ".root = true with c,r,d ");
        cypher.append("optional match (c)-[:ATTRIBUTE]->(f) where c.modelDefinition = 'Category' return d.oid as " +
                "parentOid,c.modelDefinition as modelDefinition,c.modelIcon as modelIcon,c.name as name,c.oid as oid," +
                " f");
        Map<String, Object> params = new HashMap();
        params.put("containerOid", containerOid);
        Result run = run(cypher.toString(), params);
        List<CustomerDeliveryTreeNode> result = new ArrayList<>();
        Record next;
        CustomerDeliveryTreeNode deliveryTreeNode;
        while (run.hasNext()) {
            next = run.next();
            deliveryTreeNode = new CustomerDeliveryTreeNode();
            deliveryTreeNode.setName(next.get("name", StringUtils.EMPTY));
            deliveryTreeNode.setOid(next.get("oid", StringUtils.EMPTY));
            deliveryTreeNode.setParentOid(next.get("parentOid", StringUtils.EMPTY));
            deliveryTreeNode.setModelDefinition(next.get("modelDefinition", StringUtils.EMPTY));
            deliveryTreeNode.setModelIcon(next.get("modelIcon", StringUtils.EMPTY));

            if (!StringUtils.equals(deliveryTreeNode.getModelDefinition(), "Category")) {
                result.add(deliveryTreeNode);
            } else {
                Map map = next.get("f").asMap();
                if (ObjectUtils.isNotEmpty(map)) {
                    String uOid = getOidFromExPro((JSONObject) JSONObject.toJSON(map));
                    boolean isFzrEmpty = getFzrIsEmpty((JSONObject) JSONObject.toJSON(map));
                    if (StringUtils.isBlank(userOid) || StringUtils.equals(uOid, userOid) || isFzrEmpty) {
                        result.add(deliveryTreeNode);
                    }
                }
            }
        }
        return result;
    }

    @Override
    public PageResult<ProcessOrderDetail> queryProcessOrderByCreator(String createBy, int index, int size) {
        StringBuffer cypher = new StringBuffer();
        cypher.append("call { match (p:ProcessOrder) where p.createBy = $createBy " +
                "return p.name as name,p.number as number,p.oid as oid,p.processStartTime as processStartTime,p" +
                ".createDate as createDate,p.processInstanceId as processInstanceId,p.updateDate as updateDate," +
                "p.type as type,p.processState as processState ,p.createBy as createBy union match (d:DingTaskRecord)" +
                " where d.createBy = $createBy and ( d.teamContent IS not NULL AND size(d.teamContent) > 0 ) return" +
                " d.dingProcessName as name," +
                "'' " +
                "as number,d.oid as oid,d.createDate as processStartTime,d.createDate as createDate," +
                "d.dingProcessInstanceId as processInstanceId,d.updateDate as updateDate,d.type as type,d.result as " +
                "processState ,d.createBy as createBy " +
                "} ");
        StringBuffer cntCypher = new StringBuffer(cypher);
        cntCypher.append(" return count(oid) as cnt ");
        cypher.append(" return name,number,oid,processStartTime,createDate,processInstanceId,updateDate,type," +
                "processState,createBy");
        cypher.append(" order by createDate desc");
        cypher.append(this.dealPage(size, index));
        Map<String, Object> params = new HashMap();
        params.put("createBy", createBy);
        List<ProcessOrderDetail> result = new ArrayList<>();
        Result run = run(cypher.toString(), params);
        Record next;
        ProcessOrderDetail processOrder;
        while (run.hasNext()) {
            next = run.next();
            processOrder = new ProcessOrderDetail();
            processOrder.setName(next.get("name", StringUtils.EMPTY));
            processOrder.setCreateDate(next.get("createDate", 0L));
            processOrder.setUpdateDate(next.get("updateDate", 0L));
            processOrder.setProcessStartTime(next.get("processStartTime", 0L));
            processOrder.setProcessInstanceId(next.get("processInstanceId", StringUtils.EMPTY));
            processOrder.setNumber(next.get("number", StringUtils.EMPTY));
            processOrder.setOid(next.get("oid", StringUtils.EMPTY));
            processOrder.setType(next.get("type", StringUtils.EMPTY));
            processOrder.setProcessState(next.get("processState", StringUtils.EMPTY));
            processOrder.setCreateBy(next.get("createBy", StringUtils.EMPTY));
            result.add(processOrder);
        }
        Long cnt = this.excuteCount(cntCypher.toString(), params, "cnt");
        return PageResult.init(cnt.intValue(), index, size, result);
    }

    @Override
    public PageResult<ProcessOrderDetail> fuzzyPageByBiz(ProOrdOfBizObjFuzzyPageDTO dto) {
        StringBuffer cypher = new StringBuffer();
        cypher.append("call {match (p:ProcessOrder)-[:REVIEW_FOR]->(m) where m.oid = $bizOid return p.name as name,p.number" +
                " as number,p.oid as oid,p.processStartTime as processStartTime," +
                "p.createDate as createDate,p.processInstanceId as processInstanceId,p.updateDate as updateDate,p.type as type,p.processState as processState ," +
                "p.createBy as createBy union match (d:DingTaskRecord) ");
        if(DocumentIteration.TYPE.equals(dto.getBizType())) {
            cypher.append("where Any(oid in  d.documentIterationOidList where oid = $bizOid)");
            cypher.append(" or Any(oid in  d.changeDocumentIterationOidList where oid = $bizOid)");
        } else if (ECADIteration.TYPE.equals(dto.getBizType())) {
            cypher.append("where Any(oid in  d.documentIterationOidList where oid = $bizOid)");
        }  else {
            cypher.append("where Any(oid in  d.partInterationOidList where oid = $bizOid)");
        }
        cypher.append(
                " return  d.dingProcessName as name,'' as number,d.oid as oid,d.createDate as processStartTime,d.createDate as createDate,d.dingProcessInstanceId as processInstanceId,d.updateDate as updateDate,d.type as type,d.result as processState ,d.createBy as createBy }");

        StringBuffer cntCypher = new StringBuffer(cypher);
        cntCypher.append(" return count(oid) as cnt ");
        cypher.append(" return name,number,oid,processStartTime,createDate,processInstanceId,updateDate,type," +
                "processState,createBy");
        cypher.append(" order by createDate desc");
        cypher.append(this.dealPage(dto.getSize(), dto.getIndex()));
        Map<String, Object> params = new HashMap();
        params.put("bizOid", dto.getBizOid());
        List<ProcessOrderDetail> result = new ArrayList<>();
        Result run = run(cypher.toString(), params);
        Record next;
        ProcessOrderDetail processOrder;
        while (run.hasNext()) {
            next = run.next();
            processOrder = new ProcessOrderDetail();
            processOrder.setName(next.get("name", StringUtils.EMPTY));
            processOrder.setCreateDate(next.get("createDate", 0L));
            processOrder.setUpdateDate(next.get("updateDate", 0L));
            processOrder.setProcessStartTime(next.get("processStartTime", 0L));
            processOrder.setProcessInstanceId(next.get("processInstanceId", StringUtils.EMPTY));
            processOrder.setNumber(next.get("number", StringUtils.EMPTY));
            processOrder.setOid(next.get("oid", StringUtils.EMPTY));
            processOrder.setType(next.get("type", StringUtils.EMPTY));
            processOrder.setProcessState(next.get("processState", StringUtils.EMPTY));
            processOrder.setCreateBy(next.get("createBy", StringUtils.EMPTY));
            result.add(processOrder);
        }
        Long cnt = this.excuteCount(cntCypher.toString(), params, "cnt");
        return PageResult.init(cnt.intValue(), dto.getIndex(), dto.getSize(), result);
    }
    private Boolean getFzrIsEmpty(JSONObject exProperty) {
        boolean flag = false;
        if (ObjectUtils.isNotEmpty(exProperty) && exProperty.containsKey("FZR")) {
            JSONObject uInfo = exProperty.getJSONObject("FZR");
            if (ObjectUtils.isEmpty(uInfo) || StringUtils.isBlank(uInfo.toString())) {
                flag = true;
            }
        } else if (exProperty.containsKey("FZR") && StringUtils.isBlank(exProperty.getString("FZR"))) {
            // 处理FZR属性值为空白字符串的情况
            flag = true;
        }
        return flag;
    }

    private String getOidFromExPro(JSONObject exProperty) {
        if(ObjectUtils.isNotEmpty(exProperty) && exProperty.containsKey("FZR")) {
            JSONObject uInfo = exProperty.getJSONObject("FZR");
            if(ObjectUtils.isNotEmpty(uInfo) && uInfo.containsKey("oid")) {
                return uInfo.getString("oid");
            }
        }
        return null;
    }

    public void updateDeliveryExtension(String oid, Object FZR, Object jhsj, Object wcsj){
        StringBuffer cypher = new StringBuffer();
        HashMap<String, Object> paramMap = new HashMap<>();
        if(FZR != null || jhsj != null || wcsj != null){
            paramMap.put("oid", oid);
            cypher.append(" match (s:Delivery)-[:ATTRIBUTE]->(p) where s.oid = $oid set");
            if(FZR != null){
                paramMap.put("FZR", FZR);
                cypher.append(" p.FZR = $FZR, ");
            }

            if(jhsj != null){
                paramMap.put("jhsj", jhsj);
                cypher.append(" p.jhsj = $jhsj, ");
            }

            if(wcsj != null){
                paramMap.put("wcsj", wcsj);
                cypher.append(" p.wcsj = $wcsj, ");
            }

            String cypherStr = cypher.deleteCharAt(cypher.length() - 2).toString();
            Result run = this.run(cypherStr, paramMap);
        }
    }

    public List<CustomerFolderTreeNode> searchFoldersWithPermisson(String containerType, String containerModel, String containerOid, String searchKey) throws JWIException {
        Map<String, Object> params = new HashMap();
        UserDTO currentUser = SessionHelper.getCurrentUser();
        params.put("tenantOid", currentUser.getTenantOid());
        params.put("userOid", currentUser.getOid());
        StringBuffer cypher = new StringBuffer();
        cypher.append("Match (p:").append(containerType).append(")-[").append(this.dealLabel("CONTAIN")).append("]->(f").append(this.dealLabel("Folder")).append(")").append(" where p.tenantOid=$tenantOid ");
        if (StringUtil.isNotBlank(containerModel)) {
            params.put("containerModel", containerModel);
            cypher.append(" and p.modelDefinition=$containerModel ");
        }

        if (StringUtil.isNotBlank(containerOid)) {
            params.put("containerOid", containerOid);
            cypher.append(" and p.oid=$containerOid ");
        }

        if (!currentUser.isTenantAdmin() && !currentUser.isSystemAdmin()) {
            cypher.append(" and (not p.privateFlag or (p)-[:").append("ASSIGN").append("]->(:").append("Team").append(")-[:").append("CONTAIN").append("]->(:").append("TeamRole").append(")-[:").append("CONTAIN").append("]->(:").append("User").append("{oid:$userOid}))");
        }

        cypher.append(" with p,f Match path=(f)-[r").append(this.dealLabel("CONTAIN")).append("*0..]->(s").append(this.dealLabel("Folder")).append(")");
        if (StringUtil.isNotBlank(searchKey)) {
            params.put("searchKey", this.dealSearchKey(searchKey));
            cypher.append(" where s.name=~$searchKey or s.description=~$searchKey ");
        }

        cypher.append(" return nodes(path) as nodes order by p.updateDate desc");
        Result run = this.run(cypher.toString(), params);
        return nodes2Tree(run, CustomerFolderTreeNode.class);
    }

    public List<ClsLayout> queryLayoutByClassCode(Collection<String> clsCodeList) {
        Map<String, Object> params = new HashMap();
        params.put("clsCodeList", clsCodeList);
        StringBuffer cypher = new StringBuffer();
        cypher.append("match (c:Classification)-[:LAYOUT_CONTROL]->(l:ClsLayout) where l.code = 'create' and c.code in $clsCodeList return l");
        return this.excuteList(cypher.toString(),params,"l", ClsLayout.class);
    }

    public PageResult<PermApplyEntity> queryPerm(PermApplyEntity permApplyEntity){
        Map<String, Object> params = new HashMap();
        StringBuffer cypher = new StringBuffer();


        cypher.append(" MATCH (n:PermApply) where 1 = 1 ");

        if("sys_admin".equals(permApplyEntity.getCreateBy())){

        } else if(permApplyEntity.getCreateBy() != null)
            cypher.append(" and n.createBy = '" + permApplyEntity.getCreateBy() + "' ");
        else
            cypher.append(" and n.createBy = '" + Optional.ofNullable(SessionHelper.getCurrentUser()).map(user -> user.getAccount()).orElse("") + "' ");

        if(permApplyEntity.getStatus() != null)
            cypher.append(" and n.status = '" + permApplyEntity.getStatus() + "' ");

        if(permApplyEntity.getFolderName() != null)
            cypher.append(" and '" + permApplyEntity.getFolderName().get(0) + "' in n.folderName");

        if(permApplyEntity.getPermission() != null){
            String permission = "只读".equals(permApplyEntity.getPermission()) ? "0" : "可下载".equals(permApplyEntity.getPermission()) ? "1" : permApplyEntity.getPermission();
            cypher.append(" and n.permission = '" + permission + "' ");
        }

        StringBuffer cntCypher = new StringBuffer(cypher);
        cntCypher.append(" return count(n) as cnt");

        cypher.append(" RETURN n order by n.createDate desc skip "
                + (permApplyEntity.getIndex() - 1) + " LIMIT " + permApplyEntity.getIndex() * permApplyEntity.getSize() + " ");

        Long cnt = excuteCount(cntCypher.toString(), params, "cnt");

        List<PermApplyEntity> rows = excuteList(cypher.toString(), params, "n", PermApplyEntity.class);
        rows.stream().map(it -> {
            String permission = it.getPermission();
            it.setPermission("0".equals(permission) ? "只读" : "1".equals(permission) ? "可下载" : permission);
//            return it.getFolderName().stream().map(f -> {
//                try {
//                    return it.clone().setNodeName(f);
//                } catch (CloneNotSupportedException e) {
//                    log.error(e.getMessage(), e);
//                }
//                return null;
//            });
            return it;
        }).collect(Collectors.toList());


        return PageResult.init(cnt.intValue(), permApplyEntity.getIndex(), permApplyEntity.getSize(), rows);
    }

    @Override
    public List<UserRoleInfo> queryUserRoleInfo(String account) {
        Map<String, Object> params = new HashMap();
        params.put("account",account);
        StringBuffer cypher = new StringBuffer();
        cypher.append("match (u:User)<-[c:CONTAIN]-(tr:TeamRole)<-[:CONTAIN]-(t:Team)-[:ASSIGN]-(m) where u" +
                ".account = $account return m.oid as catalogOid,m.type as catalogType ,tr.name as roleName,tr" +
                ".sourceOid as roleSourceId, tr.oid as roleOid,u.oid as userOid,t.oid as teamOid");
        Result run = run(cypher.toString(), params);
        Record next;
        List<UserRoleInfo> result = new ArrayList<>();
        UserRoleInfo userRoleInfo;
        while (run.hasNext()) {
            next = run.next();
            userRoleInfo = new UserRoleInfo();
            userRoleInfo.setRoleName(next.get("roleName", StringUtils.EMPTY));
            userRoleInfo.setRoleOid(next.get("roleOid", StringUtils.EMPTY));
            userRoleInfo.setCatalogOid(next.get("catalogOid", StringUtils.EMPTY));
            userRoleInfo.setCatalogType(next.get("catalogType", StringUtils.EMPTY));
            userRoleInfo.setUserOid(next.get("userOid", StringUtils.EMPTY));
            userRoleInfo.setTeamOid(next.get("teamOid", StringUtils.EMPTY));
            userRoleInfo.setRoleSourceId(next.get("roleSourceId", StringUtils.EMPTY));
            result.add(userRoleInfo);
        }
        return result;
    }

    @Override
    public List<UserRoleInfo> queryVisitorRoleInFolder(Collection<String> folderOidList) {
        Map<String, Object> params = new HashMap();
        params.put("folderOidList",folderOidList);
        StringBuffer cypher = new StringBuffer();
        cypher.append("MATCH (tr:TeamRole)<-[:CONTAIN]-(t:Team)-[:ASSIGN]-(m) WHERE m.oid IN $folderOidList " +
                " and tr.name='cn_jwis_visitor' RETURN m.oid as catalogOid,m.type as catalogType,t.oid as teamOid," +
                " tr.sourceOid as roleSourceId,tr.name as roleName,tr.oid as roleOid");
        Result run = run(cypher.toString(), params);
        Record next;
        List<UserRoleInfo> result = new ArrayList<>();
        UserRoleInfo userRoleInfo;
        while (run.hasNext()) {
            next = run.next();
            userRoleInfo = new UserRoleInfo();
            userRoleInfo.setRoleName(next.get("roleName", StringUtils.EMPTY));
            userRoleInfo.setRoleOid(next.get("roleOid", StringUtils.EMPTY));
            userRoleInfo.setCatalogOid(next.get("catalogOid", StringUtils.EMPTY));
            userRoleInfo.setCatalogType(next.get("catalogType", StringUtils.EMPTY));
            userRoleInfo.setTeamOid(next.get("teamOid", StringUtils.EMPTY));
            userRoleInfo.setRoleSourceId(next.get("roleSourceId", StringUtils.EMPTY));
            result.add(userRoleInfo);
        }
        return result;
    }

    @Override
    public long updateDingTalkRecordDocList(String oid, List<String> docOidList) {
        Map<String, Object> params = new HashMap();
        params.put("oid",oid);
        params.put("docOidList",docOidList);
        StringBuffer cypher = new StringBuffer();
        cypher.append("match (d:DingTaskRecord)  where d.oid = $oid set d.documentIterationOidList = $docOidList");
        return this.excuteCount(cypher.toString(),params);
    }

    @SneakyThrows
    @Override
    public List<TreeAbleEx> searchUserTree(String tenantOid, Condition condition) {
        List<TreeAbleEx> result = new LinkedList<>();
        Map<String, Object> map = new HashMap<>();
        map.put("tenantOid",tenantOid);
        StringBuffer cypher = new StringBuffer();
        cypher.append("MATCH (l)-[b:BELONG_TO]->(r)" +
                "OPTIONAL MATCH (r:USER)-[:BELONG]->(t:Tenant)" +
                "WHERE (r:USER AND t IS NOT NULL AND t.oid = $tenantOid");
        if(condition != null) {
            cypher.append(" and ").append(condition.parse(new ConditionParserNeo4j("l", map)));
        }
        cypher.append(") OR NOT r:USER RETURN l,b.toOid as positionOid");
        Result run = run(cypher.toString(), map);
        Map<String,Map<String, Object>> positionOidNameMap = new HashMap<>();
        List<UserTreeAbleEx> userList = new ArrayList<>();
        while (run.hasNext()) {
            Record next = run.next();
            Value value = next.get("l");
            Map<String, Object> itemMap = value.asMap();
            if(Position.TYPE.equals(itemMap.get("type").toString())) {
                positionOidNameMap.put(itemMap.get("oid").toString(),itemMap);
            } else if (User.TYPE.equalsIgnoreCase(itemMap.get("type").toString())) {
                itemMap = new HashMap<>(itemMap);
                itemMap.put("parentNodeId", next.get("positionOid").asString());
                itemMap.put("currentNodeId", itemMap.get("oid"));
                itemMap.put("displayName", itemMap.get("name"));
                UserTreeAbleEx userTreeAbleEx = BeanUtil.map2bean(itemMap,UserTreeAbleEx.class);
                userList.add(userTreeAbleEx);
                result.add(userTreeAbleEx);
            } else {
                TreeAbleEx treeAbleEx = (TreeAbleEx) BeanUtil.map2bean(itemMap, TYPE_CLASS_MAP.get(itemMap.get("type")));
                result.add(treeAbleEx);
            }
        }
        for (UserTreeAbleEx userTreeAbleEx : userList) {
            Map<String,Object> positionMap = positionOidNameMap.get(userTreeAbleEx.getParentNodeId());
            userTreeAbleEx.setPositions(Lists.newArrayList(positionMap.get("displayName").toString()));
            userTreeAbleEx.setParentNodeId(positionMap.get("parentOid").toString());
        }
        return result;
    }

    @Override
    public String queryClsRelCodeByOid(String oid) {
        Map<String, Object> map = new HashMap<>();
        map.put("oid",oid);
        StringBuffer cypher = new StringBuffer();
        cypher.append("match (c:Classification)<-[:SUB*0..]-(s:Classification) where c.oid = $oid and s.code <> 'cn_jwis_part' return s");
        List<Classification> list = this.excuteList(cypher.toString(),map,"s",Classification.class);
        Collections.reverse(list);
        return list.stream()
                .map(Classification::getClsCode) // 替换为你要组合的属性
                .collect(Collectors.joining(""));
    }

    @Override
    public <T> List<T> findRelationByTo(String fromType, String relType, String toType, Collection<String> toOidCollection) {
        Map<String, Object> map = new HashMap();
        map.put("toOid", toOidCollection);
        StringBuffer cypher = new StringBuffer();
        cypher.append(" match (f:").append(fromType).append(")-[r:").append(relType).append("]->(s:").append(toType).append(") where s.oid in $toOid return r ");
        return SchemaClassMapping.transformRel(this.excuteList(cypher.toString(), map, "r"));
    }

    @Override
    public Map<String,List<String>> findRelationBothSideByTo(String fromType, String relType, String toType, Collection<String> toOidCollection) {
        Map<String, Object> map = new HashMap();
        map.put("toOid", toOidCollection);
        StringBuffer cypher = new StringBuffer();
        cypher.append(" match (f:").append(fromType).append(")-[r:").append(relType).append("]-(s:").append(toType).append(") where s.oid in $toOid return f.oid as fromOid,s.oid as toOid");
        Result run = run(cypher.toString(), map);

        Map<String,List<String>> result = new HashMap<>();
        while (run.hasNext()) {
            Record next = run.next();
            String toOid = next.get("toOid").asString();
            String fromOid = next.get("fromOid").asString();
            result.computeIfAbsent(toOid,k->Lists.newArrayList()).add(fromOid);
        }
        return result;
    }

    @Override
    public List<Classification> queryTreeByChildOid(String oid) {
        Map<String,Object> map = new HashMap<>();
        map.put("oid",oid);
        StringBuffer cypher = new StringBuffer();
        cypher.append("match (c:Classification)<-[:SUB*0..]-(p:Classification) where c.oid = $oid return p");
        return this.excuteList(cypher.toString(), map, "p", Classification.class);
    }

    @Override
    public List<CustomerDeliveryTreeNode> querySelectDeliveryTreeNew(String containerOid, String userOid) {
        StringBuilder cypher = new StringBuilder();
        cypher.append("CALL { ")
                .append("MATCH (s:Delivery) <- [:CONTAIN] - (m:Container) ")
                .append("WHERE m.oid = $containerOid AND s.root = true ")
                .append("RETURN s ORDER BY id(s) ASC ")
                .append("} ")
                .append("WITH s ")
                .append("MATCH path=(s)-[r:CONTAIN*0..]->(i:Delivery) ")
                .append("WITH nodes(path) AS nodes, i ")
                .append("OPTIONAL MATCH (i)-[:REFERENCE]->(master)-[:ITERATE]->(obj) ")
                .append("WHERE obj.latest = true  ")
//                .append(" AND ( ")
//                .append("obj.lockOwnerOid IS NULL OR ")
//                .append("(obj.lockOwnerOid = $userOid AND obj.lockSourceOid IS NOT NULL) OR ")
//                .append("(obj.lockOwnerOid <> $userOid AND obj.lockSourceOid IS NULL) ")
//                .append(") ")
                .append("OPTIONAL MATCH (i)-[:ATTRIBUTE]->(f) WHERE i.modelDefinition = 'Category'")
                .append("WITH i, ")
                .append("CASE WHEN size(nodes) > 1 THEN nodes[-2].oid ELSE i.oid END AS parentOid, ")
                .append("obj, f ")
                .append("RETURN i.oid AS oid, ")
                .append("parentOid, ")
                .append("i.name AS name, ")
                .append("i.modelDefinition AS modelDefinition, ")
                .append("i.modelIcon AS modelIcon, ")
                .append("f");

        Map<String, Object> params = new HashMap<>();
        params.put("containerOid", containerOid);
        params.put("userOid", userOid);

        Result run = run(cypher.toString(), params);
        List<CustomerDeliveryTreeNode> result = new ArrayList<>();

        while (run.hasNext()) {
            Record next = run.next();
            CustomerDeliveryTreeNode deliveryTreeNode = new CustomerDeliveryTreeNode();
            deliveryTreeNode.setName(next.get("name", StringUtils.EMPTY));
            deliveryTreeNode.setOid(next.get("oid", StringUtils.EMPTY));
            deliveryTreeNode.setParentOid(next.get("parentOid", StringUtils.EMPTY));
            deliveryTreeNode.setModelDefinition(next.get("modelDefinition", StringUtils.EMPTY));
            deliveryTreeNode.setModelIcon(next.get("modelIcon", StringUtils.EMPTY));

            if (!StringUtils.equals(deliveryTreeNode.getModelDefinition(), "Category")) {
                result.add(deliveryTreeNode);
            } else {
                Map<String, Object> map = next.get("f").asMap();
                if (ObjectUtils.isNotEmpty(map)) {
                    JSONObject jsonObject = (JSONObject) JSONObject.toJSON(map);
                    String uOid = getOidFromExPro(jsonObject);
                    boolean isFzrEmpty = getFzrIsEmpty(jsonObject);
                    /*if (StringUtils.isBlank(userOid) || StringUtils.equals(uOid, userOid) || isFzrEmpty) {
                        result.add(deliveryTreeNode);
                    }*/
                    result.add(deliveryTreeNode);
                }
            }
        }
        return result;
    }

    @Override
    public List<CustomerDeliveryTreeNode> querySelectDeliveryTreeNewTwo(String containerOid, String userOid) {
        StringBuffer cypher = new StringBuffer();
        cypher.append("match (d:Delivery)-[r:CONTAIN*0..]->(c:Delivery) where d.containerOid = $containerOid and d" +
                ".root = true with c,r,d ");
        cypher.append("optional match (c)-[:ATTRIBUTE]->(f) where c.modelDefinition = 'Category' return d.oid as " +
                "parentOid,c.modelDefinition as modelDefinition,c.modelIcon as modelIcon,c.name as name,c.oid as oid," +
                " f");
        Map<String, Object> params = new HashMap();
        params.put("containerOid", containerOid);
        Result run = run(cypher.toString(), params);
        List<CustomerDeliveryTreeNode> result = new ArrayList<>();
        Record next;
        CustomerDeliveryTreeNode deliveryTreeNode;
        while (run.hasNext()) {
            next = run.next();
            deliveryTreeNode = new CustomerDeliveryTreeNode();
            deliveryTreeNode.setName(next.get("name", StringUtils.EMPTY));
            deliveryTreeNode.setOid(next.get("oid", StringUtils.EMPTY));
            deliveryTreeNode.setParentOid(next.get("parentOid", StringUtils.EMPTY));
            deliveryTreeNode.setModelDefinition(next.get("modelDefinition", StringUtils.EMPTY));
            deliveryTreeNode.setModelIcon(next.get("modelIcon", StringUtils.EMPTY));

            if (!StringUtils.equals(deliveryTreeNode.getModelDefinition(), "Category")) {
                result.add(deliveryTreeNode);
            } else {
                Map map = next.get("f").asMap();
                if (ObjectUtils.isNotEmpty(map)) {
                    String uOid = getOidFromExPro((JSONObject) JSONObject.toJSON(map));
                    boolean isFzrEmpty = getFzrIsEmpty((JSONObject) JSONObject.toJSON(map));
                    if (StringUtils.isBlank(userOid) || StringUtils.equals(uOid, userOid) || isFzrEmpty) {
                        result.add(deliveryTreeNode);
                    }
                }
            }
        }
        return result;
    }

}
