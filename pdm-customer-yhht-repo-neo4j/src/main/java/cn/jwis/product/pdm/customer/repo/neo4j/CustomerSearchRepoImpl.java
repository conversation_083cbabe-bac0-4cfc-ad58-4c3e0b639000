package cn.jwis.product.pdm.customer.repo.neo4j;

import cn.jwis.framework.base.dto.UserDTO;
import cn.jwis.framework.base.exception.JWIException;
import cn.jwis.framework.base.exception.JWIRepoException;
import cn.jwis.framework.base.response.PageResult;
import cn.jwis.framework.base.util.Assert;
import cn.jwis.framework.base.util.CollectionUtil;
import cn.jwis.framework.base.util.SpringContextUtil;
import cn.jwis.framework.base.util.StringUtil;
import cn.jwis.framework.base.web.session.SessionHelper;
import cn.jwis.framework.database.core.query.dynamic.Condition;
import cn.jwis.framework.database.neo4j.condition.Neo4jEnableConditional;
import cn.jwis.framework.database.neo4j.execution.CommonEntityTemplate;
import cn.jwis.framework.database.neo4j.util.ConditionParserNeo4j;
import cn.jwis.framework.database.neo4j.util.JSONUtil;
import cn.jwis.platform.plm.dataengine.common.JWICommonService;
import cn.jwis.platform.plm.foundation.classification.entity.Classification;
import cn.jwis.platform.plm.foundation.classification.entity.ClsAttrContent;
import cn.jwis.platform.plm.foundation.classification.responce.ClsPropertyWithRel;
import cn.jwis.platform.plm.foundation.classification.service.ClassificationPropertyHelper;
import cn.jwis.platform.plm.foundation.context.entity.Tenant;
import cn.jwis.platform.plm.foundation.extendedproperty.entity.PropertyGroup;
import cn.jwis.platform.plm.foundation.relationship.Attribute;
import cn.jwis.platform.plm.foundation.relationship.Contain;
import cn.jwis.platform.plm.foundation.utils.ModelPropUtil;
import cn.jwis.platform.plm.search.engine.entity.AdvancedSearchCondition;
import cn.jwis.platform.plm.search.engine.entity.SearchHistory;
import cn.jwis.platform.plm.search.engine.repo.Neo4jSearchRepo;
import cn.jwis.platform.plm.search.engine.repo.SearchRepo;
import cn.jwis.platform.plm.search.engine.repo.SearchRepoImpl;
import com.alibaba.excel.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.collections4.CollectionUtils;
import org.neo4j.driver.Record;
import org.neo4j.driver.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Conditional;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * <AUTHOR> lhm
 * @Date ： Created in 2024/1/9 15:23
 * @Description :
 */
@Component
@Transactional(rollbackFor = Exception.class)
@Conditional({Neo4jEnableConditional.class})
@Primary
public class CustomerSearchRepoImpl extends CommonEntityTemplate<SearchHistory> implements SearchRepo {

    //系统属性
    public final String[] searchKeyStr = {"name","number","description","owner", "cname"};
    //拓展属性
    public final String[] epSearchKeyStr = {"cn_jwis_gg"};

    public final List<String> searchKey = Arrays.asList(searchKeyStr);

    public final List<String> epSearchKey = Arrays.asList(epSearchKeyStr);

    @Autowired
    Neo4jSearchRepo neo4jSearchRepo;

    //数量缓存
    private final Map<Integer, Long> countCatch = new ConcurrentHashMap<>();


    @Resource
    JWICommonService commonService;

    @Autowired
    private PlatformTransactionManager transactionManager;

    @Autowired
    private ClassificationPropertyHelper classificationPropertyHelper;
    @Override
    public Object rename(SearchHistory dto) {
        String tenantOid = SessionHelper.getCurrentUser().getTenantOid();
        return neo4jSearchRepo.rename(dto.getOid(),dto.getName(),tenantOid);
    }

    @Override
    public List<SearchHistory> queryByUserName() {
        String account = SessionHelper.getCurrentUser().getAccount();
        String tenantOid = SessionHelper.getCurrentUser().getTenantOid();
        return neo4jSearchRepo.queryByUserName(account,tenantOid);
    }

    @Override
    public Long deleteByUserName() {
        String account = SessionHelper.getCurrentUser().getAccount();
        String tenantOid = SessionHelper.getCurrentUser().getTenantOid();
        return neo4jSearchRepo.deleteByUserName(account,tenantOid);
    }

    @Override
    public JSONObject globalSearch(SearchHistory dto, JSONObject globalSearchObjectType, JSONObject globalSearchFullTextName) {
        JSONObject json = new JSONObject();
        if(StringUtils.isNotBlank(dto.getSearchType()) && !"all".equals(dto.getSearchType())){//不传和传“all”都是查所有类型
//            if(dto.hasInvalidSp(dto.getSearchKey())) {
//                JSONObject jsonObject = new JSONObject();
//                PageResult<JSONObject> result = new PageResult<>(0,dto.getIndex(),dto.getSize(),new ArrayList<>());
//                jsonObject.put(dto.getSearchTypeDisplayName(),result);
//                return jsonObject;
//            }
            json.put(dto.getSearchTypeDisplayName(), globalQueryLatest(dto,globalSearchFullTextName));
        }else{
            UserDTO currentUser = SessionHelper.getCurrentUser();
            globalSearchObjectType.entrySet().stream().parallel().forEach(entry -> {
                SessionHelper.addCurrentUser(currentUser);
                TransactionStatus status = transactionManager.getTransaction(new DefaultTransactionDefinition());
                try {
                    SearchHistory searchHistory = copyHistory(dto);
                    //初始化子类型
                    String key = entry.getKey();
                    String value = entry.getValue().toString();
                    searchHistory.setSearchType(value);
                    json.put(key,globalQueryLatest(searchHistory,globalSearchFullTextName));
                    transactionManager.commit(status);
                } catch (Exception e) {
                    transactionManager.rollback(status);
                    e.printStackTrace();
                    throw new RuntimeException(e);
                }
            });
        }
        return json;
    }

    private SearchHistory copyHistory(SearchHistory dto){
        return JSON.parseObject(JSON.toJSONString(dto), SearchHistory.class);
    }

    private PageResult<JSONObject> globalQueryLatest(SearchHistory dto,
                                                     JSONObject globalSearchFullTextName) {
        // 留待二次查询用
        SearchHistory reQuery = copyHistory(dto);
        StringBuffer cypher = new StringBuffer();
        Map<String, Object> param = new HashMap<>();

        // 外部约束：包括--> 分类约束， 搜索词约束， 类型约束等
        externalConstraintsParse(cypher, param, dto,globalSearchFullTextName);

        // 动态属性约束
        dynamicConstraintsParse(cypher, param, dto);

        // 冗余一份countCypher
        StringBuffer cntCypher = new StringBuffer(cypher);

        // 获取全量属性
        PageResult<JSONObject> result = getPageWithTotalProperties(cypher, param, dto);
        List<String> clsOids = result.getRows().stream().map(item -> item.getJSONObject("clsProperty") != null ? item.getJSONObject("clsProperty")
                .getString("clsOid") : null).filter(StringUtil::isNotBlank).collect(Collectors.toList());
        Map<String, List<ClsPropertyWithRel>> clsMap = classificationPropertyHelper.fuzzyByClsOidsRel(clsOids);
        for (JSONObject row : result.getRows()) {
            String cls = row.getJSONObject("clsProperty") != null ? row.getJSONObject("clsProperty")
                    .getString("clsOid") : null;
            if(StringUtil.isNotBlank(cls)){
                JSONObject clsProp = row.getJSONObject("clsProperty") != null ? row.getJSONObject("clsProperty") : new JSONObject();
                List<ClsPropertyWithRel> props = clsMap.get(cls) != null ? clsMap.get(cls) : new ArrayList<>();
                for (ClsPropertyWithRel prop : props) {
                    if(clsProp.containsKey(prop.getCode())){
                        Object str = ModelPropUtil.getPropTxt(prop, clsProp);
                        clsProp.put(prop.getCode(), str);
                    }
                }
            }
        }
        int cnt = (int) getGlobalSerchPageCount(cntCypher, param);
        result.setCount(cnt);
        return result;
    }



    private long getGlobalSerchPageCount(StringBuffer cypher, Map<String, Object> param) {
        // 统计数量
        cypher.append(" return count(n) as cnt ");
        int key = (cypher + JSON.toJSONString(param)).hashCode();
        if(countCatch.containsKey(key)){
            CompletableFuture.supplyAsync(() -> {
                TransactionStatus status = transactionManager.getTransaction(new DefaultTransactionDefinition());
                try {
                    SearchRepoImpl repo = SpringContextUtil.getBean(SearchRepoImpl.class);
                    long count = repo.excuteCount(cypher.toString(), param, "cnt");
                    countCatch.put(key, count);
                    transactionManager.commit(status);
                } catch (JWIException e) {
                    transactionManager.rollback(status);
                    throw new RuntimeException(e);
                }
                return "完成";
            });
            return countCatch.get(key);
        }else{
            long count = excuteCount(cypher.toString(), param, "cnt");
            countCatch.put(key, count);
            return count;
        }
    }

    // 获取全量属性
    private PageResult<JSONObject> getPageWithTotalProperties(StringBuffer cypher, Map<String, Object> param, SearchHistory dto) {
        List<JSONObject> rows = new LinkedList<>();

        // 节点，节点扩展属性， 节点分类属性
        cypher.append(" with n optional match  (n)-[:").append(Attribute.TYPE).append("]->(cp:")
                .append(ClsAttrContent.TYPE).append(") ").append("with n,cp  optional match (n)-[:").append(Attribute.TYPE)
                .append("]->(ep:").append(PropertyGroup.TYPE)
                .append(") return n, ep, cp order by n.updateDate desc ");
        // 分页
        int index = dto.getIndex();
        int size = dto.getSize();
        cypher.append(dealPage(size, index));
//        logger.info("全局搜索的语句：{}", cypher);
//        logger.info("全局搜索的参数：{}", param.toString());
        Result run = run(cypher.toString(), param);
        Record next = null;
        JSONObject row = null;
        while (run.hasNext()){
            next = run.next();
            row = JSONUtil.toJSON(next.get("n"));
            rows.add(row);
            row.put("extensionContent", JSONUtil.toJSON(next.get("ep")));
            row.put("clsProperty", JSONUtil.toJSON(next.get("cp")));
        }
        return PageResult.init(rows.size(), index, size, rows);
    }

    // 动态属性约束
    private void dynamicConstraintsParse(StringBuffer cypher, Map<String, Object> param, SearchHistory dto) {
        List<AdvancedSearchCondition> conditions = dto.getConditions();
        if (CollectionUtil.isEmpty(conditions)){
            return ;
        }
        String connector = dto.getConnector();
        String or = "or";
        cypher.append(" with n ");
        boolean orConnectFlag = or.equalsIgnoreCase(connector);
        boolean defaultValue = false;
        if (orConnectFlag){
            cypher.append(" optional ");
        }else {
            connector = "and";
            defaultValue = true;
        }
        // 节点，节点扩展属性， 节点分类属性
        cypher.append(" match (n) ");
        StringBuffer nFilter = new StringBuffer(), epFilter = new StringBuffer(), cpFilter = new StringBuffer(), temp;
        boolean epFlag = false, cpFlag = false;
        String nodeVar = null;
        // 开始解析
        Iterator<AdvancedSearchCondition> iterator = conditions.iterator();
        while (iterator.hasNext()){
            AdvancedSearchCondition next = iterator.next();
            String searchType = next.getSearchType();
            switch (searchType) {
                case "1":
                    nodeVar = "n";
                    temp = nFilter;
                    break ;
                case "2":
                    nodeVar = "ep";
                    epFlag = true;
                    temp = epFilter;
                    break ;
                case "3":
                    nodeVar = "cp";
                    cpFlag = true;
                    temp = cpFilter;
                    break ;
                default:
                    throw new JWIRepoException("unsupported value of searchType");
            }
            StringBuffer single = advancedSearchConditionParse(param, nodeVar, next);
            if (single.length()<2){
                temp.append(" ").append(connector).append(" ").append(defaultValue);
                continue;
            }
            temp.append(" ").append(connector).append(single);
        }
        if (epFlag){
            cypher.append(", (n)-[:").append(Attribute.TYPE).append("]->(ep:").append(PropertyGroup.TYPE)
                    .append(") ");
        }
        if (cpFlag){
            cypher.append(", (n)-[:").append(Attribute.TYPE).append("]->(cp:").append(ClsAttrContent.TYPE).append(") ");
        }
        if (orConnectFlag){
            cypher.append(" with n").append(epFlag? ", ep ":" ").append(cpFlag? ", cp ":" ")
                    .append(" match (n)").append(epFlag? ", (ep) ":" ").append(cpFlag? ", (cp) ":" ")
                    .append(" where ").append(defaultValue).append(" ").append(nFilter);
            if (epFlag){
                cypher.append(" or ( ep is not null and ( ")
                        .append(defaultValue).append(" ").append(epFilter).append(")) ");
            }
            if (cpFlag){
                cypher.append(" or ( cp is not null and ( ")
                        .append(defaultValue).append(" ").append(cpFilter).append(")) ");
            }

        }else {
            cypher.append(" where ").append(defaultValue).append(" ").append(nFilter)
                    .append(epFilter).append(cpFilter);
        }


    }


    private StringBuffer advancedSearchConditionParse(Map<String, Object> param, String nodeVar, AdvancedSearchCondition condition) {
        StringBuffer cypher = new StringBuffer(" ");
        Object searchKeyA = condition.getSearchKeyA();
        if (searchKeyA==null){
            return cypher;
        }
        String fieldName = condition.getName();
        String dbCol = nodeVar+"."+fieldName;
        String paramKey = fieldName;
        if (param.containsKey(paramKey)){
            paramKey = paramKey+ param.size();
        }
        Object searchKeyB = condition.getSearchKeyB();
        String connector = condition.getConnector();
        switch (connector) {
            case "contain":
                param.put(paramKey, dealSearchKey(searchKeyA==null?"":searchKeyA.toString()));
                cypher.append(" ").append(dbCol).append(" =~ $").append(paramKey);
                break;
            case "noContain":
                param.put(paramKey, dealSearchKey(searchKeyA==null?"":searchKeyA.toString()));
                cypher.append(" not ").append(dbCol).append(" =~ $").append(paramKey);
                break;
            case ">": case "<": case "=": case ">=": case "<=":
                param.put(paramKey, searchKeyA);
                cypher.append(" ").append(dbCol).append(" ").append(connector).append(" $").append(paramKey);
                break;
            case "≠":
                param.put(paramKey, searchKeyA);
                cypher.append(" not ").append(dbCol).append(" = $").append(paramKey);
                break;
            case "between":
                param.put(paramKey, searchKeyA);
                String paramKeyB = paramKey;
                if (param.containsKey(paramKeyB)){
                    paramKeyB = paramKeyB+ param.size();
                }
                param.put(paramKeyB, searchKeyB);
                cypher.append(" ( ").append(dbCol).append(" >= $").append(paramKey)
                        .append(" and ").append(dbCol).append(" <= $").append(paramKeyB).append(") ");
                break;
        }
        return cypher;
    }

    // 部约束：包括--> 分类约束， 搜索词约束， 类型约束， 上下文约束
    private void externalConstraintsParse(StringBuffer cypher, Map<String, Object> param,
                                          SearchHistory dto,JSONObject globalSearchFullTextName) {

        // 搜索词约束 ---> 使用全文索引
        searchKeyConstrainsParse(cypher, param, dto,globalSearchFullTextName);

        // 搜索词拓展属性约束
        searchKeyEpConstrainsParse(cypher, param, dto);

        // 分类约束
        clsConstaintsParse(cypher, param, dto);

        // 类型约束
        modelDefinitionConstrainsParse(cypher, param, dto);

        // 上下文约束
        contentConstrainsParse(cypher, param, dto);

        // 文件夹约束
        folderConstrainsParse(cypher, param, dto);

        // 模型特殊约束
        modelSpecialConstrainsParse(cypher, param, dto);

        // latest约束
        latestConstrainsParse(cypher, param, dto);

    }

    //关键字拓展属性【规格】搜索
    private void  searchKeyEpConstrainsParse(StringBuffer cypher, Map<String, Object> param, SearchHistory dto){
        String searchKey = dto.getSearchKey();
        if(StringUtil.isBlank(searchKey)){
            return;
        }

        String searchType = dto.getSearchType();

        StringBuffer cypherUnion=new StringBuffer();
        cypherUnion.append("call { ");


        cypher.append(" return n UNION optional match (nn:").append(searchType).append(")-[:ATTRIBUTE]->(ep:PropertyGroup) where nn.tenantOid=$tenantOid and ep is not null and (");

        int length = epSearchKeyStr.length;
        searchKey = ".*"+changeStr(searchKey)+".*";
//        param.put("epSearchKey",searchKey);
        for (int i = 0; i < length; i++) {
            if (i!=0){
                cypher.append(" OR ");
            }
            cypher.append("ep.").append(epSearchKeyStr[i]).append("=~\"").append(searchKey).append("\"");
        }
        cypher.append("  ) with nn as n return n } with n where n is not null");

        cypherUnion.append(cypher);

        cypher.setLength(0);

        cypher.append(cypherUnion);
    }

    // 模型特殊约束
    private void modelSpecialConstrainsParse(StringBuffer cypher, Map<String, Object> param, SearchHistory dto) {
        //如果类型是流程申请单，则加上过滤“草稿”状态
        if("ProcessOrder".equals(dto.getSearchType())){
            cypher.append(" and n.processState <> 'draft' ");
        }
    }

    // latest约束
    private void latestConstrainsParse(StringBuffer cypher, Map<String, Object> param, SearchHistory dto) {
        param.put("userOid", SessionHelper.getCurrentUser().getOid());
        cypher.append(" and (n.latest is null or n.latest=true )")
                .append(" and ( n.lockOwnerOid is null or (n.lockOwnerOid=$userOid and n.lockSourceOid is not null) ")
                .append(" or (n.lockOwnerOid<>$userOid and n.lockSourceOid is null ))");
    }

    // 上下文约束
    private void contentConstrainsParse(StringBuffer cypher, Map<String, Object> param, SearchHistory dto) {
        List<AdvancedSearchCondition> conditions = dto.getConditions();
        if (CollectionUtil.isEmpty(conditions)){
            return ;
        }
        String containerStr = "containerOid";
        Iterator<AdvancedSearchCondition> iterator = conditions.iterator();
        while (iterator.hasNext()){
            AdvancedSearchCondition next = iterator.next();
            if (!containerStr.equals(next.getName())){
                continue;
            }
            Object containerOid = next.getSearchKeyA();
            iterator.remove();
            if (containerOid == null){
                return ;
            }
            String containerOidStr = containerOid.toString();
            if (StringUtil.isBlank(containerOidStr)){
                return ;
            }
            param.put("containerOid", containerOidStr);
            cypher.append(" and n.containerOid=$containerOid ");
            return ;
        }
    }

    // 文件夹约束
    private void folderConstrainsParse(StringBuffer cypher, Map<String, Object> param, SearchHistory dto) {
        List<AdvancedSearchCondition> conditions = dto.getConditions();
        if (CollectionUtil.isEmpty(conditions)){
            return ;
        }
        String catalogOidField = "catalogOid";
        Iterator<AdvancedSearchCondition> iterator = conditions.iterator();
        while (iterator.hasNext()){
            AdvancedSearchCondition next = iterator.next();
            if (!catalogOidField.equals(next.getName())){
                continue;
            }
            Object catalogOid = next.getSearchKeyA();
            iterator.remove();
            if (catalogOid == null){
                return ;
            }
            String catalogOidStr = catalogOid.toString();
            if (StringUtil.isBlank(catalogOidStr)){
                return ;
            }
            List<String> folderTreeOidList = findFolderTreeOidList(catalogOidStr);
            param.put("catalogOids", folderTreeOidList);
            cypher.append(" and n.catalogOid in $catalogOids ");
            return ;
        }
    }

    private List<String> findFolderTreeOidList(String catalogOidStr) {
        Map<String, Object> map = new HashMap<>();
        map.put("oid", catalogOidStr);
        StringBuffer cypher = new StringBuffer();
        cypher.append(" match path=(f:Folder{oid:$oid})-[:").append(Contain.TYPE).append("*0..]->(b:Folder) ")
                .append("  where all(i in nodes(path) where i:Folder) return b.oid as oid ");
        return excuteListString(cypher.toString(), map, "oid");
    }

    // 类型约束
    private void modelDefinitionConstrainsParse(StringBuffer cypher, Map<String, Object> param, SearchHistory dto) {
        String modelDefinition = dto.getModelDefinition();
        if (StringUtil.isBlank(modelDefinition)){
            return ;
        }
        if(modelDefinition.contains(";JWI;")) {
            String [] modelDefinitions = modelDefinition.split(";JWI;");
            param.put("modelDefinition", modelDefinitions);
            cypher.append(" and n.modelDefinition in $modelDefinition ");
        } else {
            param.put("modelDefinition", modelDefinition);
            cypher.append(" and n.modelDefinition= $modelDefinition ");
        }
    }

    // 搜索词约束
    private void searchKeyConstrainsParse(StringBuffer cypher, Map<String, Object> param,
                                          SearchHistory dto, JSONObject globalSearchFullTextName) {
        String searchKey = dto.getSearchKey();
        String searchType = dto.getSearchType();
        String tenantOid = SessionHelper.getCurrentUser().getTenantOid();
        if(StringUtils.isBlank(tenantOid)) {
            Tenant tenant = commonService.dynamicQueryOne(Tenant.TYPE,null,Tenant.class);
            Assert.notNull(tenant,"Tenant Can not be null");
            tenantOid = tenant.getOid();
        }
        param.put("tenantOid", tenantOid);
        if (StringUtil.isBlank(searchKey) || searchKeyStr==null || searchKeyStr.length==0){
            cypher.append(" match (n:").append(searchType).append(") where n.tenantOid=$tenantOid ");
            return ;
        }
        String fullTextIndexName = globalSearchFullTextName.get(searchType).toString();
        Assert.notBlank(fullTextIndexName, "Full Text Index of "+ searchType+ " does not exists! ");
        param.put("fullTextIndexName", fullTextIndexName);
        cypher.append(" call db.index.fulltext.queryNodes($fullTextIndexName, '(tenantOid:").append(tenantOid).append(") AND (");

        String[] keyArr = searchKey.split(",");
        for (int j = 0; j < keyArr.length; j++) {
            String key = keyArr[j];
            int length = searchKeyStr.length;
            key = "*"+changeStr(key)+"*";
            for (int i = 0; i < length; i++) {
                if (i != 0 || (i == 0 && j != 0)){
                    cypher.append(" OR ");
                }
                cypher.append(searchKeyStr[i]).append(":").append(key);
            }
        }

        cypher.append(")')  yield node as n where true ");
    }


    public static final String[] chars = {"+","-","&&", "||", "!", "(", ")","{", "}","[", "]", "^", "\"", "/","~","*", "?", ":", "\\"};
    /**
     * Lucene查询语法
     * 转义特殊字符
     * @param searchKey
     * @return
     */
    private String changeStr(String searchKey){
        String res = searchKey;
        res = res.replace("\\", "");
        for (String aChar : chars) {
            res = res.replace(aChar, "\\"+ aChar);
        }
        res = res.replace("'", "\\'");
        return res;
    }

    // 分类约束
    private void clsConstaintsParse(StringBuffer cypher, Map<String, Object> param, SearchHistory dto) {
        List<AdvancedSearchCondition> conditions = dto.getConditions();
        if (CollectionUtil.isEmpty(conditions)){
            return ;
        }
        String clsStr = "clsOid";
        Iterator<AdvancedSearchCondition> iterator = conditions.iterator();
        while (iterator.hasNext()){
            AdvancedSearchCondition next = iterator.next();
            if (!clsStr.equals(next.getName())){
                continue;
            }
            Object clsOid = next.getSearchKeyA();
            iterator.remove();
            if (clsOid == null){
                return ;
            }
            String clsOidStr = clsOid.toString();
            if (StringUtil.isBlank(clsOidStr)){
                return ;
            }
            param.put("clsOid", clsOidStr);
            cypher.append(" and (:").append(Classification.TYPE).append("{oid:$clsOid})-[:SUB*0..").append("]->(:")
                    .append(Classification.TYPE).append(")-[:").append(Contain.TYPE).append("]->(n) ");
            return ;
        }
    }

    private PageResult<JSONObject> queryOneType(SearchHistory dto) {
        //第一步,判断是否有分类属性扩展属性,组装查询头.是:match (v:PartIteration)-[r:ATTRIBUTE]->(a) 否:match (v:PartIteration)
        //获取高级查询条件中是否含有扩展属性.
        boolean haveExtAttr = getHaveExtAttr(dto);
        //获取高级查询条件中是否含有分类属性,扩展属性.有分类属性就一定有分类
        boolean haveClsAttr = getHaveClsAttr(dto);
        //获取高级查询条件中是否含有分类
        boolean haveCls = getHaveCls(dto);
        //根据查询类型，分类组装sql头
        StringBuffer cypher = getStartSql(dto,haveExtAttr,haveClsAttr,haveCls);
        //组装外部参数
        Map<String, Object> map = getParamMpa(dto);

        //第二步,拼接where条件.先拼装公共关键字模糊查询,searchKey = 名称,编码,描述.
        searchCondition(cypher,needJoinAnd(cypher),map);

        //第三步,优先特殊处理高级查询中的上下文条件，不受连接符影响固定是and条件拼接。分类oid改成用关系搜
        outsideCondition(dto,cypher,map);

        //第四步,拼接高级查询条件,key = vaule
        jointAdvancedSearchCondition(dto.getConditions(),dto.getConnector(),map,cypher);

        //第五步,业务特殊需求处理
        jointSpecialBusinessProcessing(dto,map,cypher);

        //第六步,拼装查询尾-分页返回.
        PageResult<JSONObject> result = getPageResult(dto,cypher,map);
        return result;
    }

    private PageResult<JSONObject> getPageResult(SearchHistory dto, StringBuffer cypher, Map<String, Object> map) {
        StringBuffer countCypher = new StringBuffer();
        countCypher.append(cypher);
        countCypher.append(" return count(distinct(s)) as count");
        cypher.append(" with s optional match (s)-[r:ATTRIBUTE]->(a) return distinct(s),a,labels(a)[0] as fLabel order by s.updateDate desc ");
        //处理分页
        int index = dto.getIndex();
        int pageSize = dto.getSize();
        if (index != 0 && pageSize != 0) {
            int page = (index - 1) * pageSize;
            cypher.append(" SKIP $skip LIMIT $size ");
            map.put("skip", page);
            map.put("size", pageSize*2);
        }
        logger.info("全局搜索的语句：{}", cypher);
        logger.info("全局搜索的参数：{}", map.toString());
        Long aLong = excuteCount(countCypher.toString(), map);
        Result run = run(cypher.toString(), map);
        List result = domResult(run);
        return (PageResult<JSONObject>) new PageResult(aLong.intValue(), index, pageSize, result);
    }
    private List domResult(Result run) {// 解析
        List list = new ArrayList();
        Map<String, JSONObject> cache = new HashMap<>();
        Record next = null;
        JSONObject nodeJSON = null, temp = null;
        String partOid = null, label = null, extendPropLabel = PropertyGroup.TYPE, clsPropLabel = ClsAttrContent.TYPE;
        while (run.hasNext()) {
            next = run.next();
            temp = JSONUtil.toJSON(next.get("s"));
            partOid = temp.getString("oid");
            nodeJSON = cache.get(partOid);
            if (nodeJSON == null) {
                nodeJSON = temp;
                cache.put(partOid, nodeJSON);
            }
            label = next.get("fLabel").asString("");
            JSONObject propertyJSON = JSONUtil.toJSON(next.get("a"));
            if (label.equals(extendPropLabel)) {
                // 扩展属性
                nodeJSON.put("extensionContent", propertyJSON);
            } else if (label.equals(clsPropLabel)) {
                // 分类属性
                nodeJSON.put("clsProperty", propertyJSON);
            }
        }
        for (Map.Entry<String, JSONObject> entry : cache.entrySet()) {//去重取值
            JSONObject value = entry.getValue();
            list.add(value);
        }
        return list;
    }

    private void jointSpecialBusinessProcessing(SearchHistory dto, Map<String, Object> allParamMap, StringBuffer cypher) {
        Map<String, Object> map = new HashMap();
        Condition condition = new Condition();
        //如果类型是流程申请单，则加上过滤“草稿”状态
        if("ProcessOrder".equals(dto.getSearchType())){
            condition.and(Condition.where("processState").neq("draft"));
        }
        //Condition有条件才拼装sql
        if(CollectionUtils.isNotEmpty(condition.getConditionChain())){
            if(needJoinAnd(cypher)) {
                cypher.append(" and ").append(condition.parse(new ConditionParserNeo4j("s", map)));
            }else{
                cypher.append(condition.parse(new ConditionParserNeo4j("s", map)));
            }
            //吧参数拼接回总的参数map里。
            Map<String,String> nowMap = (Map)map.get("DEFAULT_KEY");
            Map<String,String> allMap = (Map)allParamMap.get("DEFAULT_KEY");
            allMap.putAll(nowMap);
        }
    }

    private void outsideCondition(SearchHistory dto, StringBuffer cypher, Map<String, Object> allMap) {
        List<AdvancedSearchCondition> conditions = dto.getConditions();//条件集合
        Condition condition = new Condition();
        Map<String, Object> map = new HashMap<>();
        for (AdvancedSearchCondition ad : conditions) {
            String name = ad.getName();
            //只有外部连接符号是任一的时候需要调整条件语句的顺序,以达到and在前,or再后.
            if ("containerOid".equals(name)){
                condition.and(Condition.where(name).eq(ad.getSearchKeyA()));
            }
        }
        if(CollectionUtils.isNotEmpty(condition.getConditionChain())){
            cypher.append(" and ").append(condition.parse(new ConditionParserNeo4j("s", map)));
            Map<String,String> param = (Map)map.get("DEFAULT_KEY");//此方法的参数
            Map<String,String> allParam = (Map)allMap.get("DEFAULT_KEY");//整个SQL的参数
            allParam.putAll(param);
        }
    }

    private boolean getHaveCls(SearchHistory dto) {
        boolean haveCls = false;//是否存在分类
        List<AdvancedSearchCondition> conditions = dto.getConditions();//条件集合
        for(AdvancedSearchCondition ad : conditions) {
            String name = ad.getName();
            if ("clsOid".equals(name)){
                haveCls = true;
            }
        }
        return haveCls;
    }

    private boolean getHaveExtAttr(SearchHistory dto) {
        boolean haveCls = false;//是否存在扩展属性.
        List<AdvancedSearchCondition> conditions = dto.getConditions();//条件集合
        for(AdvancedSearchCondition ad : conditions) {
            String attrType = ad.getSearchType();
            if ("2".equals(attrType)){
                haveCls = true;
            }
        }
        return haveCls;
    }

    private boolean getHaveClsAttr(SearchHistory dto) {
        boolean haveCls = false;//是否存在分类属性
        List<AdvancedSearchCondition> conditions = dto.getConditions();//条件集合
        for(AdvancedSearchCondition ad : conditions) {
            String attrType = ad.getSearchType();
            if ("3".equals(attrType)){
                haveCls = true;
            }
        }
        return haveCls;
    }

    private StringBuffer getStartSql(SearchHistory dto, boolean haveExtAttr, boolean haveClsAttr, boolean haveCls) {
        StringBuffer cypher = new StringBuffer();
        String searchType = dto.getSearchType();
        String modelDefinition = dto.getModelDefinition();
        String rootClsOid = getRootClsOid(dto);
        if(searchType.contains(",")){
            //有逗号则证明是多张表算一个类型查，比如cad有mcad和ecad
            if(haveClsAttr){//有分类，有分类属性
                if(haveExtAttr) {//还有扩展属性
                    cypher.append("match (root:Classification)-[:SUB*0..]->(c:Classification)-[:CONTAIN]->(s)-[r:ATTRIBUTE]->(pro:PropertyGroup),(clsAttr:ClsAttrContent) where (");
                }else{
                    cypher.append("match (root:Classification)-[:SUB*0..]->(c:Classification)-[:CONTAIN]->(s)-[r:ATTRIBUTE]->(clsAttr:ClsAttrContent) where (");
                }
            }else if(haveCls){//没有分类属性，有分类
                if(haveExtAttr){//还有扩展属性
                    cypher.append("match (root:Classification)-[:SUB*0..]->(c:Classification)-[:CONTAIN]->(s)-[r:ATTRIBUTE]->(pro:PropertyGroup) where (");
                }else{
                    cypher.append("match (root:Classification)-[:SUB*0..]->(c:Classification)-[:CONTAIN]->(s) where (");
                }
            }else if(haveExtAttr){//没有分类属性，没有分类，有扩展属性
                cypher.append("match (s)-[r:ATTRIBUTE]->(pro:PropertyGroup)where (");
            }else{//二者都没有
                cypher.append("match (s) where (");
            }
            String[] splits = searchType.split(",");
            for(int i = 0; i < splits.length; i++){
                //(s:PartIteration or s:DocumentIteration or s:MCADDocumentIteration or s:ECADDocumentIteration)
                if(i == 0){
                    cypher.append("s:").append(splits[i]);
                }else{
                    cypher.append(" or s:").append(splits[i]);
                }
            }
            if(needJoinAnd(cypher)&&StringUtils.isNotBlank(modelDefinition)){
                cypher.append(") and s.modelDefinition = $modelDefinition");
            }else{
                cypher.append(")");
            }
            if(haveCls&&StringUtils.isNotBlank(rootClsOid)){
                if(needJoinAnd(cypher)){
                    cypher.append(" and root.oid = $rootClsOid");
                }else{
                    cypher.append(" root.oid = $rootClsOid");
                }
            }
        }else{
            if(haveClsAttr){
                if(haveExtAttr) {//还有扩展属性
                    cypher.append("match (root:Classification)-[:SUB*0..]->(c:Classification)-[:CONTAIN]->(s").append(dealLabel(searchType)).append(")-[r:ATTRIBUTE]->(pro:PropertyGroup),(clsAttr:ClsAttrContent) where");
                }else{
                    cypher.append("match (root:Classification)-[:SUB*0..]->(c:Classification)-[:CONTAIN]->(s").append(dealLabel(searchType)).append(")-[r:ATTRIBUTE]->(clsAttr:ClsAttrContent) where");
                }
            }else if(haveCls){//没有分类属性，有分类
                if(haveExtAttr){//还有扩展属性
                    cypher.append("match (root:Classification)-[:SUB*0..]->(c:Classification)-[:CONTAIN]->(s").append(dealLabel(searchType)).append(")-[r:ATTRIBUTE]->(pro:PropertyGroup) where");
                }else{
                    cypher.append("match (root:Classification)-[:SUB*0..]->(c:Classification)-[:CONTAIN]->(s").append(dealLabel(searchType)).append(") where");
                }
            }else if(haveExtAttr){//没有分类属性，没有分类，有扩展属性
                cypher.append("match (s").append(dealLabel(searchType)).append(")-[r:ATTRIBUTE]->(pro:PropertyGroup) where");
            }else{
                cypher.append("match (s").append(dealLabel(searchType)).append(") where");
            }
            if(StringUtils.isNotBlank(modelDefinition)){
                cypher.append(" s.modelDefinition = $modelDefinition");
            }
            if(haveCls&&StringUtils.isNotBlank(rootClsOid)){
                if(needJoinAnd(cypher)){
                    cypher.append(" and root.oid = $rootClsOid");
                }else{
                    cypher.append(" root.oid = $rootClsOid");
                }
            }
        }
        return cypher;
    }

    private String getRootClsOid(SearchHistory dto) {
        String oid = "";
        List<AdvancedSearchCondition> conditions = dto.getConditions();//条件集合
        for(AdvancedSearchCondition ad : conditions) {
            String name = ad.getName();
            if ("clsOid".equals(name)){
                oid = (String)ad.getSearchKeyA();
            }
        }
        return oid;
    }

    private Map<String, Object> getParamMpa(SearchHistory dto) {
        Map<String, Object> map = new HashMap<>();
        String searchType = dto.getSearchType();//搜索类型
        String modelDefinition = dto.getModelDefinition();//子类型
        String searchKey = dto.getSearchKey();//关键字
        String connector = dto.getConnector();//条件间的连接符
        map.put("modelDefinition",modelDefinition);
        map.put("connector",connector);
        map.put("searchKey",searchKey);

        //特殊处理分类oid
        List<AdvancedSearchCondition> conditions = dto.getConditions();//条件集合
        for(AdvancedSearchCondition ad : conditions) {
            String name = ad.getName();
            if ("clsOid".equals(name)){
                String rootOid = (String)ad.getSearchKeyA();
                map.put("rootClsOid",rootOid);
            }
        }
        return map;
    }

    /*
     * @param cypher
     * @return  java.lang.Boolean
     * @desc    判断传入的sql是否需要拼接and
     * <AUTHOR>
     * @create  2022/5/16 9:57
     **/
    private Boolean needJoinAnd(StringBuffer cypher) {
        Boolean needJoinAnd = false;
        String sql = cypher.toString();
        if (sql.endsWith(") ")){
            needJoinAnd = true;
        }else if (sql.endsWith(")")){
            needJoinAnd = true;
        }else if (sql.endsWith("modelDefinition ")){
            needJoinAnd = true;
        }else if (sql.endsWith("modelDefinition")){
            needJoinAnd = true;
        }else if (sql.endsWith("rootClsOid ")){
            needJoinAnd = true;
        }else if (sql.endsWith("rootClsOid")){
            needJoinAnd = true;
        }else{
            needJoinAnd = false;
        }
        return needJoinAnd;
    }

    private StringBuffer jointAdvancedSearchCondition(List<AdvancedSearchCondition> conditions, String outsideConnector, Map<String, Object> allParamMap, StringBuffer cypher) {
        //获取基本属性sql
        StringBuffer adCypher = getBaseAttrSql(conditions,outsideConnector,allParamMap);
        //获取分类属性,扩展属性sql
        StringBuffer clsCypher = getClsAttrSql(conditions,outsideConnector,allParamMap);

        if(adCypher.length() > 0 &&clsCypher.length() > 0){
            adCypher.append(" and ").append(clsCypher);
        }else{
            adCypher.append(clsCypher);
        }
        if(adCypher.length() > 0){
            if(needJoinAnd(cypher)){
                cypher.append(" and ").append(adCypher);
            }else{
                cypher.append(adCypher);
            }
        }
        return cypher;

    }

    private StringBuffer getClsAttrSql(List<AdvancedSearchCondition> conditions, String outsideConnector, Map<String, Object> allParamMap) {
        StringBuffer clsCypher = new StringBuffer();
        boolean haveCls = false;//是否存在分类属性
        int i = 0;
        Map<String, String> lastMap = new HashMap();
        for(AdvancedSearchCondition ad : conditions){
            Map<String, Object> map = new HashMap();
            StringBuffer cypher = new StringBuffer();//分类属性sql
            String searchType = ad.getSearchType();
            String name = ad.getName();
            String connector = ad.getConnector();
            Object searchKeyA = ad.getSearchKeyA();
            Object searchKeyB = ad.getSearchKeyB();
            Condition condition = new Condition();
            //高级查询连接符映射
            //jointConnector(connector,condition);
            switch (connector) {
                case "contain":
                    condition = Condition.where(name).contain(searchKeyA);
                    break;
                case "noContain":
                    condition = Condition.where(name).ncontain(searchKeyA);
                    break;
                case ">":
                    condition = Condition.where(name).gt(searchKeyA);
                    break;
                case "<":
                    condition = Condition.where(name).lt(searchKeyA);
                    break;
                case "=":
                    condition = Condition.where(name).eq(searchKeyA);
                    break;
                case "≠":
                    condition = Condition.where(name).neq(searchKeyA);
                    break;
                case ">=":
                    condition = Condition.where(name).gt(searchKeyA).or(Condition.where(name).eq(searchKeyA));
                    break;
                case "<=":
                    condition = Condition.where(name).lt(searchKeyA).or(Condition.where(name).eq(searchKeyA));;
                    break;
                case "between":
                    condition = Condition.where(name).between(searchKeyA, searchKeyB);
                    break;
            }
            //属性类型：1.基本属性，s.name；2.扩展属性,3.分类属性，
            if ("3".equals(searchType)){
                cypher.append(condition.parse(new ConditionParserNeo4j("clsAttr", map)));
                haveCls = true;
            }else if("2".equals(searchType)){//到时候吧方法移出去，扩展属性，同样是a的属性，类型不一样
                cypher.append(condition.parse(new ConditionParserNeo4j("pro", map)));
                haveCls = true;
            }else{
                continue;
            }
            //单个高级查询之间的连接。//基本属性和分类属性分开拼装
            if(i>0) {
                if ("and".equals(outsideConnector)) {
                    clsCypher.append(" and ").append(cypher);
                } else {//目前只有全部和任一。
                    clsCypher.append(" or ").append(cypher);
                }
            }else{
                clsCypher.append("(").append(cypher);
            }
            i++;
            //把map拆开，拼接参数，获得所有高级查询得参数map
            Map<String,String> nowMap = (Map)map.get("DEFAULT_KEY");
            lastMap.putAll(nowMap);
        }
        //拿到原有的DEFAULT_KEY映射，吧高级查询拼好的参数往里面加
        Map<String,String> allMap = (Map)allParamMap.get("DEFAULT_KEY");
        allMap.putAll(lastMap);
        if(conditions!=null && conditions.size()>0){
            clsCypher.append(")");
        }
        StringBuffer op = new StringBuffer();
        if(haveCls){
            op.append(clsCypher);
        }
        return op;
    }


    private StringBuffer getBaseAttrSql(List<AdvancedSearchCondition> conditions, String outsideConnector, Map<String, Object> allParamMap) {
        StringBuffer adCypher = new StringBuffer();
        int i = 0;
        boolean haveCls = false;//是否存在基本属性
        Map<String, String> lastMap = new HashMap();
        for(AdvancedSearchCondition ad : conditions){
            Map<String, Object> map = new HashMap();
            StringBuffer cypher = new StringBuffer();//普通属性sql
            String searchType = ad.getSearchType();
            String name = ad.getName();
            String connector = ad.getConnector();
            Object searchKeyA = ad.getSearchKeyA();
            Object searchKeyB = ad.getSearchKeyB();
            Condition condition = new Condition();
            if("containerOid".equals(name)||"clsOid".equals(name)){//分类和上下文跳过不在这里拼接。
                continue;//adCypher.append(" and ").append(cypher);
            }
            //高级查询连接符映射
            //jointConnector(connector,condition);
            switch (connector) {
                case "contain":
                    condition = Condition.where(name).contain(searchKeyA);
                    break;
                case "noContain":
                    condition = Condition.where(name).ncontain(searchKeyA);
                    break;
                case ">":
                    condition = Condition.where(name).gt(searchKeyA);
                    break;
                case "<":
                    condition = Condition.where(name).lt(searchKeyA);
                    break;
                case "=":
                    if((name.endsWith("Date")||name.endsWith("Time"))&& searchKeyA instanceof Long){//时间类搜索因为后台存的是时间戳，毫秒级别，所以等于的情况需要计算在一秒内
                        long param = (long)searchKeyA;
                        condition = Condition.where(name).between(param-1000, param+1000);
                    }else{
                        condition = Condition.where(name).eq(searchKeyA);
                    }
                    break;
                case "≠":
                    condition = Condition.where(name).neq(searchKeyA);
                    break;
                case ">=":
                    condition = Condition.where(name).gt(searchKeyA).or(Condition.where(name).eq(searchKeyA));
                    break;
                case "<=":
                    condition = Condition.where(name).lt(searchKeyA).or(Condition.where(name).eq(searchKeyA));;
                    break;
                case "between":
                    condition = Condition.where(name).between(searchKeyA, searchKeyB);
                    break;
            }
            //属性类型：1.基本属性，s.name；2.扩展属性，待定；3.分类属性，待定
            if("1".equals(searchType)){
                cypher.append(condition.parse(new ConditionParserNeo4j("s", map)));
                haveCls = true;
            }else{
                continue;
            }
            //单个高级查询之间的连接。//基本属性和分类属性分开拼装
            if(i>0) {
                if ("and".equals(outsideConnector)) {
                    adCypher.append(" and ").append(cypher);
                } else {//目前只有全部和任一。
                    adCypher.append(" or ").append(cypher);
                }
            }else {
                adCypher.append("(").append(cypher);
            }
            i++;
            //把map拆开，拼接参数，获得所有高级查询得参数map
            Map<String,String> nowMap = (Map)map.get("DEFAULT_KEY");
            lastMap.putAll(nowMap);
        }
        //拿到原有的DEFAULT_KEY映射，吧高级查询拼好的参数往里面加
        Map<String,String> allMap = (Map)allParamMap.get("DEFAULT_KEY");
        allMap.putAll(lastMap);
        if(haveCls){
            adCypher.append(")");
        }
        return adCypher;
    }

    private StringBuffer specialDealSearchKey(AdvancedSearchCondition ad, Map<String, Object> allParamMap) {
        return null;
    }

    private Boolean isHaveBaseAttr(List<AdvancedSearchCondition> conditions) {
        Boolean haveCls = false;
        for(AdvancedSearchCondition ad : conditions){
            String searchType = ad.getSearchType();
            if("1".equals(searchType)){
                haveCls = true;
            }
        }
        return haveCls;
    }

    private void searchCondition(StringBuffer cypher, boolean haveModelDefinition, Map<String, Object> map){
        Condition filter = new Condition();
        //最新小版本
        filter.and(Condition.where("latest").eq(true).or(Condition.where("latest").isnull()));
        //检出人
        String userOid = SessionHelper.getCurrentUser().getOid();
        filter.and(
                Condition.where("lockOwnerOid").isnull()
                        .or(Condition.where("lockOwnerOid").eq(userOid).and(Condition.where("lockSourceOid").nnull()))
                        .or(Condition.where("lockOwnerOid").neq(userOid).and(Condition.where("lockSourceOid").isnull()))
        );
        //组织
        String tenantOid = SessionHelper.getCurrentUser().getTenantOid();
        if (StringUtils.isNotBlank(tenantOid)){
            filter.and(Condition.where("tenantOid").eq(tenantOid));
        }

        if(haveModelDefinition) {
            cypher.append(" and ").append(filter.parse(new ConditionParserNeo4j("s", map)));
        }else{
            cypher.append(filter.parse(new ConditionParserNeo4j("s", map)));
        }
        //关键字-名称，编码，描述(关键字查询如果用Condition会和高级查询中的属性同名导致map里的参数值覆盖(Condition的bug)，所以改用直接拼接sql字符串)
        String searchKey = map.get("searchKey")==null ? "":map.get("searchKey").toString();
        if (StringUtil.isNotBlank(searchKey)) {
            cypher.append(" and (s.name contains $searchKey or s.cname contains $searchKey or s.number contains $searchKey or s.description contains $searchKey)");
//            filter.and(Condition.where("name").contain(searchKey)
//                    .or(Condition.where("number").contain(searchKey)
//                            .or(Condition.where("description").contain(searchKey))));
        }
    }



}
