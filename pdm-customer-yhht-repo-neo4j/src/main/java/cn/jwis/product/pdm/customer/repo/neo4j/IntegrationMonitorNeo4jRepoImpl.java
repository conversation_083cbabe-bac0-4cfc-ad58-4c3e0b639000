package cn.jwis.product.pdm.customer.repo.neo4j;

import cn.jwis.framework.base.util.StringUtil;
import cn.jwis.framework.database.neo4j.condition.Neo4jEnableConditional;
import cn.jwis.framework.database.neo4j.execution.CommonEntityTemplate;
import cn.jwis.framework.database.neo4j.util.JSONUtil;
import cn.jwis.platform.plm.foundation.common.response.InstanceEntity;
import cn.jwis.product.pdm.cad.mcad.param.Page;
import cn.jwis.product.pdm.customer.entity.IntegrationRecord;
import cn.jwis.product.pdm.customer.repo.IntegrationMonitorRepo;
import lombok.extern.slf4j.Slf4j;
import org.neo4j.driver.Record;
import org.neo4j.driver.Result;
import org.springframework.context.annotation.Conditional;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2023/8/9 22:53
 * @Email <EMAIL>
 */
@Slf4j
@Component
@Conditional(Neo4jEnableConditional.class)
public class IntegrationMonitorNeo4jRepoImpl extends CommonEntityTemplate<IntegrationRecord> implements IntegrationMonitorRepo {

    @Override
    public IntegrationRecord findByOid(String oid) {
        return super.findByOid(oid);
    }

    @Override
    public IntegrationRecord findRecordByNumber(InstanceEntity instance) {
        IntegrationRecord integrationRecord = null;
        StringBuffer cypher = new StringBuffer();
        Map<String, Object> map = new HashMap();
        map.put("bizNumber", instance.getNumber());
        map.put("bizVersion", instance.getVersion());
        cypher.append(" match (m").append(this.dealLabel("IntegrationRecord")).append(")");
        this.buildCondition(cypher, "m", map);
        cypher.append(" return m  ORDER BY m.createDate desc  LIMIT 1");
        Result result = this.run(cypher.toString(), map);
        Iterator<Record> ri = result.list().iterator();

        while(ri.hasNext()) {
            Record record = ri.next();
            if (!record.get("m").isNull()) {
                IntegrationRecord temp = JSONUtil.parseObject(record.get("m").asNode(), IntegrationRecord.class);
                integrationRecord = temp;
            }
        }
        return integrationRecord;
    }

    @Override
    public List<IntegrationRecord> findRecord(Page page) {
        List<IntegrationRecord> list = new ArrayList();
        StringBuffer cypher = new StringBuffer();
        Map<String, Object> map = new HashMap();
        map.put("skip", page.getSkip());
        map.put("size", page.getSize());
        this.initFindRecordParam(map, page.getParam());
        page.setCount(this.countRecord(map));
        cypher.append(" match (m").append(this.dealLabel("IntegrationRecord")).append(")");
        this.buildCondition(cypher, "m", map);
        cypher.append(" return m  ORDER BY m.createDate desc SKIP $skip LIMIT $size");
        Result result = this.run(cypher.toString(), map);
        Iterator<Record> ri = result.list().iterator();

        while(ri.hasNext()) {
            Record record = ri.next();
            if (!record.get("m").isNull()) {
                IntegrationRecord temp = JSONUtil.parseObject(record.get("m").asNode(), IntegrationRecord.class);
                list.add(temp);
            }
        }
        return list;
    }

    @Override
    public long updateStatusByNumber(String number, String type, String lifeCycleStatus) {
        StringBuffer cypher = new StringBuffer();
        Map<String, Object> map = new HashMap();
        map.put("number", number);
        map.put("status", lifeCycleStatus);
        cypher.append("match (n").append(this.dealLabel(type))
                .append(") where n.number=$number and n.latest = true ")
                .append(" set n.lifecycleStatus=$status");
        this.excute(cypher.toString(), map);
        return 1;
    }

    @Override
    public long updateStatusByOid(String number, String type, String lifeCycleStatus) {
        StringBuffer cypher = new StringBuffer();
        Map<String, Object> map = new HashMap();
        map.put("oid", number);
        map.put("status", lifeCycleStatus);
        cypher.append("match (n").append(this.dealLabel(type))
                .append(") where n.oid=$oid and n.latest = true ")
                .append(" set n.lifecycleStatus=$status");
        this.excute(cypher.toString(), map);
        return 1;
    }

    private int countRecord(Map<String, Object> map) {
        StringBuffer cypher = new StringBuffer();
        cypher.append("match (n").append(this.dealLabel("IntegrationRecord")).append(")");
        this.buildCondition(cypher, "n", map);
        cypher.append(" return count(n) as cnt ");
        Result result = this.run(cypher.toString(), map);
        Iterator<Record> ri = result.list().iterator();
        if (ri.hasNext()) {
            Record record = (Record)ri.next();
            return record.get("cnt").asInt();
        } else {
            return 0;
        }
    }

    private void buildCondition(StringBuffer cypher, String alis, Map<String, Object> map) {
        cypher.append(" where 1=1 and ").append(alis).append(".createDate > 0");

        if (map.containsKey("status") && StringUtil.isNotEmpty(map.get("status").toString())) {
            cypher.append(" and ").append(alis).append(".isSuccess = $status ");
        }

        if (map.containsKey("keyWord") && StringUtil.isNotEmpty(map.get("keyWord").toString())) {
            cypher.append(" and (").append(alis).append(".bizNumber =~ $keyWord or ").append(alis).append(".bizName =~ $keyWord ) ");
        }

        if (map.containsKey("startDate") && StringUtil.isNotEmpty(map.get("startDate").toString())) {
            cypher.append(" and ").append(alis).append(".createDate > $startDate ");
        }

        if (map.containsKey("endDate") && StringUtil.isNotEmpty(map.get("endDate").toString())) {
            cypher.append(" and ").append(alis).append(".createDate < $endDate ");
        }

        if (map.containsKey("bizNumber") && StringUtil.isNotEmpty(map.get("bizNumber").toString())) {
            cypher.append(" and ").append(alis).append(".bizNumber = $bizNumber ");
        }

        if (map.containsKey("bizVersion") && null != map.get("bizVersion") && StringUtil.isNotEmpty(map.get("bizVersion").toString())) {
            cypher.append(" and ").append(alis).append(".bizVersion = $bizVersion ");
        }

    }

    private void initFindRecordParam(Map<String, Object> map, Map<String, Object> param) {
        if (param != null) {
            Object statusObj = param.get("status");
            Object keyWordObj = param.get("keyWord");
            Object startDateObj = param.get("startDate");
            Object endDateObj = param.get("endDate");
            if (statusObj != null && StringUtil.isNotEmpty(statusObj.toString())) {
                if("成功".equals(statusObj.toString())){
                    map.put("status", true);
                }else if ("失败".equals(statusObj.toString())){
                    map.put("status", false);
                }else{
                    map.put("status", statusObj);
                }
            }

            if (keyWordObj != null && StringUtil.isNotEmpty(keyWordObj.toString())) {
                map.put("keyWord", this.dealSearchKey(keyWordObj.toString()));
            }

            if (startDateObj != null && StringUtil.isNotEmpty(startDateObj.toString())) {
                map.put("startDate", Long.valueOf(startDateObj.toString()));
            }

            if (endDateObj != null && StringUtil.isNotEmpty(endDateObj.toString())) {
                map.put("endDate", Long.valueOf(endDateObj.toString()));
            }
        }

    }

}
