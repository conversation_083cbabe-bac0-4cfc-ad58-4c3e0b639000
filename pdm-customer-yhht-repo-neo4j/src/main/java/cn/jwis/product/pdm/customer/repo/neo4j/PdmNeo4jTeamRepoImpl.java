package cn.jwis.product.pdm.customer.repo.neo4j;

import cn.hutool.core.util.StrUtil;
import cn.jwis.framework.base.dto.UserDTO;
import cn.jwis.framework.base.response.PageResult;
import cn.jwis.framework.base.util.StringUtil;
import cn.jwis.framework.base.web.session.SessionHelper;
import cn.jwis.framework.database.neo4j.condition.Neo4jEnableConditional;
import cn.jwis.framework.database.neo4j.execution.CommonEntityTemplate;
import cn.jwis.framework.database.neo4j.util.JSONUtil;
import cn.jwis.platform.iam.user.User;
import cn.jwis.platform.plm.account.entity.team.TeamTemplate;
import cn.jwis.platform.plm.container.entity.Container;
import cn.jwis.platform.plm.container.entity.Team;
import cn.jwis.platform.plm.container.entity.TeamRole;
import cn.jwis.product.pdm.customer.entity.CustomerUnBindUserDTO;
import cn.jwis.product.pdm.customer.entity.CustomerUserProductFolderTeamDTO;
import cn.jwis.product.pdm.customer.repo.PdmTeamRepo;
import org.neo4j.driver.Record;
import org.neo4j.driver.Result;
import org.springframework.context.annotation.Conditional;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * <AUTHOR> lhm
 * @Date ： Created in 2023/9/13 10:52
 * @Description :
 */
@Component
@Conditional(Neo4jEnableConditional.class)
@Transactional
public class PdmNeo4jTeamRepoImpl extends CommonEntityTemplate<TeamTemplate> implements PdmTeamRepo {
    @Override
    public List<TeamTemplate> findProcessTeam(String searchKey) {
        Map<String, Object> map = new HashMap();
        StringBuffer cypher = new StringBuffer();
        UserDTO currentUser = SessionHelper.getCurrentUser();
        String account = currentUser.getAccount();
        String tenantOid = currentUser.getTenantOid();

        map.put("tenantOid",tenantOid);
        cypher.append("match(m:TeamTemplate) where m.tenantOid=$tenantOid");

        if(StringUtil.isNotBlank(searchKey)){
            map.put("searchKey", this.dealSearchKey(searchKey));
            cypher.append(" and ( m.name=~$searchKey or m.description=~$searchKey) ");
        }

        if (!currentUser.isTenantAdmin() && !currentUser.isSystemAdmin()) {
            map.put("account",account);
            cypher.append(" and (not m.privateFlag or m.createBy=$account or m.modelDefinition='TeamTemplate')");
        }

        cypher.append(" return m order by m.updateDate desc ");

        List rows = this.excuteList(cypher.toString(), map, "m", TeamTemplate.class);

        return rows;
    }

    @Override
    public PageResult<TeamTemplate> findProcessTeamPage(String searchKey, int index, int size) {
        Map<String, Object> map = new HashMap();
        StringBuffer cypher = new StringBuffer();
        UserDTO currentUser = SessionHelper.getCurrentUser();
        String account = currentUser.getAccount();
        String tenantOid = currentUser.getTenantOid();
        map.put("modelDefinition","PdmTeamTemplate");
        map.put("tenantOid",tenantOid);
        cypher.append("match(m:TeamTemplate) where m.modelDefinition=$modelDefinition and m.tenantOid=$tenantOid");

        if(StringUtil.isNotBlank(searchKey)){
            map.put("searchKey", this.dealSearchKey(searchKey));
            cypher.append(" and ( m.name=~$searchKey or m.description=~$searchKey) ");
        }

        if (!currentUser.isTenantAdmin() && !currentUser.isSystemAdmin()) {
            map.put("account",account);
            cypher.append(" and (not m.privateFlag or m.createBy=$account)");
        }


        StringBuffer cntCypher = new StringBuffer(cypher);
        cntCypher.append(" return count(m) as cnt");

        cypher.append(" return m order by m.updateDate desc ").append(this.dealPage(size, index));

        List<TeamTemplate> rows = this.excuteList(cypher.toString(), map, "m", TeamTemplate.class);

        Long cnt = this.excuteCount(cntCypher.toString(), map, "cnt");
        return PageResult.init(cnt.intValue(), index, size, rows);
    }

    @Override
    public TeamRole findContainerMemberRole(String containerOid,String roleName) {
        Map<String, Object> map = new HashMap();
        StringBuffer cypher = new StringBuffer();
        map.put("containerOid",containerOid);
        map.put("roleName",roleName);
        cypher.append(" match(c:Container)-->(t:Team)-->(r:TeamRole) ");
        cypher.append("  where c.oid = $containerOid and r.name = $roleName return r  ");
        Result run = run(cypher.toString(), map);
        Record next = null;
        TeamRole teamRole = null;
        while (run.hasNext()) {
            next = run.next();
            teamRole = JSONUtil.parseObject(next.get("r").asNode(), TeamRole.class);
            break;
        }
        return teamRole;
    }

    @Override
    public List<User> findFolderTeamAllUser(String containerOid) {
        List<User> users = new ArrayList<>();
        Map<String, Object> map = new HashMap();
        StringBuffer cypher = new StringBuffer();
        map.put("containerOid",containerOid);
        cypher.append(" match(p:Container)-->(f:Folder) where p.oid = $containerOid with f ");
        cypher.append("  match(f)-[:CONTAIN*0..]->(sf:Folder) with sf ");
        cypher.append("  match(sf)-->(t:Team)-->(r:TeamRole)-->(u:User) return distinct u  ");
        Result run = run(cypher.toString(), map);
        Record next = null;
        User user = null;
        while (run.hasNext()) {
            next = run.next();
            user = JSONUtil.parseObject(next.get("u").asNode(), User.class);
            users.add(user);
        }
        return users;
    }

    @Override
    public Object searchAllTeamRole(String containerOid, String searchKey, int index, int size) {
        List<CustomerUserProductFolderTeamDTO> rows = new ArrayList<>();
        if (StrUtil.isEmpty(searchKey)) {
            return PageResult.init(0, index, size, rows);
        }

        Map<String, Object> map = new HashMap();
        StringBuffer cypher = new StringBuffer();
        UserDTO currentUser = SessionHelper.getCurrentUser();
        String tenantOid = currentUser.getTenantOid();
        map.put("tenantOid",tenantOid);
        cypher.append(" MATCH (u:User)-[:CONTAIN]-(r:TeamRole)-[:CONTAIN]-(t:Team)-[:ASSIGN]-(f:Folder) ");
        cypher.append(" MATCH (c:Container) "); // 先匹配 Container

        cypher.append(" WHERE f.containerOid = c.oid "); // 建立 Folder 和 Container 的关联

        if (StrUtil.isNotEmpty(containerOid)) {
            map.put("containerOid", containerOid);
            cypher.append(" AND c.oid = $containerOid "); // 当 containerOid 不为空时，添加过滤条件
        }

        if (StringUtil.isNotBlank(searchKey)) {
            map.put("searchKey", this.dealSearchKey(searchKey));
            cypher.append(" AND ( u.oid=~ $searchKey ) "); // 将 where 替换为 AND
        }

        StringBuffer countStr = new StringBuffer(cypher);
        countStr.append(" return count(DISTINCT f) as cnt");

        cypher.append(" RETURN collect(r) as roles,u.oid as userOid, t, f, c order by c.name desc ").append(this.dealPage(size, index));

        Result run = this.run(cypher.toString(), map);
        Record next = null;
        CustomerUserProductFolderTeamDTO row = null;
        Team team = null;
        Container container = null;
        List<TeamRole> roles = null;
        while(run.hasNext()) {
            next = run.next();

            row = JSONUtil.parseObject(next.get("f").asNode(), CustomerUserProductFolderTeamDTO.class);
            container = JSONUtil.parseObject(next.get("c").asNode(), Container.class);
            roles = next.get("roles").asList((item) -> JSONUtil.parseObject(item.asNode(), TeamRole.class));
            team = JSONUtil.parseObject(next.get("t").asNode(), Team.class);
            String userOid = next.get("userOid").asString();
            row.setUserOid(userOid);
            row.setTeam(team);
            row.setTeamRoles(Optional.ofNullable(roles).orElse(new ArrayList()));
            row.setContainer(container);
            String oid = row.getOid();
            Map<String, Object> folderNameMap = new HashMap(8);
            folderNameMap.put("oid", oid);
            folderNameMap.put("containerName", container.getName());
            // 递归查询folder名称，拼接完整的文件夹路径
            String folderCypher = "MATCH (f:Folder {oid: $oid}) " +
                    "OPTIONAL MATCH path = (parent:Folder)-[:CONTAIN*0..]->(f) " +
                    "WITH reduce(names = '', n IN nodes(path) | names + CASE names WHEN '' THEN n.name ELSE '-' + n.name END) AS folderPath " +
                    "WHERE folderPath STARTS WITH $containerName " +
                    "RETURN folderPath AS folderNames";

            String folderNames = this.excuteString(folderCypher, folderNameMap, "folderNames");
            row.setFolderNames(folderNames);
            rows.add(row);
        }

        Long cnt = this.excuteCount(countStr.toString(), map, "cnt");
        return PageResult.init(cnt.intValue(), index, size, rows);
    }

    @Override
    public void deleteAllTeamRoleUser(CustomerUnBindUserDTO dto) {
        Map<String, Object> map = new HashMap<>();
        map.put("userOid", dto.getUserOid());
        map.put("teamRoles", dto.getTeamRoles());
        StringBuilder cypher = new StringBuilder();
        cypher.append("MATCH (t:TeamRole)-[r:CONTAIN]->(u:User) ")
                .append("WHERE t.oid IN $teamRoles AND u.oid = $userOid ")
                .append("DELETE r");
        if (StrUtil.isNotEmpty(dto.getReplaceUserOid())) {
            // 绑定新的user，并创建新的关系
            map.put("replaceUserOid", dto.getReplaceUserOid());
            cypher.append(" WITH t " +
                    " MATCH (newUser:User {oid: $replaceUserOid})  " +
                    " CREATE (t)-[:CONTAIN]->(newUser)");
        }
        this.excute(cypher.toString(), map);
    }

    @Override
    public void batchHandleTeamRoleUser(CustomerUnBindUserDTO dto) {
        Map<String, Object> map = new HashMap<>();
        map.put("containerOid", dto.getContainerOid());
        map.put("userOid", dto.getUserOid());

        StringBuilder cypher = new StringBuilder();

        // 查询 TeamRole 并去重
        cypher.append("MATCH (u:User)-[:CONTAIN]-(r:TeamRole)-[:CONTAIN]-(t:Team)-[:ASSIGN]-(f:Folder) ")
              .append("MATCH (c:Container {oid: f.containerOid}) ")
              .append("WHERE u.oid = $userOid AND c.oid = $containerOid ")
              .append("WITH DISTINCT COLLECT(DISTINCT r) AS teamRoles, u ")

              // 删除旧 TeamRole 关联
              .append("UNWIND teamRoles AS t ")
              .append("MATCH (t)-[r:CONTAIN]->(u) DELETE r ");

        // 如果 replaceUserOid 不为空，则绑定新用户
        if (StrUtil.isNotEmpty(dto.getReplaceUserOid())) {
            map.put("replaceUserOid", dto.getReplaceUserOid());
            cypher.append("WITH teamRoles ")
                  .append("MATCH (newUser:User {oid: $replaceUserOid}) ")
                  .append("UNWIND teamRoles AS t ")
                  .append("MERGE (t)-[:CONTAIN]->(newUser)");
        }

        this.excute(cypher.toString(), map);
    }

}
