package cn.jwis.product.pdm.customer.repo.neo4j;

import cn.jwis.framework.base.dto.UserDTO;
import cn.jwis.framework.base.exception.JWIException;
import cn.jwis.framework.base.response.PageResult;
import cn.jwis.framework.base.util.CollectionUtil;
import cn.jwis.framework.base.util.StringUtil;
import cn.jwis.framework.base.web.session.SessionHelper;
import cn.jwis.framework.database.neo4j.condition.Neo4jEnableConditional;
import cn.jwis.framework.database.neo4j.execution.CommonEntityTemplate;
import cn.jwis.framework.database.neo4j.util.JSONUtil;
import cn.jwis.platform.iam.user.User;
import cn.jwis.platform.plm.container.entity.Folder;
import cn.jwis.platform.plm.container.entity.FolderTreeNode;
import cn.jwis.platform.plm.container.entity.Team;
import cn.jwis.platform.plm.container.entity.TeamRole;
import cn.jwis.platform.plm.foundation.classification.entity.ClsAttrContent;
import cn.jwis.platform.plm.foundation.common.response.InstanceEntity;
import cn.jwis.platform.plm.foundation.extendedproperty.entity.PropertyGroup;
import cn.jwis.platform.plm.foundation.relationship.Assign;
import cn.jwis.platform.plm.foundation.relationship.Attribute;
import cn.jwis.platform.plm.foundation.relationship.Contain;
import cn.jwis.product.pdm.container.entity.response.PDMInstanceEntityWithCatalog;
import cn.jwis.product.pdm.customer.entity.CustomerPDMInstanceEntityWithCatalog;
import cn.jwis.product.pdm.customer.entity.FuzzySubPageWithModelDTO;
import cn.jwis.product.pdm.customer.repo.PdmFolderTwoRepo;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.neo4j.driver.Record;
import org.neo4j.driver.Result;
import org.neo4j.driver.Value;
import org.neo4j.driver.types.Node;
import org.springframework.context.annotation.Conditional;
import org.springframework.stereotype.Component;

import java.util.*;

@Slf4j
@Component
@Conditional(Neo4jEnableConditional.class)
public class PdmFolderTwoRepoImpl extends CommonEntityTemplate<Folder> implements PdmFolderTwoRepo {


    @Override
    public PageResult<PDMInstanceEntityWithCatalog> fuzzyFolderContentPageWithModel(Set<String> folderOids, String searchKey, int index, int size, FuzzySubPageWithModelDTO dto) {
        List<String> subTypes = dto.getSubTypes();
        String modelDefinition = dto.getModelDefinition();
        searchKey = this.transfer(searchKey);
        StringBuffer cypher = new StringBuffer();
        Map<String, Object> map = new HashMap<>();
        UserDTO currentUser = SessionHelper.getCurrentUser();
        map.put("tenantOid", currentUser.getTenantOid());
        map.put("userOid", currentUser.getOid());
        map.put("folderOids", folderOids);
        map.put("subTypes", subTypes);

        // **第一步：查询 Folder 下的内容**
        cypher.append("MATCH (m").append(this.dealLabel("Folder")).append(")-[r:CONTAIN]->(s) ")
                .append("WHERE m.tenantOid = $tenantOid AND s.tenantOid = $tenantOid ")
                .append("AND m.oid IN $folderOids ")
                .append("AND s.type IN $subTypes ")
                .append("AND (s.latest IS NULL OR (s.latest = true AND ( ")
                .append("(s.lockOwnerOid IS NULL) ")
                .append("OR (s.lockOwnerOid = $userOid AND s.lockSourceOid IS NOT NULL) ")
                .append("OR (s.lockOwnerOid <> $userOid AND s.lockSourceOid IS NULL) ))) ");
        // **条件拼接：modelDefinition 存在时添加过滤条件**
        if (StringUtils.isNotBlank(modelDefinition)) {
            map.put("modelDefinition", modelDefinition);
            cypher.append("AND s.modelDefinition = $modelDefinition ");
        }

        // **第二步：匹配 pro 和 cls，确保 `s` 不丢失**
        cypher.append(" OPTIONAL MATCH (s)-[").append(dealLabel(Attribute.TYPE)).append("]->(pro").append(dealLabel(PropertyGroup.TYPE)).append(") ");
        cypher.append(" OPTIONAL MATCH (s)-[").append(dealLabel(Attribute.TYPE)).append("]->(cls").append(dealLabel(ClsAttrContent.TYPE)).append(") ");

        // **第三步：使用 `WITH` 确保 `s` 保持在查询中**
        cypher.append("WITH s, pro, cls, r ");

        // **第四步：`searchKey` 统一过滤 `s` 和 `pro`**
        if (StringUtil.isNotBlank(searchKey)) {
            map.put("searchKey", this.dealSearchKey(searchKey));
            cypher.append("WHERE (")
                    .append("s.name =~ $searchKey ")
                    .append("OR s.number =~ $searchKey ")
                    .append("OR s.cname =~ $searchKey ")
                    .append("OR coalesce(pro.cn_jwis_gg, '') =~ $searchKey ")
                    .append(") ");
        }

        // **统计总数**
        StringBuffer cntCypher = new StringBuffer(cypher);
        cntCypher.append(" RETURN count(s) as cnt ");

        // **返回数据**
        cypher.append("RETURN cls, pro, s, r.borrow AS borrow ")
                .append("ORDER BY s.updateDate DESC, s.name, s.number ")
                .append(this.dealPage(size, index));

        // **执行查询**
        Result run = this.run(cypher.toString(), map);
        Record next;
        List<CustomerPDMInstanceEntityWithCatalog> result = new ArrayList<>();
        Class<CustomerPDMInstanceEntityWithCatalog> clazz = CustomerPDMInstanceEntityWithCatalog.class;

        while (run.hasNext()) {
            next = run.next();
            CustomerPDMInstanceEntityWithCatalog row = JSONUtil.parseObject(next.get("s"), clazz);
            row.setBorrow(next.get("borrow").asBoolean(false));
            JSONObject extensionContent = Optional.ofNullable(JSONUtil.toJSON(next.get("pro"))).orElse(new JSONObject());
            row.setExtensionContent(extensionContent);
            JSONObject cls = Optional.ofNullable(JSONUtil.toJSON(next.get("cls"))).orElse(new JSONObject());
            row.setClsProperty(cls);
            result.add(row);
        }

        // **统计数据**
        Long cnt = this.excuteCount(cntCypher.toString(), map, "cnt");
        return PageResult.init(cnt.intValue(), index, size, result);
    }


    /**
     * 获取文件夹团队信息
     * @param folderOid
     * @return
     */
    @Override
    public Team getTeam(String folderOid) {
        StringBuffer cypher = new StringBuffer();
        Map<String, Object> params = new HashMap<>();
        cypher.append("match(f:Folder{oid: $folderOid})-[:ASSIGN]-(t:Team) return t");
        params.put("folderOid", folderOid);
        List<Team> teams = excuteList(cypher.toString(), params, "t", Team.class);
        return CollectionUtil.getFirst(teams);
    }

    @Override
    public Team getInstanceTeam(InstanceEntity instance) {
        StringBuffer cypher = new StringBuffer();
        Map<String, Object> params = new HashMap<>();
        cypher.append("match(f:" + instance.getType() + "{oid: $folderOid})-[:ASSIGN]-(t:Team) return t");
        params.put("folderOid", instance.getOid());
        List<Team> teams = excuteList(cypher.toString(), params, "t", Team.class);
        return CollectionUtil.getFirst(teams);
    }

    @Override
    public List<Folder> findByParentOid(String parentOid) {
        Map<String, Object> params = new HashMap<>();
        params.put("catalogOid", parentOid);
        StringBuffer cypher = new StringBuffer();
        cypher.append("match(p:Folder) where p.catalogOid = $catalogOid with p optional match (p)-[:ATTRIBUTE]->(attr:PropertyGroup) return p,attr");
        Result result = run(cypher.toString(), params);

        List<Folder> res = new ArrayList<>();
        while (result.hasNext()){
            Record record = result.next();
            if(!record.get("p").isNull()){
                Folder folder = JSON.parseObject(new JSONObject(record.get("p").asMap()).toJSONString(), Folder.class);
                if(!record.get("attr").isNull())
                    folder.setExtensionContent(new JSONObject(record.get("attr").asMap()));
                res.add(folder);
            }
        }
        return res;
    }

    public List<FolderTreeNode> searchFoldersWithPermisson(String containerType, String containerModel, String containerOid, String searchKey) throws JWIException {
        //searchKey = transfer(searchKey);
        Map<String, Object> params = new HashMap();
        UserDTO currentUser = SessionHelper.getCurrentUser();
        params.put("tenantOid", currentUser.getTenantOid());
        params.put("userOid", currentUser.getOid());

        StringBuffer cypher = new StringBuffer();
        cypher.append("Match (p:").append(containerType).append(")-[")
                .append(dealLabel(Contain.TYPE)).append("]->(f").append(dealLabel(Folder.TYPE)).append(")")
                .append(" where p.tenantOid=$tenantOid ");

        if (StringUtil.isNotBlank(containerModel)) {
            params.put("containerModel", containerModel);
            cypher.append(" and p.modelDefinition=$containerModel ");
        }

        if (StringUtil.isNotBlank(containerOid)) {
            params.put("containerOid", containerOid);
            cypher.append(" and p.oid=$containerOid ");
        }
        if (!(currentUser.isTenantAdmin() || currentUser.isSystemAdmin())) {
            cypher.append(" and (not p.privateFlag or (p)-[:").append(Assign.TYPE).append("]->(:")
                    .append(Team.TYPE).append(")-[:").append(Contain.TYPE).append("]->(:")
                    .append(TeamRole.TYPE).append(")-[:").append(Contain.TYPE).append("]->(:")
                    .append(User.TYPE).append("{oid:$userOid}))");
        }
        cypher.append(" with p,f Match path=(f)-[r").append(dealLabel(Contain.TYPE))
                .append("*0..]->(s").append(dealLabel(Folder.TYPE)).append(")");
        if (StringUtil.isNotBlank(searchKey)) {
            params.put("searchKey", dealSearchKey(searchKey));
            cypher.append(" where s.name=~$searchKey or s.description=~$searchKey ");
        }
        cypher.append(" with p, nodes(path) as nodes order by p.createDate desc unwind nodes as s optional match(s)-[:ATTRIBUTE]->(attr) return s, collect(attr) as attrs order by s.name ");

        Result run = run(cypher.toString(), params);
        return nodes2Tree(run);
    }

    public List<FolderTreeNode> nodes2Tree(Result run) {
        List<FolderTreeNode> res = new ArrayList<>();

        LinkedHashMap<String, FolderTreeNode> parentKeyObj = new LinkedHashMap<>();
        while (run.hasNext()){
            Record record = run.next();
             JSONObject node = new JSONObject(record.get("s").asMap());
             List<Node> attrs = record.get("attrs").asList(Value::asNode);
             FolderTreeNode folderTreeNode = JSON.parseObject(node.toJSONString(), FolderTreeNode.class);
             if(CollectionUtils.isNotEmpty(attrs)){
                 attrs.forEach((attr) -> {
                     if(attr.hasLabel(ClsAttrContent.TYPE)){
                         folderTreeNode.setClsProperty(new JSONObject(attr.asMap()));
                     }else if(attr.hasLabel(PropertyGroup.TYPE)){
                         folderTreeNode.setExtensionContent(new JSONObject(attr.asMap()));
                     }
                 });
             }
             parentKeyObj.put(folderTreeNode.getOid(), folderTreeNode);
        }
        parentKeyObj.values().forEach((item) -> {
            if(parentKeyObj.containsKey(item.getCatalogOid())){
                FolderTreeNode parent = parentKeyObj.get(item.getCatalogOid());
                if(parent.getChildren() == null){
                    parent.setChildren(new ArrayList<>());
                }
                parent.getChildren().add(item);
            }else{
                res.add(item);
            }
        });
        return res;
    }

    private static Set<Character> REGEXCHARSET = new HashSet(){{
        add("'");
        add("!");
        add("~");
        add(":");
        add("\"");
    }};

    private String transfer(String regex) {
        StringBuffer buffer = new StringBuffer();
        if (StringUtils.isNotBlank(regex)) {
            char[] chars = regex.toCharArray();
            char[] var3 = chars;
            int var4 = chars.length;
            for(int var5 = 0; var5 < var4; ++var5) {
                char aChar = var3[var5];
                if (REGEXCHARSET.contains(aChar)) {
                    buffer.append("\\");
                }

                buffer.append(aChar);
            }
        }
        return buffer.toString();
    }

}
