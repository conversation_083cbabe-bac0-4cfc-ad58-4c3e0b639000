package cn.jwis.product.pdm.customer.repo.neo4j;

import cn.jwis.framework.database.neo4j.condition.Neo4jEnableConditional;
import cn.jwis.framework.database.neo4j.execution.CommonEntityTemplate;
import cn.jwis.framework.database.neo4j.execution.ExecutionNeo4jRepository;
import cn.jwis.framework.database.neo4j.execution.Neo4jExecutionTemplate;
import cn.jwis.platform.plm.account.entity.team.TeamTemplate;
import cn.jwis.platform.plm.container.entity.Folder;
import cn.jwis.product.pdm.customer.dos.UserDo;
import cn.jwis.product.pdm.customer.repo.PdmUserRepo;
import com.alibaba.fastjson.JSONObject;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.neo4j.driver.Record;
import org.neo4j.driver.Result;
import org.neo4j.driver.internal.InternalRelationship;
import org.springframework.context.annotation.Conditional;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> yefei
 * @Date ： 2024/2/20
 * @Description :
 */

@Slf4j
@Transactional
@Component
@Conditional(Neo4jEnableConditional.class)
public class PdmUserRepoImpl extends ExecutionNeo4jRepository<UserDo> implements PdmUserRepo {
    @SneakyThrows
    @Override
    public List<UserDo> queryUserAndOrgInfo() {
        Map<String, Object> map = new HashMap();
        String cypher = "MATCH (u:User) where u.markForDelete = false " +
                "optional match (u)-[:BELONG_TO]->(p:Position)-[:BELONG_TO]->(d:Department) WITH COLLECT(DISTINCT p" +
                ".name) AS positions, COLLECT(DISTINCT d.name) AS departments,u " +
                "RETURN positions, departments,u.oid as oid,u.name as name,u.avatar as avatar,u.phone as phone,u" +
                ".email as email,u.account as account,u.number as number";
        List<UserDo> rows = new ArrayList<>();
        Result run = this.run(cypher.toString(), map);
        UserDo u;
        Record next;
        while (run.hasNext()) {
            next = run.next();
            Map<String, Object> row = next.asMap();
            u = new UserDo();
            BeanUtils.populate(u,row);
            rows.add(u);
        }
        return rows;
    }
}
