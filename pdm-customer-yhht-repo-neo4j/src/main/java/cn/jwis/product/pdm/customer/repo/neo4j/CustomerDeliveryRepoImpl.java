package cn.jwis.product.pdm.customer.repo.neo4j;

import cn.hutool.core.util.StrUtil;
import cn.jwis.framework.base.exception.JWIException;
import cn.jwis.framework.database.neo4j.condition.Neo4jEnableConditional;
import cn.jwis.framework.database.neo4j.execution.ExecutionNeo4jRepository;
import cn.jwis.product.pdm.delivery.entity.Delivery;
import com.alibaba.fastjson.JSONObject;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.neo4j.driver.Record;
import org.neo4j.driver.Result;
import org.springframework.context.annotation.Conditional;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.Map;


@Slf4j
@Transactional
@Component
@Conditional(Neo4jEnableConditional.class)
public class CustomerDeliveryRepoImpl extends ExecutionNeo4jRepository<Delivery> {

    /**
     * 根据pmsId查询Delivery节点及其扩展属性
     */
    @SneakyThrows
    public Delivery findByExtensionPmsId(String pmsId) {
        Map<String, Object> params = new HashMap<>();
        params.put("pmsId", pmsId);

        String cypher = "MATCH (d:Delivery)-[:ATTRIBUTE]->(p:PropertyGroup) " +
                        "WHERE p.pms_id = $pmsId " +
                        "RETURN d, p";

        Result result = this.run(cypher, params);

        if (!result.hasNext()) {
            return null;
        }

        Record record = result.next();

        // 把 d 节点映射到 Delivery
        Map<String, Object> deliveryMap = record.get("d").asMap();
        Delivery delivery = new Delivery();
        BeanUtils.populate(delivery, deliveryMap);

        // 把 p 节点属性转成 JSONObject
        Map<String, Object> extensionMap = record.get("p").asMap();
        JSONObject extensionJson = new JSONObject(extensionMap);
        delivery.setExtensionContent(extensionJson);

        return delivery;
    }


    /**
     * 父节点关系迁移
     *
     * @param oldParentOid 原父节点Oid
     * @param childOid     子节点Oid
     * @param newParentOid 新父节点Oid
     */
    public void move(String oldParentOid, String childOid, String newParentOid) {
        if (StrUtil.isBlank(oldParentOid)) {
            throw new JWIException("原父节点Oid不能为空");
        }
        if (StrUtil.isBlank(childOid)) {
            throw new JWIException("子节点Oid不能为空");
        }
        if (StrUtil.isBlank(newParentOid)) {
            throw new JWIException("新父节点Oid不能为空");
        }
        String cypher =
                "MATCH (oldParent:Delivery {oid: $oldParentOid})-[oldRel:CONTAIN]->(child:Delivery {oid: $childOid}) " +
                        "MATCH (newParent:Delivery {oid: $newParentOid}) " +
                        "DELETE oldRel " +
                        "CREATE (newParent)-[:CONTAIN]->(child)";


        Map<String, Object> params = new HashMap<>();
        params.put("oldParentOid", oldParentOid);
        params.put("childOid", childOid);
        params.put("newParentOid", newParentOid);

        this.run(cypher, params);

        log.info("节点 {} 已从父节点 {} 迁移到新父节点 {}", childOid, oldParentOid, newParentOid);
    }

}