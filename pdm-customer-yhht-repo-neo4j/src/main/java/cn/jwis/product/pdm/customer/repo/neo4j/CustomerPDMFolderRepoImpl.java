package cn.jwis.product.pdm.customer.repo.neo4j;

import cn.jwis.framework.base.dto.UserDTO;
import cn.jwis.framework.base.response.PageResult;
import cn.jwis.framework.base.util.StringUtil;
import cn.jwis.framework.base.web.session.SessionHelper;
import cn.jwis.framework.database.neo4j.condition.Neo4jEnableConditional;
import cn.jwis.framework.database.neo4j.execution.CommonEntityTemplate;
import cn.jwis.framework.database.neo4j.util.JSONUtil;
import cn.jwis.platform.plm.container.entity.Folder;
import cn.jwis.platform.plm.foundation.classification.entity.ClsAttrContent;
import cn.jwis.platform.plm.foundation.extendedproperty.entity.PropertyGroup;
import cn.jwis.platform.plm.foundation.relationship.Attribute;
import cn.jwis.product.pdm.container.entity.response.PDMInstanceEntityWithCatalog;
import cn.jwis.product.pdm.container.repo.PDMFolderRepo;
import cn.jwis.product.pdm.customer.entity.CustomerPDMInstanceEntityWithCatalog;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.neo4j.driver.Record;
import org.neo4j.driver.Result;
import org.springframework.context.annotation.Conditional;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;

import java.util.*;

@Component
@Conditional({Neo4jEnableConditional.class})
@Primary
public class CustomerPDMFolderRepoImpl extends CommonEntityTemplate<Folder> implements PDMFolderRepo {

    public PageResult<PDMInstanceEntityWithCatalog> fuzzyFolderContentPage(Collection<String> folderOids, String searchKey, int index, int size, List<String> subTypes) {
        searchKey = this.transfer(searchKey);
        StringBuffer cypher = new StringBuffer();
        Map<String, Object> map = new HashMap<>();
        UserDTO currentUser = SessionHelper.getCurrentUser();
        map.put("tenantOid", currentUser.getTenantOid());
        map.put("userOid", currentUser.getOid());
        map.put("folderOids", folderOids);
        map.put("subTypes", subTypes);

        // **第一步：查询 Folder 下的内容**
        cypher.append("MATCH (m").append(this.dealLabel("Folder")).append(")-[r:CONTAIN]->(s) ")
                .append("WHERE m.tenantOid = $tenantOid AND s.tenantOid = $tenantOid ")
                .append("AND m.oid IN $folderOids ")
                .append("AND s.type IN $subTypes ")
                .append("AND (s.latest IS NULL OR (s.latest = true AND ( ")
                .append("(s.lockOwnerOid IS NULL) ")
                .append("OR (s.lockOwnerOid = $userOid AND s.lockSourceOid IS NOT NULL) ")
                .append("OR (s.lockOwnerOid <> $userOid AND s.lockSourceOid IS NULL) ))) ");

        // **第二步：匹配 pro 和 cls，确保 `s` 不丢失**
        cypher.append(" OPTIONAL MATCH (s)-[").append(dealLabel(Attribute.TYPE)).append("]->(pro").append(dealLabel(PropertyGroup.TYPE)).append(") ");
        cypher.append(" OPTIONAL MATCH (s)-[").append(dealLabel(Attribute.TYPE)).append("]->(cls").append(dealLabel(ClsAttrContent.TYPE)).append(") ");

        // **第三步：使用 `WITH` 确保 `s` 保持在查询中**
        cypher.append("WITH s, pro, cls, r ");

        // **第四步：`searchKey` 统一过滤 `s` 和 `pro`**
        if (StringUtil.isNotBlank(searchKey)) {
            map.put("searchKey", this.dealSearchKey(searchKey));
            cypher.append("WHERE (")
                    .append("s.name =~ $searchKey ")
                    .append("OR s.number =~ $searchKey ")
                    .append("OR s.cname =~ $searchKey ")
                    .append("OR coalesce(pro.cn_jwis_gg, '') =~ $searchKey ")
                    .append(") ");
        }

        // **统计总数**
        StringBuffer cntCypher = new StringBuffer(cypher);
        cntCypher.append(" RETURN count(s) as cnt ");

        // **返回数据**
        cypher.append("RETURN cls, pro, s, r.borrow AS borrow ")
                .append("ORDER BY s.updateDate DESC, s.name, s.number ")
                .append(this.dealPage(size, index));

        // **执行查询**
        Result run = this.run(cypher.toString(), map);
        Record next;
        List<CustomerPDMInstanceEntityWithCatalog> result = new ArrayList<>();
        Class<CustomerPDMInstanceEntityWithCatalog> clazz = CustomerPDMInstanceEntityWithCatalog.class;

        while (run.hasNext()) {
            next = run.next();
            CustomerPDMInstanceEntityWithCatalog row = JSONUtil.parseObject(next.get("s"), clazz);
            row.setBorrow(next.get("borrow").asBoolean(false));
            JSONObject extensionContent = Optional.ofNullable(JSONUtil.toJSON(next.get("pro"))).orElse(new JSONObject());
            row.setExtensionContent(extensionContent);
            JSONObject cls = Optional.ofNullable(JSONUtil.toJSON(next.get("cls"))).orElse(new JSONObject());
            row.setClsProperty(cls);
            result.add(row);
        }

        // **统计数据**
        Long cnt = this.excuteCount(cntCypher.toString(), map, "cnt");
        return PageResult.init(cnt.intValue(), index, size, result);
    }

    private static Set<Character> REGEXCHARSET = new HashSet(){{
        add("'");
        add("!");
        add("~");
        add(":");
        add("\"");
    }};

    private String transfer(String regex) {
        StringBuffer buffer = new StringBuffer();
        if (StringUtils.isNotBlank(regex)) {
            char[] chars = regex.toCharArray();
            char[] var3 = chars;
            int var4 = chars.length;
            for(int var5 = 0; var5 < var4; ++var5) {
                char aChar = var3[var5];
                if (REGEXCHARSET.contains(aChar)) {
                    buffer.append("\\");
                }

                buffer.append(aChar);
            }
        }
        return buffer.toString();
    }

}
