package cn.jwis.product.pdm.customer.repo.neo4j.util;


import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.dingtalkoauth2_1_0.models.GetAccessTokenResponse;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiV2DepartmentGetRequest;
import com.dingtalk.api.request.OapiV2UserGetRequest;
import com.dingtalk.api.response.OapiV2DepartmentGetResponse;
import com.dingtalk.api.response.OapiV2UserGetResponse;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;


@Component
public class DingUtils {
    private static final org.slf4j.Logger logger = LoggerFactory.getLogger(DingUtils.class);
    /**
     * 根据用户email获取钉钉 userId的 AppKey和AppSecret
     */
    private static String dingTalkAppKeyNew = "ding4tq3as5pyachbj57";
    private static String dingTalkAppSecretNew="tJWV1LpEIQzLyjwDPUtcAOKZwI5CQ-3o95mtt6Y_EeI7QQx5WPesglaViS_ml7zM";
    private static final String FIND_BY_EMAIL_URL = "http://meeting-management.prod.yhroot.com/meeting-management/api/meeting/user/findByEmail?email=";
    private static final String MEETING_TOKEN = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1c2VyTm8iOiJGeDM1RDQzQ25pUDRDbDZWaWlLSzduVndpRWlFIiwiZXhwaXJlVGltZSI6IjIxMDAtMDEtMDEgMTA6MTA6MTAifQ.rjQF8ytqy3CS_Lit3gmzbtbX3Fc06iMXkh26920ZQfg";

    @Autowired
    RedisTemplate redisTemplate;

    public String findDepartById(String hid) {
        String result = "";
        if(StringUtils.isNotBlank(hid)){
            try{
                String redisKey = "DeliveryReportNew:"+hid;
                Object o1 = redisTemplate.opsForValue().get(redisKey);
                String res = ObjectUtils.isNotEmpty(o1) ? o1.toString() : "";
                if(StringUtils.isNotBlank(res)){
                    return res;
                }
                String userDetail = getUserDetail(hid);
                if (StringUtils.isEmpty(userDetail)) {
                    switch (hid) {
                        case "16267444627078299":
                            result = "平台研发部-机热能源部-结构力学组";
                            break;
                        case "16086890475802332":
                            result = "平台研发部-机热能源部-电源管理组";
                            break;
                        case "16267444847688858":
                            result = "平台研发部-机热能源部-结构力学组";
                            break;
                        case "15942044356229599":
                            result = "载荷毫米波部-相控阵微系统1组";
                            break;
                        case "16357287025507027":
                            result = "载荷毫米波部-相控阵微系统1组";
                            break;
                        case "16249585868157638":
                            result = "平台研发部-总体技术组-载荷总体组";
                            break;
                        case "13":
                            result = "";
                            break;
                    }
                    return result;
                }
                if(userDetail!=null){
                    JSONObject jsonObject = JSON.parseObject(userDetail);
                    if(jsonObject==null){
                        return result;
                    }
                    JSONObject r = jsonObject.getJSONObject("result");
                    if(r==null){
                        return result;
                    }
                    JSONArray dept_id_list = r.getJSONArray("dept_id_list");
                    if(dept_id_list==null){
                        return result;
                    }
                    Object o = dept_id_list.get(0);
                    if(o==null){
                        return result;
                    }
                    String name = getDepartmentNameDeep(Long.valueOf(String.valueOf(o)));
//                    if(StringUtils.isNotBlank(name)){
//                        String[] split = name.split("/");
//                        if(split.length>3){
//                            name = split[0]+"/"+split[1]+"/"+split[2];
//                        }
//                    }
                    if("平台研发部/电子信息部/星载软件组/星载软件".equals(name)){
                        name = "平台研发部/电子信息部/星载软件组";
                    }

                    result =name;
//                    redisTemplate.opsForValue().set(redisKey, result);
                    redisTemplate.opsForValue().set(redisKey, result, 60 * 60 * 24 * 2, TimeUnit.SECONDS);
                    return result;
                }
            }catch (Exception ex){
                ex.printStackTrace();
            }
        }
        return result;
    }


    public String getUserDetail(String userId){
        try {
            Thread.sleep(50L);
            DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/v2/user/get");
            OapiV2UserGetRequest req = new OapiV2UserGetRequest();
            req.setUserid(userId);
            String accessToken = getToken();
            OapiV2UserGetResponse rsp = client.execute(req, accessToken);
            logger.info(JSON.toJSONString(rsp));

            if(rsp.getErrcode() != 0){
                return "";
            }
            return rsp.getBody();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }


    public String getDepartmentNameDeep(Long dept_id){
        String name ="";
        return this.getDepartmentNameDeepSub(dept_id, name);
    }


    private String getDepartmentNameDeepSub(Long dept_id, String dname) {
        try {
            DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/v2/department/get");
            OapiV2DepartmentGetRequest req = new OapiV2DepartmentGetRequest();
            req.setDeptId(dept_id);
            req.setLanguage("zh_CN");
            String accessToken = getToken();
            OapiV2DepartmentGetResponse rsp = client.execute(req, accessToken);
            System.out.println(rsp.getBody());
            logger.info(JSON.toJSONString(rsp));
            JSONObject jo = JSON.parseObject(rsp.getBody());
            JSONObject rs = jo.getJSONObject("result");
            String name = rs.getString("name");
            if(StringUtils.isNotBlank(name)){
                if(StringUtils.isBlank(dname)){
                    dname +=name;
                }else{
                    dname =  name+ "-" +dname;
                }
            }
            Long super_id = rs.getLong("parent_id");
            if(super_id!=null&&super_id!=1){
                dname = this.getDepartmentNameDeepSub(super_id,dname);
            }else{
                return dname;
            }
            Thread.sleep(50L);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return dname;
    }

    public String getToken() throws Exception {
        com.aliyun.teaopenapi.models.Config config = new com.aliyun.teaopenapi.models.Config();
        config.protocol = "https";
        config.regionId = "central";
        com.aliyun.dingtalkoauth2_1_0.Client client = new com.aliyun.dingtalkoauth2_1_0.Client(config);
        com.aliyun.dingtalkoauth2_1_0.models.GetAccessTokenRequest getAccessTokenRequest = new com.aliyun.dingtalkoauth2_1_0.models.GetAccessTokenRequest()
                .setAppKey(dingTalkAppKeyNew).setAppSecret(dingTalkAppSecretNew);
        GetAccessTokenResponse response = client.getAccessToken(getAccessTokenRequest);
        return response.getBody().getAccessToken();
    }

    public String getUserIdByEmail(String email) {
        try {
            String url = FIND_BY_EMAIL_URL + email;
            HttpResponse response = HttpUtil.createGet(url)
                    .header("token", MEETING_TOKEN)
                    .execute();
            String res = response.body();
            if (StringUtils.isNotEmpty(res)) {
                JSONObject jsonObject = JSON.parseObject(res);
                if (jsonObject.containsKey("data")) {
                    MeetingDDUserVO data = JSON.parseObject(jsonObject.getString("data"), MeetingDDUserVO.class);
                    if (data != null) {
                        return data.getUserId();
                    }
                }
            }
        } catch (Exception e) {
            logger.error("Failed to get userId by email: {}", email, e);
        }
        return null;
    }
}
