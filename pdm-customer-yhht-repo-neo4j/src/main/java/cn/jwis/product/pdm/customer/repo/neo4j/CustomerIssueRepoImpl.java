package cn.jwis.product.pdm.customer.repo.neo4j;

import cn.jwis.framework.base.response.PageResult;
import cn.jwis.framework.base.web.session.SessionHelper;
import cn.jwis.framework.database.core.entity.SubPageFilter;
import cn.jwis.framework.database.core.query.dynamic.Condition;
import cn.jwis.framework.database.neo4j.condition.Neo4jEnableConditional;
import cn.jwis.framework.database.neo4j.execution.CommonEntityTemplate;
import cn.jwis.framework.database.neo4j.util.ConditionParserNeo4j;
import cn.jwis.framework.database.neo4j.util.JSONUtil;
import cn.jwis.platform.plm.foundation.location.able.info.LocationInfo;
import cn.jwis.product.pdm.change.entity.ECR;
import cn.jwis.product.pdm.change.entity.Issue;
import cn.jwis.product.pdm.change.entity.IssueHandleRecord;
import cn.jwis.product.pdm.change.entity.IssueSchema;
import cn.jwis.product.pdm.change.repo.IssueRepo;
import cn.jwis.product.pdm.change.response.IssueInfo;
import cn.jwis.product.pdm.change.response.IssueInfoData;
import com.alibaba.fastjson.JSONObject;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.neo4j.driver.Record;
import org.neo4j.driver.Result;
import org.springframework.context.annotation.Conditional;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

@Component
@Conditional({Neo4jEnableConditional.class})
@Transactional
@Primary
public class CustomerIssueRepoImpl extends CommonEntityTemplate<Issue> implements IssueRepo {
    public CustomerIssueRepoImpl() {
    }

    public void createChangeRelation(String issueOid, String oid, String type) {
        StringBuffer cypher = new StringBuffer();
        Map<String, Object> map = new HashMap();
        map.put("issueOid", issueOid);
        map.put("oid", oid);
        cypher.append(" match (m").append(this.entityLabelCypher).append("),(s").append(this.dealLabel(type)).append(") where m.oid = $issueOid  and s.oid=$oid ").append("with m,s create (m)-[r").append(this.dealLabel("IMPACT")).append("{complete:false}]->(s)");
        this.excute(cypher.toString(), map);
    }

    public PageResult<IssueInfo> findIssueByTenantOid(SubPageFilter subPageFilter) {
        String tenantOid = SessionHelper.getCurrentUser().getTenantOid();
        StringBuilder cypher = new StringBuilder();
        StringBuilder countCypher = new StringBuilder();
        Map<String, Object> map = new HashMap();
        map.put("tenantOid", tenantOid);
        int index = subPageFilter.getIndex();
        int size = subPageFilter.getSize();
        cypher.append(" Call { match (i").append(this.dealLabel("Issue")).append(")");
        cypher.append(" where i.tenantOid=$tenantOid  ");
        Condition condition = subPageFilter.getFilter();
        if (condition != null) {
            cypher.append(" and ").append(condition.parse(new ConditionParserNeo4j("i", map)));
        }

        countCypher.append(cypher);
        countCypher.append(" return count(i) as count } return count");
        cypher.append(" return i order by i.updateDate desc");
        if (index != 0 && size != 0) {
            int page = (index - 1) * size;
            cypher.append(" SKIP $skip LIMIT $size ");
            map.put("skip", page);
            map.put("size", size);
        }

        cypher.append("} with i optional match(i)-[:IMPACT]->(p) with i, collect(p) as pList optional match(i)-[:HAS]->(s:IssueSchema) with i,pList,collect(s) as s  optional match(i)-[:RECORD*..]->(h:IssueHandleRecord) ");
        cypher.append(" return i,pList,s,collect(h) as hList order by i.updateDate desc ");
        Long count = this.excuteCount(countCypher.toString(), map);
        Result run = this.run(cypher.toString(), map);
        ArrayList issueList = new ArrayList();

        while(run.hasNext()) {
            Record next = run.next();
            IssueInfo issueInfo = (IssueInfo)JSONUtil.parseObject(next.get("i").asNode(), IssueInfo.class);
            map = new HashMap(8);
            map.put("oid", issueInfo.getOid());
            cypher = new StringBuilder("match (i:Issue{oid:$oid})<-[:REVIEW_FOR]-(po:ProcessOrder) return po.processInstanceId as processInstanceId");
            String processInstanceId = this.excuteString(cypher.toString(), map, "processInstanceId");
            if (StringUtils.isNotEmpty(processInstanceId)) {
                issueInfo.setProcessInstanceId(processInstanceId);
            }

            List<JSONObject> jsonObjectList = next.get("pList").asList((item) -> {
                return JSONUtil.parseObject(item.asNode());
            });
            issueInfo.setIssueList(jsonObjectList);
            if (ObjectUtils.isNotEmpty(next.get("s")) && !next.get("s").isNull()) {
                List<IssueSchema> iList = JSONUtil.parseArray(next.get("s").asList(), IssueSchema.class);
                issueInfo.setIssueSchema(iList.stream().findAny().orElse(null));
            }

            List<IssueHandleRecord> recordList = next.get("hList").asList((item) -> {
                return (IssueHandleRecord)JSONUtil.parseObject(item.asNode(), IssueHandleRecord.class);
            });
            issueInfo.setRecords(recordList);
            LocationInfo locationInfo = new LocationInfo();
            locationInfo.setCatalogOid(issueInfo.getCatalogOid());
            locationInfo.setCatalogType(issueInfo.getCatalogType());
            locationInfo.setContainerModelDefinition(issueInfo.getContainerModelDefinition());
            locationInfo.setContainerOid(issueInfo.getContainerOid());
            locationInfo.setContainerType(issueInfo.getContainerType());
            issueInfo.setLocationInfo(locationInfo);
            issueList.add(issueInfo);
        }

        PageResult<IssueInfo> result = new PageResult(count.intValue(), index, size, issueList);
        return result;
    }

    public PageResult<IssueInfo> searchByCurrentUser(SubPageFilter subPageFilter, List<String> viewCodeList) {
        String tenantOid = SessionHelper.getCurrentUser().getTenantOid();
        String status = this.getStatus(viewCodeList);
        StringBuilder cypher = new StringBuilder();
        StringBuilder countCypher = new StringBuilder();
        Map<String, Object> map = new HashMap();
        map.put("tenantOid", tenantOid);
        map.put("userAccount", SessionHelper.getCurrentUser().getAccount());
        int index = subPageFilter.getIndex();
        int size = subPageFilter.getSize();
        cypher.append(" Call { match  p=(i").append(this.dealLabel("Issue")).append(")");
        cypher.append(" where i.tenantOid=$tenantOid ");
        Condition condition = subPageFilter.getFilter();
        if (condition != null) {
            cypher.append(" and ").append(condition.parse(new ConditionParserNeo4j("i", map)));
        }

        cypher.append("and none(s in nodes(p) where s.lifecycleStatus in [").append(status).append("]) with nodes(p)[0] as ns   ");
        countCypher.append(cypher);
        countCypher.append(" return count(ns) as count } return count");
        cypher.append(" return ns order by ns.updateDate desc");
        if (index != 0 && size != 0) {
            int page = (index - 1) * size;
            cypher.append(" SKIP $skip LIMIT $size ");
            map.put("skip", page);
            map.put("size", size);
        }

        cypher.append("} with ns optional match(ns)-[:IMPACT]->(p) with ns, collect(p) as pList optional match(ns)-[:HAS]->(s:IssueSchema) with ns,pList,s optional match(ns)-[:RECORD]->(h:IssueHandleRecord) ");
        cypher.append(" return ns,pList,s,collect(h) as hList order by ns.updateDate desc ");
        Long count = this.excuteCount(countCypher.toString(), map);
        Result run = this.run(cypher.toString(), map);
        ArrayList issueList = new ArrayList();

        while(run.hasNext()) {
            Record next = run.next();
            IssueInfo issueInfo = (IssueInfo)JSONUtil.parseObject(next.get("ns").asNode(), IssueInfo.class);
            List<JSONObject> jsonObjectList = next.get("pList").asList((item) -> {
                return JSONUtil.parseObject(item.asNode());
            });
            issueInfo.setIssueList(jsonObjectList);
            IssueSchema issueSchema = (IssueSchema)JSONUtil.parseObject(next.get("s").asNode(), IssueSchema.class);
            issueInfo.setIssueSchema(issueSchema);
            List<IssueHandleRecord> recordList = next.get("hList").asList((item) -> {
                return (IssueHandleRecord)JSONUtil.parseObject(item.asNode(), IssueHandleRecord.class);
            });
            issueInfo.setRecords(recordList);
            LocationInfo locationInfo = new LocationInfo();
            locationInfo.setCatalogOid(issueInfo.getCatalogOid());
            locationInfo.setCatalogType(issueInfo.getCatalogType());
            locationInfo.setContainerModelDefinition(issueInfo.getContainerModelDefinition());
            locationInfo.setContainerOid(issueInfo.getContainerOid());
            locationInfo.setContainerType(issueInfo.getContainerType());
            issueInfo.setLocationInfo(locationInfo);
            issueList.add(issueInfo);
        }

        PageResult<IssueInfo> result = new PageResult(count.intValue(), index, size, issueList);
        return result;
    }

    private String getStatus(List<String> viewCodeList) {
        StringBuilder state = new StringBuilder();
        Iterator var3 = viewCodeList.iterator();

        while(var3.hasNext()) {
            String code = (String)var3.next();
            state.append("'").append(code).append("',");
        }

        return state.toString().substring(0, state.lastIndexOf(","));
    }

    public PageResult<IssueInfo> searchByData(SubPageFilter subPageFilter) {
        String tenantOid = SessionHelper.getCurrentUser().getTenantOid();
        StringBuilder cypher = new StringBuilder();
        StringBuilder countCypher = new StringBuilder();
        Map<String, Object> map = new HashMap();
        map.put("tenantOid", tenantOid);
        int index = subPageFilter.getIndex();
        int size = subPageFilter.getSize();
        cypher.append(" Call { match (i").append(this.dealLabel("Issue")).append(")");
        cypher.append(" where i.tenantOid=$tenantOid ");
        Condition condition = subPageFilter.getFilter();
        if (condition != null) {
            cypher.append(" and ").append(condition.parse(new ConditionParserNeo4j("i", map)));
        }

        countCypher.append(cypher);
        countCypher.append(" return count(i) as count } return count");
        cypher.append(" return i");
        if (index != 0 && size != 0) {
            int page = (index - 1) * size;
            cypher.append(" SKIP $skip LIMIT $size ");
            map.put("skip", page);
            map.put("size", size);
        }

        cypher.append("} with i optional match(i)-[:IMPACT]->(p) with i, collect(p) as pList optional match(i)-[:HAS]->(s:IssueSchema) with i,pList,s optional match(i)-[:RECORD]->(h:IssueHandleRecord) ");
        cypher.append(" return i,pList,s,collect(h) as hList order by i.updateDate desc ");
        Long count = this.excuteCount(countCypher.toString(), map);
        Result run = this.run(cypher.toString(), map);
        ArrayList issueList = new ArrayList();

        while(run.hasNext()) {
            Record next = run.next();
            IssueInfo issueInfo = (IssueInfo)JSONUtil.parseObject(next.get("i").asNode(), IssueInfo.class);
            List<JSONObject> jsonObjectList = next.get("pList").asList((item) -> {
                return JSONUtil.parseObject(item.asNode());
            });
            issueInfo.setIssueList(jsonObjectList);
            IssueSchema issueSchema = (IssueSchema)JSONUtil.parseObject(next.get("s").asNode(), IssueSchema.class);
            issueInfo.setIssueSchema(issueSchema);
            List<IssueHandleRecord> recordList = next.get("hList").asList((item) -> {
                return (IssueHandleRecord)JSONUtil.parseObject(item.asNode(), IssueHandleRecord.class);
            });
            issueInfo.setRecords(recordList);
            LocationInfo locationInfo = new LocationInfo();
            locationInfo.setCatalogOid(issueInfo.getCatalogOid());
            locationInfo.setCatalogType(issueInfo.getCatalogType());
            locationInfo.setContainerModelDefinition(issueInfo.getContainerModelDefinition());
            locationInfo.setContainerOid(issueInfo.getContainerOid());
            locationInfo.setContainerType(issueInfo.getContainerType());
            issueInfo.setLocationInfo(locationInfo);
            issueList.add(issueInfo);
        }

        PageResult<IssueInfo> result = new PageResult(count.intValue(), index, size, issueList);
        return result;
    }

    public List<ECR> searchECR(String oid) {
        StringBuilder cypher = new StringBuilder();
        Map<String, Object> map = new HashMap();
        String tenantOid = SessionHelper.getCurrentUser().getTenantOid();
        map.put("tenantOid", tenantOid);
        map.put("oid", oid);
        cypher.append(" match (i").append(this.dealLabel("Issue")).append(")");
        cypher.append(" -[").append(this.dealLabel("GENERATE")).append("]->(e").append(this.dealLabel("ECR")).append(")");
        cypher.append(" where i.tenantOid=$tenantOid and i.oid=$oid");
        cypher.append(" return e");
        return this.excuteList(cypher.toString(), map, "e", ECR.class);
    }

    public List<IssueInfo> queryIssueByTenantOid() {
        String tenantOid = SessionHelper.getCurrentUser().getTenantOid();
        StringBuilder cypher = new StringBuilder();
        Map<String, Object> map = new HashMap();
        map.put("tenantOid", tenantOid);
        cypher.append("match (i").append(this.dealLabel("Issue")).append(")");
        cypher.append(" where i.tenantOid=$tenantOid ");
        cypher.append(" return i");
        return this.excuteList(cypher.toString(), map, "i");
    }

    public boolean checkBizObjectsExitsEcr(String oid, String type) {
        StringBuilder cypher = new StringBuilder();
        Map<String, Object> map = new HashMap();
        map.put("oid", oid);
        cypher.append("match (d").append(this.dealLabel(type)).append(")<-[:").append("IMPACT").append("]-(e:ECR)");
        cypher.append(" where d.oid=$oid ");
        cypher.append(" return e");
        List<ECR> ecrList = this.excuteList(cypher.toString(), map, "e", ECR.class);
        return !CollectionUtils.isEmpty(ecrList);
    }

    public List<IssueInfoData> queryIssueData() {
        StringBuilder cypher = new StringBuilder();
        Map<String, Object> map = new HashMap(8);
        String tenantOid = SessionHelper.getCurrentUser().getTenantOid();
        map.put("tenantOid", tenantOid);
        cypher.append(" match (i").append(this.dealLabel("Issue")).append(")");
        cypher.append(" where i.tenantOid=$tenantOid ");
        cypher.append(" return i ORDER BY i.updateDate desc ");
        List<Issue> issueList = this.excuteList(cypher.toString(), map, "i", Issue.class);
        List<IssueInfoData> infoDataList = new ArrayList();
        if (!CollectionUtils.isEmpty(issueList)) {
            Iterator var8 = issueList.iterator();

            while(var8.hasNext()) {
                Issue issue = (Issue)var8.next();
                IssueInfoData issueInfoData = new IssueInfoData();
                map = new HashMap(8);
                map.put("oid", issue.getOid());
                cypher = new StringBuilder("match (i:Issue{oid:$oid})<-[:REVIEW_FOR]-(po:ProcessOrder) return po.processInstanceId as processInstanceId");
                String processInstanceId = this.excuteString(cypher.toString(), map, "processInstanceId");
                if (StringUtils.isEmpty(processInstanceId)) {
                    cypher = new StringBuilder("match (i:Issue{oid:$oid})-[:GENERATE]->(e:ECR)<-[:REVIEW_FOR]-(p:ProcessOrder) return p.processInstanceId as processInstanceId");
                    processInstanceId = this.excuteString(cypher.toString(), map, "processInstanceId");
                }

                issueInfoData.setProcessInstanceId(processInstanceId);
                issueInfoData.setName(issue.getName());
                issueInfoData.setNumber(issue.getNumber());
                issueInfoData.setContainerName(issue.getContainerName());
                issueInfoData.setIssueType(issue.getIssueType());
                issueInfoData.setPriority(issue.getPriority());
                if (!CollectionUtils.isEmpty(issue.getProposedBy())) {
                    issueInfoData.setProposedBy(issue.getProposedBy().getString("name"));
                } else {
                    issueInfoData.setProposedBy("");
                }

                if (!CollectionUtils.isEmpty(issue.getPersonLiable())) {
                    issueInfoData.setPersonLiable(issue.getPersonLiable().getString("name"));
                } else {
                    issueInfoData.setPersonLiable("");
                }

                issueInfoData.setProposeDate(issue.getProposeDate());
                issueInfoData.setCloseDate(issue.getCloseDate());
                issueInfoData.setLifecycleStatus(issue.getLifecycleStatus());
                infoDataList.add(issueInfoData);
            }
        }

        return infoDataList;
    }
}
