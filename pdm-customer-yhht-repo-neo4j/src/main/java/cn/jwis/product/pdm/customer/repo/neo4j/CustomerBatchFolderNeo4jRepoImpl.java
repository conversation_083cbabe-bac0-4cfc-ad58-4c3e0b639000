package cn.jwis.product.pdm.customer.repo.neo4j;

import cn.jwis.framework.base.web.session.SessionHelper;
import cn.jwis.framework.database.neo4j.condition.Neo4jEnableConditional;
import cn.jwis.framework.database.neo4j.execution.CommonEntityTemplate;
import cn.jwis.framework.database.neo4j.util.JSONUtil;
import cn.jwis.platform.iam.user.User;
import cn.jwis.platform.plm.container.entity.Folder;
import cn.jwis.platform.plm.container.entity.TeamRole;
import cn.jwis.product.pdm.customer.repo.BatchFolderRepo;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.neo4j.driver.Record;
import org.neo4j.driver.Result;
import org.neo4j.driver.internal.InternalRelationship;
import org.springframework.context.annotation.Conditional;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> zhaikun
 * @Date ： 2024/2/2
 * @Description :
 */

@Slf4j
@Service
@Conditional(Neo4jEnableConditional.class)
public class CustomerBatchFolderNeo4jRepoImpl extends CommonEntityTemplate<Folder> implements BatchFolderRepo {


    @Override
    public List<JSONObject> queryFolderPaths(String containerOid) {
        StringBuffer cypher = new StringBuffer();
        Map<String, Object> map = new HashMap<>();
        map.put("tenantOid", SessionHelper.getCurrentUser().getTenantOid());
        map.put("containerOid", containerOid);
        cypher.append("match (s:Folder)<-[r:CONTAIN*1..]-(f:Folder)<-[:CONTAIN]-(p:Container) where p")
                .append(".tenantOid=$tenantOid and p.modelDefinition='ProductContainer' and p.oid=$containerOid return s")
                .append(".name as name,s.oid as oid,r");
        Result run = this.run(cypher.toString(), map);
        return getJsonObjects(run);
    }

    @Override
    public List<JSONObject> queryAdministrativeDomainPaths(String rootFolderOid) {
        StringBuffer cypher = new StringBuffer();
        Map<String, Object> map = new HashMap<>();
        map.put("tenantOid", SessionHelper.getCurrentUser().getTenantOid());
        map.put("rootFolderOid", rootFolderOid);
        cypher.append("match (s:AdministrativeDomain)<-[r:INHERIT*1..]-(f:AdministrativeDomain) where f")
                .append(".tenantOid=$tenantOid and  f.containerOid=$rootFolderOid return s")
                .append(".name as name,s.oid as oid,r");
        Result run = this.run(cypher.toString(), map);
        return getJsonObjects(run);
    }

    @Override
    public TeamRole queryContainerMemberRole(String containerOid) {
        StringBuffer cypher = new StringBuffer();
        Map<String, Object> map = new HashMap<>();
        map.put("oid", containerOid);
        cypher.append("match(n:Container) -[r:ASSIGN]->(t:Team) -[l:CONTAIN]->(tr:TeamRole) ")
                .append(" where n.oid = $oid and tr.name = 'member' return tr");
        Result run = this.run(cypher.toString(), map);
        if (run.hasNext()) {
            Record record = run.next();
            return JSONUtil.parseObject(record.get("tr"), TeamRole.class);
        }
        return null;
    }

    @Override
    public List<User> queryContainerMembers(String containerOid) {
        List<User> result = new ArrayList<>();
        StringBuffer cypher = new StringBuffer();
        Map<String, Object> map = new HashMap<>();
        map.put("oid", containerOid);
        cypher.append("match(n:Container) -[r:ASSIGN]->(t:Team) -[l:CONTAIN]->(tr:TeamRole)")
                .append(" -[ul]-> (u:User)  where n.oid = $oid and tr.name = 'member' return u");
        Result run = this.run(cypher.toString(), map);
        while (run.hasNext()) {
            Record record = run.next();
            result.add(JSONUtil.parseObject(record.get("u"), User.class));
        }
        return result;
    }

    private List<JSONObject> getJsonObjects(Result run) {
        List<JSONObject> result = new ArrayList<>();
        Record next;
        while (run.hasNext()) {
            JSONObject jsonObject = new JSONObject();
            next = run.next();
            jsonObject.put("name", next.get("name").asString());
            jsonObject.put("oid", next.get("oid").asString());
            List<Object> rList = next.get("r").asList();
            for (Object o : rList) {
                Map<String, Object> stringObjectMap = ((InternalRelationship) o).asMap();
                if (stringObjectMap.get("toOid").equals(jsonObject.get("oid"))) {
                    jsonObject.put("fromOid", stringObjectMap.get("fromOid"));
                    jsonObject.put("toOid", stringObjectMap.get("toOid"));
                }
            }
            result.add(jsonObject);
        }
        return result;
    }


}
