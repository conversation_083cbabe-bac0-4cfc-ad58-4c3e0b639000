stages:
  - deploy  #此流水线只部署到maven，不部署到开发环境

variables:
  version: "1.0.0"    # 版本号，按具体修改
  project_name: "pdm-customer-yhht-server"    # 项目名，按具体修改
  scripts_path: "/home/<USER>/cicd/scripts"
  commit_id: "${CI_COMMIT_SHA}"
  harbor_url: "harbor.jwis.cn"
  harbor_project: "gitlab-cicd"

cache:
  paths:
    - target/*.jar

deploy:
  stage: deploy
  only:
    - develop    # 分支名称，按具体修改
  tags:
    - RD-runner   # 研发用流水线
  script:
    - echo "${GITLAB_USER_EMAIL} lunch pipeline"
    - rm -rf /home/<USER>/.m2/repository/cn/jwis/*
    - mvn clean deploy