package cn.jwis.product.pdm.customer.remote;

import cn.jwis.framework.base.feign.FeignConfig;
import cn.jwis.framework.base.response.Result;
import cn.jwis.platform.plm.workflow.engine.dto.TaskRequest;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: workflow.service.gateway.url=http://***********:5001/pdm-standalone/workflow-micro
 * @date 2023/9/7 9:07
 * @Email <EMAIL>
 */
@FeignClient(name = "CustomerWorkflowRemoteFeign", configuration = FeignConfig.class, url = "${workflow.service.gateway.url}")
public interface WorkFlowRemoteFeign {

    @RequestMapping(value = "/workflow/task/finishTask", method = RequestMethod.POST)
    Result finishTask(@RequestBody TaskRequest taskRequest);

}
