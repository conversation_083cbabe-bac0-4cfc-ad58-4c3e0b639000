package cn.jwis.product.pdm.customer.remote;

import cn.hutool.json.JSONObject;
import cn.jwis.framework.base.feign.FeignConfig;
import cn.jwis.framework.base.response.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(name = "CustomerSysconfigRemoteFeign", configuration = FeignConfig.class, url = "${sysconfig.service.gateway.url}")
public interface SysconfigRemoteFeign {

    @RequestMapping(value = "/preferences/setting/query-config-value", method = RequestMethod.GET)
    Result<List<JSONObject>> findConfigVal(@RequestParam String name);

    @RequestMapping(value = "/preferences/setting/query-config?searchKey=&keyword=", method = RequestMethod.GET)
    Result<List<JSONObject>> findConfig();

}
