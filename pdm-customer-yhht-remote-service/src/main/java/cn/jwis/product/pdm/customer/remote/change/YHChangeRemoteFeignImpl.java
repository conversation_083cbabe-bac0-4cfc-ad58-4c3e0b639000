package cn.jwis.product.pdm.customer.remote.change;

import cn.jwis.framework.base.exception.JWIServiceException;
import cn.jwis.framework.base.response.Result;
import cn.jwis.framework.base.util.Assert;
import cn.jwis.platform.plm.workflow.engine.dto.SearchChangeDataDTO;
import cn.jwis.product.pdm.change.dto.ECACreateDTO;
import cn.jwis.product.pdm.change.entity.ECA;
import cn.jwis.product.pdm.change.response.ECOChangeInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR> lhm
 * @Date ： Created in 2023/12/25 13:36
 * @Description :
 */
@Component
public class YHChangeRemoteFeignImpl implements YHChangeRemote {

    @Autowired
    private YHChangeRemoteFeign changeRemoteFeign;

    @Override
    public ECA createECA(ECACreateDTO ecaCreateDTO) {
        Result<ECA> res = changeRemoteFeign.createECA(ecaCreateDTO);
        if (res.getCode() == 0) {
            return res.getResult();
        } else {
            throw new JWIServiceException(res.getMsg());
        }
    }

    @Override
    public List<ECOChangeInfo> findChangeInfoByEco(SearchChangeDataDTO searchChangeDataDTO) {
        Result<List<ECOChangeInfo>> res = changeRemoteFeign.findChangeInfoByEco(searchChangeDataDTO);
        if (res.getCode() == 0) {
            Assert.notNull(res.getResult(),"根据ECO远程查询变更对象为[]");

            return res.getResult();
        } else {
            throw new JWIServiceException(res.getMsg());
        }
    }
}
