package cn.jwis.product.pdm.customer.remote.change;

import cn.jwis.framework.base.feign.FeignConfig;
import cn.jwis.framework.base.response.Result;
import cn.jwis.platform.plm.workflow.engine.dto.SearchChangeDataDTO;
import cn.jwis.product.pdm.change.dto.ECACreateDTO;
import cn.jwis.product.pdm.change.entity.ECA;
import cn.jwis.product.pdm.change.response.ECOChangeInfo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;

/**
 * <AUTHOR> lhm
 * @Date ： Created in 2023/12/25 13:35
 * @Description :
 */
@FeignClient(name = "YHChangeRemoteFeign", configuration = FeignConfig.class, url = "${change.service.gateway.url}")
public interface YHChangeRemoteFeign {

    @RequestMapping(value = "/eca/create", method = RequestMethod.GET)
    Result<ECA> createECA(@RequestBody ECACreateDTO ecaCreateDTO);

    @RequestMapping(value = "/eco/findChangeInfo", method = RequestMethod.POST)
    Result<List<ECOChangeInfo>> findChangeInfoByEco(@RequestBody SearchChangeDataDTO searchChangeDataDTO);
}

