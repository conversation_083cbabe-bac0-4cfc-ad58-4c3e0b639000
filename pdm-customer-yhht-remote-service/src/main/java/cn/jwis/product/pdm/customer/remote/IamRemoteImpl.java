package cn.jwis.product.pdm.customer.remote;

import cn.jwis.framework.base.response.Result;
import cn.jwis.platform.iam.user.User;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> yefei
 * @Date ： 2024/6/20
 * @Description :
 */
@Component
public class IamRemoteImpl implements IamRemote {

    @Resource
    IamRemoteFeign iamRemoteFeign;
    @Override
    public List<User> findUserByAccount(List<String> accountList) {

        Result<List<User>> result = iamRemoteFeign.findUserByAccount(accountList);
        if(result.getCode() == 0) {
            return result.getResult();
        }
        return null;
    }
}
