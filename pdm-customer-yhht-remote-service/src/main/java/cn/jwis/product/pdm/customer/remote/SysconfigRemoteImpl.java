package cn.jwis.product.pdm.customer.remote;

import cn.hutool.json.JSONObject;
import cn.jwis.framework.base.exception.JWIServiceException;
import cn.jwis.framework.base.response.Result;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.util.List;

@Component
public class SysconfigRemoteImpl implements SysconfigRemote {

    @Resource
    private SysconfigRemoteFeign sysconfigRemoteFeign;

    @Override
    public List<JSONObject> findConfigVal(String name) {
        Result<List<JSONObject>> res = sysconfigRemoteFeign.findConfigVal(name);
        if (res.getCode() == 0) {
            return res.getResult();
        } else {
            throw new JWIServiceException(res.getMsg());
        }
    }

    @Override
    public List<JSONObject> findConfig() {
        Result<List<JSONObject>> res = sysconfigRemoteFeign.findConfig();
        if (res.getCode() == 0) {
            return res.getResult();
        } else {
            throw new JWIServiceException(res.getMsg());
        }
    }

}
