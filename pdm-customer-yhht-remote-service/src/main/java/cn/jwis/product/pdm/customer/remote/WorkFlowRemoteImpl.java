package cn.jwis.product.pdm.customer.remote;

import cn.jwis.platform.plm.workflow.engine.dto.TaskRequest;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2023/9/7 9:07
 * @Email <EMAIL>
 */
@Component
public class WorkFlowRemoteImpl implements WorkFlowRemote {

    @Resource
    WorkFlowRemoteFeign workFlowRemoteFeign;

    @Override
    public void finishTask(TaskRequest taskRequest) {
        workFlowRemoteFeign.finishTask(taskRequest);
    }
}
