package cn.jwis.product.pdm.customer.remote;

import cn.jwis.framework.base.feign.FeignConfig;
import cn.jwis.framework.base.response.Result;
import cn.jwis.platform.iam.user.User;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;

/**
 * <AUTHOR> yefei
 * @Date ： 2024/6/20
 * @Description :
 */

@FeignClient(name = "CustomerIamRemoteFeign", configuration = FeignConfig.class, url = "${iam.service.gateway.url}")
public interface IamRemoteFeign {
    @RequestMapping(value = "/account/queryByAccount", method = RequestMethod.POST)
    Result<List<User>> findUserByAccount(@RequestBody List<String> accountList);
}
