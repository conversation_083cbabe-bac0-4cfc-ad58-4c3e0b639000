package cn.jwis.product.pdm.customer.entity;

import cn.jwis.platform.plm.container.entity.Container;
import cn.jwis.platform.plm.container.entity.Folder;
import cn.jwis.platform.plm.container.entity.Team;
import cn.jwis.platform.plm.container.entity.TeamRole;
import lombok.Data;

import java.util.List;


/**
 * 用户产品文件夹团队角色DTO
 * <AUTHOR>
 * @date 2024/10/16
 */
@Data
public class CustomerUserProductFolderTeamDTO extends Folder {

    private List<TeamRole> teamRoles;
    private Team team;
    private Container container;
    private String folderNames;
    private String userOid;

}
