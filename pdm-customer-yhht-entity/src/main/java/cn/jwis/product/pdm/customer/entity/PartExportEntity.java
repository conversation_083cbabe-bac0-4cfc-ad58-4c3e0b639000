package cn.jwis.product.pdm.customer.entity;

import cn.jwis.framework.base.annotation.Description;
import cn.jwis.framework.base.domain.entity.BaseEntity;
import cn.jwis.platform.plm.foundation.attachment.entity.File;
import cn.jwis.product.pdm.customer.entity.assistant.anno.ExcelAnno;
import cn.jwis.product.pdm.customer.entity.assistant.anno.ExcelDataProp;
import cn.jwis.product.pdm.customer.entity.assistant.anno.ExcelTitleProp;
import cn.jwis.product.pdm.partbom.part.enums.GenericEnum;
import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiModelProperty;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;

public class PartExportEntity extends BaseEntity {
    public static final String TYPE = "PartIteration";
    @NotBlank
    @ApiModelProperty("名称")
    @ExcelAnno(titleInfos = {@ExcelTitleProp(value = "必填", fontSize = 11, fontColor = IndexedColors.RED, fillForegroundColor = IndexedColors.AUTOMATIC, fontBold = false)
                , @ExcelTitleProp(value = "物料名称(简化)", fontSize = 11, fillForegroundColor = IndexedColors.LIGHT_GREEN)
                , @ExcelTitleProp(value = "name", fontSize = 11, fontBold = false, hzAlignment = HorizontalAlignment.LEFT, fillForegroundColor = IndexedColors.LIGHT_GREEN)}
            , dataInfos = @ExcelDataProp(fontSize = 10, rowHeight = 500), order = 500)
    private String name;
    @ApiModelProperty("编码")
    @ExcelAnno(titleInfos = {@ExcelTitleProp(value = "", fillForegroundColor = IndexedColors.AUTOMATIC)
                , @ExcelTitleProp(value = "PDM编码", fontSize = 11, fillForegroundColor = IndexedColors.LIGHT_GREEN)
                , @ExcelTitleProp(value = "number", fontSize = 11, fontBold = false, hzAlignment = HorizontalAlignment.LEFT, fillForegroundColor = IndexedColors.LIGHT_GREEN)}
            , dataInfos = @ExcelDataProp(fontSize = 10, rowHeight = 500), order = 400)
    private String number;

    @ExcelAnno(titleInfos = {@ExcelTitleProp(value = "必填", fontSize = 11, fontColor = IndexedColors.RED, fillForegroundColor = IndexedColors.AUTOMATIC, fontBold = false)
                , @ExcelTitleProp(value = "物料规格/图号", fontSize = 11, fillForegroundColor = IndexedColors.LIGHT_GREEN)
                , @ExcelTitleProp(value = "cn_jwis_gg", fontSize = 11, fontBold = false, hzAlignment = HorizontalAlignment.LEFT, fillForegroundColor = IndexedColors.LIGHT_GREEN)}
            , dataInfos = @ExcelDataProp(fontSize = 10, rowHeight = 500), order = 600)
    private String cn_jwis_gg;

    @ApiModelProperty("通用类型")
    private GenericEnum genericType;
    @NotNull
    @ApiModelProperty("默认单位")
    @ExcelAnno(titleInfos = {@ExcelTitleProp(value = "必填", fontSize = 11, fontColor = IndexedColors.RED, fillForegroundColor = IndexedColors.AUTOMATIC, fontBold = false)
                , @ExcelTitleProp(value = "计量单位", fontSize = 11, fillForegroundColor = IndexedColors.LIGHT_GREEN)
                , @ExcelTitleProp(value = "defaultUnit", fontSize = 11, fontBold = false, hzAlignment = HorizontalAlignment.LEFT, fillForegroundColor = IndexedColors.LIGHT_GREEN)}
            , dataInfos = @ExcelDataProp(fontSize = 10, rowHeight = 500), order = 900)
    private String defaultUnit;

    @ExcelAnno(titleInfos = {@ExcelTitleProp(value = "必填", fontSize = 11, fontColor = IndexedColors.RED, fillForegroundColor = IndexedColors.AUTOMATIC, fontBold = false)
                , @ExcelTitleProp(value = "封装形式", fontSize = 11, fillForegroundColor = IndexedColors.LIGHT_GREEN)
                , @ExcelTitleProp(value = "cn_jwis_fzxs", fontSize = 11, fontBold = false, hzAlignment = HorizontalAlignment.LEFT, fillForegroundColor = IndexedColors.LIGHT_GREEN)}
            , dataInfos = @ExcelDataProp(fontSize = 10, rowHeight = 500), order = 1000)
    private String cn_jwis_fzxs;

    @ExcelAnno(titleInfos = {@ExcelTitleProp(value = "必填下拉选择", fontSize = 11, fontColor = IndexedColors.RED, fillForegroundColor = IndexedColors.AUTOMATIC, fontBold = false)
                , @ExcelTitleProp(value = "生产厂家", fontSize = 11, fillForegroundColor = IndexedColors.LIGHT_GREEN)
                , @ExcelTitleProp(value = "cn_jwis_sccj", fontSize = 11, fontBold = false, hzAlignment = HorizontalAlignment.LEFT, fillForegroundColor = IndexedColors.LIGHT_GREEN)}
            , dataInfos = @ExcelDataProp(fontSize = 10, rowHeight = 500), order = 1100)
    private String cn_jwis_sccj;

    @ExcelAnno(titleInfos = {@ExcelTitleProp(value = "必填下拉选择", fontSize = 11, fontColor = IndexedColors.RED, fillForegroundColor = IndexedColors.AUTOMATIC, fontBold = false)
                , @ExcelTitleProp(value = "质量等级", fontSize = 11, fillForegroundColor = IndexedColors.LIGHT_GREEN)
                , @ExcelTitleProp(value = "cn_jwis_zldj", fontSize = 11, fontBold = false, hzAlignment = HorizontalAlignment.LEFT, fillForegroundColor = IndexedColors.LIGHT_GREEN)}
            , dataInfos = @ExcelDataProp(fontSize = 10, rowHeight = 500), order = 1200)
    private String cn_jwis_zldj;

    @ExcelAnno(titleInfos = {@ExcelTitleProp(value = "必填", fontSize = 11, fontColor = IndexedColors.RED, fillForegroundColor = IndexedColors.AUTOMATIC, fontBold = false)
                , @ExcelTitleProp(value = "筛选级", fontSize = 11, fillForegroundColor = IndexedColors.LIGHT_GREEN)
                , @ExcelTitleProp(value = "cn_jwis_sxzldj", fontSize = 11, fontBold = false, hzAlignment = HorizontalAlignment.LEFT, fillForegroundColor = IndexedColors.LIGHT_GREEN)}
            , dataInfos = @ExcelDataProp(fontSize = 10, rowHeight = 500), order = 1300)
    private String cn_jwis_sxzldj;

    @ApiModelProperty("来源")
    @ExcelAnno(titleInfos = {@ExcelTitleProp(value = "必填", fontSize = 11, fontColor = IndexedColors.RED, fillForegroundColor = IndexedColors.AUTOMATIC, fontBold = false)
                , @ExcelTitleProp(value = "料品形态", fontSize = 11, fillForegroundColor = IndexedColors.LIGHT_GREEN)
                , @ExcelTitleProp(value = "source", fontSize = 11, fontBold = false, hzAlignment = HorizontalAlignment.LEFT, fillForegroundColor = IndexedColors.LIGHT_GREEN)}
            , dataInfos = @ExcelDataProp(fontSize = 10, rowHeight = 500), order = 800)
    private String source;
    @ApiModelProperty("当前阶段")
    private String currentStage;
    @ApiModelProperty("扩展属性")
    private JSONObject extensionContent;
    @ApiModelProperty("描述")
    @ExcelAnno(titleInfos = {@ExcelTitleProp(value = "选填", fontSize = 11, fillForegroundColor = IndexedColors.AUTOMATIC)
                , @ExcelTitleProp(value = "物料描述", fontSize = 11, fillForegroundColor = IndexedColors.LIGHT_GREEN)
                , @ExcelTitleProp(value = "description", fontSize = 11, fontBold = false, hzAlignment = HorizontalAlignment.LEFT, fillForegroundColor = IndexedColors.LIGHT_GREEN)}
            , dataInfos = @ExcelDataProp(fontSize = 10, rowHeight = 500), order = 700)
    private String description;
    @ApiModelProperty("缩略图")
    private String thumbnailOid;
    @ApiModelProperty("大版本")
    private String version;
    @ApiModelProperty("迭代版本")
    private int iteratedVersion;
    @ApiModelProperty("是否最新版本")
    private boolean latest;
    @Description("是否分支最新版本")
    private boolean branchLatest;
    @ApiModelProperty(
        value = "版本序号",
        notes = "用于版本排序的序号"
    )
    private int versionSortId;
    @ApiModelProperty("版本的显示字段，用于前端展示")
    private String displayVersion;
    @ApiModelProperty("挂锁 备注")
    private String lockNote;
    @ApiModelProperty("挂锁者 账号")
    private String lockOwnerAccount;
    @ApiModelProperty("挂锁者 oid")
    private String lockOwnerOid;
    @ApiModelProperty("挂锁时间")
    private Long lockedTime;
    @ApiModelProperty("挂锁时间")
    private String lockSourceOid;
    @ApiModelProperty("生命周期模板 oid")
    private String lifecycleOid;
    @ApiModelProperty("生命周期状态")
    private String lifecycleStatus;
    @ApiModelProperty("容器 oid")
    private String containerOid;
    @ApiModelProperty("容器类型 ")
    private String containerType;
    @ApiModelProperty("上下文modelDefinition")
    private String containerModelDefinition;
    @ApiModelProperty("目录 类型")
    private String catalogType;
    @ApiModelProperty("目录 oid")
    private String catalogOid;
    @ApiModelProperty("分类oid ")
    private String clsOid;
    @ApiModelProperty("分类编码")
    private String clsCode;
    @ApiModelProperty("分类显示名称")
    @ExcelAnno(titleInfos = {@ExcelTitleProp(value = "RM0301集成电路", rowHeight = 500, fontSize = 11, fontColor = IndexedColors.RED, fillForegroundColor = IndexedColors.AUTOMATIC, fontBold = false)
                , @ExcelTitleProp(value = "#部件类型", rowHeight = 400, fontSize = 11, fillForegroundColor = IndexedColors.LIGHT_GREEN)
                , @ExcelTitleProp(value = "modelDefinition", rowHeight = 400, fontSize = 11, fontBold = false, hzAlignment = HorizontalAlignment.LEFT, fillForegroundColor = IndexedColors.LIGHT_GREEN)}
            , dataInfos = @ExcelDataProp(fontSize = 10, rowHeight = 500), order = 100)
    private String clsDisplayName;
    @ExcelAnno(titleInfos = {@ExcelTitleProp(value = "必填", fontSize = 11, fontColor = IndexedColors.RED, fillForegroundColor = IndexedColors.AUTOMATIC, fontBold = false)
                , @ExcelTitleProp(value = "上下文", fontSize = 11, fillForegroundColor = IndexedColors.LIGHT_GREEN)
                , @ExcelTitleProp(value = "locationInfo", fontSize = 11, fontBold = false, hzAlignment = HorizontalAlignment.LEFT, fillForegroundColor = IndexedColors.LIGHT_GREEN)}
            , dataInfos = @ExcelDataProp(fontSize = 10, rowHeight = 500), order = 200)
    private String locationInfo;
    @ExcelAnno(titleInfos = {@ExcelTitleProp(value = "", fillForegroundColor = IndexedColors.AUTOMATIC)
                , @ExcelTitleProp(value = "需求人", fontSize = 11, fillForegroundColor = IndexedColors.LIGHT_GREEN)
                , @ExcelTitleProp(value = "cn_jwis_xqr", fontSize = 11, fontBold = false, hzAlignment = HorizontalAlignment.LEFT, fillForegroundColor = IndexedColors.LIGHT_GREEN)}
            , dataInfos = @ExcelDataProp(fontSize = 10, rowHeight = 500), order = 300)
    private String cn_jwis_xqr;
    @ApiModelProperty("分类属性")
    private JSONObject clsProperty;
    @ApiModelProperty("视图oid ")
    private String viewOid;
    @ApiModelProperty("视图名称")
    private String viewName;
    @ApiModelProperty("密级")
    private int levelForSecrecy;
    @ApiModelProperty("master 的 oid ")
    private String masterOid;
    @ApiModelProperty("master 的 类型")
    private String masterType;
    @ApiModelProperty("是否由cad生成")
    private Boolean generatedByCAD;
    @ApiModelProperty("优选等级")
    private String preferenceLevel;
    private List<File> primaryFile;
    private List<File> secondaryFile;
    private List<File> uploadFileList = new ArrayList<>();

    public PartExportEntity() {
        super.setType("PartIteration");
    }

    public static String getTYPE() {
        return TYPE;
    }

    public String getName() {
        return name;
    }

    public PartExportEntity setName(String name) {
        this.name = name;
        return this;
    }

    public String getNumber() {
        return number;
    }

    public PartExportEntity setNumber(String number) {
        this.number = number;
        return this;
    }

    public GenericEnum getGenericType() {
        return genericType;
    }

    public PartExportEntity setGenericType(GenericEnum genericType) {
        this.genericType = genericType;
        return this;
    }

    public String getDefaultUnit() {
        return defaultUnit;
    }

    public PartExportEntity setDefaultUnit(String defaultUnit) {
        this.defaultUnit = defaultUnit;
        return this;
    }

    public String getSource() {
        return source;
    }

    public PartExportEntity setSource(String source) {
        this.source = source;
        return this;
    }

    public String getCurrentStage() {
        return currentStage;
    }

    public PartExportEntity setCurrentStage(String currentStage) {
        this.currentStage = currentStage;
        return this;
    }

    public JSONObject getExtensionContent() {
        return extensionContent;
    }

    public PartExportEntity setExtensionContent(JSONObject extensionContent) {
        this.extensionContent = extensionContent;
        return this;
    }

    public String getDescription() {
        return description;
    }

    public PartExportEntity setDescription(String description) {
        this.description = description;
        return this;
    }

    public String getThumbnailOid() {
        return thumbnailOid;
    }

    public PartExportEntity setThumbnailOid(String thumbnailOid) {
        this.thumbnailOid = thumbnailOid;
        return this;
    }

    public String getVersion() {
        return version;
    }

    public PartExportEntity setVersion(String version) {
        this.version = version;
        return this;
    }

    public int getIteratedVersion() {
        return iteratedVersion;
    }

    public PartExportEntity setIteratedVersion(int iteratedVersion) {
        this.iteratedVersion = iteratedVersion;
        return this;
    }

    public boolean isLatest() {
        return latest;
    }

    public PartExportEntity setLatest(boolean latest) {
        this.latest = latest;
        return this;
    }

    public boolean isBranchLatest() {
        return branchLatest;
    }

    public PartExportEntity setBranchLatest(boolean branchLatest) {
        this.branchLatest = branchLatest;
        return this;
    }

    public int getVersionSortId() {
        return versionSortId;
    }

    public PartExportEntity setVersionSortId(int versionSortId) {
        this.versionSortId = versionSortId;
        return this;
    }

    public String getDisplayVersion() {
        return displayVersion;
    }

    public PartExportEntity setDisplayVersion(String displayVersion) {
        this.displayVersion = displayVersion;
        return this;
    }

    public String getLockNote() {
        return lockNote;
    }

    public PartExportEntity setLockNote(String lockNote) {
        this.lockNote = lockNote;
        return this;
    }

    public String getLockOwnerAccount() {
        return lockOwnerAccount;
    }

    public PartExportEntity setLockOwnerAccount(String lockOwnerAccount) {
        this.lockOwnerAccount = lockOwnerAccount;
        return this;
    }

    public String getLockOwnerOid() {
        return lockOwnerOid;
    }

    public PartExportEntity setLockOwnerOid(String lockOwnerOid) {
        this.lockOwnerOid = lockOwnerOid;
        return this;
    }

    public Long getLockedTime() {
        return lockedTime;
    }

    public PartExportEntity setLockedTime(Long lockedTime) {
        this.lockedTime = lockedTime;
        return this;
    }

    public String getLockSourceOid() {
        return lockSourceOid;
    }

    public PartExportEntity setLockSourceOid(String lockSourceOid) {
        this.lockSourceOid = lockSourceOid;
        return this;
    }

    public String getLifecycleOid() {
        return lifecycleOid;
    }

    public PartExportEntity setLifecycleOid(String lifecycleOid) {
        this.lifecycleOid = lifecycleOid;
        return this;
    }

    public String getLifecycleStatus() {
        return lifecycleStatus;
    }

    public PartExportEntity setLifecycleStatus(String lifecycleStatus) {
        this.lifecycleStatus = lifecycleStatus;
        return this;
    }

    public String getContainerOid() {
        return containerOid;
    }

    public PartExportEntity setContainerOid(String containerOid) {
        this.containerOid = containerOid;
        return this;
    }

    public String getContainerType() {
        return containerType;
    }

    public PartExportEntity setContainerType(String containerType) {
        this.containerType = containerType;
        return this;
    }

    public String getContainerModelDefinition() {
        return containerModelDefinition;
    }

    public PartExportEntity setContainerModelDefinition(String containerModelDefinition) {
        this.containerModelDefinition = containerModelDefinition;
        return this;
    }

    public String getCatalogType() {
        return catalogType;
    }

    public PartExportEntity setCatalogType(String catalogType) {
        this.catalogType = catalogType;
        return this;
    }

    public String getCatalogOid() {
        return catalogOid;
    }

    public PartExportEntity setCatalogOid(String catalogOid) {
        this.catalogOid = catalogOid;
        return this;
    }

    public String getClsOid() {
        return clsOid;
    }

    public PartExportEntity setClsOid(String clsOid) {
        this.clsOid = clsOid;
        return this;
    }

    public String getClsCode() {
        return clsCode;
    }

    public PartExportEntity setClsCode(String clsCode) {
        this.clsCode = clsCode;
        return this;
    }

    public String getClsDisplayName() {
        return clsDisplayName;
    }

    public PartExportEntity setClsDisplayName(String clsDisplayName) {
        this.clsDisplayName = clsDisplayName;
        return this;
    }

    public JSONObject getClsProperty() {
        return clsProperty;
    }

    public PartExportEntity setClsProperty(JSONObject clsProperty) {
        this.clsProperty = clsProperty;
        return this;
    }

    public String getViewOid() {
        return viewOid;
    }

    public PartExportEntity setViewOid(String viewOid) {
        this.viewOid = viewOid;
        return this;
    }

    public String getViewName() {
        return viewName;
    }

    public PartExportEntity setViewName(String viewName) {
        this.viewName = viewName;
        return this;
    }

    public int getLevelForSecrecy() {
        return levelForSecrecy;
    }

    public PartExportEntity setLevelForSecrecy(int levelForSecrecy) {
        this.levelForSecrecy = levelForSecrecy;
        return this;
    }

    public String getMasterOid() {
        return masterOid;
    }

    public PartExportEntity setMasterOid(String masterOid) {
        this.masterOid = masterOid;
        return this;
    }

    public String getMasterType() {
        return masterType;
    }

    public PartExportEntity setMasterType(String masterType) {
        this.masterType = masterType;
        return this;
    }

    public Boolean getGeneratedByCAD() {
        return generatedByCAD;
    }

    public PartExportEntity setGeneratedByCAD(Boolean generatedByCAD) {
        this.generatedByCAD = generatedByCAD;
        return this;
    }

    public String getPreferenceLevel() {
        return preferenceLevel;
    }

    public PartExportEntity setPreferenceLevel(String preferenceLevel) {
        this.preferenceLevel = preferenceLevel;
        return this;
    }

    public List<File> getPrimaryFile() {
        return primaryFile;
    }

    public PartExportEntity setPrimaryFile(List<File> primaryFile) {
        this.primaryFile = primaryFile;
        return this;
    }

    public List<File> getSecondaryFile() {
        return secondaryFile;
    }

    public PartExportEntity setSecondaryFile(List<File> secondaryFile) {
        this.secondaryFile = secondaryFile;
        return this;
    }


    public String getCn_jwis_gg() {
        return cn_jwis_gg;
    }

    public PartExportEntity setCn_jwis_gg(String cn_jwis_gg) {
        this.cn_jwis_gg = cn_jwis_gg;
        return this;
    }

    public String getCn_jwis_fzxs() {
        return cn_jwis_fzxs;
    }

    public PartExportEntity setCn_jwis_fzxs(String cn_jwis_fzxs) {
        this.cn_jwis_fzxs = cn_jwis_fzxs;
        return this;
    }

    public String getCn_jwis_sccj() {
        return cn_jwis_sccj;
    }

    public PartExportEntity setCn_jwis_sccj(String cn_jwis_sccj) {
        this.cn_jwis_sccj = cn_jwis_sccj;
        return this;
    }

    public String getCn_jwis_zldj() {
        return cn_jwis_zldj;
    }

    public PartExportEntity setCn_jwis_zldj(String cn_jwis_zldj) {
        this.cn_jwis_zldj = cn_jwis_zldj;
        return this;
    }

    public String getCn_jwis_sxzldj() {
        return cn_jwis_sxzldj;
    }

    public PartExportEntity setCn_jwis_sxzldj(String cn_jwis_sxzldj) {
        this.cn_jwis_sxzldj = cn_jwis_sxzldj;
        return this;
    }

    public String getLocationInfo() {
        return locationInfo;
    }

    public PartExportEntity setLocationInfo(String locationInfo) {
        this.locationInfo = locationInfo;
        return this;
    }

    public String getCn_jwis_xqr() {
        return cn_jwis_xqr;
    }

    public PartExportEntity setCn_jwis_xqr(String cn_jwis_xqr) {
        this.cn_jwis_xqr = cn_jwis_xqr;
        return this;
    }

    public List<File> getUploadFileList() {
        return uploadFileList;
    }

    public PartExportEntity setUploadFileList(List<File> uploadFileList) {
        this.uploadFileList = uploadFileList;
        return this;
    }
}
