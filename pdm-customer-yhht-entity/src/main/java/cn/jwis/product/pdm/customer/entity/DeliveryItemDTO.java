package cn.jwis.product.pdm.customer.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@ApiModel("交付清单项")
@Data
public class DeliveryItemDTO {

    @ApiModelProperty(value = "节点ID（用于修改/删除，新增可空）")
    private String id;

    @ApiModelProperty(value = "父节点ID（根节点为null）")
    private String parentId;

    @ApiModelProperty(value = "节点类型：Structure(目录节点)，Category(交付物节点)")
    private String type;

    @ApiModelProperty(value = "节点名称")
    private String name;

    @ApiModelProperty(value = "状态：新增、修改、删除")
    private String status;

    @ApiModelProperty(value = "责任人域账号，仅适用于Category节点")
    private String responsible;

    @ApiModelProperty(value = "计划完成时间（格式yyyy-MM-dd），仅适用于Category节点")
    private String planFinishDate;

    @ApiModelProperty(value = "文档类型（Document、ECAD、MCAD、Part），仅适用于Category节点")
    private String documentType;

    @ApiModelProperty(value = "子节点集合")
    private List<DeliveryItemDTO> children;
}