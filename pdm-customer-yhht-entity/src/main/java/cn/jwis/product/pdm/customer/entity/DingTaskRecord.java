package cn.jwis.product.pdm.customer.entity;

import cn.jwis.framework.base.annotation.Description;
import cn.jwis.framework.base.annotation.Entity;
import cn.jwis.framework.base.domain.entity.BaseEntity;
import cn.jwis.product.pdm.customer.dos.UserInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2023/8/22 22:44
 * @Email <EMAIL>
 */
@Entity("DingTaskRecord")
@Data
@EqualsAndHashCode
public class DingTaskRecord extends BaseEntity {

    public static final String TYPE = "DingTaskRecord";

    public DingTaskRecord(String dingProcessInstanceId, String pdmTaskOid){
        super();
        super.setType(TYPE);
        super.setModelDefinition(TYPE);
        this.dingProcessInstanceId = dingProcessInstanceId;
        this.pdmTaskOid = pdmTaskOid;
    }

    public DingTaskRecord(){
        super();
        super.setType(TYPE);
    }
    // 钉钉的审批流ID
    private String dingProcessInstanceId;

    // PDM的taskID
    private String pdmTaskOid;

    @Description("文档对象清单OID清单")
    private List<String> documentIterationOidList; // 技术文档oid或者变更方案oid或者ecad文档

    @Description("物料Oid清单")
    private List<String> partInterationOidList; // 技术文档oid或者变更方案oid

    private List<String> changeDocumentIterationOidList = new ArrayList<>(); // 变更对象oid

    @Description("审批人员清单")
    private List<UserInfo> teamContent = new ArrayList<>();
    // PDM的流程id
    private String pdmProcessInstanceId;

    // PDM的流程id
    private String pdmProcessOrderId;

    // PDM的流程名称
    private String pdmProcessName;

    // PDM的任务名称
    private String pdmTaskName;

    // PDM的任务责任人
    private String director;

    private String dingProcessName;

    // 钉钉的审批结果，agree & reject
    private String result;

    private String message;

    // 钉钉的processCode
    private String processCode;

    //钉钉的businessId
    private String businessId;

    // 钉钉的审核意见
    private String remark;

    // 钉钉的消息状态
    private String dingType;

    // 外发流程回执单oid
    private String outgoingCommentFileOid;

    private List<String> issueList = new ArrayList();

    private Boolean isChange = Boolean.FALSE;

    private Boolean isPart = Boolean.FALSE;

    private List<String> ecrOidList = new ArrayList<>();

    private List<String> ecrNumberList = new ArrayList<>();

    private Boolean isEcad = Boolean.FALSE; // 是否是ecad

    private String signRequest;

    private List<String> idsAuditList = new ArrayList<>();//ids 会签人钉钉id集合

    private Boolean isTaskComplete = Boolean.FALSE; //钉钉流程中bpms_task_change 类型task任务是否完成，解决重复执行业务的问题
}
