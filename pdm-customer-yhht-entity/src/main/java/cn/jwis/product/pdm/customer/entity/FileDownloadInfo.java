package cn.jwis.product.pdm.customer.entity;

import java.util.Map;

/**
 * 钉钉审批流程中附件的 文件下载信息
 */
public class FileDownloadInfo {
    private final String resourceUrl;
    private final Map<String, String> headers;

    public FileDownloadInfo(String resourceUrl, Map<String, String> headers) {
        this.resourceUrl = resourceUrl;
        this.headers = headers;
    }

    public String getResourceUrl() {
        return resourceUrl;
    }

    public Map<String, String> getHeaders() {
        return headers;
    }

    @Override
    public String toString() {
        return "FileDownloadInfo{" +
                "resourceUrl='" + resourceUrl + '\'' +
                ", headers=" + headers +
                '}';
    }
}