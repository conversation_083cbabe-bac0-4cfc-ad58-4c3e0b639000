//package cn.jwis.product.pdm.customer.entity.assistant.cvt;
//
//import cn.hutool.json.JSONObject;
//import cn.jwis.product.pdm.customer.entity.assistant.uncheckinterface.UnCheckBiFunction;
//import java.util.Collection;
//import java.util.List;
//import java.util.Map;
//import java.util.function.BiFunction;
//import java.util.function.Function;
//
//public interface CvtNormalService {
//
//    Map<String, JSONObject> handleEachJsonAndSave(List<JSONObject> jsonList, String[] strArr, BiFunction<JSONObject, Map, JSONObject> valConvertbiFun);
//
//    <T> List<T> saveDelT(List<T> rSaveList, Function<T, String> dbGroupFun, List<T> queryList, Function<Map<Boolean, Collection<T>>, List<T>> saveDelFun);
//
//    <T> List<T> json2TRflVal(Collection<Map<String, Object>> jsonCollection, Class<T> tClass);
//
//    <T> List<T> json2TRflVal1(Collection<Map<String, Object>> jsonCollection, Class<T> tClass, UnCheckBiFunction<Map<String, String>, T, T> finalBiFun);
//
//    <T> List<T> json2TNoValRef(Collection<Map<String, Object>> jsonCollection, Class<T> tClass);
//
//    <T> List<T> json2TNoValRef1(Collection<Map<String, Object>> jsonCollection, Class<T> tClass, UnCheckBiFunction<Map<String, String>, T, T> finalBiFun);
//
//    <T> List<T> obj2TRflVal(Collection jsonCollection, Class<T> tClass, UnCheckBiFunction<Map<String, String>, T, T> finalBiFun);
//
//    <T> Map<Boolean, Collection<T>> getFilterMap(List<T> rSaveList, Function<T, String> dbGroupFun, List<T> queryList);
//
//    <T> Map<Boolean, Collection<T>> getConditionFilterMap(List<T> rSaveList, Function<T, String> groupFun, Map<String, String> conditionMap, List<T> queryList);
//
//}
