package cn.jwis.product.pdm.customer.entity;

import cn.jwis.framework.base.annotation.Entity;
import cn.jwis.framework.base.domain.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

/**
 * 数组字典
 */
@Data
@EqualsAndHashCode
@Entity("DataDictionary")
public class DataDictionary extends BaseEntity{

    public static final String TYPE = "DataDictionary";

    public DataDictionary() {
        super.setType(TYPE);
    }

    /**
     * 唯一标识符
     */
    @NotBlank(message = "code不能为空")
    private String code;

    /**
     * 显示名称
     */
    @NotBlank(message = "显示名称不能为空")
    private String displayName;

    /**
     * 是否启用
     */
    private Boolean enable = true;



    /**
     * 数据类型，group,dictionary,data
     */
    @Pattern(regexp = "(group|dictionary|data)", message = "数据类型只能是group,dictionary,data等值，分组，字典，数据")
    private String dataType;

    /**
     * 上级节点code
     */
    private String parentCode;

    /**
     * 租户或容器oid
     */
    private String containerOid;

    /**
     * 容器oid
     */
    private String containerType;

}
