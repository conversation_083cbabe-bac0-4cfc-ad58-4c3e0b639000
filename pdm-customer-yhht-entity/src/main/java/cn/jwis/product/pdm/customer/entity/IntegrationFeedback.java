package cn.jwis.product.pdm.customer.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2023/8/27 13:10
 * @Email <EMAIL>
 */
@Data
@EqualsAndHashCode
public class IntegrationFeedback {
    // 标识本次发布的业务OID:  businessType + bizOid
    String businessOid;
    // 消费方：U9,SMIS等
    String consumer;
    // 是否消费成功
    Boolean isSuccess;
    // 消费失败时的原因
    String msg;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        IntegrationFeedback that = (IntegrationFeedback) o;
        return Objects.equals(businessOid, that.businessOid) && Objects.equals(consumer, that.consumer);
    }

    @Override
    public int hashCode() {
        return Objects.hash(businessOid, consumer);
    }

    public IntegrationFeedback(String businessOid, String consumer, Boolean isSuccess, String msg){
        this.businessOid = businessOid;
        this.consumer = consumer;
        this.isSuccess = isSuccess;
        this.msg = msg;
    }

}
