package cn.jwis.product.pdm.customer.entity;

import cn.jwis.platform.plm.foundation.common.response.InstanceEntity;
import cn.jwis.product.pdm.customer.entity.assistant.cvt.CvtBaseEntity;

public class PriSecFileEntity extends InstanceEntity implements CvtBaseEntity {

//    private List<String> primaryFile;
//    private List<String> secondaryFile;
//
//    public List<String> getPrimaryFile() {
//        return primaryFile;
//    }
//
//    public PriSecFileEntity setPrimaryFile(List<String> primaryFile) {
//        this.primaryFile = primaryFile;
//        return this;
//    }
//
//    public List<String> getSecondaryFile() {
//        return secondaryFile;
//    }
//
//    public PriSecFileEntity setSecondaryFile(List<String> secondaryFile) {
//        this.secondaryFile = secondaryFile;
//        return this;
//    }
//
//    public List<File> getPrimaryFileObjList(){
//        if(primaryFile != null)
//            return primaryFile.stream().map(it -> JSONUtil.toBean(new JSONObject(it), File.class)).collect(Collectors.toList());
//        return Collections.emptyList();
//    }
//
//    public List<File> getSecondFileObjList(){
//        if(secondaryFile != null)
//            return secondaryFile.stream().map(it -> JSONUtil.toBean(new JSONObject(it), File.class)).collect(Collectors.toList());
//        return Collections.emptyList();
//    }

}
