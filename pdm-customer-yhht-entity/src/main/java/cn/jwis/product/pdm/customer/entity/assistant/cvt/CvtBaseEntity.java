package cn.jwis.product.pdm.customer.entity.assistant.cvt;

public interface CvtBaseEntity<T, P> {

//    private String id;
//    private String classCode;
//    private String name;
//
//    public String getId() {
//        return id;
//    }
//
//    public void setId(String id) {
//        this.id = id;
//    }
//
//    public CvtBaseEntity setGetId(String id) {
//        this.id = id;
//        return this;
//    }
//
//    public String getClassCode() {
//        return classCode;
//    }
//
//    public void setClassCode(String classCode) {
//        this.classCode = classCode;
//    }
//
//    public CvtBaseEntity setGetClassCode(String classCode) {
//        this.classCode = classCode;
//        return this;
//    }
//
//    public String getName() {
//        return name;
//    }
//
//    public void setName(String name) {
//        this.name = name;
//    }
//
//    public CvtBaseEntity setGetName(String name) {
//        this.name = name;
//        return this;
//    }
//
//    int calcuCode(Object... fieldValArr){
//        return Arrays.stream(Optional.ofNullable(fieldValArr)
////                .orElseThrow(() -> new RuntimeException("参数不能为空")))
//                .orElseGet(null))
//                .filter(Objects::nonNull).reduce(0, (Integer r, Object obj) -> r + obj.hashCode(), (Integer r1, Integer r2) -> r1.hashCode() + r2.hashCode());
//    }
//
//    Boolean allBinaryFieldEquals(Object... fieldValArr){
//        int arrLength = fieldValArr.length, i = 0, j = 1;
//        if(fieldValArr == null || arrLength == 0 || (arrLength & 1) == 1)
//            throw new RuntimeException("参数个数必须是偶数且大于0");
//
//        for (; i < arrLength && (fieldValArr[i] == null ? fieldValArr[j] == null : fieldValArr[i].getClass().isArray() ?
//                Arrays.equals((Object[])fieldValArr[i], (Object[])fieldValArr[j]) : fieldValArr[i].equals(fieldValArr[j])); i += 2, j += 2);
//
//        return i == arrLength ? Boolean.TRUE : Boolean.FALSE;
//    }

}
