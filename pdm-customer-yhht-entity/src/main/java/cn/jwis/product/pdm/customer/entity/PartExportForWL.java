package cn.jwis.product.pdm.customer.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode
public class PartExportForWL {
    public static final String TYPE = "PartIteration";

    @ApiModelProperty("编码需求人")
    private String cn_jwis_xqr;

    @ApiModelProperty("主分类")
    private String clsDisplayName;

    @ApiModelProperty("转化后U9编码")
    private String u9Number;

    @ApiModelProperty("非筛选级/未成型物料")
    private String cn_jwis_sxzldj;

    @ApiModelProperty("料号")
    private String number;

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("规格")
    private String cn_jwis_gg;

    @ApiModelProperty("描述")
    private String description;

    @ApiModelProperty("形态属性")
    private String source;

    @ApiModelProperty("封装形式")
    private String cn_jwis_fzxs;

    @ApiModelProperty("生产厂家")
    private String cn_jwis_sccj;

    @ApiModelProperty("质量等级")
    private String cn_jwis_zldj;

    @ApiModelProperty("MSL(湿敏等级)")
    private String cn_jwis_smdj;

    @ApiModelProperty("库存主单位")
    private String defaultUnit;

    @ApiModelProperty("创建时间")
    private String createDate;


}
