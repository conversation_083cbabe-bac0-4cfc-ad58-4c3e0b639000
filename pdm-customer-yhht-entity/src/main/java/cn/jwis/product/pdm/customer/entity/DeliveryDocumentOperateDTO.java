package cn.jwis.product.pdm.customer.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("交付清单文档操作 DTO")
public class DeliveryDocumentOperateDTO {

    @ApiModelProperty(value = "PMS预期文档Id", required = true)
    private String expectDocumentId;

    @ApiModelProperty(value = "PDM文档Id", required = true)
    private String fileId;

    @ApiModelProperty(value = "文档名称", required = true)
    private String fileName;

    @ApiModelProperty(value = "文档链接")
    private String fileUrl;

    @ApiModelProperty(value = "文档上传者，用户id")
    private String uploaderId;

    @ApiModelProperty(value = "文档状态")
    private String staus;

    @ApiModelProperty(value = "实际完成时间，格式yyyy-MM-dd")
    private String actualFinishTime;

    @ApiModelProperty(value = "版本号")
    private String version;

    @ApiModelProperty(value = "标志位，1：上传，2：删除，3：版本变更")
    private String flag;
}