package cn.jwis.product.pdm.customer.dos;

import com.alibaba.excel.annotation.ExcelIgnore;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR> zhaikun
 * @Date ： 2024/2/20
 * @Description :
 */

@Data
@EqualsAndHashCode
public class FolderDto {


    /**
     * 下载
     */
    @ExcelIgnore
    private List<String> downloadAccounts;

    /**
     * 查看
     */
    @ExcelIgnore
    private List<String> readAccounts;



    /**
     * 校对
     */
    @ExcelIgnore
    private List<String> proofreaderAccounts;

    /**
     * 会签
     */
    @ExcelIgnore
    private List<String> signerAccounts;


    /**
     * 产保
     */
    @ExcelIgnore
    private List<String> cbAccounts;

    /**
     * 审核
     */
    @ExcelIgnore
    private List<String> shzAccounts;

    /**
     * 标核
     */
    @ExcelIgnore
    private List<String> bzhAccounts;


    /**
     * 批准
     */
    @ExcelIgnore
    private List<String> ratifierAccounts;


    /**
     * 前置抄送人
     */
    @ExcelIgnore
    private List<String> preCCAccounts;

    /**
     * 后置抄送人
     */
    @ExcelIgnore
    private List<String> postCCAccounts;
}
