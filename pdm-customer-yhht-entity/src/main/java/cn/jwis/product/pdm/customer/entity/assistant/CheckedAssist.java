package cn.jwis.product.pdm.customer.entity.assistant;

import cn.jwis.product.pdm.customer.entity.assistant.uncheckinterface.*;
import java.util.concurrent.Callable;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.function.*;

public interface CheckedAssist extends BaseAssist {

    CopyOnWriteArrayList<CheckedAssist> beanHolder = new CopyOnWriteArrayList<>();

    default <T, R> Function<T, R> applyNoThrow(UnCheckFunction<T, R> unCheckFunction) {
        return getBean(beanHolder).applyNoThrow(unCheckFunction);
    }

    default <R> IntFunction<R> applyInt2ObjNoThrow(UnCheckIntFunction<R> unCheckIntFunction) {
        return getBean(beanHolder).applyInt2ObjNoThrow(unCheckIntFunction);
    }

    default <T> Consumer<T> acceptNoThrow(UnCheckConsumer<T> unCheckConsumer){
        return getBean(beanHolder).acceptNoThrow(unCheckConsumer);
    }

    default <T, O> BiConsumer<T, O> biAcceptNoThrow(UnCheckBiConsumer<T, O> unCheckBiConsumer){
        return getBean(beanHolder).biAcceptNoThrow(unCheckBiConsumer);
    }

    default <T> Supplier<T> getNoThrow(UnCheckSupplier<T> unCheckSupplier){
        return getBean(beanHolder).getNoThrow(unCheckSupplier);
    }

    default Runnable runNoThrow(UnCheckRunnable unCheckRunnable){
        return getBean(beanHolder).runNoThrow(unCheckRunnable);
    }

    default <T> Callable<T> callNoThrow(UnCheckCallable<T> unCheckCallable){
        return getBean(beanHolder).callNoThrow(unCheckCallable);
    }

    default <T, U, R> BiFunction<T, U, R> biApplyNoThrow(UnCheckBiFunction<T, U, R> unCheckBiFunction){
        return getBean(beanHolder).biApplyNoThrow(unCheckBiFunction);
    }

    default <T> Predicate<T> testNoThrow(UncheckPredicate<T> uncheckPredicate){
        return getBean(beanHolder).testNoThrow(uncheckPredicate);
    }



    default <T, R> Function<T, R> applyThrow(UnCheckFunction<T, R> unCheckFunction) {
        return getBean(beanHolder).applyThrow(unCheckFunction);
    }

    default <R> IntFunction<R> applyInt2ObjThrow(UnCheckIntFunction<R> unCheckIntFunction) {
        return getBean(beanHolder).applyInt2ObjThrow(unCheckIntFunction);
    }

    default <T> Consumer<T> acceptThrow(UnCheckConsumer<T> unCheckConsumer){
        return getBean(beanHolder).acceptThrow(unCheckConsumer);
    }

    default <T, O> BiConsumer<T, O> biAcceptThrow(UnCheckBiConsumer<T, O> unCheckBiConsumer){
        return getBean(beanHolder).biAcceptThrow(unCheckBiConsumer);
    }

    default <T> Supplier<T> getThrow(UnCheckSupplier<T> unCheckSupplier){
        return getBean(beanHolder).getThrow(unCheckSupplier);
    }

    default Runnable runThrow(UnCheckRunnable unCheckRunnable){
        return getBean(beanHolder).runThrow(unCheckRunnable);
    }

    default <T> Callable<T> callThrow(UnCheckCallable<T> unCheckCallable){
        return getBean(beanHolder).callThrow(unCheckCallable);
    }

    default <T, U, R> BiFunction<T, U, R> biApplyThrow(UnCheckBiFunction<T, U, R> unCheckBiFunction){
        return getBean(beanHolder).biApplyThrow(unCheckBiFunction);
    }

    default <T> Predicate<T> testThrow(UncheckPredicate<T> uncheckPredicate){
        return getBean(beanHolder).testThrow(uncheckPredicate);
    }

}
