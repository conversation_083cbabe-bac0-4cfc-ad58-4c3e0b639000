package cn.jwis.product.pdm.customer.entity;

import cn.jwis.framework.base.annotation.Entity;
import cn.jwis.framework.base.domain.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2023/8/22 22:44
 * @Email <EMAIL>
 */
@Entity("DingTaskRecordSend")
@Data
@EqualsAndHashCode
public class DingTaskRecordSend extends BaseEntity {

    public static final String TYPE = "DingTaskRecordSend";

    public DingTaskRecordSend(String dingProcessInstanceId, String pdmTaskOid){
        super();
        super.setType(TYPE);
        super.setModelDefinition(TYPE);
        this.dingProcessInstanceId = dingProcessInstanceId;
        this.pdmTaskOid = pdmTaskOid;
    }

    public DingTaskRecordSend(){
        super();
    }
    // 钉钉的审批流ID
    private String dingProcessInstanceId;

    // PDM的taskID
    private String pdmTaskOid;

    // PDM的流程id
    private String pdmProcessInstanceId;

    // PDM的流程id
    private String pdmProcessOrderId;

    // PDM的流程名称
    private String pdmProcessName;

    // PDM的任务名称
    private String pdmTaskName;

    // PDM的任务责任人
    private String director;

    // 钉钉的审批结果，agree & reject
    private String result;

    // 钉钉的processCode
    private String processCode;

    //钉钉的businessId
    private String businessId;

    // 钉钉的审核意见
    private String remark;

    // 钉钉的消息状态
    private String dingType;
}
