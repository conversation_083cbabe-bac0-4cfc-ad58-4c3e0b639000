package cn.jwis.product.pdm.customer.entity;

import cn.jwis.framework.base.domain.able.TreeAble;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class CustomerFolderTreeNode implements TreeAble<CustomerFolderTreeNode> {

    private List<CustomerFolderTreeNode> children;

    @ApiModelProperty("名称")
    private String name;

    private String oid;

    private String catalogType;

    private String containerType;

    private String containerOid;

    @Override
    public List<CustomerFolderTreeNode> getChildren() {
        return children;
    }

    @Override
    public void setChildren(List<CustomerFolderTreeNode> children) {
        this.children = children;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Override
    public String getOid() {
        return oid;
    }

    @Override
//    @JsonIgnore
    public String getType() {
        return null;
    }

    public void setOid(String oid) {
        this.oid = oid;
    }

    public String getCatalogType() {
        return catalogType;
    }

    public void setCatalogType(String catalogType) {
        this.catalogType = catalogType;
    }

    public String getContainerType() {
        return containerType;
    }

    public void setContainerType(String containerType) {
        this.containerType = containerType;
    }

    public String getContainerOid() {
        return containerOid;
    }

    public void setContainerOid(String containerOid) {
        this.containerOid = containerOid;
    }
}
