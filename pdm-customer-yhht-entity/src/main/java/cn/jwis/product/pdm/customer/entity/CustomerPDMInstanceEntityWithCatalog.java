package cn.jwis.product.pdm.customer.entity;

import cn.jwis.product.pdm.container.entity.response.PDMInstanceEntityWithCatalog;
import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiParam;
import lombok.Data;


/**
 *
 *  <AUTHOR>
 */
@Data
public class CustomerPDMInstanceEntityWithCatalog extends PDMInstanceEntityWithCatalog {

    @ApiParam("部件分类名称")
    private String clsDisplayName;

    @ApiParam("部件分类代码")
    private String clsCode;

    @ApiParam("部件分类属性")
    private JSONObject clsProperty;


}
