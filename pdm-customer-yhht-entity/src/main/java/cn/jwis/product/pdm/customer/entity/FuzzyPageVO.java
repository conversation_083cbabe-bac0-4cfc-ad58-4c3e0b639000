package cn.jwis.product.pdm.customer.entity;

import cn.jwis.framework.base.dto.PageSimpleDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class FuzzyPageVO extends PageSimpleDTO {
    @ApiModelProperty("文件夹sub")
    private List<String> folderOids;

    @ApiModelProperty("需要查询的子类型")
    private List<String> subTypes;

    @ApiModelProperty("需要查询的模型类型")
    private List<String> models;

//    @ApiModelProperty("子类型")
//    private String modelDefinition;

    @ApiModelProperty("优选等级")
    private String preferenceLevel;

    @ApiModelProperty("供应商")
    private String supplierOid;

    @ApiModelProperty("选中的 oid")
    private List<String> oidList;
}
