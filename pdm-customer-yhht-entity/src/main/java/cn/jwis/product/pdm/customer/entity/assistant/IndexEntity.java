package cn.jwis.product.pdm.customer.entity.assistant;

public class IndexEntity<T> {

    private Integer index;

    private T t;

    public IndexEntity(Integer index, T t) {
        this.index = index;
        this.t = t;
    }

    public Integer getIndex() {
        return index;
    }

    public IndexEntity<T> setIndex(Integer index) {
        this.index = index;
        return this;
    }

    public T getT() {
        return t;
    }

    public IndexEntity<T> setT(T t) {
        this.t = t;
        return this;
    }
}
