//package cn.jwis.product.pdm.customer.entity.assistant.util;
//
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//
//import java.io.IOException;
//import java.io.InputStream;
//import java.util.Collection;
//import java.util.Enumeration;
//import java.util.function.BiFunction;
//import java.util.zip.ZipEntry;
//import java.util.zip.ZipFile;
//
//public class ZipAssistant {
//
//    private static final Logger logger = LoggerFactory.getLogger(ZipAssistant.class);
//
//    public static <T> Collection<T> unzipFile(ZipFile zipFile, BiFunction<ZipEntry, InputStream, T> tBiFunction, Collection<T> gatherCollection){
//        try {
//            Enumeration<? extends ZipEntry> entries = zipFile.entries();
//            while (entries.hasMoreElements()) {
//                ZipEntry entry = entries.nextElement();
//                if (!entry.isDirectory()){
//                    T t = tBiFunction.apply(entry, zipFile.getInputStream(entry));
//                    if (gatherCollection != null)
//                        gatherCollection.add(t);
//                }
//            }
//        } catch (IOException e) {
//            logger.error(e.getMessage(), e);
//        }
//        return gatherCollection;
//    }
//
//}
