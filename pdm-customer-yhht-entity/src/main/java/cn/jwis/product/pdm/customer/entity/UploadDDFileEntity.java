package cn.jwis.product.pdm.customer.entity;

import java.io.InputStream;

public class UploadDDFileEntity {

    public UploadDDFileEntity(InputStream inputStream, String fileCompleteName, String fileSimpleName) {
        this.inputStream = inputStream;
        this.fileCompleteName = fileCompleteName;
        this.fileSimpleName = fileSimpleName;
    }

    private InputStream inputStream;
    private String fileCompleteName;
    private String fileSimpleName;

    public InputStream getInputStream() {
        return inputStream;
    }

    public UploadDDFileEntity setInputStream(InputStream inputStream) {
        this.inputStream = inputStream;
        return this;
    }

    public String getFileCompleteName() {
        return fileCompleteName;
    }

    public UploadDDFileEntity setFileCompleteName(String fileCompleteName) {
        this.fileCompleteName = fileCompleteName;
        return this;
    }

    public String getFileSimpleName() {
        return fileSimpleName;
    }

    public UploadDDFileEntity setFileSimpleName(String fileSimpleName) {
        this.fileSimpleName = fileSimpleName;
        return this;
    }
}
