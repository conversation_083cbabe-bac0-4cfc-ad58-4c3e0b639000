package cn.jwis.product.pdm.customer.entity;

import cn.jwis.product.pdm.customer.entity.assistant.anno.ExcelAnno;
import cn.jwis.product.pdm.customer.entity.assistant.anno.ExcelTitleProp;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


@Data
public class DeliveryReportCustom {

    @ApiModelProperty(notes = "阶段")
    private String deliveryBigType;
    @ApiModelProperty(notes = "区分")
    private String deliverySecondType;
    @ApiModelProperty(notes = "类别")
    private String deliverySmallType;
    @ApiModelProperty(notes = "交付清单")
    private String deliveryFourthType;
    @ApiModelProperty(notes = "交付文档")
    private String deliverydocument;
    @ApiModelProperty(notes = "部门")
    private String position;
    @ApiModelProperty(notes = "负责人")
    private String responPerson;
    @ApiModelProperty(notes = "文档状态")
    private String status;
    private String jhsj;
    private String wcsj;
    private Integer sort;

    public String getDeliveryBigType() {
        return deliveryBigType;
    }

    public DeliveryReportCustom setDeliveryBigType(String deliveryBigType) {
        this.deliveryBigType = deliveryBigType;
        return this;
    }

    public String getDeliverySmallType() {
        return deliverySmallType;
    }

    public DeliveryReportCustom setDeliverySmallType(String deliverySmallType) {
        this.deliverySmallType = deliverySmallType;
        return this;
    }

    public String getDeliverydocument() {
        return deliverydocument;
    }

    public DeliveryReportCustom setDeliverydocument(String deliverydocument) {
        this.deliverydocument = deliverydocument;
        return this;
    }

    public String getPosition() {
        return position;
    }

    public DeliveryReportCustom setPosition(String position) {
        this.position = position;
        return this;
    }

    public String getResponPerson() {
        return responPerson;
    }

    public DeliveryReportCustom setResponPerson(String responPerson) {
        this.responPerson = responPerson;
        return this;
    }

    public String getStatus() {
        return status;
    }

    public DeliveryReportCustom setStatus(String status) {
        this.status = status;
        return this;
    }



    public String getJhsj() {
        return jhsj;
    }

    public DeliveryReportCustom setJhsj(String jhsj) {
        this.jhsj = jhsj;
        return this;
    }

    public String getWcsj() {
        return wcsj;
    }

    public DeliveryReportCustom setWcsj(String wcsj) {
        this.wcsj = wcsj;
        return this;
    }

    public String getDeliverySecondType() {
        return deliverySecondType;
    }

    public void setDeliverySecondType(String deliverySecondType) {
        this.deliverySecondType = deliverySecondType;
    }

    public String getDeliveryFourthType() {
        return deliveryFourthType;
    }

    public void setDeliveryFourthType(String deliveryFourthType) {
        this.deliveryFourthType = deliveryFourthType;
    }



    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }
}
