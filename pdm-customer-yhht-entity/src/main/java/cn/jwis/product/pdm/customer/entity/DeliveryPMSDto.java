package cn.jwis.product.pdm.customer.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@ApiModel("交付清单批量处理DTO")
@Data
public class DeliveryPMSDto {

    @ApiModelProperty("是否子项目 true-是 false-否")
    private Boolean isSubProject;

    @ApiModelProperty("项目ID")
    private String projectId;

    @ApiModelProperty("项目编号")
    private String projectCode;

    @ApiModelProperty("交付清单项列表")
    private List<DeliveryItemDTO> deliveryItemDTOs;
}