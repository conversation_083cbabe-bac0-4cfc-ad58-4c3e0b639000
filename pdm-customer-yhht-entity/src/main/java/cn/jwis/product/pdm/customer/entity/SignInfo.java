package cn.jwis.product.pdm.customer.entity;

import com.itextpdf.text.pdf.PdfReader;
import com.itextpdf.text.pdf.parser.PdfReaderContentParser;

public class SignInfo {

    private PdfReaderContentParser pdfReaderContentParser;

    private PdfReader pdfReader;

    private Boolean revert = Boolean.FALSE;

    private String pdfTemplateName;

    public String getPdfTemplateName() {
        return pdfTemplateName;
    }

    public void setPdfTemplateName(String pdfTemplateName) {
        this.pdfTemplateName = pdfTemplateName;
    }

    public SignInfo(PdfReaderContentParser pdfReaderContentParser, PdfReader pdfReader, Boolean revert) {
        this.pdfReaderContentParser = pdfReaderContentParser;
        this.pdfReader = pdfReader;
        this.revert = revert;
    }
    public SignInfo(PdfReaderContentParser pdfReaderContentParser, PdfReader pdfReader, <PERSON>ole<PERSON> revert,String  pdfTemplateName) {
        this.pdfReaderContentParser = pdfReaderContentParser;
        this.pdfReader = pdfReader;
        this.revert = revert;
        this.pdfTemplateName = pdfTemplateName;

    }


    public PdfReaderContentParser getPdfReaderContentParser() {
        return pdfReaderContentParser;
    }

    public SignInfo setPdfReaderContentParser(PdfReaderContentParser pdfReaderContentParser) {
        this.pdfReaderContentParser = pdfReaderContentParser;
        return this;
    }

    public PdfReader getPdfReader() {
        return pdfReader;
    }

    public SignInfo setPdfReader(PdfReader pdfReader) {
        this.pdfReader = pdfReader;
        return this;
    }

    public Boolean getRevert() {
        return revert;
    }

    public SignInfo setRevert(Boolean revert) {
        this.revert = revert;
        return this;
    }
}
