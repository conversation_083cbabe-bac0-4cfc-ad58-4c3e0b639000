package cn.jwis.product.pdm.customer.entity;

import cn.jwis.framework.base.annotation.Relationship;
import cn.jwis.framework.base.domain.entity.BaseRelationship;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode
@Relationship(Screen.TYPE)
public class Screen extends BaseRelationship {

    public transient static final String TYPE = "SCREEN";

    public Screen(){
        setType(TYPE);
    }
}
