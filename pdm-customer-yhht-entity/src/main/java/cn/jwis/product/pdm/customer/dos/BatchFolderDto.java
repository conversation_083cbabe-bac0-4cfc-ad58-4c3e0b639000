package cn.jwis.product.pdm.customer.dos;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> zhaikun
 * @Date ： 2024/2/2
 * @Description :
 */

@Data
@EqualsAndHashCode
public class BatchFolderDto extends FolderDto{

    @ExcelProperty(value = "层级1的代号", index = 1)
    private String level1Code;
    @ExcelProperty(value = "层级1", index = 2)
    private String level1;

    @ExcelProperty(value = "层级2的代号", index = 3)
    private String level2Code;

    @ExcelProperty(value = "层级2", index = 4)
    private String level2;

    @ExcelProperty(value = "层级3", index = 5)
    private String level3;

    @ExcelProperty(value = "层级4", index = 6)
    private String level4;


    @ExcelProperty(value = "成员", index = 7)
    private String members;



    @ExcelProperty(value = "校对", index = 8)
    private String proofreader;


    @ExcelProperty(value = "审核", index = 9)
    private String shz;


    @ExcelProperty(value = "会签", index = 10)
    private String counterSigner;

    @ExcelProperty(value = "批准", index = 11)
    private String ratifier;


    @ExcelProperty(value = "后置抄送人", index = 12)
    private String postCC;

}
