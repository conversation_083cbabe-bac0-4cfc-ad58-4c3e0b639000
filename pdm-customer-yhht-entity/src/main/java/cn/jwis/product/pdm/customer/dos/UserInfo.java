package cn.jwis.product.pdm.customer.dos;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
@EqualsAndHashCode()
public class UserInfo implements Cloneable {

    private String name;
    private String roleName;
    private List<String> users;

    @Override
    public UserInfo clone() throws CloneNotSupportedException {
        return (UserInfo) super.clone();
    }
}
