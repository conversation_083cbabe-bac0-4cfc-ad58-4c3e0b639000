package cn.jwis.product.pdm.customer.entity;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.fasterxml.jackson.annotation.JsonInclude.Include.NON_NULL;

/**
 * <AUTHOR> yefei
 * @Date ： 2024/3/26
 * @Description :
 */

@Data
@EqualsAndHashCode
public class CreoPropertyNode implements CreoTreeNode {

    private String name;

    private String code;

    private String type;

    @JsonInclude(NON_NULL)
    private List<Map<String,String>> enumerate;

    private boolean required;

    @Override
    public void addChildren(CreoTreeNode child) {
        // doNothing
    }

    @Override
    public void addEnumerate(Map<String, String> map) {
        if(ObjectUtils.isEmpty(enumerate)) {
            enumerate = new ArrayList<>();
        }
        enumerate.add(map);
    }
}
