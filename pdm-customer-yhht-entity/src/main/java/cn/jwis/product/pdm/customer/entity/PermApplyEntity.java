package cn.jwis.product.pdm.customer.entity;

import cn.jwis.framework.base.annotation.Entity;
import cn.jwis.framework.base.domain.entity.BaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@Entity("PermApply")
@JsonIgnoreProperties(allowSetters = true, value = {"index", "size"})
public class PermApplyEntity extends BaseEntity implements Cloneable {

    public static final String TYPE = "PermApply";

    private String containerOid;
    private String containerName;
    private List<String> folderOid;
    @ApiModelProperty(value = "节点")
    private List<String> folderName;
    @ApiModelProperty(value = "权限") // 0只读 1可下载
    private String permission;
    private String reason;
    private String ddCode;
    private String ddInstanceId;
    private String ddUserId;
    private String ddUserName;
    private String ddUnionId;
    @ApiModelProperty(value = "状态")
    private String status;
    @ApiModelProperty(value = "开通时间")
    private Long approvedTime;
    private String tenantOid;
    private int index = 1;
    private int size = 20;
    private int count;
    private int pageIndex;
    private int pageSize;
    private String nodeName;

    public String getContainerOid() {
        return containerOid;
    }

    public void setContainerOid(String containerOid) {
        this.containerOid = containerOid;
    }

    public String getContainerName() {
        return containerName;
    }

    public void setContainerName(String containerName) {
        this.containerName = containerName;
    }

    public List<String> getFolderOid() {
        return folderOid;
    }

    public void setFolderOid(List<String> folderOid) {
        this.folderOid = folderOid;
    }

    public List<String> getFolderName() {
        return folderName;
    }

    public void setFolderName(List<String> folderName) {
        this.folderName = folderName;
    }

    public String getPermission() {
        return permission;
    }

    public void setPermission(String permission) {
        this.permission = permission;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getDdCode() {
        return ddCode;
    }

    public void setDdCode(String ddCode) {
        this.ddCode = ddCode;
    }

    public String getDdInstanceId() {
        return ddInstanceId;
    }

    public void setDdInstanceId(String ddInstanceId) {
        this.ddInstanceId = ddInstanceId;
    }

    public String getType() {
        return TYPE;
    }

    public String getDdUserId() {
        return ddUserId;
    }

    public void setDdUserId(String ddUserId) {
        this.ddUserId = ddUserId;
    }

    public String getDdUserName() {
        return ddUserName;
    }

    public void setDdUserName(String ddUserName) {
        this.ddUserName = ddUserName;
    }

    public String getDdUnionId() {
        return ddUnionId;
    }

    public void setDdUnionId(String ddUnionId) {
        this.ddUnionId = ddUnionId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Long getApprovedTime() {
        return approvedTime;
    }

    public void setApprovedTime(Long approvedTime) {
        this.approvedTime = approvedTime;
    }

    @Override
    public String getTenantOid() {
        return tenantOid;
    }

    @Override
    public void setTenantOid(String tenantOid) {
        this.tenantOid = tenantOid;
    }

    public int getIndex() {
        return index;
    }

    public PermApplyEntity setIndex(int index) {
        this.index = index;
        return this;
    }

    public int getSize() {
        return size;
    }

    public PermApplyEntity setSize(int size) {
        this.size = size;
        return this;
    }

    public int getCount() {
        return count;
    }

    public PermApplyEntity setCount(int count) {
        this.count = count;
        return this;
    }

    public int getPageIndex() {
        return pageIndex;
    }

    public PermApplyEntity setPageIndex(int pageIndex) {
        this.pageIndex = pageIndex;
        return this;
    }

    public int getPageSize() {
        return pageSize;
    }

    public PermApplyEntity setPageSize(int pageSize) {
        this.pageSize = pageSize;
        return this;
    }

    public String getNodeName() {
        return nodeName;
    }

    public PermApplyEntity setNodeName(String nodeName) {
        this.nodeName = nodeName;
        return this;
    }

    @Override
    public PermApplyEntity clone() throws CloneNotSupportedException {
        return (PermApplyEntity) super.clone();
    }
}
