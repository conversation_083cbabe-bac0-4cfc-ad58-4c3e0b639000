package cn.jwis.product.pdm.customer.entity.assistant.sort;

import java.util.ArrayList;
import java.util.List;

public abstract class LinkNode<T, P, C> {
    public transient LinkNode<T, P, C> prev;
    public transient List<T> next = new ArrayList<>();
    public transient List<T> allSubList = new ArrayList<>();
    public Boolean subGathered = Boolean.FALSE;

    public abstract C getCode();

    public abstract T setCode(C code);

    public abstract C getParentCode();

    public abstract T setParentCode(C parentCode);
}