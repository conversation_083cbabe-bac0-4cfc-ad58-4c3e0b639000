package cn.jwis.product.pdm.customer.entity.assistant;

import com.alibaba.fastjson.JSONObject;
import java.util.*;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

public interface SafeWrapAssist extends BaseAssist {

    String EMPTY_STR = "";

    CopyOnWriteArrayList<SafeWrapAssist> beanHolder = new CopyOnWriteArrayList<>();

    default <T> List<T> safeWrapList(List<T> tList){
        return getBean(beanHolder).safeWrapList(tList);
    }

    default <T> Set<T> safeWrapSet(Set<T> tSet){
        return getBean(beanHolder).safeWrapSet(tSet);
    }

    default <K, V> Map<K, V> safeWrapMap(Map<K, V> tMap){
        return Optional.ofNullable(tMap).orElse(Collections.emptyMap());
    }

    default String getOrElse(String tStr){
        return Optional.ofNullable(tStr).orElse(EMPTY_STR);
    }

    default String getOrElse(JSONObject json, String key){
        return getOrElse(json , j -> j.getString(key), EMPTY_STR);
    }

    default <T> T getOrElse(T t, T defaultVal){
        return Optional.ofNullable(t).orElse(defaultVal);
    }

    default String getOrElse(JSONObject json, String key, String defaultStr){
        return getOrElse(json.getString(key), defaultStr);
    }

    default <T, R> R getOrElse(T t, Function<T, R> function){
        return Optional.ofNullable(t).map(it -> function.apply(it)).orElse(null);
    }

    default <T, R> R getOrElse(T t, Function<T, R> function, R defaultR){
        return Optional.ofNullable(t).map(it -> function.apply(it)).orElse(defaultR);
    }

    default <T> Optional<T> accept(T t, Consumer<T> consumer){
        return Optional.ofNullable(t).map(it -> {
            consumer.accept(it);
            return it;
        });
    }

    default void accept(Boolean excute, Runnable runnable){
        if(excute)
            runnable.run();;
    }

    default <T> Optional<T> run(T t, Runnable runnable){
        return Optional.ofNullable(t).map(it -> {
            runnable.run();
            return it;
        });
    }

    default <T> Collection<T> forEach(Collection<T> tCollection, Consumer<T> consumer){
        return Optional.ofNullable(tCollection).map(collection -> {
            collection.forEach(t -> accept(t, consumer));
            return collection;
        }).orElse(tCollection);
    }

    default <T> Stream<T> streamThen(Collection<T> tCollection){
        return Optional.ofNullable(tCollection).map(collection -> collection.stream()).orElse((Stream<T>) Collections.emptyList().stream());
    }

    default <T, R> List<R> collect(Collection<T> tCollection, Function<T, R> function){
        return Optional.ofNullable(tCollection).map(collection -> collection.stream().map(t -> function.apply(t)).filter(Objects::nonNull).collect(Collectors.toList())).orElse(Collections.emptyList());
    }

    default <T, R> List<R> collect(Collection<T> tCollection, Function<T, R> function, R defaultR){
        return Optional.ofNullable(tCollection).map(collection -> collection.stream().map(t -> getOrElse(t, function, defaultR)).filter(Objects::nonNull).collect(Collectors.toList())).orElse(Collections.emptyList());
    }

    default <T, K, V> Map toMapCollect(Collection<T> tCollection, Function<T, K> keyFun, Function<T, V> valFun){
        return Optional.ofNullable(tCollection).map(collection -> collection.stream().collect(Collectors.toMap(t -> keyFun.apply(t), t -> valFun.apply(t), (v1, v2) -> v1))).orElse(Collections.emptyMap());
    }

    default <T, R> Set<R> toSetCollect(Collection<T> tCollection, Function<T, R> valFun){
        return Optional.ofNullable(tCollection).map(collection -> collection.stream().map(t -> valFun.apply(t)).filter(Objects::nonNull).collect(Collectors.toSet())).orElse(Collections.emptySet());
    }

    default <T, R> Stream<R> collectThen(Collection<T> tCollection, Function<T, R> function){
        return Optional.ofNullable(tCollection).map(collection -> collection.stream().map(t -> function.apply(t)).filter(Objects::nonNull)).orElse((Stream<R>) Collections.emptyList().stream());
    }

    default <T, R> List<R> flatMapCollect(Collection<T> tCollection, Function<T, Stream<R>> function){
        return Optional.ofNullable(tCollection).map(collection -> collection.stream().flatMap(t -> function.apply(t)).filter(Objects::nonNull).collect(Collectors.toList())).orElse(Collections.emptyList());
    }

    default <T, R> Stream<R> flatMapCollectThen(Collection<T> tCollection, Function<T, Stream<R>> function){
        return Optional.ofNullable(tCollection).map(collection -> collection.stream().flatMap(t -> function.apply(t)).filter(Objects::nonNull)).orElse((Stream<R>) Collections.emptyList().stream());
    }

    default <T> List<T> filterCollect(Collection<T> tCollection, Predicate<T> predicate){
        return Optional.ofNullable(tCollection).map(collection -> collection.stream().filter(t -> predicate.test(t)).collect(Collectors.toList())).orElse(Collections.emptyList());
    }

    default <T> Stream<T> filterCollectThen(Collection<T> tCollection, Predicate<T> predicate){
        return Optional.ofNullable(tCollection).map(collection -> collection.stream().filter(t -> predicate.test(t))).orElse((Stream<T>) Collections.emptyList().stream());
    }

    default <T> String joinCollect(Collection<T> tCollection, Function<T, String> function, String delimiter){
        return Optional.ofNullable(tCollection).map(collection -> collection.stream().map(t -> function.apply(t)).collect(Collectors.joining(delimiter))).orElse(EMPTY_STR);
    }

    default <T> Stream<IndexEntity<T>> forEachIndex(List<T> list){
        return IntStream.range(0, list.size()).mapToObj(i -> new IndexEntity<>(i, list.get(i)));
    }

}
