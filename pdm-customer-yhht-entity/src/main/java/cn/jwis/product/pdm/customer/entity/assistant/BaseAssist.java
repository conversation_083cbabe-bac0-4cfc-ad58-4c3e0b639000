package cn.jwis.product.pdm.customer.entity.assistant;

import cn.jwis.framework.base.util.SpringContextUtil;
import org.springframework.context.ApplicationContext;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;

public interface BaseAssist {

    CopyOnWriteArrayList<ApplicationContext> applicationContextList = new CopyOnWriteArrayList<>();
    Map<Class, Object> beanMap = new ConcurrentHashMap();
    Map<String, Object> beanNameMap = new ConcurrentHashMap();

    default ApplicationContext getAppContext(){
        if(applicationContextList.size() > 0)
            return applicationContextList.get(0);
        else {
            ApplicationContext applicationContext = SpringContextUtil.getApplicationContext();
            applicationContextList.add(applicationContext);
            return applicationContext;
        }
    }

    default <T> T getBean(Class<T> tClass){
        T t;
        if((t = (T) beanMap.get(tClass)) != null)
            return t;
        else {
            t = getAppContext().getBean(tClass);
            if(t != null)
                beanMap.put(tClass ,t);
            return t;
        }
    }

    default <T> T getBean(String beanName){
        T t;
        if((t = (T) beanNameMap.get(beanName)) != null)
            return t;
        else {
            t = (T) getAppContext().getBean(beanName);
            if(t != null)
                beanNameMap.put(beanName, t);
            return t;
        }
    }

    default <T extends BaseAssist> T getBean(CopyOnWriteArrayList<T> beanHolder){
        if(beanHolder.size() > 0)
            return beanHolder.get(0);
        else {
            String className = Thread.currentThread().getStackTrace()[2].getClassName();
            String beanName = className.substring(className.lastIndexOf(".") + 1);
            T bean = (T) getAppContext().getBean(beanName);
            beanHolder.add(bean);
            return bean;
        }
    }

}
