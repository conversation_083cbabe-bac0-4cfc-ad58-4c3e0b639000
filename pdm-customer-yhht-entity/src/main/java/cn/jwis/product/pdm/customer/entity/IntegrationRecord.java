package cn.jwis.product.pdm.customer.entity;

import cn.jwis.framework.base.annotation.Entity;
import cn.jwis.framework.base.domain.entity.BaseEntity;
import cn.jwis.framework.base.util.OidGenerator;
import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2023/8/9 22:48
 * @Email <EMAIL>
 */
@Entity("IntegrationRecord")
@Data
@EqualsAndHashCode
public class IntegrationRecord extends BaseEntity {
    public static final String TYPE = "IntegrationRecord";

    public IntegrationRecord(){
        super();
    }

    public IntegrationRecord(String businessType,String operating,
                             String processNumber,String businessOid,JSONObject data){
        super();
        super.setOid(OidGenerator.newOid());
        super.setType(TYPE);
        super.setModelDefinition(TYPE);
        this.businessType = businessType;
        this.operating = operating;
        this.processNumber = processNumber;
        this.businessOid = businessOid;
        this.data = data;
        // 填充主要属性，用于前端展示,技术变更单的biz要再查一层
        JSONObject bizObject = data;
        if(data.containsKey("part")){
            bizObject = data.getJSONObject("part");
        }
        this.bizNumber = bizObject.getString("number");
        this.bizName = bizObject.getString("name");
        this.bizVersion = bizObject.getString("version");
        this.bizOid = bizObject.getString("oid");
        this.bizType = bizObject.getString("type");
        this.bizModelDefinition = bizObject.getString("modelDefinition");
        this.setOwner(bizObject.getString("updatorAccount"));
    }
    // 标识这条数据，用于接收下游的回调
    private String businessOid;
    // 发布的业务数据的oid
    private String bizOid;
    // 发布的业务数据的number
    private String bizNumber;
    // 发布的业务数据的name
    private String bizName;
    // 发布的业务数据的type
    private String bizType;
    // 发布的业务数据的modelDefinition
    private String bizModelDefinition;
    // 发布的业务数据的显示版本
    //private String bizDisplayVersion;
    // 发布的业务数据的大版本
    private String bizVersion;
    // 发布的业务数据的小版本
    //private String bizIteration;
    // 业务类型：item,bom,doc,techChange
    private String businessType;
    // 发布的数据
    private JSONObject data;
    // 流程number
    private String processNumber;
    // 操作来源，流程还是手工发布:process or  tool
    private String operating;
    // 推送mq结果
    private Boolean mqSuccess;
    // 消费方：U9,SMIS等
    String consumer;
    // 消费方是否消费成功
    Boolean isSuccess;
    // 消费方消费失败时的原因
    String msg;
    // 流程executeId
    private String executeId;

    private Boolean isPartWithOutRM;

    // 慧致造操作类型-ActionName（ImportBOM/UpdateBOM）
    private String operationType;
}
