package cn.jwis.product.pdm.customer.entity;

import lombok.Data;

@Data
public class DeliveryReportCustomExport {

    private String deliveryBigType;//阶段
    private String deliverySecondType;//区分
    private String deliverySmallType;//类别
    private String deliveryFourthType;//交付清单
    private String deliverydocument;//交付文档
    private String position;//部门
    private String responPerson;//负责人
    private String status;//文档状态
    private String jhsj;//计划时间
    private String wcsj;//完成时间

    public DeliveryReportCustomExport(DeliveryReportCustom custom) {
        this.deliveryBigType = custom.getDeliveryBigType();
        this.deliverySecondType = custom.getDeliverySecondType();
        this.deliverySmallType = custom.getDeliverySmallType();
        this.deliveryFourthType = custom.getDeliveryFourthType();
        this.deliverydocument = custom.getDeliverydocument();
        this.position = custom.getPosition();
        this.responPerson = custom.getResponPerson();
        this.status = custom.getStatus();
        this.jhsj = custom.getJhsj();
        this.wcsj = custom.getWcsj();
    }

    public String getDeliveryBigType() {
        return deliveryBigType;
    }

    public DeliveryReportCustomExport setDeliveryBigType(String deliveryBigType) {
        this.deliveryBigType = deliveryBigType;
        return this;
    }

    public String getDeliverySmallType() {
        return deliverySmallType;
    }

    public DeliveryReportCustomExport setDeliverySmallType(String deliverySmallType) {
        this.deliverySmallType = deliverySmallType;
        return this;
    }

    public String getDeliverydocument() {
        return deliverydocument;
    }

    public DeliveryReportCustomExport setDeliverydocument(String deliverydocument) {
        this.deliverydocument = deliverydocument;
        return this;
    }

    public String getPosition() {
        return position;
    }

    public DeliveryReportCustomExport setPosition(String position) {
        this.position = position;
        return this;
    }

    public String getResponPerson() {
        return responPerson;
    }

    public DeliveryReportCustomExport setResponPerson(String responPerson) {
        this.responPerson = responPerson;
        return this;
    }

    public String getStatus() {
        return status;
    }

    public DeliveryReportCustomExport setStatus(String status) {
        this.status = status;
        return this;
    }



    public String getJhsj() {
        return jhsj;
    }

    public DeliveryReportCustomExport setJhsj(String jhsj) {
        this.jhsj = jhsj;
        return this;
    }

    public String getWcsj() {
        return wcsj;
    }

    public DeliveryReportCustomExport setWcsj(String wcsj) {
        this.wcsj = wcsj;
        return this;
    }

    public String getDeliverySecondType() {
        return deliverySecondType;
    }

    public void setDeliverySecondType(String deliverySecondType) {
        this.deliverySecondType = deliverySecondType;
    }

    public String getDeliveryFourthType() {
        return deliveryFourthType;
    }

    public void setDeliveryFourthType(String deliveryFourthType) {
        this.deliveryFourthType = deliveryFourthType;
    }

}
