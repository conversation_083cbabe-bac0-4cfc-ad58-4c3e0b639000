package cn.jwis.product.pdm.customer.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> yefei
 * @Date ： 2024/3/5
 * @Description :
 */

@Data
@EqualsAndHashCode
public class CustomerDeliveryTreeNode {
    private List<CustomerDeliveryTreeNode> children = new ArrayList<>();
    private String name;
    private String parentOid;
    private String oid;
    private boolean root;
    private String modelDefinition;
    private String modelIcon;
}
