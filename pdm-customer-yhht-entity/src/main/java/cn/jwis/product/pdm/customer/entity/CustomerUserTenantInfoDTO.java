package cn.jwis.product.pdm.customer.entity;

import cn.jwis.platform.plm.account.auth.dto.UserTenantAuthInfo;
import cn.jwis.platform.plm.account.auth.dto.UserTenantInfoDTO;
import cn.jwis.platform.plm.account.entity.tenant.Tenant;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> yefei
 * @Date ： 2024/4/24
 * @Description :
 */
@Data
public class CustomerUserTenantInfoDTO {

    private List<Tenant> tenantList;
    private Tenant defaultTanant;
    private CustomerUserTenantAuthInfo userTenantAuthInfo;
}
