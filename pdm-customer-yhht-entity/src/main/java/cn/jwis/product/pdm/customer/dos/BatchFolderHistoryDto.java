package cn.jwis.product.pdm.customer.dos;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> zhaikun
 * @Date ： 2024/2/2
 * @Description :
 */

@Data
@EqualsAndHashCode
public class BatchFolderHistoryDto extends FolderDto{

    @ExcelProperty(value = "全路径", index = 1)
    private String fullPath;
    @ExcelProperty(value = "节点编码", index = 2)
    private String nodeCode;

    @ExcelProperty(value = "产保", index = 3)
    private String cb;

    @ExcelProperty(value = "校对", index = 4)
    private String proofreader;

    @ExcelProperty(value = "审核", index = 5)
    private String shz;


    @ExcelProperty(value = "会签", index = 6)
    private String counterSigner;

    @ExcelProperty(value = "标审", index = 7)
    private String bzh;

    @ExcelProperty(value = "批准", index = 8)
    private String ratifier;


    @ExcelProperty(value = "前置抄送人", index = 9)
    private String preCC;
    @ExcelProperty(value = "后置抄送人", index = 10)
    private String postCC;


    @ExcelProperty(value = "下载权限", index = 11)
    private String members;


    @ExcelProperty(value = "查看", index = 12)
    private String reads;





}
