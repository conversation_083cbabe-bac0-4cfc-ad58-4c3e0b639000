package cn.jwis.product.pdm.customer.entity.assistant.anno;

import java.lang.annotation.*;

@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface ExcelAnno {

    int order() default 0;

    enum ConvertType{NULL, Date, Datetime}

    ConvertType fieldCvtType() default ConvertType.NULL;

    ExcelTitleProp[] titleInfos() default @ExcelTitleProp;

    ExcelDataProp dataInfos() default @ExcelDataProp;

}
