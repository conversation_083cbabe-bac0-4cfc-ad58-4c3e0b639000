package cn.jwis.product.pdm.customer.entity;

import cn.jwis.product.pdm.customer.entity.assistant.anno.ExcelAnno;
import cn.jwis.product.pdm.customer.entity.assistant.anno.ExcelTitleProp;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "交付文档报表实体", description = "")
public class DeliveryReport {

    @ApiModelProperty(notes = "阶段")
    @ExcelAnno(titleInfos = @ExcelTitleProp("阶段"), order = 0)
    private String deliveryBigType;
    @ApiModelProperty(notes = "区分")
//    @ExcelAnno(titleInfos = @ExcelTitleProp("区分"), order = 0)
    private String deliverySecondType;
    @ApiModelProperty(notes = "类别")
    @ExcelAnno(titleInfos = @ExcelTitleProp("类别"), order = 0)
    private String deliverySmallType;
    @ApiModelProperty(notes = "项目")
//    @ExcelAnno(titleInfos = @ExcelTitleProp("项目"), order = 0)
    private String deliveryFourthType;
    @ExcelAnno(titleInfos = @ExcelTitleProp("交付文档"), order = 100)
    @ApiModelProperty(notes = "交付文档")
    private String deliverydocument;
//    @ExcelAnno(titleInfos = @ExcelTitleProp("上级部门"), order = 300)
    @ApiModelProperty(notes = "上级部门")
    private String  parentPosition;
//    @ExcelAnno(titleInfos = @ExcelTitleProp("部门"), order = 300)
    @ApiModelProperty(notes = "部门")
    private String position;
    @ApiModelProperty(notes = "负责人")
    @ExcelAnno(titleInfos = @ExcelTitleProp("负责人"), order = 300)
    private String responPerson;
    @ApiModelProperty(notes = "文档状态")
    @ExcelAnno(titleInfos = @ExcelTitleProp("文档状态"), order = 400)
    private String status;
    @ApiModelProperty(notes = "交付文档oid")
    private String documentOid;
    @ExcelAnno(titleInfos = @ExcelTitleProp("计划时间"), order = 500)
    private String jhsj;
    @ExcelAnno(titleInfos = @ExcelTitleProp("完成时间"), order = 600)
    private String wcsj;
    private Integer sort;

    public String getDeliveryBigType() {
        return deliveryBigType;
    }

    public DeliveryReport setDeliveryBigType(String deliveryBigType) {
        this.deliveryBigType = deliveryBigType;
        return this;
    }

    public String getDeliverySmallType() {
        return deliverySmallType;
    }

    public DeliveryReport setDeliverySmallType(String deliverySmallType) {
        this.deliverySmallType = deliverySmallType;
        return this;
    }

    public String getDeliverydocument() {
        return deliverydocument;
    }

    public DeliveryReport setDeliverydocument(String deliverydocument) {
        this.deliverydocument = deliverydocument;
        return this;
    }

    public String getPosition() {
        return position;
    }

    public DeliveryReport setPosition(String position) {
        this.position = position;
        return this;
    }

    public String getResponPerson() {
        return responPerson;
    }

    public DeliveryReport setResponPerson(String responPerson) {
        this.responPerson = responPerson;
        return this;
    }

    public String getStatus() {
        return status;
    }

    public DeliveryReport setStatus(String status) {
        this.status = status;
        return this;
    }

    public String getDocumentOid() {
        return documentOid;
    }

    public DeliveryReport setDocumentOid(String documentOid) {
        this.documentOid = documentOid;
        return this;
    }

    public String getJhsj() {
        return jhsj;
    }

    public DeliveryReport setJhsj(String jhsj) {
        this.jhsj = jhsj;
        return this;
    }

    public String getWcsj() {
        return wcsj;
    }

    public DeliveryReport setWcsj(String wcsj) {
        this.wcsj = wcsj;
        return this;
    }

    public String getDeliverySecondType() {
        return deliverySecondType;
    }

    public void setDeliverySecondType(String deliverySecondType) {
        this.deliverySecondType = deliverySecondType;
    }

    public String getDeliveryFourthType() {
        return deliveryFourthType;
    }

    public void setDeliveryFourthType(String deliveryFourthType) {
        this.deliveryFourthType = deliveryFourthType;
    }
    public String getParentPosition() {
        return parentPosition;
    }

    public void setParentPosition(String parentPosition) {
        this.parentPosition = parentPosition;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }
}
