package cn.jwis.product.pdm.customer.launcher;

import cn.jwis.framework.base.annotation.EnableJWIScannerSupporter;
import cn.jwis.framework.configration.ConfigCenterHelper;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> hsy
 * @Date ： Created in 2022/1/7 10:05
 * @Email ： <EMAIL>
 * @Description :
 */
@EnableAspectJAutoProxy
@EnableTransactionManagement
@EnableFeignClients("cn.jwis")
@EnableJWIScannerSupporter
@SpringBootApplication(scanBasePackages = {"cn.jwis"})
public class CustomerMicroApp {

    public static final String SERVICE_NAME = "customer-micro-service";

    public static void main(String[] args) {
        SpringApplication springApplication = new SpringApplication(CustomerMicroApp.class);
        Map<String, Object> defaultMap = new HashMap<>();
        ConfigCenterHelper.getConfig(defaultMap, SERVICE_NAME);// 调用sdk包内读取配置的方法
        springApplication.setDefaultProperties(defaultMap);// 将配置加载到启动项中
        springApplication.run(args);
    }
}
